#!/usr/bin/env python3
"""
Comprehensive debug program to test dialog positioning with Excel file
This will test all dialogs and ensure they appear on the correct screen
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import pandas as pd
from pathlib import Path

class DialogPositionDebugger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Dialog Position Debugger")
        self.root.geometry("800x600+100+100")
        
        # Colors for Visual Studio styling
        self.vs_colors = {
            'form_bg': '#f0f0f0',
            'control_bg': '#ffffff',
            'button_bg': '#e1e1e1',
            'text_color': '#000000',
            'group_bg': '#f0f0f0',
            'accent_blue': '#007acc'
        }
        
        self.root.configure(bg=self.vs_colors['form_bg'])
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the debug GUI"""
        # Title
        title_label = tk.Label(self.root, text="Dialog Position Debugger", 
                              font=('Microsoft Sans Serif', 14, 'bold'),
                              bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'])
        title_label.pack(pady=20)
        
        # Position info
        self.position_label = tk.Label(self.root, text="", 
                                     font=('Microsoft Sans Serif', 10),
                                     bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'])
        self.position_label.pack(pady=10)
        
        # Update position continuously
        self.update_position_info()
        
        # Instructions
        instructions = tk.Label(self.root, 
                              text="1. Move this window to your second screen\n"
                                   "2. Click the test buttons below\n"
                                   "3. Check if dialogs appear on the same screen as this window",
                              font=('Microsoft Sans Serif', 10),
                              bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'],
                              justify='left')
        instructions.pack(pady=20)
        
        # Test buttons frame
        button_frame = tk.Frame(self.root, bg=self.vs_colors['form_bg'])
        button_frame.pack(pady=20)
        
        # Test buttons
        tests = [
            ("Test Standard Messagebox", self.test_standard_messagebox),
            ("Test Standard Askstring", self.test_standard_askstring),
            ("Test Custom Positioned Dialog", self.test_custom_positioned_dialog),
            ("Test Excel Column Mapping", self.test_excel_column_mapping),
            ("Create Test Excel File", self.create_test_excel_file),
            ("Test Component Finder Integration", self.test_component_finder_integration)
        ]
        
        for i, (text, command) in enumerate(tests):
            btn = tk.Button(button_frame, text=text, command=command,
                          bg=self.vs_colors['button_bg'], fg=self.vs_colors['text_color'],
                          font=('Microsoft Sans Serif', 9), width=30, height=2)
            btn.pack(pady=5)
            
    def update_position_info(self):
        """Update position information continuously"""
        try:
            # Get various position methods
            x = self.root.winfo_x()
            y = self.root.winfo_y()
            rootx = self.root.winfo_rootx()
            rooty = self.root.winfo_rooty()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            
            position_text = f"Window Position:\n"
            position_text += f"winfo_x/y: ({x}, {y})\n"
            position_text += f"winfo_rootx/y: ({rootx}, {rooty})\n"
            position_text += f"Size: {width}x{height}"
            
            self.position_label.config(text=position_text)
        except:
            pass
            
        # Schedule next update
        self.root.after(1000, self.update_position_info)
        
    def test_standard_messagebox(self):
        """Test standard tkinter messagebox"""
        print("=== TESTING STANDARD MESSAGEBOX ===")
        self.log_current_position("Before messagebox")
        
        result = messagebox.showinfo("Standard Messagebox Test", 
                                   "This is a standard tkinter messagebox.\n"
                                   "Is it on the same screen as the main window?")
        print(f"Messagebox result: {result}")
        
    def test_standard_askstring(self):
        """Test standard tkinter askstring"""
        print("=== TESTING STANDARD ASKSTRING ===")
        self.log_current_position("Before askstring")
        
        result = simpledialog.askstring("Standard Askstring Test",
                                      "Enter some text:\n"
                                      "Is this dialog on the same screen as the main window?",
                                      initialvalue="test")
        print(f"Askstring result: {result}")
        
    def test_custom_positioned_dialog(self):
        """Test custom positioned dialog"""
        print("=== TESTING CUSTOM POSITIONED DIALOG ===")
        self.log_current_position("Before custom dialog")
        
        dialog = tk.Toplevel(self.root)
        dialog.title("Custom Positioned Dialog")
        dialog.configure(bg=self.vs_colors['form_bg'])
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Position it using absolute coordinates
        self.position_dialog_on_main_window(dialog, 400, 200)
        
        # Content
        main_frame = tk.Frame(dialog, bg=self.vs_colors['form_bg'], padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        label = tk.Label(main_frame, 
                        text="This is a custom positioned dialog.\n"
                             "Is it on the same screen as the main window?",
                        bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'],
                        font=('Microsoft Sans Serif', 10))
        label.pack(pady=20)
        
        ok_btn = tk.Button(main_frame, text="OK", command=dialog.destroy,
                         bg=self.vs_colors['button_bg'], fg=self.vs_colors['text_color'],
                         font=('Microsoft Sans Serif', 9), width=10)
        ok_btn.pack()
        
        dialog.wait_window()
        
    def position_dialog_on_main_window(self, dialog, width, height):
        """Position dialog centered on main window"""
        try:
            # Force updates
            self.root.update()
            self.root.update_idletasks()
            
            # Get absolute coordinates
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()
            
            print(f"Main window absolute position: ({main_x}, {main_y}) size: {main_width}x{main_height}")
            
            # Calculate centered position
            dialog_x = main_x + (main_width - width) // 2
            dialog_y = main_y + (main_height - height) // 2
            
            print(f"Positioning dialog at: ({dialog_x}, {dialog_y})")
            
            # Set geometry
            dialog.geometry(f"{width}x{height}+{dialog_x}+{dialog_y}")
            dialog.lift()
            dialog.attributes('-topmost', True)
            dialog.after(100, lambda: dialog.attributes('-topmost', False))
            
        except Exception as e:
            print(f"Error positioning dialog: {e}")
            dialog.geometry(f"{width}x{height}")
            
    def test_excel_column_mapping(self):
        """Test Excel column mapping dialog (like Component Finder)"""
        print("=== TESTING EXCEL COLUMN MAPPING DIALOG ===")
        self.log_current_position("Before column mapping dialog")
        
        # Create sample data
        sample_columns = ['Column A', 'Column B', 'Manufacturer', 'Part Number', 'Column E']
        
        dialog = tk.Toplevel(self.root)
        dialog.title("📊 Column Mapping Test")
        dialog.configure(bg=self.vs_colors['form_bg'])
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Position it
        self.position_dialog_on_main_window(dialog, 600, 400)
        
        # Content
        main_frame = tk.Frame(dialog, bg=self.vs_colors['form_bg'], padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        title_label = tk.Label(main_frame, text="Excel Column Mapping Test",
                             font=('Microsoft Sans Serif', 12, 'bold'),
                             bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'])
        title_label.pack(pady=(0, 20))
        
        # Manufacturer dropdown
        tk.Label(main_frame, text="Manufacturer Column:",
                bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'],
                font=('Microsoft Sans Serif', 9)).pack(anchor='w')
        
        mfg_var = tk.StringVar(value=sample_columns[2])
        mfg_combo = ttk.Combobox(main_frame, textvariable=mfg_var, values=sample_columns, width=30)
        mfg_combo.pack(pady=(5, 15), anchor='w')
        
        # Part number dropdown
        tk.Label(main_frame, text="Part Number Column:",
                bg=self.vs_colors['form_bg'], fg=self.vs_colors['text_color'],
                font=('Microsoft Sans Serif', 9)).pack(anchor='w')
        
        part_var = tk.StringVar(value=sample_columns[3])
        part_combo = ttk.Combobox(main_frame, textvariable=part_var, values=sample_columns, width=30)
        part_combo.pack(pady=(5, 20), anchor='w')
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg=self.vs_colors['form_bg'])
        button_frame.pack()
        
        def on_ok():
            print(f"Selected - Manufacturer: {mfg_var.get()}, Part: {part_var.get()}")
            dialog.destroy()
            
        ok_btn = tk.Button(button_frame, text="OK", command=on_ok,
                         bg=self.vs_colors['button_bg'], fg=self.vs_colors['text_color'],
                         font=('Microsoft Sans Serif', 9), width=10)
        ok_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(button_frame, text="Cancel", command=dialog.destroy,
                             bg=self.vs_colors['button_bg'], fg=self.vs_colors['text_color'],
                             font=('Microsoft Sans Serif', 9), width=10)
        cancel_btn.pack(side='left')
        
        dialog.wait_window()
        
    def create_test_excel_file(self):
        """Create a test Excel file for testing"""
        try:
            # Create sample data
            data = {
                'Manufacturer': ['Texas Instruments', 'Murata', 'Analog Devices'],
                'Part Number': ['LM358P', 'GCM155R71H104KE02D', 'AD8065'],
                'Description': ['Op Amp', 'Capacitor', 'Op Amp'],
                'Package': ['DIP-8', '0402', 'SOIC-8']
            }
            
            df = pd.DataFrame(data)
            excel_file = Path("test_components.xlsx")
            df.to_excel(excel_file, index=False)
            
            messagebox.showinfo("Excel File Created", 
                              f"Test Excel file created: {excel_file}\n"
                              f"Contains {len(data['Manufacturer'])} sample components")
            print(f"Created test Excel file: {excel_file}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not create Excel file: {e}")
            print(f"Error creating Excel file: {e}")
            
    def test_component_finder_integration(self):
        """Test integration with Component Finder"""
        print("=== TESTING COMPONENT FINDER INTEGRATION ===")
        try:
            # Try to import and test Component Finder
            from component_finder import ComponentFinderGUI
            
            # Create a test instance
            test_root = tk.Toplevel(self.root)
            test_root.title("Component Finder Test")
            test_root.geometry("1000x750+200+200")
            
            finder = ComponentFinderGUI(test_root)
            
            messagebox.showinfo("Component Finder Test", 
                              "Component Finder instance created.\n"
                              "Test the dialogs in the Component Finder window.")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not test Component Finder: {e}")
            print(f"Error testing Component Finder: {e}")
            
    def log_current_position(self, context):
        """Log current window position"""
        try:
            x = self.root.winfo_x()
            y = self.root.winfo_y()
            rootx = self.root.winfo_rootx()
            rooty = self.root.winfo_rooty()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            
            print(f"{context}:")
            print(f"  winfo_x/y: ({x}, {y})")
            print(f"  winfo_rootx/y: ({rootx}, {rooty})")
            print(f"  Size: {width}x{height}")
            
        except Exception as e:
            print(f"Error logging position: {e}")
            
    def run(self):
        """Run the debugger"""
        print("=== DIALOG POSITION DEBUGGER STARTED ===")
        print("Move the main window to your second screen and test the dialogs")
        self.root.mainloop()

if __name__ == "__main__":
    debugger = DialogPositionDebugger()
    debugger.run()
