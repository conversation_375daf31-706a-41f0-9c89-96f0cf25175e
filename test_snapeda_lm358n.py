#!/usr/bin/env python3
"""
Test SnapEDA specifically with LM358N to debug download issues
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_driver():
    """Setup Chrome driver with download preferences"""
    download_dir = os.path.abspath("3d")
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    chrome_options = Options()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def login_to_snapeda(driver):
    """Login to SnapEDA"""
    print("🔐 Logging into SnapEDA...")
    
    # Go to login page
    driver.get("https://www.snapeda.com/account/login/")
    
    # Wait for page to load
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "id_username"))
    )
    
    # Fill credentials
    email_field = driver.find_element(By.ID, "id_username")
    password_field = driver.find_element(By.ID, "id_password")
    
    email_field.send_keys("<EMAIL>")
    password_field.send_keys("Lenny123!")
    
    # Click login button
    login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
    login_button.click()
    
    # Wait for login to complete
    time.sleep(3)
    
    # Check if login was successful
    if "home" in driver.current_url or "dashboard" in driver.current_url:
        print("✅ Login successful!")
        return True
    else:
        print("❌ Login failed!")
        return False

def search_for_part(driver, manufacturer, part_number):
    """Search for the specific part"""
    print(f"🔍 Searching for {manufacturer} {part_number}...")
    
    # Try direct URL first
    direct_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer}/view-part/"
    print(f"🔍 Trying direct URL: {direct_url}")
    
    driver.get(direct_url)
    time.sleep(3)
    
    # Check if we're on a valid part page
    if "view-part" in driver.current_url and part_number.lower() in driver.current_url.lower():
        print("✅ Found part via direct URL")
        return True
    
    # If direct URL failed, try search
    print("⚠️ Direct URL failed, trying search...")
    
    # Go to search page
    search_url = f"https://www.snapeda.com/search?q={part_number}"
    driver.get(search_url)
    time.sleep(3)
    
    # Look for search results
    try:
        # Wait for search results
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".search-result, .part-result"))
        )
        
        # Find the specific part result
        results = driver.find_elements(By.XPATH, f"//a[contains(@href, '{part_number}') and contains(@href, '{manufacturer}')]")
        
        if results:
            print(f"✅ Found {len(results)} search results")
            # Click the first result
            results[0].click()
            time.sleep(3)
            print("✅ Clicked search result")
            return True
        else:
            print("❌ No matching search results found")
            return False
            
    except TimeoutException:
        print("❌ Search results did not load")
        return False

def find_3d_model_tab(driver):
    """Find and click the 3D Model tab"""
    print("🔸 Looking for 3D Model tab...")
    
    # Common selectors for 3D model tab
    tab_selectors = [
        "//a[contains(text(), '3D Model')]",
        "//a[contains(text(), '3D')]",
        "//button[contains(text(), '3D Model')]",
        "//button[contains(text(), '3D')]",
        "//div[contains(@class, 'tab') and contains(text(), '3D')]",
        "//li[contains(text(), '3D')]//a"
    ]
    
    for selector in tab_selectors:
        try:
            tab = driver.find_element(By.XPATH, selector)
            if tab.is_displayed():
                print(f"✅ Found 3D Model tab with selector: {selector}")
                tab.click()
                time.sleep(2)
                print("✅ Clicked 3D Model tab")
                return True
        except:
            continue
    
    print("❌ 3D Model tab not found")
    return False

def find_download_elements(driver):
    """Find all potential download elements and analyze them"""
    print("🔍 Analyzing page for download elements...")
    
    # Get page source for analysis
    page_source = driver.page_source.lower()
    
    # Check for 3D model indicators
    indicators = ['3d model', 'step', 'download', '3d file', 'cad model']
    found_indicators = [ind for ind in indicators if ind in page_source]
    
    if found_indicators:
        print(f"✅ Found 3D indicators in page: {found_indicators}")
    else:
        print("❌ No 3D model indicators found in page")
    
    # Find all clickable elements that might be download buttons
    download_selectors = [
        "//a[contains(text(), 'Download')]",
        "//button[contains(text(), 'Download')]",
        "//a[contains(text(), '3D Model')]",
        "//button[contains(text(), '3D Model')]",
        "//a[contains(text(), 'STEP')]",
        "//button[contains(text(), 'STEP')]",
        "//a[contains(@class, 'download')]",
        "//button[contains(@class, 'download')]",
        "//a[contains(@href, 'download')]",
        "//a[contains(@href, 'step')]",
        "//a[contains(@href, '3d')]"
    ]
    
    print("🔍 Looking for download elements...")
    download_elements = []
    
    for selector in download_selectors:
        try:
            elements = driver.find_elements(By.XPATH, selector)
            for elem in elements:
                if elem.is_displayed():
                    text = elem.text.strip()
                    href = elem.get_attribute('href') or 'no-href'
                    class_attr = elem.get_attribute('class') or 'no-class'
                    download_elements.append({
                        'element': elem,
                        'text': text,
                        'href': href,
                        'class': class_attr,
                        'selector': selector
                    })
        except:
            continue
    
    if download_elements:
        print(f"✅ Found {len(download_elements)} potential download elements:")
        for i, elem_info in enumerate(download_elements[:10]):  # Show first 10
            print(f"   Element {i+1}: '{elem_info['text']}' -> {elem_info['href'][:50]}...")
            print(f"              Class: {elem_info['class']}")
            print(f"              Selector: {elem_info['selector']}")
        return download_elements
    else:
        print("❌ No download elements found")
        return []

def test_snapeda_lm358n():
    """Test SnapEDA with LM358N specifically"""
    print("🎯 TESTING SNAPEDA WITH LM358N")
    print("=" * 50)
    
    driver = setup_driver()
    
    try:
        # Step 1: Login
        if not login_to_snapeda(driver):
            return False
        
        # Step 2: Search for LM358N
        if not search_for_part(driver, "Texas Instruments", "LM358N"):
            return False
        
        print(f"📍 Current URL: {driver.current_url}")
        
        # Step 3: Find 3D Model tab
        if not find_3d_model_tab(driver):
            print("⚠️ No 3D Model tab found, analyzing current page...")
        
        # Step 4: Analyze download elements
        download_elements = find_download_elements(driver)
        
        if download_elements:
            print("🎯 Attempting to click first download element...")
            try:
                first_element = download_elements[0]['element']
                driver.execute_script("arguments[0].click();", first_element)
                print("✅ Clicked download element")
                
                # Wait and check for downloads
                time.sleep(5)
                
                # Check download directory
                download_dir = os.path.abspath("3d")
                files_after = os.listdir(download_dir)
                new_files = [f for f in files_after if f.endswith(('.step', '.stp', '.zip'))]
                
                if new_files:
                    print(f"🎉 SUCCESS! Downloaded: {new_files}")
                    return True
                else:
                    print("❌ No files downloaded")
                    return False
                    
            except Exception as e:
                print(f"❌ Error clicking download: {e}")
                return False
        else:
            print("❌ No download elements found - part may not have 3D model")
            return False
    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    test_snapeda_lm358n()
