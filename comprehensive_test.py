#!/usr/bin/env python3
"""
Comprehensive test script for Component Finder
Tests all major functionality without user intervention
"""

import sys
import time
import threading
from unittest.mock import patch, MagicMock
import tkinter as tk

def test_component_finder():
    """Test the component finder with various scenarios"""
    
    print("🚀 Starting Comprehensive Component Finder Test")
    print("=" * 60)
    
    try:
        # Import the component finder
        from component_finder import ComponentFinderGUI
        
        # Create a test root window
        root = tk.Tk()
        root.withdraw()  # Hide the window during testing
        
        # Create the GUI instance
        app = ComponentFinderGUI(root)
        
        print("✅ Component Finder GUI created successfully")
        
        # Test 1: Texas Instruments LM358N (should find via Digi-Key/Mouser)
        print("\n📋 Test 1: Texas Instruments LM358N")
        print("-" * 40)
        
        app.manufacturer_var.set("Texas Instruments")
        app.part_number_var.set("LM358N")
        
        # Mock the GUI update methods to prevent actual GUI operations
        app.add_comment = lambda msg: print(f"   {msg}")
        app.status_text = MagicMock()
        app.pdf_file_text = MagicMock()
        app.step_file_text = MagicMock()
        app.pdf_file_text.get.return_value = ""
        app.step_file_text.get.return_value = ""
        
        # Mock the ask_for_help methods to prevent user prompts
        app.ask_for_website = lambda *args: None
        app.ask_for_help_finding_file = lambda *args: None
        
        # Run the search
        print("   🔍 Starting search...")
        app.search_component("Texas Instruments", "LM358N")
        
        print("✅ Test 1 completed")
        
        # Test 2: Check API credentials
        print("\n📋 Test 2: API Credentials Check")
        print("-" * 40)
        
        # Check Digi-Key credentials
        try:
            with open('digikey_api_credentials.json', 'r') as f:
                import json
                dk_creds = json.load(f)
                if 'client_id' in dk_creds and 'client_secret' in dk_creds:
                    print("   ✅ Digi-Key API credentials found")
                else:
                    print("   ❌ Digi-Key API credentials incomplete")
        except FileNotFoundError:
            print("   ❌ Digi-Key API credentials file not found")
        
        # Check Mouser credentials
        try:
            with open('mouser_api_credentials.json', 'r') as f:
                import json
                mouser_creds = json.load(f)
                if 'api_key' in mouser_creds:
                    print("   ✅ Mouser API credentials found")
                else:
                    print("   ❌ Mouser API credentials incomplete")
        except FileNotFoundError:
            print("   ❌ Mouser API credentials file not found")
        
        # Test 3: Check 3D Model Search Options
        print("\n📋 Test 3: 3D Model Search Options")
        print("-" * 40)
        
        # Check if 3D search options are enabled
        ultra_enabled = app.search_3d_ultralibrarian.get()
        samacsys_enabled = app.search_3d_samacsys.get()
        snapeda_enabled = app.search_3d_snapeda.get()
        
        print(f"   UltraLibrarian: {'✅ Enabled' if ultra_enabled else '❌ Disabled'}")
        print(f"   SamacSys: {'✅ Enabled' if samacsys_enabled else '❌ Disabled'}")
        print(f"   SnapEDA: {'✅ Enabled' if snapeda_enabled else '❌ Disabled'}")
        
        if not (ultra_enabled or samacsys_enabled or snapeda_enabled):
            print("   ⚠️ All 3D searches are disabled - enabling them for testing")
            app.search_3d_ultralibrarian.set(True)
            app.search_3d_samacsys.set(True)
            app.search_3d_snapeda.set(True)
        
        # Test 4: Test Distributor API Methods
        print("\n📋 Test 4: Distributor API Methods")
        print("-" * 40)
        
        try:
            # Test Digi-Key API
            print("   🔍 Testing Digi-Key API...")
            dk_result = app.try_digikey_api_fallback("Texas Instruments", "LM358N")
            if dk_result:
                print("   ✅ Digi-Key API working")
            else:
                print("   ❌ Digi-Key API not working")
        except Exception as e:
            print(f"   ❌ Digi-Key API error: {str(e)[:50]}")
        
        try:
            # Test Mouser API
            print("   🔍 Testing Mouser API...")
            mouser_result = app.try_mouser_api_fallback("Texas Instruments", "LM358N")
            if mouser_result:
                print("   ✅ Mouser API working")
            else:
                print("   ❌ Mouser API not working")
        except Exception as e:
            print(f"   ❌ Mouser API error: {str(e)[:50]}")
        
        # Test 5: Test 3D Model Search
        print("\n📋 Test 5: 3D Model Search")
        print("-" * 40)
        
        try:
            print("   🔍 Testing 3D model search...")
            step_result = app.search_step_enhanced("Texas Instruments", "LM358N")
            if step_result and step_result.get('success'):
                print(f"   ✅ 3D model found via {step_result.get('source', 'unknown')}")
            else:
                print("   ❌ No 3D models found")
        except Exception as e:
            print(f"   ❌ 3D model search error: {str(e)[:50]}")
        
        print("\n" + "=" * 60)
        print("🎉 Comprehensive test completed!")
        print("✅ All major components tested")
        
        # Clean up
        root.destroy()
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_component_finder()
