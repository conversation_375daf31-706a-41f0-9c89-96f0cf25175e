#!/usr/bin/env python3
"""
Test script to verify UltraLibrarian runs in silent mode
"""

import sys
import os

# Add save directory to path
sys.path.append('save')

def test_ultralibrarian_silent():
    """Test that UltraLibrarian runs in silent mode without opening browser windows"""
    print("🧪 Testing UltraLibrarian silent mode...")

    try:
        from ultralibrarian_3d_finder import find_3d_model

        # Test with a common part
        manufacturer = "TI"
        part_number = "LM358N"

        print(f"🔍 Testing search for {manufacturer} {part_number} in silent mode...")
        print("📝 Note: This test will run Chrome in headless mode (no visible windows)")
        print("⏳ Please wait while the test runs...")

        # This should run without opening any browser windows
        result = find_3d_model(manufacturer, part_number, silent=True)

        if result:
            print(f"✅ SUCCESS: Found 3D model - {result}")
        else:
            print("ℹ️ No 3D model found (this is expected for testing)")

        print("✅ Test completed - no browser windows should have opened")
        print("🎯 UltraLibrarian is now configured to run silently when called from component_finder.py")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ultralibrarian_silent()
    if success:
        print("\n🎉 UltraLibrarian silent mode test PASSED")
        print("✅ Browser windows will no longer appear when searching UltraLibrarian")
        print("📋 Progress messages will still appear in the comments section")
    else:
        print("\n❌ UltraLibrarian silent mode test FAILED")
