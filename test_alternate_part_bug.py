#!/usr/bin/env python3
"""
Test Alternate Part Number Bug
Extract and test the alternate part selection code to fix the bug
"""

import tkinter as tk
from tkinter import messagebox, simpledialog

class AlternatePartTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Alternate Part Number Bug Test")
        self.root.geometry("600x500")
        
        # Mock VS colors for styling
        self.vs_colors = {
            'form_bg': '#f0f0f0',
            'control_bg': 'white',
            'text_color': 'black',
            'button_bg': '#e1e1e1',
            'accent_blue': '#0078d4'
        }
        
        self.create_test_interface()
    
    def create_test_interface(self):
        """Create test interface"""
        tk.Label(self.root, text="Alternate Part Number Bug Test", 
                font=('Arial', 16, 'bold')).pack(pady=20)
        
        tk.Label(self.root, text="This tests the alternate part selection bug\n"
                                "Click 'Test Alternate Selection' and select a part\n"
                                "Check if it shows the selection dialog again",
                justify=tk.CENTER).pack(pady=10)
        
        tk.Button(self.root, text="Test Alternate Selection (Single)", 
                 command=self.test_single_alternate, width=30).pack(pady=5)
        
        tk.Button(self.root, text="Test Alternate Selection (Multiple)", 
                 command=self.test_multiple_alternates, width=30).pack(pady=5)
        
        tk.Button(self.root, text="Quit", command=self.root.quit, width=30).pack(pady=20)
        
        # Results display
        self.results_text = tk.Text(self.root, height=15, width=70)
        self.results_text.pack(pady=10, fill=tk.BOTH, expand=True)
        
        self.log("Alternate Part Test Ready")
    
    def log(self, message):
        """Log message to results"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()
    
    def _show_messagebox_on_main(self, msg_type, title, message, **kwargs):
        """Simplified messagebox function"""
        if msg_type == 'yesno':
            return messagebox.askyesno(title, message, parent=self.root, **kwargs)
        elif msg_type == 'info':
            return messagebox.showinfo(title, message, parent=self.root, **kwargs)
        else:
            return messagebox.showinfo(title, message, parent=self.root, **kwargs)
    
    def show_alternate_part_acceptance_dialog(self, manufacturer, original_part, alternates):
        """EXACT COPY of the alternate part selection code from component_finder.py"""
        try:
            self.log(f"🔄 Starting alternate dialog for {original_part}")
            
            if not alternates:
                self.log("❌ No alternates provided")
                return None

            # Create message with alternates
            alternate_list = "\n".join([f"  • {alt}" for alt in alternates[:5]])  # Show max 5

            self.log(f"🔄 Showing alternate part numbers dialog")
            response = self._show_messagebox_on_main("yesno",
                "Alternate Part Numbers Found",
                f"Original part number '{original_part}' not found for {manufacturer}.\n\n"
                f"Found these alternate part numbers:\n{alternate_list}\n\n"
                f"Would you like to accept one of these alternates?\n\n"
                f"YES = Select an alternate\n"
                f"NO = Continue with original part number"
            )
            
            self.log(f"🔄 User response to alternate question: {response}")

            if response:  # YES - Select alternate
                if len(alternates) == 1:
                    # Only one alternate, ask for confirmation
                    self.log(f"🔄 Single alternate - showing confirmation dialog")
                    confirm = self._show_messagebox_on_main("yesno",
                        "Confirm Alternate",
                        f"Use '{alternates[0]}' instead of '{original_part}'?"
                    )
                    if confirm:
                        self.log(f"✅ User confirmed single alternate: {original_part} → {alternates[0]}")
                        return alternates[0]
                    else:
                        self.log(f"❌ User rejected single alternate")
                        return None
                else:
                    # Multiple alternates, let user choose - VS Forms style
                    self.log(f"🔄 Multiple alternates - showing selection dialog")
                    choice_dialog = tk.Toplevel(self.root)
                    choice_dialog.title("Select Alternate Part Number")
                    choice_dialog.geometry("500x400")
                    choice_dialog.configure(bg=self.vs_colors['form_bg'])
                    choice_dialog.transient(self.root)
                    choice_dialog.grab_set()

                    selected_part = tk.StringVar()

                    # Title with VS Forms styling
                    title_label = tk.Label(choice_dialog,
                                         text=f"Select Alternate for {original_part}",
                                         font=('Microsoft Sans Serif', 10, 'bold'),
                                         bg=self.vs_colors['form_bg'],
                                         fg=self.vs_colors['text_color'])
                    title_label.pack(pady=10)

                    # Listbox for alternates with VS Forms styling
                    listbox_frame = tk.Frame(choice_dialog, bg=self.vs_colors['form_bg'])
                    listbox_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                    listbox = tk.Listbox(listbox_frame,
                                       font=('Microsoft Sans Serif', 9),
                                       bg=self.vs_colors['control_bg'],
                                       fg=self.vs_colors['text_color'],
                                       selectbackground=self.vs_colors['accent_blue'],
                                       selectforeground='white',
                                       relief='sunken',
                                       borderwidth=1)
                    scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
                    listbox.config(yscrollcommand=scrollbar.set)
                    scrollbar.config(command=listbox.yview)

                    for alt in alternates:
                        listbox.insert(tk.END, alt)

                    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                    # Buttons with VS Forms styling
                    button_frame = tk.Frame(choice_dialog, bg=self.vs_colors['form_bg'])
                    button_frame.pack(pady=10)

                    def on_select():
                        selection = listbox.curselection()
                        if selection:
                            selected_part.set(alternates[selection[0]])
                            self.log(f"🔄 User selected from listbox: {selected_part.get()}")
                            choice_dialog.destroy()
                        else:
                            self.log("❌ No selection made in listbox")

                    def on_cancel():
                        selected_part.set("")
                        self.log(f"🔄 User cancelled selection")
                        choice_dialog.destroy()

                    # VS Forms style buttons
                    select_btn = tk.Button(button_frame, text="Select", command=on_select,
                                         bg=self.vs_colors['button_bg'],
                                         fg=self.vs_colors['text_color'],
                                         font=('Microsoft Sans Serif', 8),
                                         relief='raised',
                                         borderwidth=1,
                                         width=10)
                    select_btn.pack(side=tk.LEFT, padx=5)

                    cancel_btn = tk.Button(button_frame, text="Cancel", command=on_cancel,
                                         bg=self.vs_colors['button_bg'],
                                         fg=self.vs_colors['text_color'],
                                         font=('Microsoft Sans Serif', 8),
                                         relief='raised',
                                         borderwidth=1,
                                         width=10)
                    cancel_btn.pack(side=tk.LEFT, padx=5)

                    # Wait for dialog to close
                    self.log(f"🔄 Waiting for user selection...")
                    choice_dialog.wait_window()

                    chosen_part = selected_part.get()
                    self.log(f"🔄 After dialog closed, chosen_part = '{chosen_part}'")
                    
                    if chosen_part:
                        self.log(f"✅ SUCCESS: Accepted alternate part number: {original_part} → {chosen_part}")
                        return chosen_part
                    else:
                        self.log(f"❌ No part chosen - user cancelled or no selection")
                        return None

            # If we get here, user either said NO or cancelled
            self.log(f"⏭️ User said NO - continuing with original part number: {original_part}")
            return None  # Return None instead of original_part to avoid confusion

        except Exception as e:
            self.log(f"⚠️ ERROR in alternate part dialog: {str(e)}")
            return None
    
    def test_single_alternate(self):
        """Test with single alternate"""
        self.log("\n" + "="*50)
        self.log("TESTING SINGLE ALTERNATE")
        self.log("="*50)
        
        manufacturer = "Test Manufacturer"
        original_part = "ORIGINAL-123"
        alternates = ["ALTERNATE-456"]
        
        result = self.show_alternate_part_acceptance_dialog(manufacturer, original_part, alternates)
        
        self.log(f"FINAL RESULT: {result}")
        if result == alternates[0]:
            self.log("✅ TEST PASSED: Correctly returned selected alternate")
        elif result is None:
            self.log("⚠️ TEST RESULT: User cancelled or said no")
        else:
            self.log(f"❌ TEST FAILED: Expected '{alternates[0]}' or None, got '{result}'")
    
    def test_multiple_alternates(self):
        """Test with multiple alternates"""
        self.log("\n" + "="*50)
        self.log("TESTING MULTIPLE ALTERNATES")
        self.log("="*50)
        
        manufacturer = "Test Manufacturer"
        original_part = "ORIGINAL-789"
        alternates = ["ALT-001", "ALT-002", "ALT-003", "ALT-004"]
        
        result = self.show_alternate_part_acceptance_dialog(manufacturer, original_part, alternates)
        
        self.log(f"FINAL RESULT: {result}")
        if result in alternates:
            self.log(f"✅ TEST PASSED: Correctly returned selected alternate: {result}")
        elif result is None:
            self.log("⚠️ TEST RESULT: User cancelled or said no")
        else:
            self.log(f"❌ TEST FAILED: Expected one of {alternates} or None, got '{result}'")
    
    def run(self):
        """Run the test"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🐛 ALTERNATE PART NUMBER BUG TEST")
    print("=" * 50)
    print("This script tests the alternate part selection bug")
    print("Follow the prompts and check if the bug occurs")
    print("")
    
    test = AlternatePartTest()
    test.run()

if __name__ == "__main__":
    main()
