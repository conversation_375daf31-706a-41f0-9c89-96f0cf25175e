#!/usr/bin/env python3
"""
Clean Launch of Component Finder
Forces a fresh start without cached modules
"""

import os
import sys
import subprocess

def clean_launch():
    """Launch component finder with clean environment"""
    print("🚀 CLEAN LAUNCH - COMPONENT FINDER")
    print("=" * 50)
    
    # Clear Python cache
    print("🧹 Clearing Python cache...")
    for root, dirs, files in os.walk('.'):
        for d in dirs[:]:
            if d == '__pycache__':
                import shutil
                shutil.rmtree(os.path.join(root, d))
                print(f"   Removed: {os.path.join(root, d)}")
                dirs.remove(d)
    
    # Remove .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))
                print(f"   Removed: {os.path.join(root, file)}")
    
    print("✅ Cache cleared")
    
    # Launch with fresh Python process
    print("🖥️ Starting Component Finder with fresh environment...")
    
    env = os.environ.copy()
    env['PYTHONDONTWRITEBYTECODE'] = '1'  # Don't create .pyc files
    
    try:
        # Start the process
        process = subprocess.Popen([
            sys.executable, 'component_finder.py'
        ], env=env, cwd=os.getcwd())
        
        print("✅ Component Finder launched successfully!")
        print("🔍 Look for window: 'Component Finder - Interactive Learning System'")
        print("")
        print("🎯 NEW FEATURES ACTIVE:")
        print("   ✅ Enhanced 3D search (downloads actual files)")
        print("   ✅ Multi-monitor positioning fixes")
        print("   ✅ Dialogs stay on same screen as main window")
        print("")
        print("🧪 TEST THE ENHANCED 3D SEARCH:")
        print("   1. Enter manufacturer: 'Arduino'")
        print("   2. Enter part number: 'Uno'")
        print("   3. Click '🔍 Search Component'")
        print("   4. Should see: '🔍 Searching Arduino website for 3D model...'")
        print("   5. Then either: '✅ Downloaded 3D model: [filename]' or '❌ No 3D model found'")
        print("")
        print("💡 NO MORE verbose analysis with URLs - just direct results!")
        
        return True
        
    except Exception as e:
        print(f"❌ Launch failed: {e}")
        return False

if __name__ == "__main__":
    clean_launch()
