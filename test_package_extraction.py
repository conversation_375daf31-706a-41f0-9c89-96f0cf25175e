#!/usr/bin/env python3
"""
Test the package extraction functionality
"""

import sys
import os
from unittest.mock import Mock

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_package_extraction():
    """Test the package extraction method"""
    print("🧪 Testing Package Extraction")
    print("=" * 40)
    
    try:
        # Import the component finder
        from component_finder import ComponentFinderGUI
        import tkinter as tk
        
        # Create a minimal tkinter root for testing
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Create component finder instance
        finder = ComponentFinderGUI(root)
        
        # Mock the add_comment method to capture messages
        comments = []
        def mock_add_comment(comment):
            comments.append(comment)
            print(f"COMMENT: {comment}")
        
        finder.add_comment = mock_add_comment
        
        # Test the package extraction method
        print("\n🔍 Testing package extraction for Texas Instruments LM358N")
        print("-" * 50)
        
        finder.extract_and_display_package_info("Texas Instruments", "LM358N")
        
        print(f"\n📊 Package Extraction Results:")
        print(f"   Total comments: {len(comments)}")
        
        print(f"\n📝 Comment Messages:")
        for i, comment in enumerate(comments, 1):
            print(f"   {i}. {comment}")
        
        # Verify expected messages
        expected_messages = ["Part Number", "Package"]
        
        print(f"\n✅ Message Verification:")
        for msg in expected_messages:
            found = any(msg in comment for comment in comments)
            status = "✅ PASS" if found else "❌ FAIL"
            print(f"   {msg} message: {status}")
        
        # Check for specific format
        part_found = any("Part Number Found" in comment or "Part Number not found" in comment for comment in comments)
        package_found = any("Package Found" in comment or "Package not found" in comment for comment in comments)
        
        print(f"\n📋 Format Verification:")
        print(f"   Part Number message format: {'✅ PASS' if part_found else '❌ FAIL'}")
        print(f"   Package message format: {'✅ PASS' if package_found else '❌ FAIL'}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Package Extraction Test")
    print("=" * 50)
    
    success = test_package_extraction()
    
    if success:
        print(f"\n🎉 Package extraction test completed!")
        print(f"The method now generates specific 'Part Number Found/not found' and 'Package Found/not found' messages.")
    else:
        print(f"\n💥 Package extraction test failed.")
