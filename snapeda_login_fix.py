#!/usr/bin/env python3
"""
SnapEDA Login Fix - Make sure Log In button is clicked properly
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def snapeda_login_fix():
    print("🔍 SNAPEDA LOGIN FIX - ENSURE LOG IN BUTTON IS CLICKED")
    print("=" * 70)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 15)
    
    try:
        # Step 1: Go to SnapEDA
        print("🔸 Step 1: Going to SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Step 2: Click login link
        print("🔸 Step 2: Clicking login link...")
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        print("✅ On login page")
        
        # Step 3: Fill ONLY email and password (ignore any search fields)
        print("🔸 Step 3: Filling login credentials...")
        
        # Find and fill email
        email_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "#id_username")))
        email_field.clear()
        email_field.send_keys("<EMAIL>")
        print("✅ Email entered")
        
        # Find and fill password
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.clear()
        password_field.send_keys("Lennyai123#")
        print("✅ Password entered")
        
        # Step 4: Find and click the Log In button (try multiple methods)
        print("🔸 Step 4: Finding and clicking Log In button...")
        
        login_clicked = False
        
        # Method 1: Look for submit button
        try:
            submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            if submit_btn.is_displayed() and submit_btn.is_enabled():
                print("✅ Found submit button - clicking...")
                submit_btn.click()
                login_clicked = True
        except Exception as e:
            print(f"⚠️ Method 1 failed: {e}")
        
        # Method 2: Look for button with "Log In" text
        if not login_clicked:
            try:
                login_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'Log In') or contains(text(), 'Login')]")
                if login_btn.is_displayed() and login_btn.is_enabled():
                    print("✅ Found Log In button by text - clicking...")
                    login_btn.click()
                    login_clicked = True
            except Exception as e:
                print(f"⚠️ Method 2 failed: {e}")
        
        # Method 3: Look for input submit
        if not login_clicked:
            try:
                submit_input = driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
                if submit_input.is_displayed() and submit_input.is_enabled():
                    print("✅ Found submit input - clicking...")
                    submit_input.click()
                    login_clicked = True
            except Exception as e:
                print(f"⚠️ Method 3 failed: {e}")
        
        # Method 4: Press Enter on password field
        if not login_clicked:
            try:
                print("✅ Trying Enter key on password field...")
                password_field.send_keys(Keys.RETURN)
                login_clicked = True
            except Exception as e:
                print(f"⚠️ Method 4 failed: {e}")
        
        if login_clicked:
            print("✅ Log In button clicked successfully!")
        else:
            print("❌ Could not click Log In button")
            return
        
        # Step 5: Wait for login to complete
        print("🔸 Step 5: Waiting for login to complete...")
        time.sleep(10)
        
        # Check if we're logged in
        current_url = driver.current_url.lower()
        if "login" not in current_url:
            print("✅ Login successful - redirected from login page")
            print(f"   Current URL: {driver.current_url}")
        else:
            print("❌ Still on login page - login may have failed")
            print(f"   Current URL: {driver.current_url}")
            
            # Check for error messages
            try:
                error_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'error') or contains(text(), 'invalid') or contains(text(), 'incorrect')]")
                if error_elements:
                    print("   Error messages found:")
                    for elem in error_elements:
                        if elem.is_displayed():
                            print(f"     - {elem.text}")
            except:
                pass
            return
        
        # Step 6: Now search for the part
        print("🔸 Step 6: Searching for part number...")
        
        # Look for search field on the main page
        search_selectors = [
            "input[name='q']",
            "input[placeholder*='search']",
            "#search-input",
            ".search-input",
            "input[type='search']"
        ]
        
        search_field = None
        for selector in search_selectors:
            try:
                search_field = driver.find_element(By.CSS_SELECTOR, selector)
                if search_field.is_displayed():
                    print(f"✅ Found search field: {selector}")
                    break
            except:
                continue
        
        if search_field:
            search_field.clear()
            search_field.send_keys("LM358N")
            search_field.send_keys(Keys.RETURN)
            time.sleep(5)
            print("✅ Part number search submitted")
        else:
            print("⚠️ No search field found, going directly to part page")
        
        # Step 7: Go to part page
        print("🔸 Step 7: Going to LM358N part page...")
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        print(f"✅ On part page")
        
        # Step 8: Look for 3D Model tab
        print("🔸 Step 8: Looking for 3D Model tab...")
        three_d_tab = None
        
        three_d_selectors = [
            "//li[text()='3D Model']",
            "//a[text()='3D Model']",
            "//span[text()='3D Model']"
        ]
        
        for selector in three_d_selectors:
            try:
                tab = driver.find_element(By.XPATH, selector)
                if tab.is_displayed():
                    three_d_tab = tab
                    print(f"✅ Found 3D Model tab")
                    break
            except:
                continue
        
        if three_d_tab:
            three_d_tab.click()
            time.sleep(10)
            print("✅ 3D Model tab clicked")
            
            # Look for download button
            print("🔸 Step 9: Looking for download button...")
            download_selectors = [
                "//a[contains(@class, '3D-model-download')]",
                "//a[contains(text(), 'Download 3D Model')]"
            ]
            
            download_button = None
            for selector in download_selectors:
                try:
                    button = driver.find_element(By.XPATH, selector)
                    if button.is_displayed():
                        download_button = button
                        print(f"✅ Found download button")
                        break
                except:
                    continue
            
            if download_button:
                # Monitor files before download
                before_files = set(os.listdir(download_dir))
                
                print("🔸 Step 10: Clicking download button...")
                download_button.click()
                print("✅ Download button clicked!")
                
                # Monitor for downloads
                print("📁 Monitoring for downloads...")
                for i in range(30):
                    time.sleep(1)
                    current_files = set(os.listdir(download_dir))
                    new_files = current_files - before_files
                    
                    if new_files:
                        print(f"🎉 Download detected after {i+1} seconds!")
                        for f in new_files:
                            print(f"  📄 {f}")
                        break
                    
                    if i % 5 == 0 and i > 0:
                        print(f"   ⏳ {i+1}/30 seconds...")
                
                if not new_files:
                    print("❌ No download detected")
            else:
                print("❌ Download button not found")
        else:
            print("❌ 3D Model tab not found")
        
        print(f"\n🔸 Process complete - browser staying open")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    snapeda_login_fix()
