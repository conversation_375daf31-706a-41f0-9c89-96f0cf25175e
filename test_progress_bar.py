#!/usr/bin/env python3
"""
Test the colored progress bar
"""

import tkinter as tk
from tkinter import ttk
import time

def test_colored_progress_bar():
    """Test the colored progress bar"""
    
    root = tk.Tk()
    root.title("Progress Bar Test")
    root.geometry("400x200")
    root.configure(bg='#f0f0f0')
    
    # VS Colors
    vs_colors = {
        'form_bg': '#f0f0f0',
        'button_bg': '#e1e1e1',
        'accent_blue': '#007acc'
    }
    
    # Configure colored progress bar style
    style = ttk.Style()
    style.configure('Colored.Horizontal.TProgressbar',
                   background='#007acc',  # Blue color
                   troughcolor=vs_colors['button_bg'],
                   borderwidth=1,
                   lightcolor='#007acc',
                   darkcolor='#005a9e')
    
    # Create frame
    frame = tk.Frame(root, bg=vs_colors['form_bg'])
    frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Label
    label = tk.Label(frame, text="Testing Colored Progress Bar", 
                    bg=vs_colors['form_bg'], font=('Microsoft Sans Serif', 12, 'bold'))
    label.pack(pady=10)
    
    # Progress bar
    progress = ttk.Progressbar(frame, 
                              mode='indeterminate', 
                              length=300,
                              style='Colored.Horizontal.TProgressbar')
    progress.pack(pady=20)
    
    # Status label
    status_label = tk.Label(frame, text="Ready", 
                           bg=vs_colors['form_bg'], font=('Microsoft Sans Serif', 9))
    status_label.pack(pady=5)
    
    # Buttons
    button_frame = tk.Frame(frame, bg=vs_colors['form_bg'])
    button_frame.pack(pady=10)
    
    def start_progress():
        progress.start(10)  # 10ms interval for smooth animation
        status_label.config(text="Searching... (Moving Blue Bar)")
        
    def stop_progress():
        progress.stop()
        status_label.config(text="Search Complete!")
        
    def test_sequence():
        """Test the full sequence"""
        status_label.config(text="Starting search...")
        root.after(500, lambda: progress.start(10))
        root.after(600, lambda: status_label.config(text="Searching APIs..."))
        root.after(2000, lambda: status_label.config(text="Downloading files..."))
        root.after(3500, lambda: status_label.config(text="Processing results..."))
        root.after(5000, lambda: progress.stop())
        root.after(5100, lambda: status_label.config(text="Search Complete! ✅"))
    
    start_btn = tk.Button(button_frame, text="Start", command=start_progress,
                         bg=vs_colors['button_bg'], font=('Microsoft Sans Serif', 8))
    start_btn.pack(side='left', padx=5)
    
    stop_btn = tk.Button(button_frame, text="Stop", command=stop_progress,
                        bg=vs_colors['button_bg'], font=('Microsoft Sans Serif', 8))
    stop_btn.pack(side='left', padx=5)
    
    test_btn = tk.Button(button_frame, text="Test Sequence", command=test_sequence,
                        bg=vs_colors['button_bg'], font=('Microsoft Sans Serif', 8))
    test_btn.pack(side='left', padx=5)
    
    # Info
    info_label = tk.Label(frame, 
                         text="The progress bar should show a moving blue animation when active",
                         bg=vs_colors['form_bg'], font=('Microsoft Sans Serif', 8),
                         fg='gray')
    info_label.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_colored_progress_bar()
