#!/usr/bin/env python3
"""
SnapEDA Click Below Password - Click the specific button below the password entry
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>

def snapeda_click_below_password():
    print("🔍 SNAPEDA CLICK BELOW PASSWORD - FIND BUTTON BELOW PASSWORD FIELD")
    print("=" * 75)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Go to SnapEDA
        print("🔸 Step 1: Going to SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(4)
        
        # Step 2: Click login link (try multiple methods)
        print("🔸 Step 2: Clicking login link...")

        login_clicked = False

        # Method 1: Find login link and click with ActionChains
        try:
            login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
            if login_link.is_displayed():
                actions = ActionChains(driver)
                actions.move_to_element(login_link).pause(0.5).click().perform()
                login_clicked = True
                print("✅ Clicked login link with ActionChains")
        except Exception as e:
            print(f"⚠️ ActionChains login click failed: {e}")

        # Method 2: Direct click
        if not login_clicked:
            try:
                login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
                login_link.click()
                login_clicked = True
                print("✅ Clicked login link directly")
            except Exception as e:
                print(f"⚠️ Direct login click failed: {e}")

        # Method 3: JavaScript click
        if not login_clicked:
            try:
                login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
                driver.execute_script("arguments[0].click();", login_link)
                login_clicked = True
                print("✅ Clicked login link with JavaScript")
            except Exception as e:
                print(f"⚠️ JavaScript login click failed: {e}")

        # Method 4: Try different selectors
        if not login_clicked:
            login_selectors = [
                "//a[contains(text(), 'Log In')]",
                "//a[contains(text(), 'Login')]",
                "//a[contains(text(), 'Sign In')]",
                ".login-link",
                "#login-link"
            ]

            for selector in login_selectors:
                try:
                    if selector.startswith("//"):
                        login_link = driver.find_element(By.XPATH, selector)
                    else:
                        login_link = driver.find_element(By.CSS_SELECTOR, selector)

                    if login_link.is_displayed():
                        login_link.click()
                        login_clicked = True
                        print(f"✅ Clicked login link with selector: {selector}")
                        break
                except:
                    continue

        if not login_clicked:
            print("❌ Could not click login link")
            return

        time.sleep(4)

        # Verify we're on login page
        current_url = driver.current_url
        if "login" in current_url.lower():
            print("✅ On login page")
        else:
            print(f"⚠️ May not be on login page. Current URL: {current_url}")
        
        # Step 3: Enter email
        print("🔸 Step 3: Entering email...")
        email_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "#id_username"))
        )
        email_field.clear()
        email_field.send_keys("<EMAIL>")
        print("✅ Email entered")
        
        # Step 4: Enter password
        print("🔸 Step 4: Entering password...")
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.clear()
        password_field.send_keys("Lennyai123#")
        print("✅ Password entered")
        
        # Step 5: Find the button BELOW the password field
        print("🔸 Step 5: Looking for button below password field...")
        
        # Get the password field's position
        password_location = password_field.location
        password_size = password_field.size
        password_bottom = password_location['y'] + password_size['height']
        
        print(f"   Password field position: y={password_location['y']}, bottom={password_bottom}")
        
        # Find all buttons and inputs on the page
        all_buttons = driver.find_elements(By.XPATH, "//button | //input[@type='submit'] | //input[@type='button']")
        
        print(f"   Found {len(all_buttons)} total buttons/inputs")
        
        # Look for buttons that are below the password field
        buttons_below_password = []
        for i, button in enumerate(all_buttons):
            try:
                if button.is_displayed():
                    button_location = button.location
                    button_text = button.text or button.get_attribute('value') or button.get_attribute('type')
                    button_class = button.get_attribute('class') or ''
                    
                    print(f"   Button {i+1}: y={button_location['y']}, text='{button_text}', class='{button_class}'")
                    
                    # Check if this button is below the password field
                    if button_location['y'] > password_bottom:
                        buttons_below_password.append((button, button_location['y'], button_text))
                        print(f"     ✅ This button is BELOW password field")
            except Exception as e:
                print(f"     ⚠️ Error checking button {i+1}: {e}")
        
        if not buttons_below_password:
            print("❌ No buttons found below password field")
            
            # Try to find the closest button to the password field
            print("🔸 Looking for closest button to password field...")
            closest_button = None
            closest_distance = float('inf')
            
            for button in all_buttons:
                try:
                    if button.is_displayed():
                        button_location = button.location
                        distance = abs(button_location['y'] - password_bottom)
                        button_text = button.text or button.get_attribute('value') or 'no-text'
                        
                        print(f"   Button '{button_text}' distance from password: {distance}")
                        
                        if distance < closest_distance:
                            closest_distance = distance
                            closest_button = button
                except:
                    continue
            
            if closest_button:
                print(f"✅ Using closest button (distance: {closest_distance})")
                buttons_below_password = [(closest_button, 0, closest_button.text or 'closest')]
        
        if buttons_below_password:
            # Sort by y-position (closest to password field first)
            buttons_below_password.sort(key=lambda x: x[1])
            
            # Try clicking the first (topmost) button below password
            button_to_click, y_pos, button_text = buttons_below_password[0]
            
            print(f"🔸 Step 6: Clicking button below password: '{button_text}'")
            
            # Try multiple click methods
            clicked = False
            
            # Method 1: ActionChains
            try:
                actions = ActionChains(driver)
                actions.move_to_element(button_to_click).pause(0.5).click().perform()
                clicked = True
                print("✅ Clicked with ActionChains")
            except Exception as e:
                print(f"⚠️ ActionChains failed: {e}")
            
            # Method 2: Direct click
            if not clicked:
                try:
                    button_to_click.click()
                    clicked = True
                    print("✅ Clicked directly")
                except Exception as e:
                    print(f"⚠️ Direct click failed: {e}")
            
            # Method 3: JavaScript click
            if not clicked:
                try:
                    driver.execute_script("arguments[0].click();", button_to_click)
                    clicked = True
                    print("✅ Clicked with JavaScript")
                except Exception as e:
                    print(f"⚠️ JavaScript click failed: {e}")
            
            if clicked:
                print("✅ Button below password clicked!")
            else:
                print("❌ Could not click button below password")
                return
        else:
            print("❌ No suitable button found")
            return
        
        # Step 7: Wait for login result
        print("🔸 Step 7: Waiting for login result...")
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"✅ Current URL after login attempt: {current_url}")
        
        if "login" in current_url.lower():
            print("❌ Still on login page - login failed")
            
            # Show any error messages
            try:
                error_selectors = [".alert", ".error", ".errorlist", "[class*='error']", ".invalid-feedback"]
                for selector in error_selectors:
                    try:
                        errors = driver.find_elements(By.CSS_SELECTOR, selector)
                        for error in errors:
                            if error.is_displayed() and error.text.strip():
                                print(f"   Error message: {error.text.strip()}")
                    except:
                        continue
            except:
                pass
        else:
            print("🎉 LOGIN SUCCESSFUL! Redirected from login page")
            
            # Continue with part search
            print("🔸 Step 8: Going to LM358N part page...")
            part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
            driver.get(part_url)
            time.sleep(8)
            
            # Look for 3D Model tab
            print("🔸 Step 9: Looking for 3D Model tab...")
            three_d_selectors = [
                "//li[text()='3D Model']",
                "//a[text()='3D Model']",
                "//span[text()='3D Model']"
            ]
            
            three_d_tab = None
            for selector in three_d_selectors:
                try:
                    tab = driver.find_element(By.XPATH, selector)
                    if tab.is_displayed():
                        three_d_tab = tab
                        print("✅ Found 3D Model tab")
                        break
                except:
                    continue
            
            if three_d_tab:
                three_d_tab.click()
                time.sleep(8)
                print("✅ 3D Model tab clicked")
                
                # Look for download button
                print("🔸 Step 10: Looking for download button...")
                download_selectors = [
                    "//a[contains(@class, '3D-model-download')]",
                    "//a[contains(text(), 'Download 3D Model')]"
                ]
                
                download_button = None
                for selector in download_selectors:
                    try:
                        button = driver.find_element(By.XPATH, selector)
                        if button.is_displayed():
                            download_button = button
                            print("✅ Found download button")
                            break
                    except:
                        continue
                
                if download_button:
                    before_files = set(os.listdir(download_dir))
                    
                    print("🔸 Step 11: Clicking download button...")
                    download_button.click()
                    print("✅ Download button clicked!")
                    
                    # Monitor downloads
                    print("📁 Monitoring downloads...")
                    for i in range(30):
                        time.sleep(1)
                        current_files = set(os.listdir(download_dir))
                        new_files = current_files - before_files
                        
                        if new_files:
                            print(f"🎉 Download detected after {i+1} seconds!")
                            for f in new_files:
                                file_path = os.path.join(download_dir, f)
                                file_size = os.path.getsize(file_path)
                                print(f"  📄 {f} ({file_size} bytes)")

                                if f.endswith('.step') or f.endswith('.stp') or f.endswith('.STEP') or f.endswith('.STP'):
                                    # Create proper SnapEDA filename
                                    final_name = f"SnapEDA_Texas_Instruments_LM358N.step"
                                    final_path = os.path.join(download_dir, final_name)

                                    # Remove existing file if it exists
                                    if os.path.exists(final_path):
                                        os.remove(final_path)

                                    # Rename the downloaded file
                                    os.rename(file_path, final_path)
                                    print(f"✅ Renamed to: {final_name}")
                            break
                        
                        if i % 5 == 0 and i > 0:
                            print(f"   ⏳ {i+1}/30 seconds...")
                    
                    if not new_files:
                        print("❌ No download detected")
                else:
                    print("❌ Download button not found")
            else:
                print("❌ 3D Model tab not found")
        
        print(f"\n🔸 Process complete - closing browser automatically")
        time.sleep(2)  # Brief pause to see final status
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        time.sleep(3)  # Brief pause to see error
    
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    snapeda_click_below_password()
