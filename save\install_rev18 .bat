@echo off
REM === Activate environment and run Diodes Asset Finder ===

REM Set path to your Python environment (adjust if needed)
SET PYTHON_PATH=C:\Path\To\Your\Python\python.exe

REM Set path to your script
SET SCRIPT_PATH=C:\Path\To\Your\Script\diodes_gui.py

REM Optional: echo paths for debug
echo Using Python: %PYTHON_PATH%
echo Running script: %SCRIPT_PATH%

REM Launch the GUI
"%PYTHON_PATH%" "%SCRIPT_PATH%"

REM Pause to keep window open after execution
pause
