#!/usr/bin/env python3
"""
Test UltraLibrarian website accessibility
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def test_with_requests():
    """Test with requests library"""
    print("🔍 Testing UltraLibrarian with requests...")
    
    try:
        response = requests.get('https://www.ultralibrarian.com/', timeout=10)
        print(f"✅ Requests successful: Status {response.status_code}")
        print(f"   Content length: {len(response.text)} characters")
        if "UltraLibrarian" in response.text:
            print("✅ UltraLibrarian content detected")
        else:
            print("⚠️ UltraLibrarian content not detected")
        return True
    except requests.exceptions.Timeout:
        print("❌ Requests timeout")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Requests error: {e}")
        return False

def test_with_selenium():
    """Test with Selenium"""
    print("\n🔍 Testing UltraLibrarian with Selenium...")
    
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(15)
        
        print("   Loading https://www.ultralibrarian.com/...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(3)
        
        title = driver.title
        url = driver.current_url
        
        print(f"✅ Selenium successful")
        print(f"   Title: {title}")
        print(f"   URL: {url}")
        
        # Check for error indicators
        if "can't be reached" in title.lower() or "error" in title.lower():
            print("❌ Error page detected")
            return False
        elif "ultralibrarian" in title.lower():
            print("✅ UltraLibrarian page loaded successfully")
            return True
        else:
            print("⚠️ Unknown page loaded")
            return False
            
    except Exception as e:
        print(f"❌ Selenium error: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def test_alternative_urls():
    """Test alternative URLs"""
    print("\n🔍 Testing alternative UltraLibrarian URLs...")
    
    urls = [
        'http://www.ultralibrarian.com/',
        'https://ultralibrarian.com/',
        'http://ultralibrarian.com/'
    ]
    
    for url in urls:
        try:
            print(f"   Testing {url}...")
            response = requests.get(url, timeout=5)
            print(f"   ✅ {url} - Status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url} - Error: {e}")

if __name__ == "__main__":
    print("🌐 ULTRALIBRARIAN ACCESSIBILITY TEST")
    print("=" * 50)
    
    # Test 1: Requests
    requests_ok = test_with_requests()
    
    # Test 2: Selenium
    selenium_ok = test_with_selenium()
    
    # Test 3: Alternative URLs
    test_alternative_urls()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print(f"   Requests: {'✅ OK' if requests_ok else '❌ FAILED'}")
    print(f"   Selenium: {'✅ OK' if selenium_ok else '❌ FAILED'}")
    
    if requests_ok or selenium_ok:
        print("🎉 UltraLibrarian is accessible!")
    else:
        print("🚫 UltraLibrarian is not accessible from this location")
        print("   Possible causes:")
        print("   - Website is down")
        print("   - Network/firewall blocking")
        print("   - Geographic restrictions")
        print("   - ISP blocking")
