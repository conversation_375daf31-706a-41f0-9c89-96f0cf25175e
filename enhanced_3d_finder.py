#!/usr/bin/env python3
"""
Enhanced 3D Model Finder
Searches multiple 3D model repositories and manufacturer sites
"""

import requests
from bs4 import BeautifulSoup
import os
import json
import time
from urllib.parse import urljoin, urlparse, quote
import csv
import re

def log_3d_download(manufacturer, part_number, filename, source):
    """Log successful 3D model download"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")
    
    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)
    
    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            if not file_exists:
                writer.writeheader()
            
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': source,
                'STEP_FILE_NAME': filename
            })
    except:
        pass  # Silent fail for logging

def download_3d_file(url, manufacturer, part_number, source="DIRECT"):
    """Download 3D model file from URL"""
    try:
        # Skip if URL looks suspicious or invalid
        if not url or len(url) < 10 or 'javascript:' in url.lower():
            return None
            
        response = requests.get(url, timeout=15, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }, allow_redirects=True)
        
        if response.status_code == 200 and len(response.content) > 100:  # Must be at least 100 bytes
            # Determine file extension from URL or content-type
            url_lower = url.lower()
            content_type = response.headers.get('content-type', '').lower()
            
            if '.step' in url_lower or '.stp' in url_lower or 'step' in content_type:
                ext = '.step'
            elif '.stl' in url_lower or 'stl' in content_type:
                ext = '.stl'
            elif '.igs' in url_lower or '.iges' in url_lower or 'iges' in content_type:
                ext = '.igs'
            elif 'octet-stream' in content_type or 'application/' in content_type:
                ext = '.step'  # Default for binary files
            else:
                # Check if content looks like a 3D file
                content_start = response.content[:200].decode('utf-8', errors='ignore').upper()
                if 'ISO-10303' in content_start or 'STEP' in content_start:
                    ext = '.step'
                elif 'STL' in content_start or 'SOLID' in content_start:
                    ext = '.stl'
                else:
                    return None  # Doesn't look like a 3D file
            
            # Create filename
            filename = f"{source}-{manufacturer.replace(' ', '_')}-{part_number}{ext}"
            filepath = os.path.join("3d", filename)
            
            # Ensure 3d directory exists
            os.makedirs("3d", exist_ok=True)
            
            # Save file
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            # Log the download
            log_3d_download(manufacturer, part_number, filename, source)
            
            return filename
    except Exception as e:
        pass
    
    return None

def search_grabcad(manufacturer, part_number):
    """Search GrabCAD for 3D models"""
    try:
        search_query = f"{manufacturer} {part_number}"
        search_url = f"https://grabcad.com/library?query={quote(search_query)}"
        
        response = requests.get(search_url, timeout=10, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for model links
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if '/library/' in href and part_number.lower() in link.get_text().lower():
                    model_url = urljoin("https://grabcad.com", href)
                    
                    # Try to find download link on model page
                    try:
                        model_response = requests.get(model_url, timeout=10, headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        })
                        
                        if model_response.status_code == 200:
                            model_soup = BeautifulSoup(model_response.text, 'html.parser')
                            
                            # Look for download buttons/links
                            for dl_link in model_soup.find_all(['a', 'button'], href=True):
                                dl_href = dl_link.get('href', '')
                                dl_text = dl_link.get_text().lower()
                                
                                if ('download' in dl_text or 'step' in dl_text) and any(ext in dl_href.lower() for ext in ['.step', '.stp', '.stl']):
                                    download_url = urljoin("https://grabcad.com", dl_href)
                                    filename = download_3d_file(download_url, manufacturer, part_number, "GrabCAD")
                                    if filename:
                                        return filename
                    except:
                        continue
    except:
        pass
    
    return None

def search_thingiverse(manufacturer, part_number):
    """Search Thingiverse for 3D models"""
    try:
        search_query = f"{manufacturer} {part_number}"
        search_url = f"https://www.thingiverse.com/search?q={quote(search_query)}&type=things"
        
        response = requests.get(search_url, timeout=10, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for thing links
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if '/thing:' in href and part_number.lower() in link.get_text().lower():
                    thing_url = urljoin("https://www.thingiverse.com", href)
                    
                    # Try to find download link on thing page
                    try:
                        thing_response = requests.get(thing_url, timeout=10, headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        })
                        
                        if thing_response.status_code == 200:
                            thing_soup = BeautifulSoup(thing_response.text, 'html.parser')
                            
                            # Look for download links
                            for dl_link in thing_soup.find_all('a', href=True):
                                dl_href = dl_link.get('href', '')
                                
                                if any(ext in dl_href.lower() for ext in ['.stl', '.step', '.stp']):
                                    download_url = urljoin("https://www.thingiverse.com", dl_href)
                                    filename = download_3d_file(download_url, manufacturer, part_number, "Thingiverse")
                                    if filename:
                                        return filename
                    except:
                        continue
    except:
        pass
    
    return None

def search_manufacturer_direct(manufacturer, part_number):
    """Search manufacturer website directly"""
    try:
        # Load manufacturer patterns
        try:
            with open('manufacturer_3d_patterns.json', 'r') as f:
                patterns = json.load(f)
        except:
            patterns = {}
        
        manufacturer_lower = manufacturer.lower()
        manufacturer_patterns = patterns.get(manufacturer_lower, {})
        
        # Get websites to try
        websites = manufacturer_patterns.get('websites', [])
        if not websites:
            base = manufacturer_lower.replace(" ", "").replace(".", "")
            websites = [f"https://www.{base}.com"]
        
        # Try each website
        for website in websites[:1]:  # Try first website only
            try:
                # Try direct product URLs
                direct_urls = [
                    f"{website}/product/{part_number}",
                    f"{website}/products/{part_number}",
                    f"{website}/en/products/{part_number}"
                ]
                
                for url in direct_urls:
                    try:
                        response = requests.get(url, timeout=10, headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        })
                        
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            
                            # Look for 3D model download links
                            for link in soup.find_all('a', href=True):
                                href = link.get('href', '').lower()
                                text = link.get_text().lower()
                                
                                if (any(ext in href for ext in ['.step', '.stp', '.stl', '.igs']) or
                                    ('3d' in text and 'download' in text)):
                                    
                                    # Get full URL
                                    if href.startswith('http'):
                                        download_url = link['href']
                                    elif href.startswith('/'):
                                        download_url = urljoin(website, link['href'])
                                    else:
                                        continue
                                    
                                    filename = download_3d_file(download_url, manufacturer, part_number, "Manufacturer")
                                    if filename:
                                        return filename
                    except:
                        continue
            except:
                continue
    except:
        pass
    
    return None

def find_enhanced_3d_model(manufacturer, part_number):
    """
    Enhanced search for 3D models using multiple sources
    Returns filename if found, None if not found
    """
    
    # Try different sources in order of preference
    sources = [
        ("Manufacturer Direct", search_manufacturer_direct),
        ("GrabCAD", search_grabcad),
        ("Thingiverse", search_thingiverse)
    ]
    
    for source_name, search_func in sources:
        try:
            result = search_func(manufacturer, part_number)
            if result:
                return result
        except:
            continue
    
    return None

def main():
    """Test the enhanced 3D finder"""
    import sys
    
    if len(sys.argv) == 3:
        manufacturer = sys.argv[1]
        part_number = sys.argv[2]
    else:
        manufacturer = input("Enter manufacturer: ").strip()
        part_number = input("Enter part number: ").strip()
    
    if not manufacturer or not part_number:
        print("❌ Both manufacturer and part number required")
        return
    
    print(f"🔍 Searching multiple sources for {manufacturer} {part_number} 3D model...")
    
    result = find_enhanced_3d_model(manufacturer, part_number)
    
    if result:
        print(f"✅ Found and downloaded: {result}")
    else:
        print(f"❌ No 3D model found from any source")

if __name__ == "__main__":
    main()
