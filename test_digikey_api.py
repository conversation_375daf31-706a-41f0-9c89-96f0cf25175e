#!/usr/bin/env python3
"""
Test what the Digi-Key API actually returns for LM358N
"""

from digikey_datasheet_improved import DigikeyDatasheetFinder

def test_lm358n_api():
    print("🔍 Testing Digi-Key API for LM358N")
    print("=" * 40)
    
    finder = DigikeyDatasheetFinder()
    
    print("📍 Step 1: Loading credentials...")
    if not finder.load_credentials():
        print("❌ Credentials not loaded")
        return
    
    print("✅ Credentials loaded")
    
    print("📍 Step 2: Searching for Texas Instruments LM358N...")
    result = finder.search_part('Texas Instruments', 'LM358N')
    
    print("📍 Step 3: Analyzing API response...")
    print(f"Raw result: {result}")
    
    if result:
        print("✅ API returned results")
        datasheet_url = result.get('datasheet_url', 'No datasheet URL')
        print(f"Datasheet URL: {datasheet_url}")
        
        if datasheet_url and datasheet_url != 'No datasheet URL':
            print("📍 Step 4: Checking what type of URL this is...")
            if datasheet_url.endswith('.pdf'):
                print("✅ URL points directly to PDF")
            elif 'pdf' in datasheet_url.lower():
                print("⚠️ URL contains 'pdf' but doesn't end with .pdf")
            else:
                print("❌ URL does not appear to be a direct PDF link")
                print("This might be why we're getting HTML instead of PDF")
        else:
            print("❌ No datasheet URL in API response")
    else:
        print("❌ API returned no results")

if __name__ == "__main__":
    test_lm358n_api()
