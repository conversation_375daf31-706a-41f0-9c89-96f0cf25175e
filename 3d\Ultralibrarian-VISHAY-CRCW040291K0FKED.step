ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('STEP AP214'),'1');
FILE_NAME('RES_CRCW_0402','2025-10-03T06:01:56',(''),(''),'','','');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN'));
ENDSEC;
DATA;
#1=SHAPE_DEFINITION_REPRESENTATION(#2,#3);
#2=PRODUCT_DEFINITION_SHAPE('',$,#4);
#3=SHAPE_REPRESENTATION('',(#188,#484,#780,#19),#11);
#4=PRODUCT_DEFINITION('design','example product_definition',#6,#5);
#5=PRODUCT_DEFINITION_CONTEXT('3D Mechanical Parts',#10,'design');
#6=PRODUCT_DEFINITION_FORMATION('1.0','first version',#8);
#7=APPLICATION_PROTOCOL_DEFINITION('international standard','automotive_design',2003,#10);
#8=PRODUCT('product','part','',(#9));
#9=PRODUCT_CONTEXT('3D Mechanical Parts',#10,'mechanical');
#10=APPLICATION_CONTEXT('Core Data for Automotive Mechanical Design Process');
#11=(GEOMETRIC_REPRESENTATION_CONTEXT(3) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#12)) GLOBAL_UNIT_ASSIGNED_CONTEXT((#13,#14,#18)) REPRESENTATION_CONTEXT('ID1','3D'));
#12=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-005),#13,'DISTANCE_ACCURACY_VALUE','Maximum model space distance between geometric entities at asserted connectivities');
#13=(LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.));
#14=(CONVERSION_BASED_UNIT('degree',#16) NAMED_UNIT(#15) PLANE_ANGLE_UNIT());
#15=DIMENSIONAL_EXPONENTS(0.,0.,0.,0.,0.,0.,0.);
#16=MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(0.01745329252),#17);
#17=(NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.));
#18=(NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT());
#19=AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20=CARTESIAN_POINT('',(0.0,0.0,0.0));
#21=DIRECTION('',(0.0,0.0,1.0));
#22=DIRECTION('',(1.0,0.0,0.0));
#25=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION($,(#190,#239,#288,#337,#386,#435,#486,#535,#584,#633,#682,#731,#782,#831,#880,#929,#978,#1027),#11);
#26=PRODUCT_CATEGORY_RELATIONSHIP('','',#27,#28);
#27=PRODUCT_CATEGORY('part','');
#28=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#8));
#34=PRESENTATION_STYLE_ASSIGNMENT((#35,#40));
#35=SURFACE_STYLE_USAGE(.BOTH.,#36);
#36=SURFACE_SIDE_STYLE('',(#37));
#37=SURFACE_STYLE_FILL_AREA(#38);
#38=FILL_AREA_STYLE('',(#39));
#39=FILL_AREA_STYLE_COLOUR('',#41);
#40=CURVE_STYLE('',#42,POSITIVE_LENGTH_MEASURE(0.1),#41);
#41=COLOUR_RGB('Aluminum',0.725,0.725,0.725);
#42=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#43=PRESENTATION_STYLE_ASSIGNMENT((#44,#49));
#44=SURFACE_STYLE_USAGE(.BOTH.,#45);
#45=SURFACE_SIDE_STYLE('',(#46));
#46=SURFACE_STYLE_FILL_AREA(#47);
#47=FILL_AREA_STYLE('',(#48));
#48=FILL_AREA_STYLE_COLOUR('',#50);
#49=CURVE_STYLE('',#51,POSITIVE_LENGTH_MEASURE(0.1),#50);
#50=COLOUR_RGB('Black',0.196,0.196,0.196);
#51=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#52=PRESENTATION_STYLE_ASSIGNMENT((#53,#58));
#53=SURFACE_STYLE_USAGE(.BOTH.,#54);
#54=SURFACE_SIDE_STYLE('',(#55));
#55=SURFACE_STYLE_FILL_AREA(#56);
#56=FILL_AREA_STYLE('',(#57));
#57=FILL_AREA_STYLE_COLOUR('',#59);
#58=CURVE_STYLE('',#60,POSITIVE_LENGTH_MEASURE(0.1),#59);
#59=COLOUR_RGB('Pin1Wrap',0.98,0.706,0.176);
#60=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#61=PRESENTATION_STYLE_ASSIGNMENT((#62,#67));
#62=SURFACE_STYLE_USAGE(.BOTH.,#63);
#63=SURFACE_SIDE_STYLE('',(#64));
#64=SURFACE_STYLE_FILL_AREA(#65);
#65=FILL_AREA_STYLE('',(#66));
#66=FILL_AREA_STYLE_COLOUR('',#68);
#67=CURVE_STYLE('',#69,POSITIVE_LENGTH_MEASURE(0.1),#68);
#68=COLOUR_RGB('HeatTab',0.588,0.588,0.588);
#69=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#70=PRESENTATION_STYLE_ASSIGNMENT((#71,#76));
#71=SURFACE_STYLE_USAGE(.BOTH.,#72);
#72=SURFACE_SIDE_STYLE('',(#73));
#73=SURFACE_STYLE_FILL_AREA(#74);
#74=FILL_AREA_STYLE('',(#75));
#75=FILL_AREA_STYLE_COLOUR('',#77);
#76=CURVE_STYLE('',#78,POSITIVE_LENGTH_MEASURE(0.1),#77);
#77=COLOUR_RGB('Gold',0.843,0.686,0.0);
#78=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#79=PRESENTATION_STYLE_ASSIGNMENT((#80,#85));
#80=SURFACE_STYLE_USAGE(.BOTH.,#81);
#81=SURFACE_SIDE_STYLE('',(#82));
#82=SURFACE_STYLE_FILL_AREA(#83);
#83=FILL_AREA_STYLE('',(#84));
#84=FILL_AREA_STYLE_COLOUR('',#86);
#85=CURVE_STYLE('',#87,POSITIVE_LENGTH_MEASURE(0.1),#86);
#86=COLOUR_RGB('Brown',0.459,0.345,0.176);
#87=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#88=PRESENTATION_STYLE_ASSIGNMENT((#89,#94));
#89=SURFACE_STYLE_USAGE(.BOTH.,#90);
#90=SURFACE_SIDE_STYLE('',(#91));
#91=SURFACE_STYLE_FILL_AREA(#92);
#92=FILL_AREA_STYLE('',(#93));
#93=FILL_AREA_STYLE_COLOUR('',#95);
#94=CURVE_STYLE('',#96,POSITIVE_LENGTH_MEASURE(0.1),#95);
#95=COLOUR_RGB('Tan',0.784,0.686,0.51);
#96=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#97=PRESENTATION_STYLE_ASSIGNMENT((#98,#103));
#98=SURFACE_STYLE_USAGE(.BOTH.,#99);
#99=SURFACE_SIDE_STYLE('',(#100));
#100=SURFACE_STYLE_FILL_AREA(#101);
#101=FILL_AREA_STYLE('',(#102));
#102=FILL_AREA_STYLE_COLOUR('',#104);
#103=CURVE_STYLE('',#105,POSITIVE_LENGTH_MEASURE(0.1),#104);
#104=COLOUR_RGB('Gray',0.431,0.431,0.431);
#105=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#106=PRESENTATION_STYLE_ASSIGNMENT((#107,#112));
#107=SURFACE_STYLE_USAGE(.BOTH.,#108);
#108=SURFACE_SIDE_STYLE('',(#109));
#109=SURFACE_STYLE_FILL_AREA(#110);
#110=FILL_AREA_STYLE('',(#111));
#111=FILL_AREA_STYLE_COLOUR('',#113);
#112=CURVE_STYLE('',#114,POSITIVE_LENGTH_MEASURE(0.1),#113);
#113=COLOUR_RGB('Red',0.6,0.0,0.0);
#114=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#115=PRESENTATION_STYLE_ASSIGNMENT((#116,#121));
#116=SURFACE_STYLE_USAGE(.BOTH.,#117);
#117=SURFACE_SIDE_STYLE('',(#118));
#118=SURFACE_STYLE_FILL_AREA(#119);
#119=FILL_AREA_STYLE('',(#120));
#120=FILL_AREA_STYLE_COLOUR('',#122);
#121=CURVE_STYLE('',#123,POSITIVE_LENGTH_MEASURE(0.1),#122);
#122=COLOUR_RGB('Blue',0.157,0.157,0.588);
#123=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#124=PRESENTATION_STYLE_ASSIGNMENT((#125,#130));
#125=SURFACE_STYLE_USAGE(.BOTH.,#126);
#126=SURFACE_SIDE_STYLE('',(#127));
#127=SURFACE_STYLE_FILL_AREA(#128);
#128=FILL_AREA_STYLE('',(#129));
#129=FILL_AREA_STYLE_COLOUR('',#131);
#130=CURVE_STYLE('',#132,POSITIVE_LENGTH_MEASURE(0.1),#131);
#131=COLOUR_RGB('Maroon',0.294,0.0,0.0);
#132=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#133=PRESENTATION_STYLE_ASSIGNMENT((#134,#139));
#134=SURFACE_STYLE_USAGE(.BOTH.,#135);
#135=SURFACE_SIDE_STYLE('',(#136));
#136=SURFACE_STYLE_FILL_AREA(#137);
#137=FILL_AREA_STYLE('',(#138));
#138=FILL_AREA_STYLE_COLOUR('',#140);
#139=CURVE_STYLE('',#141,POSITIVE_LENGTH_MEASURE(0.1),#140);
#140=COLOUR_RGB('Green',0.0,0.294,0.0);
#141=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#142=PRESENTATION_STYLE_ASSIGNMENT((#143,#148));
#143=SURFACE_STYLE_USAGE(.BOTH.,#144);
#144=SURFACE_SIDE_STYLE('',(#145));
#145=SURFACE_STYLE_FILL_AREA(#146);
#146=FILL_AREA_STYLE('',(#147));
#147=FILL_AREA_STYLE_COLOUR('',#149);
#148=CURVE_STYLE('',#150,POSITIVE_LENGTH_MEASURE(0.1),#149);
#149=COLOUR_RGB('Pin1',0.588,0.588,0.059);
#150=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#151=PRESENTATION_STYLE_ASSIGNMENT((#152,#157));
#152=SURFACE_STYLE_USAGE(.BOTH.,#153);
#153=SURFACE_SIDE_STYLE('',(#154));
#154=SURFACE_STYLE_FILL_AREA(#155);
#155=FILL_AREA_STYLE('',(#156));
#156=FILL_AREA_STYLE_COLOUR('',#158);
#157=CURVE_STYLE('',#159,POSITIVE_LENGTH_MEASURE(0.1),#158);
#158=COLOUR_RGB('Pin1Rad',0.588,0.588,0.588);
#159=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#160=PRESENTATION_STYLE_ASSIGNMENT((#161,#166));
#161=SURFACE_STYLE_USAGE(.BOTH.,#162);
#162=SURFACE_SIDE_STYLE('',(#163));
#163=SURFACE_STYLE_FILL_AREA(#164);
#164=FILL_AREA_STYLE('',(#165));
#165=FILL_AREA_STYLE_COLOUR('',#167);
#166=CURVE_STYLE('',#168,POSITIVE_LENGTH_MEASURE(0.1),#167);
#167=COLOUR_RGB('Pin1Axial',0.98,0.706,0.176);
#168=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#169=PRESENTATION_STYLE_ASSIGNMENT((#170,#175));
#170=SURFACE_STYLE_USAGE(.BOTH.,#171);
#171=SURFACE_SIDE_STYLE('',(#172));
#172=SURFACE_STYLE_FILL_AREA(#173);
#173=FILL_AREA_STYLE('',(#174));
#174=FILL_AREA_STYLE_COLOUR('',#176);
#175=CURVE_STYLE('',#177,POSITIVE_LENGTH_MEASURE(0.1),#176);
#176=COLOUR_RGB('Pin1Tant',0.459,0.345,0.176);
#177=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#178=PRESENTATION_STYLE_ASSIGNMENT((#179,#184));
#179=SURFACE_STYLE_USAGE(.BOTH.,#180);
#180=SURFACE_SIDE_STYLE('',(#181));
#181=SURFACE_STYLE_FILL_AREA(#182);
#182=FILL_AREA_STYLE('',(#183));
#183=FILL_AREA_STYLE_COLOUR('',#185);
#184=CURVE_STYLE('',#186,POSITIVE_LENGTH_MEASURE(0.1),#185);
#185=COLOUR_RGB('Shroud',0.235,0.235,0.235);
#186=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#188=MANIFOLD_SOLID_BREP($,#189);
#189=CLOSED_SHELL('',(#191,#240,#289,#338,#387,#436));
#190=STYLED_ITEM('',(#43),#191);
#191=ADVANCED_FACE('',(#197),#192,.T.);
#192=PLANE('',#193);
#193=AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194=CARTESIAN_POINT('',(-0.216,-0.271,0.008));
#195=DIRECTION('',(0.0,-1.0,0.0));
#196=DIRECTION('',(0.,0.,1.));
#197=FACE_OUTER_BOUND('',#198,.T.);
#198=EDGE_LOOP('',(#199,#209,#219,#229));
#202=CARTESIAN_POINT('',(0.216,-0.271,0.008));
#201=VERTEX_POINT('',#202);
#204=CARTESIAN_POINT('',(-0.216,-0.271,0.008));
#203=VERTEX_POINT('',#204);
#200=EDGE_CURVE('',#201,#203,#205,.T.);
#205=LINE('',#202,#207);
#207=VECTOR('',#208,0.4318);
#208=DIRECTION('',(-1.0,0.0,0.0));
#199=ORIENTED_EDGE('',*,*,#200,.F.);
#212=CARTESIAN_POINT('',(0.216,-0.271,0.398));
#211=VERTEX_POINT('',#212);
#210=EDGE_CURVE('',#211,#201,#215,.T.);
#215=LINE('',#212,#217);
#217=VECTOR('',#218,0.390434285714286);
#218=DIRECTION('',(0.0,0.0,-1.0));
#209=ORIENTED_EDGE('',*,*,#210,.F.);
#222=CARTESIAN_POINT('',(-0.216,-0.271,0.398));
#221=VERTEX_POINT('',#222);
#220=EDGE_CURVE('',#221,#211,#225,.T.);
#225=LINE('',#222,#227);
#227=VECTOR('',#228,0.4318);
#228=DIRECTION('',(1.0,0.0,0.0));
#219=ORIENTED_EDGE('',*,*,#220,.F.);
#230=EDGE_CURVE('',#203,#221,#235,.T.);
#235=LINE('',#204,#237);
#237=VECTOR('',#238,0.390434285714286);
#238=DIRECTION('',(0.0,0.0,1.0));
#229=ORIENTED_EDGE('',*,*,#230,.F.);
#239=STYLED_ITEM('',(#43),#240);
#240=ADVANCED_FACE('',(#246),#241,.T.);
#241=PLANE('',#242);
#242=AXIS2_PLACEMENT_3D('',#243,#244,#245);
#243=CARTESIAN_POINT('',(0.216,-0.271,0.008));
#244=DIRECTION('',(1.0,0.0,0.0));
#245=DIRECTION('',(0.,0.,1.));
#246=FACE_OUTER_BOUND('',#247,.T.);
#247=EDGE_LOOP('',(#248,#258,#268,#278));
#251=CARTESIAN_POINT('',(0.216,0.271,0.008));
#250=VERTEX_POINT('',#251);
#249=EDGE_CURVE('',#250,#201,#254,.T.);
#254=LINE('',#251,#256);
#256=VECTOR('',#257,0.542834285714286);
#257=DIRECTION('',(0.0,-1.0,0.0));
#248=ORIENTED_EDGE('',*,*,#249,.F.);
#261=CARTESIAN_POINT('',(0.216,0.271,0.398));
#260=VERTEX_POINT('',#261);
#259=EDGE_CURVE('',#260,#250,#264,.T.);
#264=LINE('',#261,#266);
#266=VECTOR('',#267,0.390434285714286);
#267=DIRECTION('',(0.0,0.0,-1.0));
#258=ORIENTED_EDGE('',*,*,#259,.F.);
#269=EDGE_CURVE('',#211,#260,#274,.T.);
#274=LINE('',#212,#276);
#276=VECTOR('',#277,0.542834285714286);
#277=DIRECTION('',(0.0,1.0,0.0));
#268=ORIENTED_EDGE('',*,*,#269,.F.);
#278=ORIENTED_EDGE('',*,*,#210,.T.);
#288=STYLED_ITEM('',(#43),#289);
#289=ADVANCED_FACE('',(#295),#290,.T.);
#290=PLANE('',#291);
#291=AXIS2_PLACEMENT_3D('',#292,#293,#294);
#292=CARTESIAN_POINT('',(0.216,0.271,0.008));
#293=DIRECTION('',(0.0,1.0,0.0));
#294=DIRECTION('',(0.,0.,1.));
#295=FACE_OUTER_BOUND('',#296,.T.);
#296=EDGE_LOOP('',(#297,#307,#317,#327));
#300=CARTESIAN_POINT('',(-0.216,0.271,0.008));
#299=VERTEX_POINT('',#300);
#298=EDGE_CURVE('',#299,#250,#303,.T.);
#303=LINE('',#300,#305);
#305=VECTOR('',#306,0.4318);
#306=DIRECTION('',(1.0,0.0,0.0));
#297=ORIENTED_EDGE('',*,*,#298,.F.);
#310=CARTESIAN_POINT('',(-0.216,0.271,0.398));
#309=VERTEX_POINT('',#310);
#308=EDGE_CURVE('',#309,#299,#313,.T.);
#313=LINE('',#310,#315);
#315=VECTOR('',#316,0.390434285714286);
#316=DIRECTION('',(0.0,0.0,-1.0));
#307=ORIENTED_EDGE('',*,*,#308,.F.);
#318=EDGE_CURVE('',#260,#309,#323,.T.);
#323=LINE('',#261,#325);
#325=VECTOR('',#326,0.4318);
#326=DIRECTION('',(-1.0,0.0,0.0));
#317=ORIENTED_EDGE('',*,*,#318,.F.);
#327=ORIENTED_EDGE('',*,*,#259,.T.);
#337=STYLED_ITEM('',(#43),#338);
#338=ADVANCED_FACE('',(#344),#339,.T.);
#339=PLANE('',#340);
#340=AXIS2_PLACEMENT_3D('',#341,#342,#343);
#341=CARTESIAN_POINT('',(-0.216,0.271,0.008));
#342=DIRECTION('',(-1.0,0.0,0.0));
#343=DIRECTION('',(0.,0.,1.));
#344=FACE_OUTER_BOUND('',#345,.T.);
#345=EDGE_LOOP('',(#346,#356,#366,#376));
#347=EDGE_CURVE('',#203,#299,#352,.T.);
#352=LINE('',#204,#354);
#354=VECTOR('',#355,0.542834285714286);
#355=DIRECTION('',(0.0,1.0,0.0));
#346=ORIENTED_EDGE('',*,*,#347,.F.);
#356=ORIENTED_EDGE('',*,*,#230,.T.);
#367=EDGE_CURVE('',#309,#221,#372,.T.);
#372=LINE('',#310,#374);
#374=VECTOR('',#375,0.542834285714286);
#375=DIRECTION('',(0.0,-1.0,0.0));
#366=ORIENTED_EDGE('',*,*,#367,.F.);
#376=ORIENTED_EDGE('',*,*,#308,.T.);
#386=STYLED_ITEM('',(#43),#387);
#387=ADVANCED_FACE('',(#393),#388,.T.);
#388=PLANE('',#389);
#389=AXIS2_PLACEMENT_3D('',#390,#391,#392);
#390=CARTESIAN_POINT('',(-0.216,0.271,0.008));
#391=DIRECTION('',(0.0,0.0,-1.0));
#392=DIRECTION('',(0.,1.,0.));
#393=FACE_OUTER_BOUND('',#394,.T.);
#394=EDGE_LOOP('',(#395,#405,#415,#425));
#395=ORIENTED_EDGE('',*,*,#298,.T.);
#405=ORIENTED_EDGE('',*,*,#249,.T.);
#415=ORIENTED_EDGE('',*,*,#200,.T.);
#425=ORIENTED_EDGE('',*,*,#347,.T.);
#435=STYLED_ITEM('',(#43),#436);
#436=ADVANCED_FACE('',(#442),#437,.T.);
#437=PLANE('',#438);
#438=AXIS2_PLACEMENT_3D('',#439,#440,#441);
#439=CARTESIAN_POINT('',(-0.216,-0.271,0.398));
#440=DIRECTION('',(0.0,0.0,1.0));
#441=DIRECTION('',(0.,1.,0.));
#442=FACE_OUTER_BOUND('',#443,.T.);
#443=EDGE_LOOP('',(#444,#454,#464,#474));
#444=ORIENTED_EDGE('',*,*,#220,.T.);
#454=ORIENTED_EDGE('',*,*,#269,.T.);
#464=ORIENTED_EDGE('',*,*,#318,.T.);
#474=ORIENTED_EDGE('',*,*,#367,.T.);
#484=MANIFOLD_SOLID_BREP($,#485);
#485=CLOSED_SHELL('',(#487,#536,#585,#634,#683,#732));
#486=STYLED_ITEM('',(#34),#487);
#487=ADVANCED_FACE('',(#493),#488,.T.);
#488=PLANE('',#489);
#489=AXIS2_PLACEMENT_3D('',#490,#491,#492);
#490=CARTESIAN_POINT('',(0.216,-0.279,0.0));
#491=DIRECTION('',(0.0,-1.0,0.0));
#492=DIRECTION('',(0.,0.,1.));
#493=FACE_OUTER_BOUND('',#494,.T.);
#494=EDGE_LOOP('',(#495,#505,#515,#525));
#498=CARTESIAN_POINT('',(0.521,-0.279,0.0));
#497=VERTEX_POINT('',#498);
#500=CARTESIAN_POINT('',(0.216,-0.279,0.0));
#499=VERTEX_POINT('',#500);
#496=EDGE_CURVE('',#497,#499,#501,.T.);
#501=LINE('',#498,#503);
#503=VECTOR('',#504,0.3048);
#504=DIRECTION('',(-1.0,0.0,0.0));
#495=ORIENTED_EDGE('',*,*,#496,.F.);
#508=CARTESIAN_POINT('',(0.521,-0.279,0.406));
#507=VERTEX_POINT('',#508);
#506=EDGE_CURVE('',#507,#497,#511,.T.);
#511=LINE('',#508,#513);
#513=VECTOR('',#514,0.4064);
#514=DIRECTION('',(0.0,0.0,-1.0));
#505=ORIENTED_EDGE('',*,*,#506,.F.);
#518=CARTESIAN_POINT('',(0.216,-0.279,0.406));
#517=VERTEX_POINT('',#518);
#516=EDGE_CURVE('',#517,#507,#521,.T.);
#521=LINE('',#518,#523);
#523=VECTOR('',#524,0.3048);
#524=DIRECTION('',(1.0,0.0,0.0));
#515=ORIENTED_EDGE('',*,*,#516,.F.);
#526=EDGE_CURVE('',#499,#517,#531,.T.);
#531=LINE('',#500,#533);
#533=VECTOR('',#534,0.4064);
#534=DIRECTION('',(0.0,0.0,1.0));
#525=ORIENTED_EDGE('',*,*,#526,.F.);
#535=STYLED_ITEM('',(#34),#536);
#536=ADVANCED_FACE('',(#542),#537,.T.);
#537=PLANE('',#538);
#538=AXIS2_PLACEMENT_3D('',#539,#540,#541);
#539=CARTESIAN_POINT('',(0.521,-0.279,0.0));
#540=DIRECTION('',(1.0,0.0,0.0));
#541=DIRECTION('',(0.,0.,1.));
#542=FACE_OUTER_BOUND('',#543,.T.);
#543=EDGE_LOOP('',(#544,#554,#564,#574));
#547=CARTESIAN_POINT('',(0.521,0.279,0.0));
#546=VERTEX_POINT('',#547);
#545=EDGE_CURVE('',#546,#497,#550,.T.);
#550=LINE('',#547,#552);
#552=VECTOR('',#553,0.5588);
#553=DIRECTION('',(0.0,-1.0,0.0));
#544=ORIENTED_EDGE('',*,*,#545,.F.);
#557=CARTESIAN_POINT('',(0.521,0.279,0.406));
#556=VERTEX_POINT('',#557);
#555=EDGE_CURVE('',#556,#546,#560,.T.);
#560=LINE('',#557,#562);
#562=VECTOR('',#563,0.4064);
#563=DIRECTION('',(0.0,0.0,-1.0));
#554=ORIENTED_EDGE('',*,*,#555,.F.);
#565=EDGE_CURVE('',#507,#556,#570,.T.);
#570=LINE('',#508,#572);
#572=VECTOR('',#573,0.5588);
#573=DIRECTION('',(0.0,1.0,0.0));
#564=ORIENTED_EDGE('',*,*,#565,.F.);
#574=ORIENTED_EDGE('',*,*,#506,.T.);
#584=STYLED_ITEM('',(#34),#585);
#585=ADVANCED_FACE('',(#591),#586,.T.);
#586=PLANE('',#587);
#587=AXIS2_PLACEMENT_3D('',#588,#589,#590);
#588=CARTESIAN_POINT('',(0.521,0.279,0.0));
#589=DIRECTION('',(0.0,1.0,0.0));
#590=DIRECTION('',(0.,0.,1.));
#591=FACE_OUTER_BOUND('',#592,.T.);
#592=EDGE_LOOP('',(#593,#603,#613,#623));
#596=CARTESIAN_POINT('',(0.216,0.279,0.0));
#595=VERTEX_POINT('',#596);
#594=EDGE_CURVE('',#595,#546,#599,.T.);
#599=LINE('',#596,#601);
#601=VECTOR('',#602,0.3048);
#602=DIRECTION('',(1.0,0.0,0.0));
#593=ORIENTED_EDGE('',*,*,#594,.F.);
#606=CARTESIAN_POINT('',(0.216,0.279,0.406));
#605=VERTEX_POINT('',#606);
#604=EDGE_CURVE('',#605,#595,#609,.T.);
#609=LINE('',#606,#611);
#611=VECTOR('',#612,0.4064);
#612=DIRECTION('',(0.0,0.0,-1.0));
#603=ORIENTED_EDGE('',*,*,#604,.F.);
#614=EDGE_CURVE('',#556,#605,#619,.T.);
#619=LINE('',#557,#621);
#621=VECTOR('',#622,0.3048);
#622=DIRECTION('',(-1.0,0.0,0.0));
#613=ORIENTED_EDGE('',*,*,#614,.F.);
#623=ORIENTED_EDGE('',*,*,#555,.T.);
#633=STYLED_ITEM('',(#34),#634);
#634=ADVANCED_FACE('',(#640),#635,.T.);
#635=PLANE('',#636);
#636=AXIS2_PLACEMENT_3D('',#637,#638,#639);
#637=CARTESIAN_POINT('',(0.216,0.279,0.0));
#638=DIRECTION('',(-1.0,0.0,0.0));
#639=DIRECTION('',(0.,0.,1.));
#640=FACE_OUTER_BOUND('',#641,.T.);
#641=EDGE_LOOP('',(#642,#652,#662,#672));
#643=EDGE_CURVE('',#499,#595,#648,.T.);
#648=LINE('',#500,#650);
#650=VECTOR('',#651,0.5588);
#651=DIRECTION('',(0.0,1.0,0.0));
#642=ORIENTED_EDGE('',*,*,#643,.F.);
#652=ORIENTED_EDGE('',*,*,#526,.T.);
#663=EDGE_CURVE('',#605,#517,#668,.T.);
#668=LINE('',#606,#670);
#670=VECTOR('',#671,0.5588);
#671=DIRECTION('',(0.0,-1.0,0.0));
#662=ORIENTED_EDGE('',*,*,#663,.F.);
#672=ORIENTED_EDGE('',*,*,#604,.T.);
#682=STYLED_ITEM('',(#34),#683);
#683=ADVANCED_FACE('',(#689),#684,.T.);
#684=PLANE('',#685);
#685=AXIS2_PLACEMENT_3D('',#686,#687,#688);
#686=CARTESIAN_POINT('',(0.216,0.279,0.0));
#687=DIRECTION('',(0.0,0.0,-1.0));
#688=DIRECTION('',(0.,1.,0.));
#689=FACE_OUTER_BOUND('',#690,.T.);
#690=EDGE_LOOP('',(#691,#701,#711,#721));
#691=ORIENTED_EDGE('',*,*,#594,.T.);
#701=ORIENTED_EDGE('',*,*,#545,.T.);
#711=ORIENTED_EDGE('',*,*,#496,.T.);
#721=ORIENTED_EDGE('',*,*,#643,.T.);
#731=STYLED_ITEM('',(#34),#732);
#732=ADVANCED_FACE('',(#738),#733,.T.);
#733=PLANE('',#734);
#734=AXIS2_PLACEMENT_3D('',#735,#736,#737);
#735=CARTESIAN_POINT('',(0.216,-0.279,0.406));
#736=DIRECTION('',(0.0,0.0,1.0));
#737=DIRECTION('',(0.,1.,0.));
#738=FACE_OUTER_BOUND('',#739,.T.);
#739=EDGE_LOOP('',(#740,#750,#760,#770));
#740=ORIENTED_EDGE('',*,*,#516,.T.);
#750=ORIENTED_EDGE('',*,*,#565,.T.);
#760=ORIENTED_EDGE('',*,*,#614,.T.);
#770=ORIENTED_EDGE('',*,*,#663,.T.);
#780=MANIFOLD_SOLID_BREP($,#781);
#781=CLOSED_SHELL('',(#783,#832,#881,#930,#979,#1028));
#782=STYLED_ITEM('',(#34),#783);
#783=ADVANCED_FACE('',(#789),#784,.T.);
#784=PLANE('',#785);
#785=AXIS2_PLACEMENT_3D('',#786,#787,#788);
#786=CARTESIAN_POINT('',(-0.521,-0.279,0.0));
#787=DIRECTION('',(0.0,-1.0,0.0));
#788=DIRECTION('',(0.,0.,1.));
#789=FACE_OUTER_BOUND('',#790,.T.);
#790=EDGE_LOOP('',(#791,#801,#811,#821));
#794=CARTESIAN_POINT('',(-0.216,-0.279,0.0));
#793=VERTEX_POINT('',#794);
#796=CARTESIAN_POINT('',(-0.521,-0.279,0.0));
#795=VERTEX_POINT('',#796);
#792=EDGE_CURVE('',#793,#795,#797,.T.);
#797=LINE('',#794,#799);
#799=VECTOR('',#800,0.3048);
#800=DIRECTION('',(-1.0,0.0,0.0));
#791=ORIENTED_EDGE('',*,*,#792,.F.);
#804=CARTESIAN_POINT('',(-0.216,-0.279,0.406));
#803=VERTEX_POINT('',#804);
#802=EDGE_CURVE('',#803,#793,#807,.T.);
#807=LINE('',#804,#809);
#809=VECTOR('',#810,0.4064);
#810=DIRECTION('',(0.0,0.0,-1.0));
#801=ORIENTED_EDGE('',*,*,#802,.F.);
#814=CARTESIAN_POINT('',(-0.521,-0.279,0.406));
#813=VERTEX_POINT('',#814);
#812=EDGE_CURVE('',#813,#803,#817,.T.);
#817=LINE('',#814,#819);
#819=VECTOR('',#820,0.3048);
#820=DIRECTION('',(1.0,0.0,0.0));
#811=ORIENTED_EDGE('',*,*,#812,.F.);
#822=EDGE_CURVE('',#795,#813,#827,.T.);
#827=LINE('',#796,#829);
#829=VECTOR('',#830,0.4064);
#830=DIRECTION('',(0.0,0.0,1.0));
#821=ORIENTED_EDGE('',*,*,#822,.F.);
#831=STYLED_ITEM('',(#34),#832);
#832=ADVANCED_FACE('',(#838),#833,.T.);
#833=PLANE('',#834);
#834=AXIS2_PLACEMENT_3D('',#835,#836,#837);
#835=CARTESIAN_POINT('',(-0.216,-0.279,0.0));
#836=DIRECTION('',(1.0,0.0,0.0));
#837=DIRECTION('',(0.,0.,1.));
#838=FACE_OUTER_BOUND('',#839,.T.);
#839=EDGE_LOOP('',(#840,#850,#860,#870));
#843=CARTESIAN_POINT('',(-0.216,0.279,0.0));
#842=VERTEX_POINT('',#843);
#841=EDGE_CURVE('',#842,#793,#846,.T.);
#846=LINE('',#843,#848);
#848=VECTOR('',#849,0.5588);
#849=DIRECTION('',(0.0,-1.0,0.0));
#840=ORIENTED_EDGE('',*,*,#841,.F.);
#853=CARTESIAN_POINT('',(-0.216,0.279,0.406));
#852=VERTEX_POINT('',#853);
#851=EDGE_CURVE('',#852,#842,#856,.T.);
#856=LINE('',#853,#858);
#858=VECTOR('',#859,0.4064);
#859=DIRECTION('',(0.0,0.0,-1.0));
#850=ORIENTED_EDGE('',*,*,#851,.F.);
#861=EDGE_CURVE('',#803,#852,#866,.T.);
#866=LINE('',#804,#868);
#868=VECTOR('',#869,0.5588);
#869=DIRECTION('',(0.0,1.0,0.0));
#860=ORIENTED_EDGE('',*,*,#861,.F.);
#870=ORIENTED_EDGE('',*,*,#802,.T.);
#880=STYLED_ITEM('',(#34),#881);
#881=ADVANCED_FACE('',(#887),#882,.T.);
#882=PLANE('',#883);
#883=AXIS2_PLACEMENT_3D('',#884,#885,#886);
#884=CARTESIAN_POINT('',(-0.216,0.279,0.0));
#885=DIRECTION('',(0.0,1.0,0.0));
#886=DIRECTION('',(0.,0.,1.));
#887=FACE_OUTER_BOUND('',#888,.T.);
#888=EDGE_LOOP('',(#889,#899,#909,#919));
#892=CARTESIAN_POINT('',(-0.521,0.279,0.0));
#891=VERTEX_POINT('',#892);
#890=EDGE_CURVE('',#891,#842,#895,.T.);
#895=LINE('',#892,#897);
#897=VECTOR('',#898,0.3048);
#898=DIRECTION('',(1.0,0.0,0.0));
#889=ORIENTED_EDGE('',*,*,#890,.F.);
#902=CARTESIAN_POINT('',(-0.521,0.279,0.406));
#901=VERTEX_POINT('',#902);
#900=EDGE_CURVE('',#901,#891,#905,.T.);
#905=LINE('',#902,#907);
#907=VECTOR('',#908,0.4064);
#908=DIRECTION('',(0.0,0.0,-1.0));
#899=ORIENTED_EDGE('',*,*,#900,.F.);
#910=EDGE_CURVE('',#852,#901,#915,.T.);
#915=LINE('',#853,#917);
#917=VECTOR('',#918,0.3048);
#918=DIRECTION('',(-1.0,0.0,0.0));
#909=ORIENTED_EDGE('',*,*,#910,.F.);
#919=ORIENTED_EDGE('',*,*,#851,.T.);
#929=STYLED_ITEM('',(#34),#930);
#930=ADVANCED_FACE('',(#936),#931,.T.);
#931=PLANE('',#932);
#932=AXIS2_PLACEMENT_3D('',#933,#934,#935);
#933=CARTESIAN_POINT('',(-0.521,0.279,0.0));
#934=DIRECTION('',(-1.0,0.0,0.0));
#935=DIRECTION('',(0.,0.,1.));
#936=FACE_OUTER_BOUND('',#937,.T.);
#937=EDGE_LOOP('',(#938,#948,#958,#968));
#939=EDGE_CURVE('',#795,#891,#944,.T.);
#944=LINE('',#796,#946);
#946=VECTOR('',#947,0.5588);
#947=DIRECTION('',(0.0,1.0,0.0));
#938=ORIENTED_EDGE('',*,*,#939,.F.);
#948=ORIENTED_EDGE('',*,*,#822,.T.);
#959=EDGE_CURVE('',#901,#813,#964,.T.);
#964=LINE('',#902,#966);
#966=VECTOR('',#967,0.5588);
#967=DIRECTION('',(0.0,-1.0,0.0));
#958=ORIENTED_EDGE('',*,*,#959,.F.);
#968=ORIENTED_EDGE('',*,*,#900,.T.);
#978=STYLED_ITEM('',(#34),#979);
#979=ADVANCED_FACE('',(#985),#980,.T.);
#980=PLANE('',#981);
#981=AXIS2_PLACEMENT_3D('',#982,#983,#984);
#982=CARTESIAN_POINT('',(-0.521,0.279,0.0));
#983=DIRECTION('',(0.0,0.0,-1.0));
#984=DIRECTION('',(0.,1.,0.));
#985=FACE_OUTER_BOUND('',#986,.T.);
#986=EDGE_LOOP('',(#987,#997,#1007,#1017));
#987=ORIENTED_EDGE('',*,*,#890,.T.);
#997=ORIENTED_EDGE('',*,*,#841,.T.);
#1007=ORIENTED_EDGE('',*,*,#792,.T.);
#1017=ORIENTED_EDGE('',*,*,#939,.T.);
#1027=STYLED_ITEM('',(#34),#1028);
#1028=ADVANCED_FACE('',(#1034),#1029,.T.);
#1029=PLANE('',#1030);
#1030=AXIS2_PLACEMENT_3D('',#1031,#1032,#1033);
#1031=CARTESIAN_POINT('',(-0.521,-0.279,0.406));
#1032=DIRECTION('',(0.0,0.0,1.0));
#1033=DIRECTION('',(0.,1.,0.));
#1034=FACE_OUTER_BOUND('',#1035,.T.);
#1035=EDGE_LOOP('',(#1036,#1046,#1056,#1066));
#1036=ORIENTED_EDGE('',*,*,#812,.T.);
#1046=ORIENTED_EDGE('',*,*,#861,.T.);
#1056=ORIENTED_EDGE('',*,*,#910,.T.);
#1066=ORIENTED_EDGE('',*,*,#959,.T.);
ENDSEC;
END-ISO-10303-21;
