#!/usr/bin/env python3
"""
3D Vendor Screen Tracker GUI
Shows numbered screens for SnapEDA, UltraLibrarian, and SamacSys
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time

class VendorScreenTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("3D Vendor Screen Tracker")
        self.root.geometry("800x600")
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs for each vendor
        self.snapeda_frame = ttk.Frame(self.notebook)
        self.ultralib_frame = ttk.Frame(self.notebook)
        self.samacsys_frame = ttk.Frame(self.notebook)
        
        self.notebook.add(self.snapeda_frame, text="SnapEDA")
        self.notebook.add(self.ultralib_frame, text="UltraLibrarian")
        self.notebook.add(self.samacsys_frame, text="SamacSys")
        
        # Setup each vendor tab
        self.setup_snapeda_tab()
        self.setup_ultralib_tab()
        self.setup_samacsys_tab()
        
        # Current screen tracking
        self.current_screens = {
            'snapeda': 0,
            'ultralib': 0,
            'samacsys': 0
        }
        
    def setup_snapeda_tab(self):
        # Title
        title = tk.Label(self.snapeda_frame, text="SnapEDA Screen Flow", font=('Arial', 16, 'bold'))
        title.pack(pady=10)
        
        # Current screen display
        self.snapeda_current = tk.Label(self.snapeda_frame, text="Screen: Not Started", 
                                       font=('Arial', 14), bg='lightgray')
        self.snapeda_current.pack(pady=5, fill='x', padx=20)
        
        # Screen log
        self.snapeda_log = scrolledtext.ScrolledText(self.snapeda_frame, height=25, width=80)
        self.snapeda_log.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Add initial screen descriptions
        screens = [
            "Screen 1: Browser Launch - Chrome opens, navigates to SnapEDA login",
            "Screen 2: Login Page - Email/password form visible",
            "Screen 3: Login Submit - Clicking login button",
            "Screen 4: Post-Login Redirect - URL changes from login page",
            "Screen 5: Welcome/Onboarding Screen - Skip/Continue buttons",
            "Screen 6: Survey/Feedback Screen - No thanks/Dismiss buttons", 
            "Screen 7: Part Search - Direct URL or search page",
            "Screen 8: Part Page - Component details and 3D model tab",
            "Screen 9: 3D Model Tab - Download button visible",
            "Screen 10: Download Click - Modal/popup may appear",
            "Screen 11: File Download - Monitoring download folder"
        ]
        
        for i, screen in enumerate(screens, 1):
            self.snapeda_log.insert(tk.END, f"{screen}\n\n")
            
    def setup_ultralib_tab(self):
        # Title
        title = tk.Label(self.ultralib_frame, text="UltraLibrarian Screen Flow", font=('Arial', 16, 'bold'))
        title.pack(pady=10)
        
        # Current screen display
        self.ultralib_current = tk.Label(self.ultralib_frame, text="Screen: Not Started", 
                                        font=('Arial', 14), bg='lightgray')
        self.ultralib_current.pack(pady=5, fill='x', padx=20)
        
        # Screen log
        self.ultralib_log = scrolledtext.ScrolledText(self.ultralib_frame, height=25, width=80)
        self.ultralib_log.pack(fill='both', expand=True, padx=20, pady=10)
        
        screens = [
            "Screen 1: Browser Launch - Chrome opens, navigates to UltraLibrarian",
            "Screen 2: Homepage - Search box visible",
            "Screen 3: Search Submit - Entering part number and searching",
            "Screen 4: Search Results - List of matching parts",
            "Screen 5: Part Selection - Clicking on correct part",
            "Screen 6: Part Details - Component information page",
            "Screen 7: Download Options - CAD model download buttons",
            "Screen 8: Format Selection - STEP file format selection",
            "Screen 9: Download Start - File download begins",
            "Screen 10: Download Complete - File appears in folder"
        ]
        
        for i, screen in enumerate(screens, 1):
            self.ultralib_log.insert(tk.END, f"Screen {i}: {screen}\n\n")
            
    def setup_samacsys_tab(self):
        # Title  
        title = tk.Label(self.samacsys_frame, text="SamacSys Screen Flow", font=('Arial', 16, 'bold'))
        title.pack(pady=10)
        
        # Current screen display
        self.samacsys_current = tk.Label(self.samacsys_frame, text="Screen: Not Started", 
                                        font=('Arial', 14), bg='lightgray')
        self.samacsys_current.pack(pady=5, fill='x', padx=20)
        
        # Screen log
        self.samacsys_log = scrolledtext.ScrolledText(self.samacsys_frame, height=25, width=80)
        self.samacsys_log.pack(fill='both', expand=True, padx=20, pady=10)
        
        screens = [
            "Screen 1: Browser Launch - Chrome opens, navigates to SamacSys",
            "Screen 2: Search Page - Part number search box",
            "Screen 3: Search Results - Component listings",
            "Screen 4: Part Page - Component details and downloads",
            "Screen 5: CAD Downloads - 3D model download section",
            "Screen 6: STEP Download - Clicking STEP file download",
            "Screen 7: Download Processing - File preparation",
            "Screen 8: Download Complete - File saved to folder"
        ]
        
        for i, screen in enumerate(screens, 1):
            self.samacsys_log.insert(tk.END, f"Screen {i}: {screen}\n\n")
    
    def update_screen(self, vendor, screen_num, description, status="CURRENT"):
        """Update current screen for a vendor"""
        self.current_screens[vendor] = screen_num
        
        if vendor == 'snapeda':
            current_label = self.snapeda_current
            log_widget = self.snapeda_log
        elif vendor == 'ultralib':
            current_label = self.ultralib_current  
            log_widget = self.ultralib_log
        else:  # samacsys
            current_label = self.samacsys_current
            log_widget = self.samacsys_log
            
        # Update current screen display
        color = 'lightgreen' if status == "COMPLETE" else 'yellow' if status == "CURRENT" else 'lightcoral'
        current_label.config(text=f"Screen {screen_num}: {description}", bg=color)
        
        # Add to log
        timestamp = time.strftime("%H:%M:%S")
        log_widget.insert(tk.END, f"[{timestamp}] Screen {screen_num}: {description} - {status}\n")
        log_widget.see(tk.END)
        
        # Update display
        self.root.update()
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    tracker = VendorScreenTracker()
    tracker.run()
