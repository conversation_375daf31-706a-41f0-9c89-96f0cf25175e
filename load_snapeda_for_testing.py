#!/usr/bin/env python3
"""
Load SnapEDA website with visible browser for manual testing
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def load_snapeda_for_testing():
    print("🌐 LOADING SNAPEDA WEBSITE FOR MANUAL TESTING")
    print("=" * 60)
    
    # Setup Chrome with visible browser (maximized)
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    print("🔸 Starting Chrome browser...")
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Load SnapEDA homepage
        print("🔸 Loading SnapEDA homepage...")
        driver.get("https://www.snapeda.com/")
        time.sleep(3)
        
        print(f"✅ Loaded: {driver.current_url}")
        print(f"✅ Title: {driver.title}")
        
        print("\n🎯 BROWSER IS NOW READY FOR MANUAL TESTING!")
        print("=" * 60)
        print("You can now:")
        print("1. Look for the login link on the right side")
        print("2. Click login and see the login form")
        print("3. Test the login flow:")
        print("   - Enter email: <EMAIL>")
        print("   - Enter password: Lennyai123#")
        print("   - Enter part number: LM358N")
        print("   - Click login button")
        print("4. See where it takes you")
        print("5. Look for 3D model download options")
        print("\n🔍 Press Enter when you're done testing...")
        
        # Keep browser open for manual testing
        input()
        
        print("✅ Manual testing complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to close...")
    
    finally:
        print("🔸 Closing browser...")
        driver.quit()
        print("✅ Browser closed")

if __name__ == "__main__":
    load_snapeda_for_testing()
