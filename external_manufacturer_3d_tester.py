#!/usr/bin/env python3
"""
External Manufacturer 3D Model Tester
Standalone program that can be called from component_finder.py
Usage: python external_manufacturer_3d_tester.py <manufacturer> <part_number>
Example: python external_manufacturer_3d_tester.py "Texas Instruments" "LM358N"
"""

import sys
import os
import json
import subprocess
from pathlib import Path

def call_manufacturer_3d_tester(manufacturer, part_number):
    """Call the manufacturer 3D tester and return results"""
    print(f"🎯 EXTERNAL MANUFACTURER 3D TESTER")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 50)
    
    result = {
        'success': False,
        'accessible_websites': 0,
        'sites_with_3d_content': 0,
        'urls_with_potential_models': 0,
        'total_potential_3d_links': 0,
        'best_urls': [],
        'error': None,
        'recommendations': []
    }
    
    try:
        # Import and use the simple manufacturer tester
        from simple_3d_test import simple_manufacturer_test
        
        print("🔍 Running manufacturer website analysis...")
        test_results = simple_manufacturer_test(manufacturer, part_number)
        
        # Extract key information
        result['success'] = True
        result['accessible_websites'] = test_results.get('accessible_sites', 0)
        result['sites_with_3d_content'] = test_results.get('sites_with_3d_content', 0)
        
        # Count potential URLs (simplified)
        potential_urls = test_results.get('potential_urls', [])
        result['urls_with_potential_models'] = min(len(potential_urls), 5)  # Limit for display
        result['total_potential_3d_links'] = len(potential_urls)
        result['best_urls'] = potential_urls[:3]  # First 3 URLs
        
        # Generate recommendations
        if result['accessible_websites'] > 0:
            if result['sites_with_3d_content'] > 0:
                result['recommendations'].append("✅ Manufacturer website has 3D content - worth manual checking")
                result['recommendations'].append("🔗 Try the best URLs listed above")
            else:
                result['recommendations'].append("⚠️ Manufacturer website accessible but no obvious 3D content")
                result['recommendations'].append("🔍 May require search or navigation to find 3D models")
        else:
            result['recommendations'].append("❌ Manufacturer website not accessible or not found")
            result['recommendations'].append("🌐 Check if manufacturer has different website URL")
        
        # Always recommend using existing working finders
        result['recommendations'].append("💡 Your existing 3D finders (SnapEDA, UltraLibrarian, SamacSys) are more reliable")
        
        return result
        
    except ImportError as e:
        result['error'] = f"Import error: {str(e)}"
        result['recommendations'].append("❌ Simple 3D test module not available")
        return result
    except Exception as e:
        result['error'] = f"Test error: {str(e)}"
        result['recommendations'].append("❌ Manufacturer website test failed")
        return result

def main():
    """Main function for command line usage"""
    if len(sys.argv) != 3:
        print("Usage: python external_manufacturer_3d_tester.py <manufacturer> <part_number>")
        print('Example: python external_manufacturer_3d_tester.py "Texas Instruments" "LM358N"')
        sys.exit(1)
    
    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    
    # Run the test
    result = call_manufacturer_3d_tester(manufacturer, part_number)
    
    # Print result as JSON for easy parsing by main program
    print("\nRESULT:", json.dumps(result, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if result['success'] else 1)

if __name__ == "__main__":
    main()
