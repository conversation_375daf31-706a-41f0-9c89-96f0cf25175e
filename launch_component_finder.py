#!/usr/bin/env python3
"""
Simple launcher for component_finder.py
This bypasses any terminal issues and launches the GUI directly
"""

import os
import sys
import subprocess

def launch_component_finder():
    """Launch the component finder GUI"""
    print("🚀 LAUNCHING COMPONENT FINDER GUI")
    print("=" * 50)
    
    try:
        # Get the current directory
        current_dir = os.getcwd()
        print(f"📁 Working directory: {current_dir}")
        
        # Check if component_finder.py exists
        component_finder_path = os.path.join(current_dir, "component_finder.py")
        if not os.path.exists(component_finder_path):
            print(f"❌ component_finder.py not found at: {component_finder_path}")
            return False
        
        print(f"✅ Found component_finder.py")
        
        # Launch the GUI using subprocess
        print("🖥️ Starting GUI...")
        
        # Use subprocess to launch in a new process
        process = subprocess.Popen([
            sys.executable, "component_finder.py"
        ], cwd=current_dir)
        
        print("✅ Component Finder GUI launched successfully!")
        print("🔍 Look for window: 'Component Finder - Interactive Learning System'")
        print("📋 The GUI should be visible on your screen now.")
        print("")
        print("💡 What to do next:")
        print("   1. Enter a manufacturer (e.g., 'Texas Instruments')")
        print("   2. Enter a part number (e.g., 'LM358N')")
        print("   3. Click '🔍 Search Component'")
        print("   4. Watch the comments area for the new manufacturer analysis")
        print("")
        print("🎯 You should see the new external manufacturer 3D tester in action!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")
        return False

if __name__ == "__main__":
    success = launch_component_finder()
    if success:
        print("\n🎉 Launch successful! Check your screen for the GUI window.")
    else:
        print("\n❌ Launch failed. Please try running 'python component_finder.py' directly.")
        input("Press Enter to exit...")
