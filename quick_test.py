#!/usr/bin/env python3
"""
Quick test for the tuple error fix
"""

import sys
import tkinter as tk
from unittest.mock import MagicMock

def test_tuple_fix():
    """Test that the tuple error is fixed"""
    
    print("🚀 Testing Tuple Error Fix")
    print("=" * 40)
    
    try:
        # Import the component finder
        from component_finder import ComponentFinderGUI
        
        # Create a test root window
        root = tk.Tk()
        root.withdraw()  # Hide the window during testing
        
        # Create the GUI instance
        app = ComponentFinderGUI(root)
        
        # Mock the GUI update methods
        app.add_comment = lambda msg: print(f"   {msg}")
        app.status_text = MagicMock()
        app.pdf_file_text = MagicMock()
        app.step_file_text = MagicMock()
        app.pdf_file_text.get.return_value = ""
        app.step_file_text.get.return_value = ""
        
        # Mock the ask_for_help methods
        app.ask_for_website = lambda *args: None
        app.ask_for_help_finding_file = lambda *args: None
        app.download_file_from_url = lambda *args: None
        app.extract_and_display_package_info = lambda *args: None
        
        print("✅ Component Finder GUI created successfully")
        
        # Test the distributor search method directly
        print("\n📋 Testing Distributor Search Method")
        print("-" * 40)
        
        try:
            result = app.search_distributors_for_part_info("Texas Instruments", "LM358N")
            if result:
                print(f"   ✅ Distributor search returned: {type(result)}")
                if isinstance(result, dict):
                    print(f"   ✅ Result is dictionary with keys: {list(result.keys())}")
                    # Test .get() method
                    datasheet_url = result.get('datasheet_url')
                    print(f"   ✅ .get() method works: datasheet_url = {datasheet_url is not None}")
                else:
                    print(f"   ❌ Result is not dictionary: {type(result)}")
            else:
                print("   ❌ No result returned")
        except Exception as e:
            print(f"   ❌ Error in distributor search: {e}")
        
        # Test Digi-Key API directly
        print("\n📋 Testing Digi-Key API Method")
        print("-" * 40)
        
        try:
            dk_result = app.try_digikey_api_fallback("Texas Instruments", "LM358N")
            if dk_result:
                print(f"   ✅ Digi-Key API returned: {type(dk_result)}")
                if isinstance(dk_result, dict):
                    print(f"   ✅ Result is dictionary with keys: {list(dk_result.keys())}")
                    # Test .get() method
                    datasheet_url = dk_result.get('datasheet_url')
                    print(f"   ✅ .get() method works: datasheet_url = {datasheet_url is not None}")
                else:
                    print(f"   ❌ Result is not dictionary: {type(dk_result)}")
            else:
                print("   ❌ No result returned")
        except Exception as e:
            print(f"   ❌ Error in Digi-Key API: {e}")
        
        print("\n" + "=" * 40)
        print("🎉 Tuple fix test completed!")
        
        # Clean up
        root.destroy()
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tuple_fix()
