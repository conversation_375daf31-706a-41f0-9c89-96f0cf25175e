#!/usr/bin/env python3
"""
Complete SnapEDA Test - End to End
==================================
Test the entire SnapEDA workflow to ensure it works 100%
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Import the actual SnapEDA finder
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from snapeda_3d_finder_working import find_3d_model

def test_snapeda_complete():
    """Test complete SnapEDA workflow"""
    print("🧪 TESTING SNAPEDA COMPLETE WORKFLOW")
    print("=" * 50)
    
    # Test component
    manufacturer = "Murata"
    part_number = "GCM155R71H104KE02D"
    
    print(f"📦 Testing component: {manufacturer} {part_number}")
    
    # Test login and search only, then STOP for user guidance
    try:
        print("\n🔍 Step 1: Testing SnapEDA login and search...")

        # Create VISIBLE Chrome driver directly (not using finder setup)
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options

        chrome_options = Options()
        # NO minimization options at all
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        driver = webdriver.Chrome(options=chrome_options)
        driver.maximize_window()  # Force maximize
        print("🖥️ Browser window should be MAXIMIZED and VISIBLE")

        # Login manually (simple approach)
        print("🔐 Logging in...")
        driver.get("https://www.snapeda.com/account/login/")
        time.sleep(3)

        from selenium.webdriver.common.by import By
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")

        email_field.send_keys("<EMAIL>")
        password_field.send_keys("Lennyai123#")

        login_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Log in')]")
        login_button.click()
        time.sleep(5)

        print("✅ Login successful")

        # Search for part
        print(f"🔍 Searching for {manufacturer} {part_number}...")
        part_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
        print(f"🔍 Going to: {part_url}")
        driver.get(part_url)

        import time
        time.sleep(2)  # Reduced delay

        print("🔍 Looking for 3D model section...")
        from selenium.webdriver.common.by import By

        # Debug: Show what's on the page
        print("🔍 DEBUG: Looking for all clickable elements with '3D' text...")
        all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D') or contains(@class, '3d') or contains(@class, 'model')]")
        for i, elem in enumerate(all_elements[:10]):
            try:
                text = elem.text.strip()
                tag = elem.tag_name
                classes = elem.get_attribute('class') or ''
                print(f"  Element {i+1}: <{tag}> '{text}' class='{classes}'")
            except:
                pass

        # Look for the EXACT 3D Model tab (based on terminal output)
        three_d_selectors = [
            "//li[text()='3D Model']",  # EXACT match first
            "//li[contains(text(), '3D Model')]",  # Then contains
            "//li[contains(@class, '3D_model_tab')]"  # Then by class
        ]

        print("🔍 Looking for EXACT 3D Model tab...")
        tab_found = False

        # Try the EXACT selector first
        try:
            three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
            if three_d_tab.is_displayed() and three_d_tab.is_enabled():
                print(f"✅ Found EXACT 3D Model tab: '{three_d_tab.text}'")
                print("🔸 Clicking with JavaScript...")
                driver.execute_script("arguments[0].click();", three_d_tab)
                time.sleep(2)
                print("✅ 3D Model tab clicked!")
                tab_found = True
            else:
                print("❌ 3D Model tab found but not clickable")
        except Exception as e:
            print(f"❌ EXACT selector failed: {e}")

        if not tab_found:
            print("❌ Could not click 3D Model tab")

        if not tab_found:
            print("❌ No 3D button found - showing all buttons on page:")
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            for i, btn in enumerate(all_buttons[:10]):
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  Button {i+1}: '{text}'")
                except:
                    pass

        print("⏸️ Program will auto-terminate in 15 seconds!")
        print("⏸️ Watch the browser to see if 3D tab gets clicked...")

        # Auto-terminate after 15 seconds (shorter timeout)
        time.sleep(15)
        print("⏰ 15 seconds elapsed - terminating program")
        print("🔍 CHECKING: Did the 3D Model tab actually get clicked?")

        driver.quit()
        result = "STOPPED_FOR_USER_GUIDANCE"
        
        if result:
            print("🎉 SUCCESS: SnapEDA workflow completed successfully!")
            print(f"✅ Result: {result}")
            
            # Check if file was actually downloaded
            download_dir = os.path.join(os.getcwd(), "3d")
            if os.path.exists(download_dir):
                files = [f for f in os.listdir(download_dir) if part_number in f and f.endswith('.step')]
                if files:
                    print(f"✅ Downloaded file found: {files[0]}")
                    return True
                else:
                    print("❌ No downloaded file found")
                    return False
            else:
                print("❌ Download directory not found")
                return False
        else:
            print("❌ FAILED: SnapEDA workflow failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Complete SnapEDA Test...")
    
    success = test_snapeda_complete()
    
    if success:
        print("\n🎉 SnapEDA TEST PASSED - 100% WORKING!")
    else:
        print("\n❌ SnapEDA TEST FAILED - NEEDS MORE FIXES")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
