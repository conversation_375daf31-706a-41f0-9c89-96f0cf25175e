#!/usr/bin/env python3
"""
Create a comprehensive test Excel file for Component Finder testing
"""

import pandas as pd
from pathlib import Path

def create_test_excel_file():
    """Create a test Excel file with various component data"""
    
    # Sample component data
    components = [
        {
            'Manufacturer': 'Texas Instruments',
            'Part Number': 'LM358P',
            'Description': 'Dual Operational Amplifier',
            'Package': 'DIP-8',
            'Category': 'Op Amp'
        },
        {
            'Manufacturer': 'Murata',
            'Part Number': 'GCM155R71H104KE02D',
            'Description': '0.1µF Ceramic Capacitor',
            'Package': '0402',
            'Category': 'Capacitor'
        },
        {
            'Manufacturer': 'Analog Devices',
            'Part Number': 'AD8065',
            'Description': 'High Speed Op Amp',
            'Package': 'SOIC-8',
            'Category': 'Op Amp'
        },
        {
            'Manufacturer': 'STMicroelectronics',
            'Part Number': 'STM32F103C8T6',
            'Description': '32-bit ARM Microcontroller',
            'Package': 'LQFP-48',
            'Category': 'Microcontroller'
        },
        {
            'Manufacturer': 'Vishay',
            'Part Number': '1N4148',
            'Description': 'Fast Switching Diode',
            'Package': 'DO-35',
            'Category': 'Diode'
        },
        {
            'Manufacturer': 'Microchip',
            'Part Number': 'PIC16F877A',
            'Description': '8-bit Microcontroller',
            'Package': 'DIP-40',
            'Category': 'Microcontroller'
        },
        {
            'Manufacturer': 'WURTH',
            'Part Number': '74279215100',
            'Description': 'Inductor 10µH',
            'Package': '1210',
            'Category': 'Inductor'
        },
        {
            'Manufacturer': 'TDK',
            'Part Number': 'C1005X5R1E105K050BC',
            'Description': '1µF Ceramic Capacitor',
            'Package': '0402',
            'Category': 'Capacitor'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(components)
    
    # Save to Excel file
    excel_file = Path("test_components_comprehensive.xlsx")
    
    try:
        df.to_excel(excel_file, index=False, sheet_name='Components')
        print(f"✅ Created comprehensive test Excel file: {excel_file}")
        print(f"📊 Contains {len(components)} test components")
        print("\nComponents included:")
        for i, comp in enumerate(components, 1):
            print(f"{i:2d}. {comp['Manufacturer']} - {comp['Part Number']}")
        
        # Also create a version with different column names to test column mapping
        df_alt = df.copy()
        df_alt.columns = ['Mfg Name', 'Part Num', 'Desc', 'Pkg', 'Type']
        
        alt_excel_file = Path("test_components_alt_columns.xlsx")
        df_alt.to_excel(alt_excel_file, index=False, sheet_name='Parts')
        print(f"\n✅ Created alternative column test file: {alt_excel_file}")
        print("📋 Uses different column names to test column mapping dialog")
        
        return excel_file, alt_excel_file
        
    except Exception as e:
        print(f"❌ Error creating Excel files: {e}")
        return None, None

def test_excel_reading():
    """Test reading the Excel files"""
    files = ["test_components_comprehensive.xlsx", "test_components_alt_columns.xlsx"]
    
    for file in files:
        if Path(file).exists():
            try:
                df = pd.read_excel(file)
                print(f"\n📖 Successfully read {file}:")
                print(f"   Columns: {list(df.columns)}")
                print(f"   Rows: {len(df)}")
                print(f"   First row: {df.iloc[0].to_dict()}")
            except Exception as e:
                print(f"❌ Error reading {file}: {e}")
        else:
            print(f"❌ File not found: {file}")

if __name__ == "__main__":
    print("🔧 Creating comprehensive test Excel files for Component Finder...")
    print("=" * 60)
    
    # Create the files
    main_file, alt_file = create_test_excel_file()
    
    if main_file and alt_file:
        print("\n" + "=" * 60)
        print("📋 TESTING INSTRUCTIONS:")
        print("=" * 60)
        print("1. Use the Dialog Position Debugger to test dialog positioning")
        print("2. Load the Excel files in Component Finder:")
        print(f"   - {main_file} (standard columns)")
        print(f"   - {alt_file} (alternative columns - tests column mapping)")
        print("3. Test that all dialogs appear on the correct screen")
        print("4. Test Excel processing with these known components")
        
        # Test reading
        print("\n🧪 Testing Excel file reading...")
        test_excel_reading()
        
    print("\n✅ Test setup complete!")
