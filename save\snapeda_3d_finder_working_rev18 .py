#!/usr/bin/env python3
"""
SnapEDA 3D Model Finder - Working Version with Smart Waits
Based on successful test that clicks the correct login button below password field
Optimized with intelligent waits instead of fixed delays
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from smart_wait_utils import create_smart_wait, CommonLocators

class SnapEDA3DFinder:
    def __init__(self, silent=False, debug_mode=True):
        self.silent = silent
        self.debug_mode = debug_mode  # Enable debug mode by default
        self.download_dir = os.path.abspath('3d')
        os.makedirs(self.download_dir, exist_ok=True)

        # Load credentials
        self.credentials = self.load_credentials()

    def log_print(self, message):
        """Print message only if not in silent mode"""
        if not self.silent:
            print(message)
        # In silent mode, only show critical errors and final results
        elif "❌" in message or "✅ Silent mode download successful" in message or "LOGIN SUCCESSFUL" in message:
            print(f"[SILENT] {message}")

    def load_credentials(self):
        """Load SnapEDA credentials from JSON file"""
        try:
            with open('component_site_credentials.json', 'r') as f:
                data = json.load(f)
                return data.get('SnapEDA', {})
        except Exception as e:
            if not hasattr(self, 'silent') or not self.silent:
                print(f"⚠️ Could not load credentials: {e}")
            return {
                'email': '<EMAIL>',
                'password': 'Lennyai123#'
            }

    def validate_3d_model(self, file_path, expected_manufacturer, expected_part_number):
        """
        Validate that the downloaded 3D model file is for the correct manufacturer and part number.
        This is CRITICAL to prevent downloading wrong parts with misleading filenames.
        """
        try:
            self.log_print(f"🔍 Validating 3D model for {expected_manufacturer} {expected_part_number}")

            # Read the file content to check for part number and manufacturer
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().upper()  # Convert to uppercase for case-insensitive matching

            expected_part_upper = expected_part_number.upper()
            expected_manufacturer_upper = expected_manufacturer.upper()

            # Check if the expected part number appears in the file
            part_number_found = expected_part_upper in content

            # Check if the expected manufacturer appears in the file
            manufacturer_found = expected_manufacturer_upper in content

            # Also check for common variations of the part number
            part_variations = [
                expected_part_upper,
                expected_part_upper.replace('-', ''),
                expected_part_upper.replace('_', ''),
                expected_part_upper.replace(' ', ''),
            ]

            part_variation_found = any(variation in content for variation in part_variations)

            # Log validation results
            self.log_print(f"   Part number '{expected_part_number}' found: {part_number_found}")
            self.log_print(f"   Part variations found: {part_variation_found}")
            self.log_print(f"   Manufacturer '{expected_manufacturer}' found: {manufacturer_found}")

            # Validation criteria:
            # 1. Must find the part number (or a variation) in the file
            # 2. Preferably find the manufacturer, but not strictly required (some files don't include manufacturer)
            if part_number_found or part_variation_found:
                if manufacturer_found:
                    self.log_print(f"✅ VALIDATION PASSED: Both part number and manufacturer found")
                    return True
                else:
                    self.log_print(f"⚠️ PARTIAL VALIDATION: Part number found but manufacturer not found")
                    # Still accept if part number is found - manufacturer might not be in the 3D file
                    return True
            else:
                self.log_print(f"❌ VALIDATION FAILED: Part number not found in 3D model file")

                # Debug: Show what part numbers we did find in the file
                import re
                # Look for common part number patterns in the file
                part_patterns = re.findall(r'[A-Z0-9]{3,}[-_]?[A-Z0-9]{2,}', content[:2000])  # First 2000 chars
                if part_patterns:
                    unique_patterns = list(set(part_patterns[:10]))  # Show up to 10 unique patterns
                    self.log_print(f"🔍 Found these part-like patterns in file: {unique_patterns}")

                return False

        except Exception as e:
            self.log_print(f"❌ Validation error: {e}")
            # If we can't validate, assume it's wrong to be safe
            return False
    
    def setup_driver(self):
        """Setup Chrome driver with download configuration"""
        chrome_options = Options()
        # Only minimize in silent mode
        if self.silent:
            chrome_options.add_argument("--start-minimized")  # Start minimized to taskbar
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Silent mode: normal window with minimal output, closes quickly after download
        if self.silent:
            chrome_options.add_argument("--disable-logging")
            chrome_options.add_argument("--log-level=3")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            # Window runs normally but closes immediately after download completes

        # Download preferences
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)

        driver = webdriver.Chrome(options=chrome_options)

        # Remove webdriver traces
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Only minimize window in silent mode
        if self.silent:
            try:
                driver.minimize_window()
                print("🔇 Window minimized to taskbar")
            except Exception as e:
                print(f"⚠️ Could not minimize window: {e}")
        else:
            print("🖥️ Browser window visible for debugging")

        # Initialize smart wait utility
        self.smart_wait = create_smart_wait(driver, timeout=15)

        # In silent mode, window runs normally but closes quickly
        if self.silent:
            print("🔇 Silent mode: Window appears briefly, closes immediately after download")

        return driver
    
    def login_to_snapeda(self, driver):
        """Login to SnapEDA using hardcoded credentials - WORKING VERSION"""
        self.log_print("🔐 Logging into SnapEDA...")

        try:
            # Go DIRECTLY to login page (simplified approach)
            self.log_print("🔸 Going directly to SnapEDA login page...")
            driver.get("https://www.snapeda.com/account/login/?next=/home/")

            # Event-driven waiting: Wait for login form to appear instead of fixed delay
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.NAME, "username"))
                )
                self.log_print(f"✅ Login form loaded: {driver.current_url}")
            except:
                self.log_print(f"✅ On login page: {driver.current_url}")

            # Use the correct SnapEDA credentials
            email = "<EMAIL>"
            password = "Lennyai123#"
            self.log_print(f"✅ Using SnapEDA credentials: {email}")

            # Event-driven waiting: Wait for page to be ready instead of fixed delay
            try:
                WebDriverWait(driver, 10).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                self.log_print("✅ Page fully loaded")
            except:
                self.log_print("⚠️ Page load timeout, continuing...")

            # Wait for login page to load instead of fixed delay
            if self.smart_wait.for_url_contains("login", timeout=10):
                self.log_print("✅ On login page")
            else:
                current_url = driver.current_url
                self.log_print(f"⚠️ May not be on login page. Current URL: {current_url}")
                # Continue anyway as sometimes the URL doesn't contain 'login' but we're still on the right page

            # Find email field - use the correct SnapEDA selector first (WORKING VERSION)
            email_field = None
            email_selectors = ["#id_username", "input[type='email']", "input[name='email']", "input[id*='email']"]

            self.log_print("🔸 Looking for email field...")
            for selector in email_selectors:
                try:
                    email_field = driver.find_element(By.CSS_SELECTOR, selector)
                    if email_field.is_displayed() and email_field.is_enabled():
                        self.log_print(f"✅ Found email field with selector: {selector}")
                        break
                except:
                    continue

            if not email_field:
                self.log_print("❌ Could not find email field")
                return False

            # Fill email with JavaScript to avoid interactability issues (WORKING VERSION)
            driver.execute_script("arguments[0].value = arguments[1];", email_field, email)
            self.log_print("✅ Filled email field")

            # Find password field (WORKING VERSION)
            password_field = None
            password_selectors = ["input[type='password']", "input[name='password']", "input[id*='password']"]

            for selector in password_selectors:
                try:
                    password_field = driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        break
                except:
                    continue

            if not password_field:
                self.log_print("❌ Could not find password field")
                return False

            # Fill password (WORKING VERSION)
            password_field.clear()
            password_field.send_keys(password)
            self.log_print("✅ Filled password field")

            # Step 5: Find and click the button BELOW the password field
            self.log_print("🔸 Looking for button below password field...")

            # Get the password field element first
            try:
                password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
                # Get the password field's position
                password_location = password_field.location
                password_size = password_field.size
                password_bottom = password_location['y'] + password_size['height']
            except Exception as e:
                self.log_print(f"❌ Could not find password field: {e}")
                return False
            
            # Find all buttons and look for ones below the password field
            all_buttons = driver.find_elements(By.XPATH, "//button | //input[@type='submit'] | //input[@type='button']")
            
            buttons_below_password = []
            for button in all_buttons:
                try:
                    if button.is_displayed():
                        button_location = button.location
                        button_text = button.text or button.get_attribute('value') or 'no-text'
                        
                        # Check if this button is below the password field
                        if button_location['y'] > password_bottom:
                            buttons_below_password.append((button, button_location['y'], button_text))
                except:
                    continue
            
            if buttons_below_password:
                # Sort by y-position (closest to password field first)
                buttons_below_password.sort(key=lambda x: x[1])
                
                # Click the first (topmost) button below password
                button_to_click, y_pos, button_text = buttons_below_password[0]

                self.log_print(f"🔸 Clicking button below password: '{button_text}'")

                # Try ActionChains click
                try:
                    actions = ActionChains(driver)
                    actions.move_to_element(button_to_click).pause(0.5).click().perform()
                    self.log_print("✅ Clicked login button with ActionChains")
                except Exception as e:
                    self.log_print(f"⚠️ ActionChains failed: {e}")
                    # Fallback to direct click
                    try:
                        button_to_click.click()
                        self.log_print("✅ Clicked login button directly")
                    except Exception as e2:
                        self.log_print(f"❌ Could not click login button: {e2}")
                        return False
            else:
                self.log_print("❌ No suitable login button found")
                return False

            # Step 6: Wait for login result - wait for URL to change away from login
            self.log_print("🔸 Waiting for login result...")
            current_url = driver.current_url

            # Wait for URL to change (indicating successful login)
            if self.smart_wait.for_url_changes(current_url, timeout=15):
                new_url = driver.current_url
                if "login" not in new_url.lower():
                    self.log_print("🎉 LOGIN SUCCESSFUL!")
                    return True
                else:
                    self.log_print("⚠️ URL changed but still contains 'login'")

            # Check final URL
            final_url = driver.current_url
            if "login" in final_url.lower():
                self.log_print("❌ Still on login page - login failed")
                return False
            else:
                self.log_print("🎉 LOGIN SUCCESSFUL!")
                return True

        except Exception as e:
            self.log_print(f"❌ Login error: {e}")
            return False
    
    def search_and_download(self, driver, manufacturer, part_number):
        """Search for part and download 3D model"""
        try:
            self.log_print(f"🔍 Searching for {manufacturer} {part_number}...")

            # Try direct part page first
            part_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
            self.log_print(f"🔍 Trying direct URL: {part_url}")
            driver.get(part_url)
            self.smart_wait.for_page_load(timeout=15)

            # Check if we got a valid part page or need to search
            if "404" in driver.title or "not found" in driver.page_source.lower():
                self.log_print("⚠️ Direct URL failed, trying search...")
                # Go to search page
                search_url = f"https://www.snapeda.com/search?q={part_number}"
                driver.get(search_url)
                self.smart_wait.for_page_load(timeout=15)

                # Wait for search results to load
                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//a[contains(@href, '/parts/')]"))
                    )
                    self.log_print("✅ Search results loaded")
                except:
                    self.log_print("⚠️ Search results timeout")

                # Look for search results - be very specific to avoid navigation links
                search_results = []

                # First, try the most specific selectors for actual part search results
                specific_selectors = [
                    # Look for search result containers with part links
                    f"//div[contains(@class, 'search-result') or contains(@class, 'result')]//a[contains(@href, '/parts/{part_number}/')]",
                    f"//div[contains(@class, 'search-result') or contains(@class, 'result')]//a[contains(@href, '/parts/') and contains(text(), '{part_number}')]",
                    # Look for part links that are NOT navigation links
                    f"//a[contains(@href, '/parts/{part_number}/') and not(contains(@href, 'my_part_requests')) and not(contains(@href, '/add'))]",
                    f"//a[contains(@href, '/parts/') and contains(text(), '{part_number}') and not(contains(@href, 'my_part_requests')) and not(contains(@href, '/add'))]"
                ]

                for i, selector in enumerate(specific_selectors):
                    try:
                        search_results = driver.find_elements(By.XPATH, selector)
                        if search_results:
                            self.log_print(f"✅ Found {len(search_results)} specific results with selector {i+1}")

                            # Debug: Show what we found
                            for j, result in enumerate(search_results[:3]):
                                try:
                                    href = result.get_attribute('href') or 'no-href'
                                    text = result.text.strip()[:50] or 'no-text'
                                    self.log_print(f"   Result {j+1}: '{text}' -> {href}")
                                except:
                                    pass

                            # Filter out navigation links more aggressively
                            filtered_results = []
                            for result in search_results:
                                href = result.get_attribute('href') or ''
                                # Exclude common navigation patterns
                                if not any(pattern in href for pattern in ['my_part_requests', '/add', '/messages', '/invitations', '/libraries', '/profiles']):
                                    # Must contain the part number in the URL
                                    if part_number.lower() in href.lower():
                                        filtered_results.append(result)

                            if filtered_results:
                                search_results = filtered_results
                                self.log_print(f"✅ Filtered to {len(search_results)} valid part results")
                                break
                    except:
                        continue

                if search_results:
                    # Try multiple click strategies for the first result
                    first_result = search_results[0]
                    self.log_print(f"🔸 Attempting to click search result: {first_result.text[:50]}")

                    # Strategy 1: Wait for element to be clickable
                    try:
                        clickable_result = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable(first_result)
                        )
                        clickable_result.click()
                        self.log_print("✅ Clicked search result (strategy 1)")
                    except:
                        try:
                            # Strategy 2: Scroll to element and click
                            driver.execute_script("arguments[0].scrollIntoView(true);", first_result)
                            time.sleep(1)
                            first_result.click()
                            self.log_print("✅ Clicked search result (strategy 2)")
                        except:
                            try:
                                # Strategy 3: JavaScript click
                                driver.execute_script("arguments[0].click();", first_result)
                                self.log_print("✅ Clicked search result (strategy 3)")
                            except:
                                try:
                                    # Strategy 4: ActionChains click
                                    actions = ActionChains(driver)
                                    actions.move_to_element(first_result).click().perform()
                                    self.log_print("✅ Clicked search result (strategy 4)")
                                except Exception as e:
                                    self.log_print(f"❌ All click strategies failed: {e}")
                                    return None

                    self.smart_wait.for_page_load(timeout=15)
                    self.log_print("✅ Found part via search")
                else:
                    self.log_print("❌ Part not found in search results")
                    return None
            else:
                self.log_print("✅ Direct part page loaded")

            # Verify we're on the correct manufacturer's part page
            page_source = driver.page_source.lower()
            manufacturer_lower = manufacturer.lower()

            # Check if the manufacturer name appears on the page
            if manufacturer_lower not in page_source:
                self.log_print(f"⚠️ Warning: Manufacturer '{manufacturer}' not found on page")
                self.log_print(f"🔍 This may be a different manufacturer's part")

                # Look for actual manufacturer on page
                try:
                    # Try to find manufacturer info in common locations
                    manufacturer_elements = driver.find_elements(By.XPATH, "//span[contains(@class, 'manufacturer')] | //div[contains(@class, 'manufacturer')] | //td[contains(text(), 'Manufacturer')]")
                    for elem in manufacturer_elements:
                        if elem.is_displayed() and elem.text.strip():
                            self.log_print(f"📋 Found manufacturer on page: {elem.text.strip()}")
                            break
                except:
                    pass

            # FIRST: Click 3D Model tab (WORKING VERSION APPROACH)
            self.log_print("🔸 Looking for 3D Model tab...")

            # Use the EXACT working selector from save folder
            three_d_found = False
            try:
                three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
                three_d_tab.click()

                # Event-driven waiting: Wait for 3D content to load instead of fixed delay
                try:
                    WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//*[contains(text(), '3D') or contains(text(), 'STEP') or contains(text(), 'Download')]"))
                    )
                    self.log_print("✅ Clicked 3D Model tab - content loaded")
                except:
                    self.log_print("✅ Clicked 3D Model tab")
                three_d_found = True
            except:
                self.log_print("⚠️ Exact selector failed, trying alternatives...")
                # Fallback selectors
                three_d_selectors = [
                    "//li[contains(text(), '3D Model')]",
                    "//a[contains(text(), '3D Model')]",
                    "//li[contains(text(), '3D')]",
                    "//a[contains(text(), '3D')]"
                ]

                for selector in three_d_selectors:
                    try:
                        tabs = driver.find_elements(By.XPATH, selector)
                        for tab in tabs:
                            if tab.is_displayed() and tab.is_enabled():
                                self.log_print(f"✅ Clicking 3D tab: {tab.text}")
                                tab.click()

                                # Event-driven waiting: Wait for content to change instead of fixed delay
                                try:
                                    WebDriverWait(driver, 3).until(
                                        lambda d: len(d.find_elements(By.XPATH, "//*[contains(text(), 'STEP') or contains(text(), 'Download')]")) > 0
                                    )
                                except:
                                    pass  # Continue even if specific content not found

                                three_d_found = True
                                break
                        if three_d_found:
                            break
                    except:
                        continue

            if not three_d_found:
                self.log_print("❌ No 3D model tab found")

                # Check if this part actually has 3D models available
                self.log_print("🔍 Checking if part has any 3D content...")
                page_text = driver.page_source.lower()

                # Look for indicators that 3D models might be available
                three_d_indicators = ['3d model', 'step file', 'cad model', 'download', '3d cad']
                found_indicators = [indicator for indicator in three_d_indicators if indicator in page_text]

                if found_indicators:
                    self.log_print(f"🔍 Found 3D indicators: {found_indicators}")

                    # Try to find any download links or buttons
                    download_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Download') or contains(text(), 'STEP') or contains(text(), '3D')]")
                    if download_elements:
                        self.log_print(f"🔍 Found {len(download_elements)} potential download elements")
                        for i, elem in enumerate(download_elements[:5]):
                            try:
                                if elem.is_displayed():
                                    text = elem.text.strip()[:50]
                                    tag = elem.tag_name
                                    self.log_print(f"   Element {i+1}: <{tag}> '{text}'")
                            except:
                                pass
                else:
                    self.log_print("❌ No 3D model indicators found on page")

                # Debug: Show current page info
                try:
                    current_url = driver.current_url
                    page_title = driver.title
                    self.log_print(f"🔍 Current page: {page_title}")
                    self.log_print(f"🔍 URL: {current_url}")

                    # Check if we're actually on a part page
                    if '/parts/' not in current_url:
                        self.log_print("⚠️ Not on a part page - search may have failed")
                        return None

                    # Show some navigation tabs/links to understand page structure
                    nav_elements = driver.find_elements(By.XPATH, "//li | //a[contains(@class, 'tab')] | //div[contains(@class, 'tab')]")[:10]
                    if nav_elements:
                        self.log_print(f"🔍 Found {len(nav_elements)} navigation elements:")
                        for i, elem in enumerate(nav_elements):
                            try:
                                if elem.is_displayed() and elem.text.strip():
                                    text = elem.text.strip()[:30]
                                    self.log_print(f"   Nav {i+1}: {text}")
                            except:
                                pass
                except:
                    pass

                return None

            # Dismiss any chat popups that might intercept clicks
            try:
                # Try to close Intercom chat if present
                chat_close_selectors = [
                    "//iframe[contains(@name, 'intercom')]",
                    "//div[contains(@class, 'intercom')]//button",
                    "//button[contains(@aria-label, 'Close')]"
                ]
                for selector in chat_close_selectors:
                    try:
                        chat_element = driver.find_element(By.XPATH, selector)
                        if "iframe" in selector:
                            # Hide iframe
                            driver.execute_script("arguments[0].style.display = 'none';", chat_element)
                        else:
                            # Click close button
                            driver.execute_script("arguments[0].click();", chat_element)
                        self.log_print("🔇 Dismissed chat popup")
                        break
                    except:
                        continue
            except:
                pass

            # Find and click download button (EXACT WORKING SELECTORS from save folder)
            self.log_print("🎯 Finding download button...")
            selectors = [
                "//a[contains(@class, '3D-model-download')]",
                "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
                "//a[text()='Download 3D Model' and contains(@class, 'orange')]"
            ]

            download_button = None
            for selector in selectors:
                try:
                    button = driver.find_element(By.XPATH, selector)
                    if button.is_displayed():
                        download_button = button
                        self.log_print(f"✅ Found button with selector: {selector}")
                        break
                except:
                    continue

            if not download_button:
                self.log_print("❌ Download button not found with any selector")
                # Enhanced debug: Show all clickable elements
                try:
                    all_clickable = driver.find_elements(By.XPATH, "//a | //button | //input[@type='button'] | //input[@type='submit']")
                    self.log_print(f"🔍 DEBUG: Found {len(all_clickable)} clickable elements")

                    for i, elem in enumerate(all_clickable[:15]):  # Show more elements
                        try:
                            if elem.is_displayed():
                                text = elem.text.strip()[:40] or elem.get_attribute('value') or 'no-text'
                                href = elem.get_attribute('href') or ''
                                class_attr = elem.get_attribute('class') or ''
                                self.log_print(f"   Element {i+1}: '{text}' href='{href[:30]}' class='{class_attr[:30]}'")
                        except:
                            pass
                except:
                    pass
                return None
            
            # Monitor files before download
            before_files = set(os.listdir(self.download_dir))
            self.log_print(f"📁 Download dir: {self.download_dir}")
            self.log_print(f"📁 Files before download: {len(before_files)}")

            # Click download button using JavaScript to avoid interception
            self.log_print("🔸 Clicking download button with JavaScript...")
            driver.execute_script("arguments[0].click();", download_button)
            self.log_print("✅ Download button clicked with JavaScript!")

            # Wait a moment for any popups or redirects
            time.sleep(1)  # Reduced from 3 to 1

            # Check if we're still on the same page or redirected
            current_url = driver.current_url
            self.log_print(f"📍 Current URL after click: {current_url}")

            # Check for any modals, popups, or additional download buttons
            self.log_print("🔍 Checking for modals/popups...")

            # Look for modal dialogs with more comprehensive search
            modals = driver.find_elements(By.XPATH, "//div[contains(@class, 'modal') or contains(@class, 'popup') or contains(@class, 'dialog') or contains(@id, 'modal') or contains(@id, 'popup')]")
            if modals:
                self.log_print(f"📋 Found {len(modals)} potential modals")
                for i, modal in enumerate(modals):
                    if modal.is_displayed():
                        self.log_print(f"  Modal {i+1}: visible")

                        # Get modal text to understand what it contains
                        modal_text = modal.text[:200] if modal.text else "no-text"
                        self.log_print(f"    Modal text: '{modal_text}'")

                        # Look for ALL clickable elements in modal
                        modal_clickables = modal.find_elements(By.XPATH, ".//a | .//button | .//input[@type='submit'] | .//div[@onclick] | .//span[@onclick]")
                        self.log_print(f"    Found {len(modal_clickables)} clickable elements in modal")

                        for j, element in enumerate(modal_clickables):
                            if element.is_displayed():
                                element_text = element.text.strip()
                                element_tag = element.tag_name
                                element_href = element.get_attribute('href') or ''
                                element_onclick = element.get_attribute('onclick') or ''

                                self.log_print(f"      Element {j+1}: {element_tag} '{element_text}' href='{element_href[:50]}' onclick='{element_onclick[:50]}'")

                                # Look for 3D model download indicators
                                if any(keyword in element_text.lower() for keyword in ['3d', 'model', 'step', 'download']) or \
                                   any(keyword in element_href.lower() for keyword in ['3d', 'model', 'step', 'download']) or \
                                   any(keyword in element_onclick.lower() for keyword in ['3d', 'model', 'step', 'download']):
                                    self.log_print(f"🎯 Clicking potential 3D download element: '{element_text}'")
                                    try:
                                        element.click()
                                        time.sleep(1)  # Reduced from 3 to 1
                                        break  # Exit after first successful click
                                    except:
                                        try:
                                            driver.execute_script("arguments[0].click();", element)
                                            time.sleep(1)  # Reduced from 3 to 1
                                            break
                                        except:
                                            continue

            # Look for any new download links that appeared
            new_download_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'download') or contains(text(), 'Download')]")
            self.log_print(f"🔗 Found {len(new_download_links)} download links after click")

            # Monitor for downloads with shorter intervals and more debugging
            self.log_print("📁 Monitoring downloads...")
            download_started = False

            # Optimized monitoring with adaptive timing
            max_wait_time = 45  # Reduced from 60 to 45 seconds
            check_interval = 1.5  # Increased from 1 to 1.5 seconds
            max_checks = int(max_wait_time / check_interval)

            for i in range(max_checks):
                try:
                    current_files = set(os.listdir(self.download_dir))
                    new_files = current_files - before_files

                    if new_files:
                        self.log_print(f"🎉 New files detected after {i * check_interval:.1f}s: {list(new_files)}")
                        download_started = True
                        break

                    if i % 7 == 0 and i > 0:  # Show every ~10 seconds
                        self.log_print(f"⏳ Still waiting... {i * check_interval:.1f}s/{max_wait_time}s")

                    time.sleep(check_interval)
                except Exception as e:
                    self.log_print(f"⚠️ Error checking files: {e}")
                    time.sleep(check_interval)

            if new_files:
                self.log_print("🎉 Download detected!")
                download_started = True

                for f in new_files:
                    file_path = os.path.join(self.download_dir, f)
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        self.log_print(f"  📄 {f} ({file_size} bytes)")

                        # Check for completed STEP files
                        if f.endswith('.step') or f.endswith('.stp') or f.endswith('.STEP') or f.endswith('.STP'):
                            self.log_print(f"🔍 Validating downloaded 3D model: {f}")

                            # CRITICAL: Validate that this is actually the correct part
                            if not self.validate_3d_model(file_path, manufacturer, part_number):
                                self.log_print(f"❌ Downloaded file does not match {manufacturer} {part_number}")
                                # Remove the incorrect file
                                try:
                                    os.remove(file_path)
                                    self.log_print(f"🗑️ Removed incorrect file: {f}")
                                except:
                                    pass
                                continue

                            # Create proper SnapEDA filename only after validation
                            final_name = f"SnapEDA_{manufacturer.replace(' ', '_')}_{part_number}.step"
                            final_path = os.path.join(self.download_dir, final_name)

                            # Remove existing file if it exists
                            if os.path.exists(final_path):
                                os.remove(final_path)

                            # Rename the validated file
                            os.rename(file_path, final_path)
                            self.log_print(f"✅ Validated and renamed to: {final_name}")
                            return final_name

                        # Check if .crdownload file has finished (no longer exists or became a STEP file)
                        elif f.endswith('.crdownload'):
                            # Wait for download to complete
                            self.log_print(f"⏳ Download in progress: {f}")
                            continue

                # Check if any .crdownload files have completed
                if download_started:
                    current_files = set(os.listdir(self.download_dir))
                    for f in current_files:
                        # Accept .step, .stp, .snap files from SnapEDA
                        if f not in before_files and (f.endswith('.step') or f.endswith('.stp') or f.endswith('.STEP') or f.endswith('.STP') or f.endswith('.snap')):
                            file_path = os.path.join(self.download_dir, f)

                            self.log_print(f"🔍 Validating downloaded 3D model: {f}")

                            # CRITICAL: Validate that this is actually the correct part
                            if not self.validate_3d_model(file_path, manufacturer, part_number):
                                self.log_print(f"❌ Downloaded file does not match {manufacturer} {part_number}")
                                # Remove the incorrect file
                                try:
                                    os.remove(file_path)
                                    self.log_print(f"🗑️ Removed incorrect file: {f}")
                                except:
                                    pass
                                continue

                            # Keep original extension for .snap files
                            if f.endswith('.snap'):
                                final_name = f"SnapEDA_{manufacturer.replace(' ', '_')}_{part_number}.snap"
                            else:
                                final_name = f"SnapEDA_{manufacturer.replace(' ', '_')}_{part_number}.step"
                            final_path = os.path.join(self.download_dir, final_name)

                            # Remove existing file if it exists
                            if os.path.exists(final_path):
                                os.remove(final_path)

                            # Rename the validated file
                            os.rename(file_path, final_path)
                            self.log_print(f"✅ Validated and renamed to: {final_name}")
                            return final_name

                if i % 10 == 0 and i > 0:
                    self.log_print(f"   ⏳ {i+1}/60 seconds...")

            self.log_print(f"❌ No download completed within {max_wait_time}s timeout")
            return None

        except Exception as e:
            self.log_print(f"❌ Search/download error: {e}")
            return None

def find_3d_model(manufacturer, part_number, silent=False):
    """Main function to find and download 3D model from SnapEDA"""

    if silent:
        print(f"🔇 SnapEDA Silent mode: Searching for {manufacturer} {part_number}")

    finder = SnapEDA3DFinder(silent=silent)
    driver = finder.setup_driver()

    try:
        # Login to SnapEDA
        if silent:
            print("🔇 Attempting login...")
        login_result = finder.login_to_snapeda(driver)
        if not login_result:
            if silent:
                print("❌ Login failed in silent mode")
            else:
                print("❌ Login failed")
            return None

        if not silent:
            print("✅ Login successful")

        # Search and download
        if silent:
            print("🔇 Starting search and download...")
        result = finder.search_and_download(driver, manufacturer, part_number)

        if result:
            if silent:
                print(f"✅ Silent mode download successful: {result}")
            else:
                print(f"✅ Download successful: {result}")
            return result
        else:
            if silent:
                print("❌ Silent mode download failed")
            else:
                print("❌ Download failed")
            return None

    except Exception as e:
        return None
    
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    # Test with LM358N
    result = find_3d_model("Texas Instruments", "LM358N")
    if result:
        print(f"\n✅ Downloaded: {result}")
    else:
        print(f"\n❌ Download failed")
