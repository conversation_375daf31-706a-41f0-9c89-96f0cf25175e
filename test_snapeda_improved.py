#!/usr/bin/env python3
"""
Test the improved SnapEDA finder with redirect handling
"""

import sys
import os
import time

# Add the save directory to path to import the finder
sys.path.append('save')

def test_snapeda_improved():
    print("🎯 TESTING IMPROVED SNAPEDA FINDER")
    print("=" * 60)
    
    manufacturer = "Texas Instruments"
    part_number = "LM358N"
    
    print(f"🔍 Testing part: {manufacturer} {part_number}")
    print(f"   Running in silent mode with redirect handling")
    
    try:
        from snapeda_3d_finder_final import find_3d_model as snapeda_find
        
        start_time = time.time()
        result = snapeda_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        print(f"\n📊 RESULT:")
        if result:
            print(f"✅ SUCCESS: {result} ({end_time-start_time:.1f}s)")
        else:
            print(f"❌ FAILED ({end_time-start_time:.1f}s)")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Check downloaded files
    if os.path.exists('3d'):
        files = os.listdir('3d')
        step_files = [f for f in files if f.endswith('.step')]
        if step_files:
            print(f"\n📁 DOWNLOADED STEP FILES:")
            for f in step_files:
                print(f"   ✅ {f}")
        else:
            print(f"\n📁 No STEP files found in 3d directory")
    else:
        print(f"\n📁 3d directory not found")

if __name__ == "__main__":
    test_snapeda_improved()
