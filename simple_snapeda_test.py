#!/usr/bin/env python3
"""
Simple SnapEDA test - just open and show login buttons
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

print("🔍 Starting simple SnapEDA test...")

# Setup Chrome
chrome_options = Options()
chrome_options.add_argument("--start-maximized")
driver = webdriver.Chrome(options=chrome_options)

try:
    print("🔸 Loading SnapEDA...")
    driver.get("https://www.snapeda.com/")
    time.sleep(3)
    
    print(f"✅ URL: {driver.current_url}")
    print(f"✅ Title: {driver.title}")
    
    # Look for login links
    print("🔸 Looking for login links...")
    login_elements = driver.find_elements(By.XPATH, "//a[contains(text(), 'Login') or contains(text(), 'Log In') or contains(text(), 'Sign In')]")
    
    print(f"✅ Found {len(login_elements)} login elements:")
    for i, elem in enumerate(login_elements):
        try:
            if elem.is_displayed():
                text = elem.text.strip()
                href = elem.get_attribute('href')
                print(f"  {i+1}. '{text}' -> {href}")
        except:
            pass
    
    print("\n🎯 Test complete - browser will stay open")
    print("Press Enter to close browser...")
    input()
    
except Exception as e:
    print(f"❌ Error: {e}")
    input("Press Enter to close...")
    
finally:
    driver.quit()
    print("✅ Browser closed")
