#!/usr/bin/env python3
"""
Integrated 3D Finder Test
Combines your existing 3D finders with the new manufacturer website tester
"""

import os
import sys
import time
import json
from datetime import datetime

def test_existing_3d_finders(manufacturer, part_number):
    """Test your existing 3D finders"""
    print("🔄 TESTING EXISTING 3D FINDERS")
    print("-" * 40)
    
    results = {}
    
    # Test 1: SamacSys
    print("1️⃣ Testing SamacSys...")
    try:
        from samacsys_3d_finder import find_3d_model as samacsys_find
        start_time = time.time()
        result = samacsys_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        if result:
            results['SamacSys'] = {
                'status': 'success',
                'filename': result,
                'time': f"{end_time-start_time:.1f}s"
            }
            print(f"   ✅ SUCCESS: {result}")
        else:
            results['SamacSys'] = {
                'status': 'failed',
                'time': f"{end_time-start_time:.1f}s"
            }
            print(f"   ❌ FAILED")
    except Exception as e:
        results['SamacSys'] = {'status': 'error', 'error': str(e)}
        print(f"   ❌ ERROR: {e}")
    
    # Test 2: UltraLibrarian
    print("2️⃣ Testing UltraLibrarian...")
    try:
        from ultralibrarian_3d_finder import find_3d_model as ultra_find
        start_time = time.time()
        result = ultra_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        if result:
            results['UltraLibrarian'] = {
                'status': 'success',
                'filename': result,
                'time': f"{end_time-start_time:.1f}s"
            }
            print(f"   ✅ SUCCESS: {result}")
        else:
            results['UltraLibrarian'] = {
                'status': 'failed',
                'time': f"{end_time-start_time:.1f}s"
            }
            print(f"   ❌ FAILED")
    except Exception as e:
        results['UltraLibrarian'] = {'status': 'error', 'error': str(e)}
        print(f"   ❌ ERROR: {e}")
    
    # Test 3: SnapEDA
    print("3️⃣ Testing SnapEDA...")
    try:
        from snapeda_3d_finder import find_3d_model as snapeda_find
        start_time = time.time()
        result = snapeda_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        if result:
            results['SnapEDA'] = {
                'status': 'success',
                'filename': result,
                'time': f"{end_time-start_time:.1f}s"
            }
            print(f"   ✅ SUCCESS: {result}")
        else:
            results['SnapEDA'] = {
                'status': 'failed',
                'time': f"{end_time-start_time:.1f}s"
            }
            print(f"   ❌ FAILED")
    except Exception as e:
        results['SnapEDA'] = {'status': 'error', 'error': str(e)}
        print(f"   ❌ ERROR: {e}")
    
    return results

def test_manufacturer_website_discovery(manufacturer, part_number):
    """Test the new manufacturer website discovery"""
    print("\n🌐 TESTING MANUFACTURER WEBSITE DISCOVERY")
    print("-" * 40)
    
    try:
        from manufacturer_3d_tester import test_manufacturer_3d_availability
        
        print("🔍 Analyzing manufacturer website...")
        results = test_manufacturer_3d_availability(manufacturer, part_number, save_results=False)
        
        summary = results['summary']
        
        print(f"   🌐 Accessible websites: {summary['accessible_websites']}")
        print(f"   🎯 Sites with 3D content: {summary['websites_with_3d_content']}")
        print(f"   📦 URLs with potential models: {summary['urls_with_potential_models']}")
        print(f"   🔗 Total potential 3D links: {summary['total_potential_3d_links']}")
        
        # Show best URLs
        urls_with_models = [r for r in results['url_test_results'] 
                           if r['status'] == 'success' and r.get('model_links')]
        
        if urls_with_models:
            print(f"\n   🎯 BEST URLS FOR MANUAL TESTING:")
            for i, url_result in enumerate(urls_with_models[:3], 1):
                print(f"   {i}. {url_result['url']}")
                print(f"      📦 {len(url_result['model_links'])} potential links")
        
        return {
            'status': 'success',
            'summary': summary,
            'best_urls': [r['url'] for r in urls_with_models[:3]]
        }
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return {'status': 'error', 'error': str(e)}

def run_integrated_test(manufacturer, part_number):
    """Run integrated test combining all methods"""
    print("🚀 INTEGRATED 3D MODEL FINDER TEST")
    print("=" * 60)
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test existing finders
    existing_results = test_existing_3d_finders(manufacturer, part_number)
    
    # Test manufacturer website discovery
    discovery_results = test_manufacturer_website_discovery(manufacturer, part_number)
    
    # Combined analysis
    print("\n📊 INTEGRATED ANALYSIS")
    print("=" * 60)
    
    # Count successes from existing finders
    successful_finders = [name for name, result in existing_results.items() 
                         if result.get('status') == 'success']
    
    print(f"✅ Working 3D finders: {len(successful_finders)}/3")
    for finder in successful_finders:
        filename = existing_results[finder]['filename']
        print(f"   • {finder}: {filename}")
    
    # Show discovery results
    if discovery_results.get('status') == 'success':
        summary = discovery_results['summary']
        if summary['urls_with_potential_models'] > 0:
            print(f"\n🌐 Manufacturer website analysis:")
            print(f"   • Found {summary['urls_with_potential_models']} promising URLs")
            print(f"   • Total potential 3D links: {summary['total_potential_3d_links']}")
            
            if discovery_results.get('best_urls'):
                print(f"\n🎯 Recommended URLs for manual checking:")
                for i, url in enumerate(discovery_results['best_urls'], 1):
                    print(f"   {i}. {url}")
        else:
            print(f"\n🌐 Manufacturer website: No obvious 3D model URLs found")
    
    # Check existing downloads
    print(f"\n📁 EXISTING DOWNLOADS:")
    if os.path.exists('3d'):
        existing_files = [f for f in os.listdir('3d') if f.endswith('.step')]
        part_files = [f for f in existing_files 
                     if part_number.lower() in f.lower() or 
                        manufacturer.lower().replace(' ', '').replace('_', '') in f.lower()]
        
        if part_files:
            print(f"   Found {len(part_files)} existing files for this part:")
            for f in part_files:
                print(f"   ✅ {f}")
        else:
            print(f"   No existing files found for this part")
            if existing_files:
                print(f"   (Total files in 3d folder: {len(existing_files)})")
    else:
        print(f"   3d folder not found")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if len(successful_finders) >= 2:
        print(f"   ✅ You have {len(successful_finders)} working 3D finders - excellent coverage!")
    elif len(successful_finders) == 1:
        print(f"   ⚠️ Only 1 working 3D finder - consider testing others")
    else:
        print(f"   ❌ No working 3D finders - check your setup")
    
    if discovery_results.get('status') == 'success':
        summary = discovery_results['summary']
        if summary['urls_with_potential_models'] > 0:
            print(f"   🌐 Manufacturer website has potential - consider adding direct scraping")
        else:
            print(f"   🌐 Manufacturer website may not have direct 3D downloads")
    
    return {
        'existing_finders': existing_results,
        'discovery_results': discovery_results,
        'successful_finders': successful_finders,
        'recommendations': []
    }

def main():
    """Main function"""
    print("🎯 INTEGRATED 3D MODEL FINDER TESTER")
    print("Tests your existing finders + new manufacturer discovery")
    print("=" * 60)
    
    # Default test case
    manufacturer = "Texas Instruments"
    part_number = "LM358N"
    
    print("Default test case:")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print()
    
    choice = input("Use default test case? (y/n): ").strip().lower()
    
    if choice != 'y':
        manufacturer = input("Enter manufacturer name: ").strip()
        part_number = input("Enter part number: ").strip()
        
        if not manufacturer or not part_number:
            print("❌ Both manufacturer and part number are required")
            return
    
    print()
    
    # Run the integrated test
    try:
        results = run_integrated_test(manufacturer, part_number)
        
        print(f"\n🎯 TEST COMPLETE!")
        print(f"Check the output above for detailed results and recommendations.")
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
