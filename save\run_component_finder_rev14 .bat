@echo off
echo 🚀 Starting Component Finder GUI...
echo 📁 Working directory: %CD%
echo.

REM Try different Python commands
python component_finder.py
if %errorlevel% neq 0 (
    echo Python command failed, trying 'py'...
    py component_finder.py
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ Failed to start Component Finder GUI
    echo.
    echo 🔧 Troubleshooting:
    echo    1. Make sure Python is installed
    echo    2. Make sure you're in the correct directory
    echo    3. Check that component_finder.py exists
    echo.
    pause
)
