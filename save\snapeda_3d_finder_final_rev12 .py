#!/usr/bin/env python3
"""
SnapEDA 3D Model Finder - Complete automation for downloading 3D models
Based on working snapeda_complete_flow.py
"""

import os
import sys
import time
import csv
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

def log_to_csv(manufacturer, part_number, source, step_filename):
    """Log the download information to CSV file - prevents duplicates"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")

    # Check for duplicates by reading existing entries
    if os.path.exists(csv_filename):
        try:
            with open(csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if (row['MANUFACTURER'].lower() == manufacturer.lower() and
                        row['PART_NUMBER'].lower() == part_number.lower() and
                        row['SOURCE'].lower() == source.lower()):
                        print(f"ℹ️ Entry already exists in CSV: {manufacturer}, {part_number}, {source}")
                        return
        except Exception as e:
            print(f"⚠️ Could not read CSV for duplicate check: {e}")

    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)

    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header if file is new
            if not file_exists:
                writer.writeheader()
                print(f"✅ Created new CSV file: {csv_filename}")

            # Write the data
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': source,
                'STEP_FILE_NAME': step_filename
            })

            print(f"✅ Logged to CSV: {manufacturer}, {part_number}, {source}, {step_filename}")

    except Exception as e:
        print(f"⚠️ Could not write to CSV: {e}")

class SnapEDA3DFinder:
    def __init__(self):
        self.driver = None
    
    def setup_driver(self, silent=False):
        """Setup Chrome driver with download preferences"""
        chrome_options = Options()

        # Configure for silent mode
        if silent:
            chrome_options.add_argument('--headless')  # Run in background
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
        else:
            chrome_options.add_argument("--start-maximized")

        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download preferences - same as SamacSys
        download_dir = os.path.abspath('3d')
        os.makedirs(download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        return webdriver.Chrome(options=chrome_options)
    
    def login_to_snapeda(self, driver):
        """Login to SnapEDA using hardcoded credentials - part number entered AFTER login"""
        print("🔐 Logging into SnapEDA...")

        try:
            # Go to SnapEDA homepage
            driver.get("https://www.snapeda.com/")
            time.sleep(8)
            print(f"✅ Loaded homepage: {driver.current_url}")

            # Look for login link on the right side of the page first
            print("🔸 Looking for login link on the right side...")
            login_clicked = False

            # Try multiple selectors for login link - avoid LinkedIn OAuth
            login_selectors = [
                "//a[contains(@href, '/account/login') and not(contains(@href, 'linkedin'))]",  # Direct SnapEDA login
                "//a[contains(text(), 'Login') and not(contains(text(), 'Linkedin'))]",
                "//a[contains(text(), 'Log In') and not(contains(text(), 'Linkedin'))]",
                "//a[contains(text(), 'Sign In') and not(contains(text(), 'Linkedin'))]"
            ]

            for selector in login_selectors:
                try:
                    login_elements = driver.find_elements(By.XPATH, selector)
                    for element in login_elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''

                            # Make sure it's not LinkedIn OAuth
                            if 'linkedin' not in href.lower() and 'snapeda.com' in href:
                                print(f"✅ Found direct SnapEDA login link: '{text}' href='{href}'")
                                element.click()
                                time.sleep(5)
                                login_clicked = True
                                break
                    if login_clicked:
                        break
                except Exception as e:
                    continue

            # Fallback: go directly to login page if no link found
            if not login_clicked:
                print("🔸 No login link found, going directly to login page...")
                driver.get("https://www.snapeda.com/account/login/")
                time.sleep(5)

            print(f"✅ On login page: {driver.current_url}")

            # Use the correct SnapEDA credentials
            email = "<EMAIL>"
            password = "Lennyai123#"
            print(f"✅ Using SnapEDA credentials: {email}")
            
            # Wait for page to fully load
            time.sleep(5)

            # Find email field - use the correct SnapEDA selector first
            email_field = None
            email_selectors = ["#id_username", "input[type='email']", "input[name='email']", "input[id*='email']"]

            print("🔸 Looking for email field...")
            for selector in email_selectors:
                try:
                    email_field = driver.find_element(By.CSS_SELECTOR, selector)
                    if email_field.is_displayed() and email_field.is_enabled():
                        print(f"✅ Found email field with selector: {selector}")
                        break
                except:
                    continue

            if not email_field:
                print("❌ Could not find email field")
                # Debug: show all input fields
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                print(f"🔍 DEBUG: Found {len(all_inputs)} input fields:")
                for i, inp in enumerate(all_inputs):
                    try:
                        print(f"  {i+1}. type='{inp.get_attribute('type')}' name='{inp.get_attribute('name')}' id='{inp.get_attribute('id')}'")
                    except:
                        pass
                return False

            # Fill email with JavaScript to avoid interactability issues
            driver.execute_script("arguments[0].value = arguments[1];", email_field, email)
            print("✅ Filled email field")
            
            # Find password field
            password_field = None
            password_selectors = ["input[type='password']", "input[name='password']", "input[id*='password']"]
            
            for selector in password_selectors:
                try:
                    password_field = driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        break
                except:
                    continue
            
            if not password_field:
                print("❌ Could not find password field")
                return False
            
            # Fill password
            password_field.clear()
            password_field.send_keys(password)
            print("✅ Filled password field")

            # Submit login form (email + password only) - SECOND LOGIN BUTTON
            print("🔸 CRITICAL: Must click SECOND 'Log In' button to submit form...")

            # Wait a moment for page to be ready
            time.sleep(2)

            # FORCE FIND AND CLICK THE SUBMIT BUTTON
            login_clicked = False

            # Method 1: Try all buttons and click any that say "Log In"
            print("🔸 Method 1: Checking all buttons for 'Log In' text...")
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            for i, btn in enumerate(all_buttons):
                try:
                    if btn.is_displayed() and btn.is_enabled():
                        text = btn.text.strip().lower()
                        if 'log in' in text or 'login' in text:
                            print(f"✅ Found button {i+1} with text '{btn.text}' - CLICKING NOW!")
                            btn.click()
                            login_clicked = True
                            break
                except Exception as e:
                    continue

            # Method 2: Try all submit inputs
            if not login_clicked:
                print("🔸 Method 2: Checking all submit inputs...")
                all_submits = driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
                for i, inp in enumerate(all_submits):
                    try:
                        if inp.is_displayed() and inp.is_enabled():
                            value = inp.get_attribute('value') or ''
                            if 'log in' in value.lower() or 'login' in value.lower():
                                print(f"✅ Found submit {i+1} with value '{value}' - CLICKING NOW!")
                                inp.click()
                                login_clicked = True
                                break
                    except Exception as e:
                        continue

            # Method 3: Try any submit button/input regardless of text
            if not login_clicked:
                print("🔸 Method 3: Clicking ANY submit button...")
                try:
                    submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                    if submit_btn.is_displayed() and submit_btn.is_enabled():
                        print("✅ Found submit button - CLICKING NOW!")
                        submit_btn.click()
                        login_clicked = True
                except:
                    try:
                        submit_inp = driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
                        if submit_inp.is_displayed() and submit_inp.is_enabled():
                            print("✅ Found submit input - CLICKING NOW!")
                            submit_inp.click()
                            login_clicked = True
                    except:
                        pass

            # Check if any method worked
            if login_clicked:
                print("✅ SECOND login button was clicked successfully!")
            else:
                print("❌ FAILED to click SECOND login button - trying final methods...")

            if not login_clicked:
                print("❌ Could not find or click SECOND login button")
                print("🔍 Trying alternative methods...")

                # Try submitting the form directly
                try:
                    form = driver.find_element(By.TAG_NAME, "form")
                    form.submit()
                    print("✅ Submitted form directly")
                    login_clicked = True
                except Exception as form_error:
                    print(f"⚠️ Form submit failed: {form_error}")

                # Try pressing Enter on password field
                if not login_clicked:
                    try:
                        from selenium.webdriver.common.keys import Keys
                        password_field.send_keys(Keys.RETURN)
                        print("✅ Pressed Enter on password field")
                        login_clicked = True
                    except Exception as enter_error:
                        print(f"⚠️ Enter key failed: {enter_error}")

                if not login_clicked:
                    print("❌ All login methods failed")
                    return False
            
            time.sleep(10)
            
            # Check if login was successful
            if "login" not in driver.current_url.lower():
                print("✅ Login successful")
                return True
            else:
                print("❌ Login failed")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def search_and_download(self, manufacturer, part_number, silent=False):
        """Search for part and download 3D model"""
        if not silent:
            print(f"\n🎯 SNAPEDA 3D MODEL FINDER")
            print("=" * 50)
            print(f"🔍 Looking for: {manufacturer} {part_number}")
        
        driver = self.setup_driver(silent=silent)
        if not driver:
            return None
        
        try:
            # Use the EXACT working flow from snapeda_final_test.py
            print(f"\n🔸 Using proven working SnapEDA flow...")
            driver.get("https://www.snapeda.com/")
            time.sleep(5)

            login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
            login_link.click()
            time.sleep(5)

            search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
            search_field.send_keys(part_number)
            print(f"✅ Entered part number: {part_number}")

            email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
            email_field.send_keys("<EMAIL>")

            password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_field.send_keys("Lennyai123#")

            submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            submit_btn.click()
            time.sleep(10)
            print(f"✅ Login and search completed")

            # Navigate to part page
            part_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer}/view-part/"
            driver.get(part_url)
            time.sleep(10)
            print(f"✅ Navigated to part page: {part_url}")

            # Click 3D Model tab
            three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
            three_d_tab.click()
            time.sleep(10)
            print(f"✅ Clicked 3D Model tab")

            # Find and click download button (using working selectors)
            print(f"\n🎯 Finding download button...")
            selectors = [
                "//a[contains(@class, '3D-model-download')]",
                "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
                "//a[text()='Download 3D Model' and contains(@class, 'orange')]"
            ]

            download_button = None
            for selector in selectors:
                try:
                    button = driver.find_element(By.XPATH, selector)
                    if button.is_displayed():
                        download_button = button
                        print(f"✅ Found button with selector: {selector}")
                        break
                except:
                    continue

            if download_button:
                print(f"✅ Download button found!")
                print(f"   Text: '{download_button.text.strip()}'")

                # Click the download button
                print(f"\n🔸 CLICKING DOWNLOAD BUTTON...")
                download_button.click()
                print(f"✅ CLICKED!")

                # Monitor for downloads
                import os
                download_dir = os.path.abspath('3d')
                before_files = set(os.listdir(download_dir)) if os.path.exists(download_dir) else set()

                print(f"\n📁 Monitoring downloads for 30 seconds...")
                for i in range(30):
                    time.sleep(1)
                    if os.path.exists(download_dir):
                        current_files = set(os.listdir(download_dir))
                        new_files = current_files - before_files

                        if new_files:
                            print(f"🎉 NEW FILE DOWNLOADED after {i+1} seconds!")
                            for f in sorted(new_files):
                                if f.endswith('.step') or f.endswith('.stp'):
                                    file_path = os.path.join(download_dir, f)
                                    final_name = f"SnapEDA-{manufacturer}-{part_number}.step"
                                    final_path = os.path.join(download_dir, final_name)

                                    # Rename file
                                    if os.path.exists(file_path):
                                        os.rename(file_path, final_path)
                                        print(f"✅ Downloaded and renamed: {final_name}")
                                        return final_path
                            break

                    if i % 5 == 0:
                        print(f"   ⏳ {i+1}/30 seconds...")

                print(f"❌ No STEP file downloaded")
                return None
            else:
                print(f"❌ Download button not found")
                return None

            print(f"✅ On part page: {driver.current_url}")
            
            # Look for 3D link/button
            print(f"\n🔸 STEP 3: Looking for 3D Model link...")

            # Debug: Show all clickable elements with "3D" in text and their parents
            print(f"🔍 DEBUG: All elements containing '3D':")
            all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D')]")
            for i, elem in enumerate(all_elements):
                try:
                    if elem.is_displayed():
                        text = elem.text.strip()
                        tag = elem.tag_name
                        href = elem.get_attribute('href') or 'no-href'
                        clickable = elem.is_enabled() and (tag in ['a', 'button'] or href != 'no-href')
                        parent_tag = elem.find_element(By.XPATH, "..").tag_name if elem else 'no-parent'
                        print(f"  {i+1}. <{tag}> '{text}' href='{href[:30]}' clickable={clickable} parent=<{parent_tag}>")
                except:
                    pass

            # Try multiple selectors for clickable 3D Model tab/link
            three_d_selectors = [
                "//a[text()='3D Model']",
                "//li[text()='3D Model']//a",
                "//li[text()='3D Model']",
                "//span[text()='3D Model']//parent::a",
                "//span[text()='3D Model']",
                "//button[text()='3D Model']",
                "//a[contains(@href, '3d-model')]",
                "//a[contains(@href, '3d')]",
                "//div[contains(@class, '3d')]//a",
                "//li[contains(@class, '3d')]//a",
                "a[href*='#download-3d-modal']"
            ]

            download_3d_link = None
            for selector in three_d_selectors:
                try:
                    if selector.startswith("//"):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            # Look for EXACT "3D Model" text (not "Symbol, Footprint, 3D Model")
                            if text == "3D Model":
                                print(f"✅ Found exact 3D Model element: '{text}'")
                                # If it's an <li>, look for clickable link inside it
                                if element.tag_name == 'li':
                                    try:
                                        clickable_link = element.find_element(By.TAG_NAME, "a")
                                        print(f"   🔗 Found clickable link inside <li>")
                                        download_3d_link = clickable_link
                                    except:
                                        # If no <a> inside, try clicking the <li> itself
                                        download_3d_link = element
                                else:
                                    download_3d_link = element
                                break
                            elif "3D Model" in text and "Symbol" not in text and not download_3d_link:
                                print(f"✅ Found 3D Model element: '{text}'")
                                download_3d_link = element

                    if download_3d_link:
                        break
                except:
                    continue

            if not download_3d_link:
                print(f"❌ Could not find 3D link")
                return None
            
            if download_3d_link:
                print(f"🔸 Clicking 3D Model tab...")
                driver.execute_script("arguments[0].click();", download_3d_link)

                # Wait for popup/modal to appear and load
                print(f"🔸 Waiting for 3D Model popup/modal to load...")
                time.sleep(10)

                # Look for modal/popup container
                modal_selectors = [
                    ".modal",
                    "#modal",
                    ".popup",
                    ".dialog",
                    "[role='dialog']",
                    ".overlay"
                ]

                modal_found = False
                for selector in modal_selectors:
                    try:
                        modal = driver.find_element(By.CSS_SELECTOR, selector)
                        if modal.is_displayed():
                            print(f"✅ Found modal/popup with selector: {selector}")
                            modal_found = True
                            break
                    except:
                        continue

                if not modal_found:
                    print(f"⚠️ No modal detected, looking in main page content...")

                # Look for download button
                print(f"🔸 Looking for Download button in 3D Model section...")

                # Debug: Show all clickable elements (buttons and links)
                print(f"🔍 DEBUG: All clickable elements (buttons and links):")
                clickable_elements = driver.find_elements(By.XPATH, "//a | //button | //input[@type='submit']")
                for i, elem in enumerate(clickable_elements):
                    try:
                        if elem.is_displayed() and elem.is_enabled():
                            text = elem.text.strip()
                            tag = elem.tag_name
                            href = elem.get_attribute('href') or 'no-href'
                            onclick = elem.get_attribute('onclick') or 'no-onclick'
                            class_attr = elem.get_attribute('class') or 'no-class'
                            if text or 'download' in href.lower() or 'download' in onclick.lower() or 'download' in class_attr.lower():
                                print(f"  {i+1}. <{tag}> '{text}' href='{href[:30]}' class='{class_attr[:30]}'")
                    except:
                        pass

                download_selectors = [
                    "//a[contains(text(), 'Download')]",
                    "//button[contains(text(), 'Download')]",
                    "//input[@type='submit' and contains(@value, 'Download')]",
                    "//a[contains(text(), 'download')]",
                    "//button[contains(text(), 'download')]",
                    ".download-btn",
                    "#download-btn"
                ]
                
                download_clicked = False

                # Use the correct selector for the visible download button
                download_button_selectors = [
                    "//a[contains(@class, '3D-model-download')]",
                    "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
                    "//a[text()='Download 3D Model' and contains(@class, 'orange')]"
                ]

                for selector in download_button_selectors:
                    try:
                        download_3d_button = driver.find_element(By.XPATH, selector)
                        if download_3d_button.is_displayed():
                            text = download_3d_button.text.strip()
                            # Validate this is actually the Download 3D Model button
                            if "Download 3D Model" in text:
                                print(f"✅ Found correct download button: '{text}'")
                                download_3d_button.click()
                                time.sleep(5)
                                download_clicked = True
                                print(f"✅ Clicked Download 3D Model button!")
                                break
                            else:
                                print(f"⚠️ Found button but wrong text: '{text}' - skipping")
                    except Exception as e:
                        print(f"⚠️ Selector '{selector}' failed: {e}")
                        continue

                # If that didn't work, try JavaScript click on the specific button we see in debug
                if not download_clicked:
                    try:
                        # Use JavaScript to find and click the Download 3D Model button
                        js_script = """
                        var links = document.querySelectorAll('a');
                        for (var i = 0; i < links.length; i++) {
                            if (links[i].textContent.trim() === 'Download 3D Model') {
                                links[i].click();
                                return 'SUCCESS: Clicked Download 3D Model button';
                            }
                        }
                        return 'FAILED: Download 3D Model button not found';
                        """
                        result = driver.execute_script(js_script)
                        print(f"🔸 JavaScript result: {result}")

                        if "SUCCESS" in result:
                            download_clicked = True
                            print(f"✅ JavaScript click successful!")

                    except Exception as e:
                        print(f"⚠️ JavaScript click failed: {e}")
                
                if download_clicked:
                    print(f"✅ Download initiated!")

                    # Check for redirect to external site (SnapEDA often redirects to 3rd party sites)
                    print(f"🔸 Checking for redirects...")
                    initial_handles = driver.window_handles
                    initial_url = driver.current_url

                    # Wait for potential redirect or new window
                    time.sleep(5)

                    current_handles = driver.window_handles
                    current_url = driver.current_url

                    # Check for new window/tab (external site redirect)
                    if len(current_handles) > len(initial_handles):
                        print(f"🎯 New window/tab detected - switching to external site...")
                        driver.switch_to.window(current_handles[-1])
                        external_url = driver.current_url
                        print(f"   External site URL: {external_url}")

                        # Wait longer for external site download
                        time.sleep(10)

                    # Check for URL change in same window
                    elif current_url != initial_url:
                        print(f"🎯 URL changed - redirect detected: {current_url}")
                        time.sleep(5)

                    # Monitor for download completion like UltraLibrarian finder
                    print(f"🔸 Monitoring for downloads...")
                    downloads_dir = os.path.abspath('3d')
                    initial_files = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()

                    # Wait up to 30 seconds for download
                    for i in range(30):
                        time.sleep(1)
                        if os.path.exists(downloads_dir):
                            current_files = set(os.listdir(downloads_dir))
                            new_files = current_files - initial_files

                            if new_files:
                                print(f"🎉 New files detected after {i+1} seconds: {list(new_files)}")
                                # Process the downloaded file
                                return self.process_downloaded_file(manufacturer, part_number, new_files)

                        if i % 5 == 0 and i > 0:
                            print(f"   ⏳ Still waiting... {i}/30 seconds")

                    print(f"❌ No download detected after 30 seconds")
                    return None
                else:
                    print(f"❌ No download button could be clicked")
                    return None
            else:
                print(f"❌ No 3D download link found")
                return None
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
        
        finally:
            driver.quit()
    
    def process_downloaded_file(self, manufacturer, part_number, new_files=None):
        """Process the downloaded STEP file"""
        downloads_dir = os.path.abspath('3d')

        # Create target filename
        new_name = f"SnapEDA-{manufacturer}-{part_number}.step"
        target_file = os.path.join(downloads_dir, new_name)

        # If we have new_files from monitoring, check those first
        if new_files:
            for filename in new_files:
                if filename.lower().endswith(('.step', '.stp')):
                    source_file = os.path.join(downloads_dir, filename)
                    if os.path.exists(source_file):
                        # Remove existing target file if it exists
                        if os.path.exists(target_file):
                            os.remove(target_file)

                        shutil.move(source_file, target_file)
                        print(f"✅ File moved from {filename} to: {new_name}")

                        # Log to CSV
                        log_to_csv(manufacturer, part_number, "SNAPEDA", new_name)

                        return new_name

        # Fallback: Try different possible source filenames
        clean_part = part_number.replace('/', '_')
        possible_names = [
            f"{clean_part}.STEP",
            f"{clean_part}.step",
            f"{part_number}.STEP",
            f"{part_number}.step"
        ]

        moved = False
        for name in possible_names:
            source_file = os.path.join(downloads_dir, name)
            if os.path.exists(source_file):
                # Remove existing target file if it exists
                if os.path.exists(target_file):
                    os.remove(target_file)

                shutil.move(source_file, target_file)
                print(f"✅ File moved from {name} to: {new_name}")

                # Log to CSV
                log_to_csv(manufacturer, part_number, "SNAPEDA", new_name)

                moved = True
                break
        
        if not moved:
            print(f"❌ Could not find downloaded file")
            return None
        
        return new_name

def find_3d_model(manufacturer, part_number, silent=False):
    """Main function to find and download 3D model"""
    finder = SnapEDA3DFinder()
    result = finder.search_and_download(manufacturer, part_number, silent=silent)
    
    if result:
        print(f"🎉 SUCCESS: {result} downloaded!")
        return result
    else:
        print(f"❌ FAILED: No 3D model obtained for {part_number}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python snapeda_3d_finder_final.py <manufacturer> <part_number>")
        print("Example: python snapeda_3d_finder_final.py \"Texas Instruments\" LM358N")
        sys.exit(1)
    
    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    
    find_3d_model(manufacturer, part_number)
