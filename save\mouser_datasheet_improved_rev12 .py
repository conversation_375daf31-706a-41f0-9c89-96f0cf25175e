#!/usr/bin/env python3
"""
IMPROVED MOUSER DATASHEET FINDER
===============================
Enhanced version with proper error handling, verification, and detailed output.

Features:
- Detailed step-by-step output
- Error handling with immediate stop
- File verification (size, format, content)
- Success/failure reporting
- Proper logging

Usage:
    python mouser_datasheet_improved.py "Texas Instruments" "LM358N"
"""

import requests
import json
import os
import sys
import time
from datetime import datetime
import argparse

class MouserDatasheetFinder:
    def __init__(self):
        self.session = requests.Session()
        self.credentials = None
        self.datasheets_dir = "datasheets"
        self.base_url = "https://api.mouser.com/api/v1"
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")
    
    def load_credentials(self):
        """Load and verify Mouser API credentials"""
        self.log("Loading Mouser API credentials...")
        
        try:
            if not os.path.exists('mouser_api_credentials.json'):
                self.log("Credentials file 'mouser_api_credentials.json' not found", "ERROR")
                return False

            with open('mouser_api_credentials.json', 'r') as f:
                self.credentials = json.load(f)
            
            # Verify required fields
            required_fields = ['api_key']
            for field in required_fields:
                if field not in self.credentials or not self.credentials[field]:
                    self.log(f"Missing or empty field '{field}' in Mouser credentials", "ERROR")
                    return False

            # Check if API key is still the placeholder
            if self.credentials['api_key'] == 'YOUR_MOUSER_API_KEY_HERE':
                self.log("Please update mouser_api_credentials.json with your actual API key", "ERROR")
                return False
            
            self.log("Mouser credentials loaded successfully", "SUCCESS")
            return True
            
        except json.JSONDecodeError as e:
            self.log(f"Invalid JSON in credentials file: {e}", "ERROR")
            return False
        except Exception as e:
            self.log(f"Error loading credentials: {e}", "ERROR")
            return False
    
    def setup_directories(self):
        """Create necessary directories"""
        self.log("Setting up directories...")
        
        try:
            os.makedirs(self.datasheets_dir, exist_ok=True)
            self.log(f"Directory '{self.datasheets_dir}' ready", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating directory: {e}", "ERROR")
            return False
    
    def search_part(self, manufacturer, part_number):
        """Search for a part using Mouser API"""
        self.log(f"Searching for part: {manufacturer} {part_number}")
        
        try:
            # Construct search URL
            search_url = f"{self.base_url}/search/keyword"
            
            # Search payload
            payload = {
                "SearchByKeywordRequest": {
                    "keyword": part_number,
                    "records": 10,
                    "startingRecord": 0
                }
            }
            
            # Headers
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # Add API key to URL
            search_url_with_key = f"{search_url}?apiKey={self.credentials['api_key']}"
            
            self.log(f"Making API request to Mouser...")
            response = self.session.post(search_url_with_key, headers=headers, json=payload, timeout=30)
            
            self.log(f"API Response Status: {response.status_code}")
            
            if response.status_code == 401:
                self.log("API authentication failed - check API key", "ERROR")
                return None
            elif response.status_code == 429:
                self.log("API rate limit exceeded", "ERROR")
                return None
            elif response.status_code != 200:
                self.log(f"API request failed with status {response.status_code}: {response.text}", "ERROR")
                return None
            
            data = response.json()

            # Debug: Print response structure
            self.log(f"API Response keys: {list(data.keys()) if data else 'None'}")

            # Check for errors first
            if 'Errors' in data and data['Errors']:
                self.log(f"API returned errors: {data['Errors']}", "ERROR")
                return None

            if 'SearchResults' not in data:
                self.log("No SearchResults in API response", "WARNING")
                self.log(f"Response data: {data}")
                return None

            search_results = data['SearchResults']
            if 'Parts' not in search_results:
                self.log("No Parts in SearchResults", "WARNING")
                self.log(f"SearchResults keys: {list(search_results.keys()) if search_results else 'None'}")
                return None

            parts = search_results['Parts']
            
            if not parts:
                self.log("No parts found in search results", "WARNING")
                return None
            
            self.log(f"Found {len(parts)} parts", "SUCCESS")
            
            # Find best match by manufacturer
            best_match = None
            for part in parts:
                part_manufacturer = part.get('Manufacturer', '') or ''  # Handle None values
                if not isinstance(part_manufacturer, str):
                    part_manufacturer = str(part_manufacturer) if part_manufacturer else ''
                part_manufacturer = part_manufacturer.lower()

                # Debug output
                self.log(f"Checking part manufacturer: '{part_manufacturer}' vs '{manufacturer.lower()}'")

                if manufacturer.lower() in part_manufacturer or part_manufacturer in manufacturer.lower():
                    best_match = part
                    break
            
            if not best_match:
                best_match = parts[0]  # Use first result if no manufacturer match
                self.log("No manufacturer match found, using first result", "WARNING")
            else:
                self.log(f"Found manufacturer match: {best_match.get('Manufacturer', 'Unknown')}", "SUCCESS")
            
            return best_match
            
        except requests.exceptions.Timeout:
            self.log("API request timed out", "ERROR")
            return None
        except requests.exceptions.RequestException as e:
            self.log(f"API request error: {e}", "ERROR")
            return None
        except json.JSONDecodeError as e:
            self.log(f"Invalid JSON response: {e}", "ERROR")
            return None
        except Exception as e:
            self.log(f"Unexpected error during search: {e}", "ERROR")
            return None
    
    def download_datasheet(self, part, manufacturer, part_number):
        """Download datasheet from part data"""
        self.log("Looking for datasheet URL...")
        
        try:
            # Look for datasheet URL in part data
            datasheet_url = None
            
            if 'DataSheetUrl' in part and part['DataSheetUrl']:
                datasheet_url = part['DataSheetUrl']
            elif 'ProductDetailUrl' in part and part['ProductDetailUrl']:
                # Sometimes datasheet is linked from product detail page
                self.log("No direct datasheet URL, found product detail URL", "WARNING")
                datasheet_url = part['ProductDetailUrl']
            
            if not datasheet_url:
                self.log("No datasheet URL found in part data", "WARNING")
                return False
            
            self.log(f"Found datasheet URL: {datasheet_url}")
            
            # Generate filename
            clean_manufacturer = manufacturer.replace(' ', '_').replace('/', '_')
            clean_part = part_number.replace('/', '_').replace(' ', '_')
            filename = f"{clean_manufacturer}_{clean_part}_datasheet.pdf"
            filepath = os.path.join(self.datasheets_dir, filename)
            
            self.log(f"Downloading to: {filepath}")
            
            # Download the file
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = self.session.get(datasheet_url, headers=headers, timeout=60, stream=True)
            
            if response.status_code != 200:
                self.log(f"Download failed with status {response.status_code}", "ERROR")
                return False
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not datasheet_url.lower().endswith('.pdf'):
                self.log(f"Warning: Content type is '{content_type}', may not be PDF", "WARNING")
            
            # Download with progress
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r  📥 Progress: {progress:.1f}% ({downloaded_size:,} / {total_size:,} bytes)", end='')
            
            print()  # New line after progress
            
            # Verify the downloaded file
            return self.verify_datasheet(filepath, filename)
            
        except Exception as e:
            self.log(f"Error downloading datasheet: {e}", "ERROR")
            return False
    
    def verify_datasheet(self, filepath, filename):
        """Verify that the downloaded file is valid"""
        self.log("Verifying downloaded file...")
        
        try:
            # Check if file exists
            if not os.path.exists(filepath):
                self.log("Downloaded file does not exist", "ERROR")
                return False
            
            # Check file size
            file_size = os.path.getsize(filepath)
            if file_size == 0:
                self.log("Downloaded file is empty", "ERROR")
                return False
            elif file_size < 1024:  # Less than 1KB
                self.log(f"Downloaded file is very small ({file_size} bytes) - may be invalid", "WARNING")
            
            # Check if it's actually a PDF
            with open(filepath, 'rb') as f:
                header = f.read(4)
                if header != b'%PDF':
                    self.log("Downloaded file is not a valid PDF", "ERROR")
                    return False
            
            self.log(f"File verified successfully: {filename} ({file_size:,} bytes)", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error verifying file: {e}", "ERROR")
            return False
    
    def find_datasheet(self, manufacturer, part_number):
        """Main method to find and download datasheet"""
        self.log(f"Starting datasheet search for: {manufacturer} {part_number}")
        self.log("=" * 60)
        
        # Step 1: Load credentials
        if not self.load_credentials():
            self.log("Failed to load credentials - stopping", "ERROR")
            return False
        
        # Step 2: Setup directories
        if not self.setup_directories():
            self.log("Failed to setup directories - stopping", "ERROR")
            return False
        
        # Step 3: Search for part
        part = self.search_part(manufacturer, part_number)
        if not part:
            self.log("Failed to find part - stopping", "ERROR")
            return False
        
        # Step 4: Download datasheet
        success = self.download_datasheet(part, manufacturer, part_number)
        if not success:
            self.log("Failed to download datasheet - stopping", "ERROR")
            return False
        
        self.log("Datasheet download completed successfully!", "SUCCESS")
        return True

def main():
    parser = argparse.ArgumentParser(description='Download datasheet from Mouser')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    finder = MouserDatasheetFinder()
    success = finder.find_datasheet(args.manufacturer, args.part_number)
    
    if success:
        print(f"\n🎉 SUCCESS: Datasheet downloaded for {args.manufacturer} {args.part_number}")
        sys.exit(0)
    else:
        print(f"\n💥 FAILED: Could not download datasheet for {args.manufacturer} {args.part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
