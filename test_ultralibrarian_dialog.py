#!/usr/bin/env python3
"""
Test the fixed UltraLibrarian dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_ultralibrarian_dialog():
    """Test the UltraLibrarian alternative dialog with proper styling"""
    
    # Mock alternatives data
    alternatives = [
        ("link1", "GCM21BC71E106KE36L", "href1"),
        ("link2", "GCM21BC71E106KE37L", "href2"), 
        ("link3", "GCM21BC71E106KE38L", "href3")
    ]
    original_part = "GCM21BC71E475KE36L"
    
    class AlternativeSelector:
        def __init__(self):
            self.selected_index = None
            self.cancelled = False
            self.checkbox_vars = []

        def show_dialog(self):
            root = tk.Tk()
            root.title("UltraLibrarian - Select Alternative Part")
            root.geometry("500x400")
            root.resizable(False, False)
            
            # Visual Studio Forms colors
            vs_colors = {
                'form_bg': '#f0f0f0',
                'control_bg': '#ffffff',
                'button_bg': '#e1e1e1',
                'text_color': '#000000',
                'accent_blue': '#007acc'
            }
            root.configure(bg=vs_colors['form_bg'])

            # Center the window
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - (500 // 2)
            y = (root.winfo_screenheight() // 2) - (400 // 2)
            root.geometry(f"500x400+{x}+{y}")

            # Main frame with VS styling
            main_frame = tk.Frame(root, bg=vs_colors['form_bg'], padx=20, pady=20)
            main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

            # Title with VS styling
            title_label = tk.Label(main_frame, text="Part Not Available",
                                 font=("Microsoft Sans Serif", 14, "bold"),
                                 bg=vs_colors['form_bg'],
                                 fg=vs_colors['text_color'])
            title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

            # Original part info with VS styling
            orig_label = tk.Label(main_frame, text=f"Original part '{original_part}' has no 3D model available.",
                                bg=vs_colors['form_bg'],
                                fg=vs_colors['text_color'],
                                font=("Microsoft Sans Serif", 9))
            orig_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

            # Alternatives label with VS styling
            alt_label = tk.Label(main_frame, text="Available alternatives:",
                               font=("Microsoft Sans Serif", 10, "bold"),
                               bg=vs_colors['form_bg'],
                               fg=vs_colors['text_color'])
            alt_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))

            # Checkboxes for alternatives with VS styling
            checkbox_frame = tk.Frame(main_frame, bg=vs_colors['form_bg'])
            checkbox_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))

            # Create scrollable frame for checkboxes with VS styling
            canvas = tk.Canvas(checkbox_frame, height=150, 
                             bg=vs_colors['control_bg'],
                             highlightthickness=1,
                             highlightbackground=vs_colors['text_color'])
            scrollbar = ttk.Scrollbar(checkbox_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=vs_colors['control_bg'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

            # Create checkboxes for each alternative - using tk.Checkbutton for proper styling
            self.checkbox_vars = []
            for i, (link, text, href) in enumerate(alternatives):
                var = tk.BooleanVar(value=False)  # Explicitly set to False (unchecked)
                checkbox = tk.Checkbutton(scrollable_frame,
                                        text=f"{i+1}. {text}",
                                        variable=var,
                                        bg=vs_colors['control_bg'],
                                        fg=vs_colors['text_color'],
                                        font=("Microsoft Sans Serif", 8),
                                        anchor='w',
                                        selectcolor=vs_colors['control_bg'],  # Background when checked
                                        activebackground=vs_colors['control_bg'],
                                        activeforeground=vs_colors['text_color'])
                checkbox.grid(row=i, column=0, sticky=tk.W, pady=2, padx=5)
                self.checkbox_vars.append(var)

            # Buttons frame with VS styling
            button_frame = tk.Frame(main_frame, bg=vs_colors['form_bg'])
            button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

            def on_select():
                selected_indices = []
                for i, var in enumerate(self.checkbox_vars):
                    if var.get():
                        selected_indices.append(i)

                if selected_indices:
                    self.selected_index = selected_indices
                    print(f"✅ Selected alternatives: {[alternatives[i][1] for i in selected_indices]}")
                    root.destroy()
                else:
                    messagebox.showwarning("No Selection", "Please select at least one alternative part.")

            def on_cancel():
                self.cancelled = True
                print("❌ User cancelled selection")
                root.destroy()

            # VS styled buttons
            select_btn = tk.Button(button_frame, text="Use Selected Alternatives",
                                 command=on_select, 
                                 bg=vs_colors['button_bg'],
                                 fg=vs_colors['text_color'],
                                 font=("Microsoft Sans Serif", 8),
                                 relief='raised',
                                 borderwidth=1,
                                 width=22)
            select_btn.grid(row=0, column=0, padx=(0, 10))

            cancel_btn = tk.Button(button_frame, text="Cancel",
                                 command=on_cancel,
                                 bg=vs_colors['button_bg'],
                                 fg=vs_colors['text_color'],
                                 font=("Microsoft Sans Serif", 8),
                                 relief='raised',
                                 borderwidth=1,
                                 width=15)
            cancel_btn.grid(row=0, column=1)

            # Configure grid weights
            root.columnconfigure(0, weight=1)
            root.rowconfigure(0, weight=1)
            main_frame.columnconfigure(0, weight=1)
            checkbox_frame.columnconfigure(0, weight=1)

            # Show dialog
            root.lift()
            root.attributes('-topmost', True)
            root.after(100, lambda: root.attributes('-topmost', False))
            root.focus_force()
            root.grab_set()
            root.mainloop()

            return self.selected_index, self.cancelled

    print("🧪 Testing UltraLibrarian Alternative Dialog...")
    print("📋 The checkboxes should be EMPTY (unchecked) by default")
    print("🎨 The dialog should have Visual Studio Forms styling")
    print("=" * 60)
    
    selector = AlternativeSelector()
    result = selector.show_dialog()
    
    print(f"🎯 Dialog result: {result}")

if __name__ == "__main__":
    test_ultralibrarian_dialog()
