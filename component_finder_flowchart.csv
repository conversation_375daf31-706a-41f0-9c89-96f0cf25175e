Step,Node_ID,Node_Type,Description,Comment_Text,Next_Step,Alt_Path,Function_Location
1,A,Start,User clicks Search Component,,2,,GUI Button Click
2,B,Comment,Starting search comment,🔍 Searching for datasheet and 3D Models for {manufacturer} {part_number},3,,search_component()
3,C,Process,Try distributors first,,4,,search_component()
4,D,Function,Call distributor search,Call search_distributors_for_part_info,5,,search_distributors_for_part_info()
5,E,Decision,Distributor found info?,,6,28,search_distributors_for_part_info()
6,F,Comment,Digi-Key API search,📡 Searching API Digi-Key website for {manufacturer} {part_number} Datasheet,7,,try_digikey_api()
7,G,Process,Try Digi-Key API,,8,,try_digikey_api()
8,H,Decision,Digi-Key success?,,9,10,try_digikey_api()
9,I,Comment,Found datasheet link,✅ Found Link for {manufacturer} {part_number} DataSheet,11,,try_digikey_api()
10,J,<PERSON>mme<PERSON>,<PERSON> not found,❌ Link for {manufacturer} {part_number} DataSheet Not Found,19,,try_digikey_api()
11,K,Comment,Link to manufacturer website,🔗 Link to manufacturers website - If found Updating Manufacturer to Manufacturer website cross reference in CSV file,12,,search_component()
12,L,Comment,Downloading datasheet,📥 Downloading from {manufacturer} {part_number} Datasheet,13,,search_component()
13,M,Process,Actually download file,,14,,download_file_from_url()
14,N,Comment,Search datasheet for part match,🔍 Searching Datasheet for part number match. If its correct then we look for the package type,15,,search_component()
15,O,Comment,Part number and package status,📋 Part-Number – Found or not found Package Found or Not Found,16,,search_component()
16,P,Process,Save to CSV,,17,,_save_manufacturer_to_csv()
17,Q,Process,Try manufacturer website search,,18,,search_with_known_website()
18,R,Process,Try 3D model search,,33,,search_step_enhanced()
19,S,Decision,Try Mouser?,,20,28,search_distributors_for_part_info()
20,T,Comment,Mouser API search,📡 Searching API Mouser website for {manufacturer} {part_number} Datasheet,21,,try_mouser_api()
21,U,Process,Try Mouser API,,22,,try_mouser_api()
22,V,Decision,Mouser success?,,23,24,try_mouser_api()
23,W,Comment,Found on Mouser,✅ Found Link for {manufacturer} {part_number} DataSheet on Mouser,11,,try_mouser_api()
24,X,Comment,Not found on Mouser,❌ Link for {manufacturer} {part_number} DataSheet Not Found on Mouser,25,,try_mouser_api()
25,Y,Process,No distributor success - try knowledge base,,26,,search_component()
26,Z,Comment,Not in knowledge base,🔍 {MANUFACTURER}.COM not in knowledge base for predefined searches,27,,search_component()
27,AA,Process,Try to find manufacturer website,,29,,find_manufacturer_website()
28,Y2,Process,No distributor info - try knowledge base,,26,,search_component()
29,BB,Process,Search manufacturer website,,30,,search_with_known_website()
30,CC,Decision,Found datasheet on manufacturer site?,,31,32,search_with_known_website()
31,DD,Comment,Found on manufacturer website,✅ Found datasheet on manufacturer website,18,,search_with_known_website()
32,EE,Comment,No datasheet found,❌ No datasheet found - Ask user for help,35,,search_with_known_website()
33,HH,Comment,Search manufacturer for 3D,🔍 Searching {manufacturer} for a 3D model for {part_number},34,,search_step_enhanced()
34,II,Decision,3D found on manufacturer?,,36,37,search_step_enhanced()
35,FF,Comment,All methods failed,🤔 All automatic search methods failed,38,,search_component()
36,JJ,Comment,3D found on manufacturer,✅ 3D Model found on {manufacturer}.com for {part_number},39,,search_step_enhanced()
37,KK,Comment,3D not found on manufacturer,❌ 3D Model not found on {manufacturer}.com for {part_number},39,,search_step_enhanced()
38,GG,Process,Prompt user for help,,END,,ask_for_help_finding_file()
39,LL,Process,Try UltraLibrarian,,40,,search_step_enhanced()
40,MM,Comment,Search UltraLibrarian,🔍 Searching UltraLibrarian for a 3D model for {part_number},41,,ultralibrarian_3d_finder
41,NN,Decision,UltraLibrarian success?,,42,43,ultralibrarian_3d_finder
42,OO,Comment,Found on UltraLibrarian,✅ 3D Model found on UltraLibrarian.com for {part_number},44,,ultralibrarian_3d_finder
43,PP,Comment,Not found on UltraLibrarian,❌ 3D Model not found on UltraLibrarian.com for {part_number},44,,ultralibrarian_3d_finder
44,QQ,Process,Try SamacSys,,45,,search_step_enhanced()
45,RR,Comment,Search SamacSys,🔍 Searching SamacSys for a 3D model for {part_number},46,,samacsys_3d_finder
46,SS,Decision,SamacSys success?,,47,48,samacsys_3d_finder
47,TT,Comment,Found on SamacSys,✅ 3D Model found on SamacSys.com for {part_number},49,,samacsys_3d_finder
48,UU,Comment,Not found on SamacSys,❌ 3D Model not found on SamacSys.com for {part_number},50,,samacsys_3d_finder
49,VV,Comment,Downloaded STEP file,📁 Downloaded: SamacSys-{manufacturer}-{part_number}.step,50,,samacsys_3d_finder
50,WW,Process,Try SnapEDA,,51,,search_step_enhanced()
51,XX,Comment,Search SnapEDA,🔍 Searching SnapEDA for a 3D model for {part_number},52,,snapeda_3d_finder
52,YY,Decision,SnapEDA success?,,53,54,snapeda_3d_finder
53,ZZ,Comment,Found on SnapEDA,✅ 3D Model found on SnapEDA.com for {part_number},55,,snapeda_3d_finder
54,AAA,Comment,Not found on SnapEDA,❌ 3D Model not found on SnapEDA.com for {part_number},55,,snapeda_3d_finder
55,BBB,End,Search complete,,END,,search_complete()
