#!/usr/bin/env python3
"""
Clean SnapEDA test with GUI - Shows every screen step by step
"""

from snapeda_3d_finder import SnapEDA3DFinder
from working_screen_gui import get_working_gui

def main():
    print("🚀 Starting SnapEDA with GUI screen tracking...")
    
    # Create GUI
    gui = get_working_gui()
    
    # Create SnapEDA finder and assign GUI
    finder = SnapEDA3DFinder()
    finder.gui = gui
    
    # Start the process
    result = finder.search_and_download("Texas Instruments", "LM358N", silent=False)
    
    if result:
        print(f"✅ SUCCESS: Downloaded {result}")
    else:
        print("❌ FAILED: No file downloaded")

if __name__ == "__main__":
    main()
