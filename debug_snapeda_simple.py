#!/usr/bin/env python3
"""
Simple debug test for SnapEDA
"""

print("🔍 Starting SnapEDA debug test...")

try:
    print("🔸 Importing selenium...")
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    print("✅ Selenium imported successfully")
    
    print("🔸 Setting up Chrome options...")
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    print("✅ Chrome options set")
    
    print("🔸 Starting Chrome driver...")
    driver = webdriver.Chrome(options=chrome_options)
    print("✅ Chrome driver started")
    
    print("🔸 Loading SnapEDA...")
    driver.get("https://www.snapeda.com/")
    print("✅ SnapEDA loaded")
    
    print(f"🔍 URL: {driver.current_url}")
    print(f"🔍 Title: {driver.title}")
    
    print("🔸 Looking for login links...")
    login_elements = driver.find_elements(By.XPATH, "//a[contains(text(), 'Login') or contains(text(), 'Log In') or contains(text(), 'Sign In')]")
    print(f"✅ Found {len(login_elements)} login elements")
    
    for i, elem in enumerate(login_elements):
        try:
            if elem.is_displayed():
                text = elem.text.strip()
                href = elem.get_attribute('href')
                location = elem.location
                print(f"  {i+1}. '{text}' href='{href}' x={location['x']}")
        except:
            pass
    
    print("\n🎯 Browser is open - you can see the page!")
    print("🔍 Press Enter to close...")
    input()
    
    driver.quit()
    print("✅ Test complete")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
