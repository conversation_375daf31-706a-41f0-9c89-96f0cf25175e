#!/usr/bin/env python3
"""
Direct Manufacturer 3D Model Finder
Simple, focused search that tries to actually find and download 3D models from manufacturer websites
Minimal messages - just tries to find the file and reports success/failure
"""

import requests
from bs4 import BeautifulSoup
import os
import json
import time
from urllib.parse import urljoin, urlparse, quote
import csv
import re

def log_3d_download(manufacturer, part_number, filename):
    """Log successful 3D model download"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")
    
    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)
    
    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            if not file_exists:
                writer.writeheader()
            
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': 'MANUFACTURER_DIRECT',
                'STEP_FILE_NAME': filename
            })
    except:
        pass  # Silent fail for logging

def download_3d_file(url, manufacturer, part_number):
    """Download 3D model file from URL"""
    try:
        # Skip if URL looks suspicious or invalid
        if not url or len(url) < 10 or 'javascript:' in url.lower():
            return None

        response = requests.get(url, timeout=15, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }, allow_redirects=True)

        if response.status_code == 200 and len(response.content) > 100:  # Must be at least 100 bytes
            # Determine file extension from URL or content-type
            url_lower = url.lower()
            content_type = response.headers.get('content-type', '').lower()

            if '.step' in url_lower or '.stp' in url_lower or 'step' in content_type:
                ext = '.step'
            elif '.stl' in url_lower or 'stl' in content_type:
                ext = '.stl'
            elif '.igs' in url_lower or '.iges' in url_lower or 'iges' in content_type:
                ext = '.igs'
            elif 'octet-stream' in content_type or 'application/' in content_type:
                ext = '.step'  # Default for binary files
            else:
                # Check if content looks like a 3D file
                content_start = response.content[:200].decode('utf-8', errors='ignore').upper()
                if 'ISO-10303' in content_start or 'STEP' in content_start:
                    ext = '.step'
                elif 'STL' in content_start or 'SOLID' in content_start:
                    ext = '.stl'
                else:
                    return None  # Doesn't look like a 3D file

            # Create filename
            filename = f"Manufacturer-{manufacturer.replace(' ', '_')}-{part_number}{ext}"
            filepath = os.path.join("3d", filename)

            # Ensure 3d directory exists
            os.makedirs("3d", exist_ok=True)

            # Save file
            with open(filepath, 'wb') as f:
                f.write(response.content)

            # Log the download
            log_3d_download(manufacturer, part_number, filename)

            return filename
    except Exception as e:
        pass

    return None

def google_search_3d_models(manufacturer, part_number):
    """Search Google for 3D model files using filetype filters"""
    try:
        # Create search query with file type filters
        query = f"{manufacturer} {part_number} 3D model filetype:step OR filetype:stl OR filetype:iges OR filetype:stp"
        search_url = f"https://www.google.com/search?q={quote(query)}"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

        response = requests.get(search_url, headers=headers, timeout=10)
        if response.status_code != 200:
            return []

        soup = BeautifulSoup(response.text, "html.parser")

        # Extract all links
        links = []
        for a in soup.find_all('a', href=True):
            href = a.get('href', '')
            if 'http' in href and any(ext in href.lower() for ext in ['.step', '.stp', '.stl', '.iges', '.igs']):
                # Clean up Google redirect URLs
                if '/url?q=' in href:
                    try:
                        actual_url = href.split('/url?q=')[1].split('&')[0]
                        actual_url = actual_url.replace('%3A', ':').replace('%2F', '/').replace('%3F', '?').replace('%3D', '=')
                        links.append(actual_url)
                    except:
                        continue
                elif href.startswith('http'):
                    links.append(href)

        # Filter for direct file links
        model_links = []
        for link in links[:20]:  # Check first 20 links
            if any(ext in link.lower() for ext in ['.step', '.stp', '.stl', '.iges', '.igs']):
                model_links.append(link)

        return model_links[:5]  # Return top 5 candidates

    except Exception as e:
        return []

def find_manufacturer_3d_model(manufacturer, part_number):
    """
    Search for 3D models using Google search first, then manufacturer website
    Returns filename if found, None if not found
    """

    # Method 1: Try Google search for direct 3D model files
    google_links = google_search_3d_models(manufacturer, part_number)
    for link in google_links:
        filename = download_3d_file(link, manufacturer, part_number)
        if filename:
            return filename

    # Method 2: Try manufacturer website directly
    # Load manufacturer patterns
    try:
        with open('manufacturer_3d_patterns.json', 'r') as f:
            patterns = json.load(f)
    except:
        patterns = {}

    manufacturer_lower = manufacturer.lower()
    manufacturer_patterns = patterns.get(manufacturer_lower, {})

    # Get websites to try
    websites = manufacturer_patterns.get('websites', [])
    if not websites:
        base = manufacturer_lower.replace(" ", "").replace(".", "")
        websites = [f"https://www.{base}.com"]
    
    # Try each website
    for website in websites[:2]:  # Try first 2 websites
        try:
            # Try direct product URLs first
            direct_urls = [
                f"{website}/product/{part_number}",
                f"{website}/products/{part_number}",
                f"{website}/en/products/{part_number}",
                f"{website}/part/{part_number}",
                f"{website}/parts/{part_number}"
            ]
            
            for url in direct_urls:
                try:
                    response = requests.get(url, timeout=10, headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    })
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # Look for direct 3D model download links
                        for link in soup.find_all('a', href=True):
                            href = link.get('href', '').lower()
                            text = link.get_text().lower()
                            
                            # Check if this is a 3D model file
                            if (any(ext in href for ext in ['.step', '.stp', '.stl', '.igs', '.iges']) or
                                (any(keyword in text for keyword in ['3d model', 'step file', 'cad download']) and 
                                 any(keyword in href for keyword in ['download', 'cad', '3d', 'model']))):
                                
                                # Get full URL
                                if href.startswith('http'):
                                    download_url = link['href']
                                elif href.startswith('/'):
                                    download_url = urljoin(website, link['href'])
                                else:
                                    continue
                                
                                # Try to download
                                filename = download_3d_file(download_url, manufacturer, part_number)
                                if filename:
                                    return filename
                        
                        # Look for download buttons/sections
                        download_sections = soup.find_all(['div', 'section'], 
                                                        class_=lambda x: x and any(keyword in x.lower() for keyword in ['download', 'cad', '3d', 'model']))
                        
                        for section in download_sections:
                            for link in section.find_all('a', href=True):
                                href = link.get('href', '')
                                if any(ext in href.lower() for ext in ['.step', '.stp', '.stl', '.igs']):
                                    if href.startswith('http'):
                                        download_url = href
                                    elif href.startswith('/'):
                                        download_url = urljoin(website, href)
                                    else:
                                        continue
                                    
                                    filename = download_3d_file(download_url, manufacturer, part_number)
                                    if filename:
                                        return filename
                
                except:
                    continue
            
            # Try search if direct URLs don't work
            try:
                search_url = f"{website}/search"
                response = requests.get(search_url, params={'q': part_number}, timeout=10, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Look for part links in search results
                    for link in soup.find_all('a', href=True):
                        if part_number.lower() in link.get_text().lower():
                            part_url = urljoin(website, link['href'])
                            
                            # Check this part page for 3D models
                            try:
                                part_response = requests.get(part_url, timeout=10, headers={
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                                })
                                
                                if part_response.status_code == 200:
                                    part_soup = BeautifulSoup(part_response.text, 'html.parser')
                                    
                                    for part_link in part_soup.find_all('a', href=True):
                                        href = part_link.get('href', '').lower()
                                        if any(ext in href for ext in ['.step', '.stp', '.stl', '.igs']):
                                            if href.startswith('http'):
                                                download_url = part_link['href']
                                            elif href.startswith('/'):
                                                download_url = urljoin(website, part_link['href'])
                                            else:
                                                continue
                                            
                                            filename = download_3d_file(download_url, manufacturer, part_number)
                                            if filename:
                                                return filename
                            except:
                                continue
            except:
                continue
                
        except:
            continue
    
    return None

def main():
    """Test the direct manufacturer 3D finder"""
    import sys
    
    if len(sys.argv) == 3:
        manufacturer = sys.argv[1]
        part_number = sys.argv[2]
    else:
        manufacturer = input("Enter manufacturer: ").strip()
        part_number = input("Enter part number: ").strip()
    
    if not manufacturer or not part_number:
        print("❌ Both manufacturer and part number required")
        return
    
    print(f"🔍 Searching {manufacturer} for {part_number} 3D model...")
    
    result = find_manufacturer_3d_model(manufacturer, part_number)
    
    if result:
        print(f"✅ Found and downloaded: {result}")
    else:
        print(f"❌ No 3D model found on {manufacturer} website")

if __name__ == "__main__":
    main()
