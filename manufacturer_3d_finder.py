"""
Manufacturer 3D Model Finder
Searches manufacturer websites directly for 3D models/STEP files
"""

import os
import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from smart_wait_utils import create_smart_wait, CommonLocators
from working_screen_gui import get_working_gui

def log_to_csv(manufacturer, part_number, source, step_filename):
    """Log the download information to CSV file - prevents duplicates"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")

    # Check for duplicates by reading existing entries
    if os.path.exists(csv_filename):
        try:
            with open(csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if (row['MANUFACTURER'].lower() == manufacturer.lower() and
                        row['PART_NUMBER'].lower() == part_number.lower() and
                        row['SOURCE'].lower() == source.lower()):
                        print(f"ℹ️ Entry already exists in CSV: {manufacturer}, {part_number}, {source}")
                        return
        except Exception as e:
            print(f"⚠️ Could not read CSV for duplicate check: {e}")

    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)

    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header if file is new
            if not file_exists:
                writer.writeheader()
                print(f"✅ Created new CSV file: {csv_filename}")

            # Write the data
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': source,
                'STEP_FILE_NAME': step_filename
            })

            print(f"✅ Logged to CSV: {manufacturer}, {part_number}, {source}, {step_filename}")

    except Exception as e:
        print(f"⚠️ Could not write to CSV: {e}")


class Manufacturer3DFinder:
    def __init__(self, silent=False, debug_mode=False):
        self.silent = silent
        self.debug_mode = debug_mode
        self.download_dir = os.path.abspath('3d')
        os.makedirs(self.download_dir, exist_ok=True)

        # Initialize GUI if debug mode is enabled
        if self.debug_mode:
            try:
                self.gui = get_working_gui()
                self.log_print("✅ GUI initialized for step-by-step debugging")
            except Exception as e:
                self.log_print(f"⚠️ GUI failed to initialize: {e}")
                self.gui = None
        else:
            self.gui = None

        # Load manufacturer patterns
        self.patterns = self.load_patterns()

    def log_print(self, message):
        """Print message only if not in silent mode"""
        if not self.silent:
            print(message)
        # In silent mode, only show critical errors and final results
        elif "❌" in message or "✅ Silent mode download successful" in message:
            print(f"[SILENT] {message}")

    def load_patterns(self):
        """Load manufacturer-specific search patterns"""
        try:
            with open('manufacturer_3d_patterns.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            self.log_print(f"⚠️ Could not load manufacturer patterns: {e}")
            return {}

    def save_patterns(self):
        """Save manufacturer-specific search patterns"""
        try:
            with open('manufacturer_3d_patterns.json', 'w') as f:
                json.dump(self.patterns, f, indent=2)
        except Exception as e:
            self.log_print(f"⚠️ Could not save manufacturer patterns: {e}")

    def setup_driver(self):
        """Setup Chrome driver for downloads"""
        chrome_options = Options()
        
        # Set download directory
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        if self.silent:
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            self.log_print("🔇 Window minimized to taskbar")
            self.log_print("🔇 Silent mode: Window appears briefly, closes immediately after download")

        driver = webdriver.Chrome(options=chrome_options)
        
        if not self.silent:
            print("🖥️ Browser window visible for debugging")

        return driver

    def search_manufacturer_website(self, driver, manufacturer, part_number):
        """Search manufacturer's website for 3D models - like a real user"""
        try:
            # Get manufacturer-specific patterns
            manufacturer_lower = manufacturer.lower()
            patterns = self.patterns.get(manufacturer_lower, {})

            # Common manufacturer website patterns
            common_websites = [
                f"https://www.{manufacturer_lower.replace(' ', '')}.com",
                f"https://{manufacturer_lower.replace(' ', '')}.com",
                f"https://www.{manufacturer_lower.replace(' ', '-')}.com"
            ]

            # Add known manufacturer websites
            known_sites = patterns.get('websites', [])
            websites = known_sites + common_websites

            self.log_print(f"🔍 Searching {manufacturer} websites for {part_number}...")

            for website in websites[:3]:  # Try first 3 websites
                try:
                    self.log_print(f"🌐 Going to main page: {website}")

                    # Step 1: Go to main page
                    driver.get(website)
                    time.sleep(3)

                    # GUI Screen 1: Main page loaded
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.update_screen("Manufacturer", 1, "Main Page Loaded", driver.current_url,
                                             f"SCREEN 1 - MAIN PAGE LOADED:\n✅ Manufacturer: {manufacturer}\n✅ Website: {website}\n✅ Current URL: {driver.current_url}\n\n👀 YOU SHOULD SEE:\n   - Manufacturer's main homepage\n   - Navigation menu\n   - Search bar (if available)\n\n🎯 WHAT I WILL DO NEXT:\n   → Look for search bar\n   → Enter part number: {part_number}\n   → Search for the part")
                        self.gui.wait_for_continue()

                    # Step 2: Look for search bar and search
                    search_result = self.search_for_part(driver, part_number)
                    if search_result:
                        # GUI Screen 2: Search results loaded
                        if hasattr(self, 'gui') and self.gui:
                            self.gui.update_screen("Manufacturer", 2, "Search Results", driver.current_url,
                                                 f"SCREEN 2 - SEARCH RESULTS:\n✅ Searched for: {part_number}\n✅ Current URL: {driver.current_url}\n\n👀 YOU SHOULD SEE:\n   - Search results page\n   - Part listings or product pages\n   - Links to part details\n\n🎯 WHAT I WILL DO NEXT:\n   → Click on part link\n   → Look for 3D models/CAD files\n   → Download STEP files")
                            self.gui.wait_for_continue()

                        # Step 3: Look for 3D models on results/product page
                        step_files = self.find_and_download_3d_models(driver, manufacturer, part_number)
                        if step_files:
                            return step_files

                    # If search didn't work, try direct URLs
                    self.log_print(f"🔄 Search bar not found, trying direct URLs...")
                    direct_result = self.try_direct_urls(driver, website, part_number, manufacturer)
                    if direct_result:
                        return direct_result

                except Exception as e:
                    self.log_print(f"⚠️ Error with {website}: {str(e)[:50]}")
                    continue

            return None

        except Exception as e:
            self.log_print(f"❌ Manufacturer search error: {e}")
            return None

    def search_for_part(self, driver, part_number):
        """Look for search bar and search for the part number"""
        try:
            # Common search bar selectors
            search_selectors = [
                "input[type='search']",
                "input[name='search']",
                "input[name='q']",
                "input[name='query']",
                "input[placeholder*='search' i]",
                "input[placeholder*='Search' i]",
                "input[id*='search']",
                "input[class*='search']",
                "#search",
                ".search-input",
                "[data-testid*='search']"
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    search_input = driver.find_element(By.CSS_SELECTOR, selector)
                    if search_input.is_displayed() and search_input.is_enabled():
                        self.log_print(f"✅ Found search bar with selector: {selector}")
                        break
                except:
                    continue

            if not search_input:
                self.log_print("⚠️ No search bar found on main page")
                return False

            # Clear and enter part number
            search_input.clear()
            search_input.send_keys(part_number)
            self.log_print(f"✅ Entered part number: {part_number}")

            # Look for search button and click it
            search_button_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button[class*='search']",
                "button[id*='search']",
                ".search-button",
                "[data-testid*='search-button']"
            ]

            search_button = None
            for selector in search_button_selectors:
                try:
                    # Look for search button near the search input
                    search_button = driver.find_element(By.CSS_SELECTOR, selector)
                    if search_button.is_displayed() and search_button.is_enabled():
                        self.log_print(f"✅ Found search button with selector: {selector}")
                        break
                except:
                    continue

            if search_button:
                search_button.click()
                self.log_print("✅ Clicked search button")
            else:
                # Try pressing Enter
                from selenium.webdriver.common.keys import Keys
                search_input.send_keys(Keys.RETURN)
                self.log_print("✅ Pressed Enter to search")

            # Wait for search results to load
            time.sleep(3)
            return True

        except Exception as e:
            self.log_print(f"⚠️ Search failed: {str(e)[:50]}")
            return False

    def try_direct_urls(self, driver, website, part_number, manufacturer):
        """Try direct URLs when search bar is not available"""
        try:
            # Try common direct URL patterns
            direct_urls = [
                f"{website}/search?q={part_number}",
                f"{website}/products/{part_number}",
                f"{website}/part/{part_number}",
                f"{website}/product/{part_number}",
                f"{website}/en/products/{part_number}",
                f"{website}/en/product/{part_number}"
            ]

            for url in direct_urls:
                try:
                    self.log_print(f"🔗 Trying direct URL: {url}")
                    driver.get(url)
                    time.sleep(2)

                    # Check if we got a valid page (not 404)
                    if "404" not in driver.title.lower() and "not found" not in driver.page_source.lower():
                        step_files = self.find_and_download_3d_models(driver, manufacturer, part_number)
                        if step_files:
                            return step_files

                except Exception as e:
                    continue

            return None

        except Exception as e:
            self.log_print(f"⚠️ Direct URL search failed: {str(e)[:50]}")
            return None

    def find_and_download_3d_models(self, driver, manufacturer, part_number):
        """Find and download 3D models from the current page"""
        try:
            # First, look for part links in search results
            part_links = self.find_part_links(driver, part_number)

            for part_link in part_links[:3]:  # Try first 3 part links
                try:
                    self.log_print(f"🔗 Clicking part link: {part_link['text'][:30]}...")
                    part_link['element'].click()
                    time.sleep(3)

                    # GUI Screen 3: Part page loaded
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.update_screen("Manufacturer", 3, "Part Page Loaded", driver.current_url,
                                             f"SCREEN 3 - PART PAGE LOADED:\n✅ Part: {part_number}\n✅ Current URL: {driver.current_url}\n\n👀 YOU SHOULD SEE:\n   - Part detail page\n   - Technical specifications\n   - Download sections\n   - CAD/3D model links\n\n🎯 WHAT I WILL DO NEXT:\n   → Look for 3D model downloads\n   → Look for CAD files\n   → Download STEP files")
                        self.gui.wait_for_continue()

                    # Look for 3D model downloads on this page
                    step_files = self.find_and_download_step_files(driver, manufacturer, part_number)
                    if step_files:
                        return step_files

                except Exception as e:
                    self.log_print(f"⚠️ Error with part link: {str(e)[:30]}")
                    continue

            # If no part links found, look for 3D models on current page
            return self.find_and_download_step_files(driver, manufacturer, part_number)

        except Exception as e:
            self.log_print(f"⚠️ 3D model search failed: {str(e)[:50]}")
            return None

    def find_part_links(self, driver, part_number):
        """Find links to part detail pages"""
        part_links = []

        # Look for links that contain the part number
        try:
            all_links = driver.find_elements(By.TAG_NAME, "a")
            for link in all_links:
                try:
                    href = link.get_attribute('href')
                    text = link.text.strip()

                    if href and (
                        part_number.lower() in text.lower() or
                        part_number.lower() in href.lower() or
                        'product' in href.lower() or
                        'part' in href.lower()
                    ):
                        part_links.append({
                            'url': href,
                            'text': text,
                            'element': link
                        })
                except:
                    continue

        except Exception as e:
            self.log_print(f"⚠️ Error finding part links: {str(e)[:30]}")

        return part_links

    def find_and_download_step_files(self, driver, manufacturer, part_number):
        """Find and download STEP files from current page"""
        try:
            # Look for 3D model download links
            step_links = []

            # Common selectors for 3D model downloads
            selectors = [
                "a[href*='.step']",
                "a[href*='.stp']",
                "a[href*='3d']",
                "a[href*='cad']",
                "a[href*='download']",
                "a[contains(text(), 'STEP')]",
                "a[contains(text(), '3D')]",
                "a[contains(text(), 'CAD')]",
                "a[contains(text(), 'Model')]",
                "a[contains(text(), 'Download')]"
            ]

            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        text = element.text.strip()

                        if href and (
                            '.step' in href.lower() or
                            '.stp' in href.lower() or
                            '3d' in text.lower() or
                            'cad' in text.lower() or
                            'step' in text.lower() or
                            'model' in text.lower()
                        ):
                            step_links.append({
                                'url': href,
                                'text': text,
                                'element': element
                            })
                except:
                    continue

            if not step_links:
                self.log_print("⚠️ No 3D model download links found")
                return None

            self.log_print(f"✅ Found {len(step_links)} potential 3D model links")

            # Try to download from each link
            for link in step_links[:5]:  # Try first 5 links
                try:
                    self.log_print(f"📥 Trying to download: {link['text'][:40]}...")

                    # Click the download link
                    link['element'].click()
                    time.sleep(5)  # Wait longer for download

                    # Check for downloaded files
                    downloaded_files = self.check_downloads(manufacturer, part_number)
                    if downloaded_files:
                        return downloaded_files[0]  # Return first successful download

                except Exception as e:
                    self.log_print(f"⚠️ Download failed: {str(e)[:30]}")
                    continue

            return None

        except Exception as e:
            self.log_print(f"⚠️ STEP file download failed: {str(e)[:50]}")
            return None

    def check_downloads(self, manufacturer, part_number):
        """Check for downloaded STEP files"""
        downloaded_files = []
        
        for filename in os.listdir(self.download_dir):
            if filename.endswith(('.step', '.stp')) and filename not in ['temp.step', 'temp.stp']:
                file_path = os.path.join(self.download_dir, filename)
                
                # Rename to standard format
                final_name = f"Manufacturer-{manufacturer.replace(' ', '_')}-{part_number}.step"
                final_path = os.path.join(self.download_dir, final_name)
                
                if os.path.exists(final_path):
                    os.remove(final_path)
                    
                os.rename(file_path, final_path)
                self.log_print(f"✅ Downloaded and renamed to: {final_name}")
                
                # Log to CSV
                log_to_csv(manufacturer, part_number, "MANUFACTURER", final_name)
                
                downloaded_files.append(final_name)
                
        return downloaded_files


def find_3d_model(manufacturer, part_number, silent=False, debug_mode=False):
    """Main function to find and download 3D model from manufacturer website"""
    
    if silent:
        print(f"🔇 Manufacturer Silent mode: Searching for {manufacturer} {part_number}")

    # Create finder instance
    finder = Manufacturer3DFinder(silent=silent, debug_mode=debug_mode)
    driver = finder.setup_driver()

    try:
        # Search manufacturer website
        if silent:
            print("🔇 Starting manufacturer website search...")
        result = finder.search_manufacturer_website(driver, manufacturer, part_number)

        if result:
            if silent:
                print(f"✅ Silent mode download successful: {result}")
            else:
                print(f"✅ 3D model found: {result}")
            return result
        else:
            if silent:
                print("❌ Silent mode download failed")
            else:
                print("❌ No 3D model found on manufacturer website")
            return None

    except Exception as e:
        print(f"❌ Manufacturer search error: {e}")
        return None
    finally:
        driver.quit()


if __name__ == "__main__":
    # Test the manufacturer 3D finder
    result = find_3d_model("Texas Instruments", "LM358N", silent=False, debug_mode=True)
    print(f"Result: {result}")
