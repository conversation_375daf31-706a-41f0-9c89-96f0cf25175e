flowchart TD
    A[Start: User clicks Search Component] --> B["Comment 1: 🔍 Searching for datasheet and 3D Models for {manufacturer} {part_number}"]
    
    B --> C[Step 0: Try Distributors First]
    C --> D[Call search_distributors_for_part_info]
    
    D --> E{Distributor Found Info?}
    
    E -->|YES| F["Comment 2: 📡 Searching API Digi-Key website for {manufacturer} {part_number} Datasheet"]
    F --> G[Try Digi-Key API]
    G --> H{Digi-Key Success?}
    
    H -->|YES| I["Comment 3: ✅ Found Link for {manufacturer} {part_number} DataSheet"]
    H -->|NO| J["Comment 3: ❌ Link for {manufacturer} {part_number} DataSheet Not Found"]
    
    I --> K["Comment 4: 🔗 Link to manufacturers website - If found Updating Manufacturer to Manufacturer website cross reference in CSV file"]
    K --> L["Comment 5: 📥 Downloading from {manufacturer} {part_number} Datasheet"]
    L --> M[Actually Download File]
    M --> N["Comment 6: 🔍 Searching Datasheet for part number match. If its correct then we look for the package type"]
    N --> O["Comment 7: 📋 Part-Number – Found or not found, Package Found or Not Found"]
    
    O --> P[Save to CSV]
    P --> Q[Try Manufacturer Website Search]
    Q --> R[Try 3D Model Search]
    
    J --> S{Try Mouser?}
    S -->|YES| T["Comment: 📡 Searching API Mouser website for {manufacturer} {part_number} Datasheet"]
    T --> U[Try Mouser API]
    U --> V{Mouser Success?}
    V -->|YES| W["Comment: ✅ Found Link for {manufacturer} {part_number} DataSheet on Mouser"]
    V -->|NO| X["Comment: ❌ Link for {manufacturer} {part_number} DataSheet Not Found on Mouser"]
    
    W --> K
    X --> Y[No Distributor Success - Try Knowledge Base]
    
    E -->|NO| Y
    
    Y --> Z["Comment: 🔍 {MANUFACTURER}.COM not in knowledge base for predefined searches"]
    Z --> AA[Try to Find Manufacturer Website]
    AA --> BB[Search Manufacturer Website]
    BB --> CC{Found Datasheet?}
    
    CC -->|YES| DD["Comment: ✅ Found datasheet on manufacturer website"]
    CC -->|NO| EE["Comment: ❌ No datasheet found - Ask user for help"]
    
    DD --> R
    EE --> FF["Comment: 🤔 All automatic search methods failed"]
    FF --> GG[Prompt User for Help]
    
    R --> HH["Comment: 🔍 Searching {manufacturer} for a 3D model for {part_number}"]
    HH --> II{3D Found on Manufacturer?}
    II -->|YES| JJ["Comment: ✅ 3D Model found on {manufacturer}.com for {part_number}"]
    II -->|NO| KK["Comment: ❌ 3D Model not found on {manufacturer}.com for {part_number}"]
    
    JJ --> LL[Try UltraLibrarian]
    KK --> LL
    
    LL --> MM["Comment: 🔍 Searching UltraLibrarian for a 3D model for {part_number}"]
    MM --> NN{UltraLibrarian Success?}
    NN -->|YES| OO["Comment: ✅ 3D Model found on UltraLibrarian.com for {part_number}"]
    NN -->|NO| PP["Comment: ❌ 3D Model not found on UltraLibrarian.com for {part_number}"]
    
    OO --> QQ[Try SamacSys]
    PP --> QQ
    
    QQ --> RR["Comment: 🔍 Searching SamacSys for a 3D model for {part_number}"]
    RR --> SS{SamacSys Success?}
    SS -->|YES| TT["Comment: ✅ 3D Model found on SamacSys.com for {part_number}"]
    SS -->|NO| UU["Comment: ❌ 3D Model not found on SamacSys.com for {part_number}"]
    
    TT --> VV["Comment: 📁 Downloaded: SamacSys-{manufacturer}-{part_number}.step"]
    UU --> WW[Try SnapEDA]
    VV --> WW
    
    WW --> XX["Comment: 🔍 Searching SnapEDA for a 3D model for {part_number}"]
    XX --> YY{SnapEDA Success?}
    YY -->|YES| ZZ["Comment: ✅ 3D Model found on SnapEDA.com for {part_number}"]
    YY -->|NO| AAA["Comment: ❌ 3D Model not found on SnapEDA.com for {part_number}"]
    
    ZZ --> BBB[Search Complete]
    AAA --> BBB
