#!/usr/bin/env python3
"""
Debug SnapEDA redirect behavior after download button click
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def debug_snapeda_redirect():
    print("🔍 DEBUGGING SNAPEDA REDIRECT BEHAVIOR")
    print("=" * 60)
    
    # Setup Chrome with visible browser to see what happens
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Set download directory
    downloads_dir = os.path.abspath('3d')
    os.makedirs(downloads_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": downloads_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Login to SnapEDA first
        print("🔐 Logging into SnapEDA...")
        driver.get("https://www.snapeda.com/account/login/")
        time.sleep(3)
        
        # Fill login form (using saved credentials)
        try:
            import json
            with open('save/component_site_credentials.json', 'r') as f:
                creds = json.load(f)
                snapeda_creds = creds.get('snapeda', {})
                email = snapeda_creds.get('email', '')
                password = snapeda_creds.get('password', '')
        except:
            print("❌ Could not load credentials")
            return
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        password_field = driver.find_element(By.CSS_SELECTOR, "#id_password")
        
        email_field.send_keys(email)
        password_field.send_keys(password)
        
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(5)
        
        print("✅ Login completed")
        
        # Go to part page
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        print(f"🔸 Going to part page: {part_url}")
        driver.get(part_url)
        time.sleep(5)
        
        # Click 3D Model tab
        print("🔸 Looking for 3D Model tab...")
        model_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D Model')]")
        for element in model_elements:
            if element.is_displayed():
                print(f"✅ Found 3D Model tab: {element.text}")
                element.click()
                time.sleep(3)
                break
        
        # Find download button
        print("🔸 Looking for Download 3D Model button...")
        download_btn = driver.find_element(By.XPATH, "//a[contains(text(), 'Download 3D Model')]")
        
        if download_btn:
            print(f"✅ Found download button")
            print(f"   Text: '{download_btn.text}'")
            print(f"   href: '{download_btn.get_attribute('href')}'")
            print(f"   class: '{download_btn.get_attribute('class')}'")
            
            # Get initial URL and window handles
            initial_url = driver.current_url
            initial_handles = driver.window_handles
            print(f"🔸 Initial URL: {initial_url}")
            print(f"🔸 Initial windows: {len(initial_handles)}")
            
            # Click download button
            print("🔸 Clicking download button...")
            download_btn.click()
            
            # Monitor for changes
            for i in range(30):
                time.sleep(1)
                current_url = driver.current_url
                current_handles = driver.window_handles
                
                # Check for URL changes
                if current_url != initial_url:
                    print(f"🎯 URL CHANGED after {i+1}s: {current_url}")
                
                # Check for new windows/tabs
                if len(current_handles) > len(initial_handles):
                    print(f"🎯 NEW WINDOW/TAB opened after {i+1}s")
                    print(f"   Total windows: {len(current_handles)}")
                    
                    # Switch to new window
                    for handle in current_handles:
                        if handle not in initial_handles:
                            print(f"🔸 Switching to new window...")
                            driver.switch_to.window(handle)
                            new_url = driver.current_url
                            print(f"   New window URL: {new_url}")
                            
                            # Check if this is a download URL or redirect
                            if any(domain in new_url for domain in ['download', 'file', 'cdn', 'amazonaws', 'blob']):
                                print(f"🎯 DOWNLOAD URL DETECTED: {new_url}")
                            
                            # Wait a bit more to see if download starts
                            time.sleep(5)
                            break
                    break
                
                # Check for downloads in directory
                if os.path.exists(downloads_dir):
                    files = os.listdir(downloads_dir)
                    if files:
                        print(f"🎉 FILES DETECTED after {i+1}s: {files}")
                        break
                
                if i % 5 == 0 and i > 0:
                    print(f"   ⏳ Still monitoring... {i}/30 seconds")
            
            # Final status
            print("\n📊 FINAL STATUS:")
            print(f"   Current URL: {driver.current_url}")
            print(f"   Total windows: {len(driver.window_handles)}")
            
            if os.path.exists(downloads_dir):
                files = os.listdir(downloads_dir)
                print(f"   Files in 3d directory: {files}")
            
            # Keep browser open for manual inspection
            print("\n🔍 Browser will stay open for 30 seconds for manual inspection...")
            time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_snapeda_redirect()
