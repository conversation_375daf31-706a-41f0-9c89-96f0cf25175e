"""
ENHANCED MANUFACTURER 3D MODEL TESTER
GOAL: Test and validate 3D model retrieval from manufacturer websites
ROLES:
- User: <PERSON> (Architect, Debug Supervisor)
- Agent: AugmentCode (Executor, Compliance Reporter)
- AI: Auditor (Optional)

REQUIRES:
- requests
- beautifulsoup4
- selenium (for advanced testing)
"""

import requests
from bs4 import BeautifulSoup
import logging
import os
import json
import csv
import time
from datetime import datetime
from urllib.parse import urljoin, urlparse
import re

# === CONFIG ===
LOG_DIR = "logs"
MODEL_EXTENSIONS = [".step", ".stl", ".igs", ".zip", ".stp", ".iges"]
AGGREGATORS = {
    "SnapEDA": "https://www.snapeda.com/search/?q={}",
    "UltraLibrarian": "https://www.ultralibrarian.com/search?q={}",
    "SamacSys": "https://componentsearchengine.com/part.php?partID={}"
}

# Load existing manufacturer patterns
def load_manufacturer_patterns():
    """Load manufacturer patterns from existing JSON file"""
    try:
        with open('manufacturer_3d_patterns.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ Could not load manufacturer patterns: {e}")
        return {}

MANUFACTURER_PATTERNS = load_manufacturer_patterns()

# === SETUP LOGGING ===
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)
log_file = os.path.join(LOG_DIR, f"manufacturer_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(filename=log_file, level=logging.INFO, format='%(asctime)s - %(message)s')

def log(msg):
    print(msg)
    logging.info(msg)

# === ENHANCED FUNCTIONS ===
def test_manufacturer_website_availability(manufacturer):
    """Test if manufacturer website is accessible and has 3D model sections"""
    log(f"🌐 Testing website availability for: {manufacturer}")
    
    manufacturer_lower = manufacturer.lower()
    patterns = MANUFACTURER_PATTERNS.get(manufacturer_lower, {})
    
    # Get known websites or generate common patterns
    websites = patterns.get('websites', [])
    if not websites:
        base = manufacturer_lower.replace(" ", "").replace(".", "")
        websites = [
            f"https://www.{base}.com",
            f"https://{base}.com",
            f"https://www.{base}.net",
            f"https://www.{base}-semi.com"
        ]
    
    results = []
    for website in websites[:3]:  # Test first 3 websites
        try:
            log(f"  🔍 Testing: {website}")
            response = requests.get(website, timeout=10, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                # Check for 3D model related content
                content = response.text.lower()
                has_3d_content = any(keyword in content for keyword in [
                    '3d model', 'cad', 'step', 'download', 'design resources', 
                    'mechanical', 'package', 'footprint'
                ])
                
                results.append({
                    'url': website,
                    'status': 'accessible',
                    'has_3d_content': has_3d_content,
                    'title': BeautifulSoup(response.text, 'html.parser').title.string if BeautifulSoup(response.text, 'html.parser').title else 'No title'
                })
                log(f"    ✅ Accessible - 3D content: {'Yes' if has_3d_content else 'No'}")
            else:
                results.append({
                    'url': website,
                    'status': f'error_{response.status_code}',
                    'has_3d_content': False
                })
                log(f"    ❌ Error {response.status_code}")
                
        except Exception as e:
            results.append({
                'url': website,
                'status': 'unreachable',
                'error': str(e)[:50],
                'has_3d_content': False
            })
            log(f"    ❌ Unreachable: {str(e)[:50]}")
    
    return results

def discover_3d_model_urls(manufacturer, part_number):
    """Discover potential 3D model URLs using heuristics"""
    log(f"🔍 Discovering 3D model URLs for: {manufacturer} / {part_number}")
    
    manufacturer_lower = manufacturer.lower()
    patterns = MANUFACTURER_PATTERNS.get(manufacturer_lower, {})
    
    # Get base websites
    websites = patterns.get('websites', [])
    if not websites:
        base = manufacturer_lower.replace(" ", "").replace(".", "")
        websites = [f"https://www.{base}.com"]
    
    discovered_urls = []
    
    for website in websites[:2]:  # Test first 2 websites
        # Common 3D model URL patterns
        url_patterns = [
            f"{website}/products/{part_number}",
            f"{website}/product/{part_number}",
            f"{website}/en/products/{part_number}",
            f"{website}/cad/{part_number}",
            f"{website}/3d-models/{part_number}",
            f"{website}/downloads/{part_number}",
            f"{website}/design-resources/{part_number}",
            f"{website}/support/cad-data/{part_number}",
            f"{website}/lit/zip/{part_number}",
            f"{website}/search?q={part_number}",
            f"{website}/parametric-search?partNumber={part_number}"
        ]
        
        # Add manufacturer-specific patterns
        specific_patterns = patterns.get('search_patterns', [])
        for pattern in specific_patterns:
            url_patterns.append(website + pattern.format(part_number=part_number))
        
        discovered_urls.extend(url_patterns)
    
    return discovered_urls

def test_url_for_3d_models(url):
    """Test a specific URL for 3D model availability"""
    try:
        log(f"  🔗 Testing URL: {url}")
        response = requests.get(url, timeout=10, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        if response.status_code != 200:
            return {'status': 'error', 'code': response.status_code}
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Look for 3D model download links
        model_links = []
        
        # Find direct file links
        for link in soup.find_all('a', href=True):
            href = link.get('href', '').lower()
            text = link.get_text().lower()
            
            if any(ext in href for ext in MODEL_EXTENSIONS):
                model_links.append({
                    'url': urljoin(url, link['href']),
                    'text': link.get_text().strip()[:50],
                    'type': 'direct_file'
                })
            elif any(keyword in text for keyword in ['3d model', 'cad', 'step', 'download']):
                model_links.append({
                    'url': urljoin(url, link['href']),
                    'text': link.get_text().strip()[:50],
                    'type': 'potential_3d'
                })
        
        # Look for download sections
        download_sections = soup.find_all(['div', 'section'], 
                                        class_=re.compile(r'download|cad|3d|model', re.I))
        
        return {
            'status': 'success',
            'model_links': model_links[:10],  # Limit to first 10 links
            'download_sections': len(download_sections),
            'page_title': soup.title.string if soup.title else 'No title'
        }
        
    except Exception as e:
        return {'status': 'error', 'error': str(e)[:50]}

def comprehensive_manufacturer_test(manufacturer, part_number):
    """Comprehensive test of manufacturer's 3D model availability"""
    log(f"🎯 COMPREHENSIVE MANUFACTURER TEST")
    log(f"Manufacturer: {manufacturer}")
    log(f"Part Number: {part_number}")
    log("=" * 60)
    
    results = {
        'manufacturer': manufacturer,
        'part_number': part_number,
        'timestamp': datetime.now().isoformat(),
        'website_availability': [],
        'discovered_urls': [],
        'url_test_results': [],
        'aggregator_urls': {},
        'summary': {}
    }
    
    # Step 1: Test website availability
    log("📡 Step 1: Testing website availability...")
    results['website_availability'] = test_manufacturer_website_availability(manufacturer)
    
    # Step 2: Discover potential URLs
    log("🔍 Step 2: Discovering potential 3D model URLs...")
    discovered_urls = discover_3d_model_urls(manufacturer, part_number)
    results['discovered_urls'] = discovered_urls
    
    # Step 3: Test discovered URLs
    log("🧪 Step 3: Testing discovered URLs for 3D models...")
    for url in discovered_urls[:15]:  # Test first 15 URLs
        test_result = test_url_for_3d_models(url)
        test_result['url'] = url
        results['url_test_results'].append(test_result)
        
        if test_result['status'] == 'success' and test_result.get('model_links'):
            log(f"    ✅ Found {len(test_result['model_links'])} potential 3D links")
        elif test_result['status'] == 'error':
            log(f"    ❌ Error: {test_result.get('error', test_result.get('code', 'Unknown'))}")
        else:
            log(f"    ⚠️ No 3D models found")
    
    # Step 4: Get aggregator URLs
    log("🌐 Step 4: Getting aggregator URLs...")
    for name, url_template in AGGREGATORS.items():
        results['aggregator_urls'][name] = url_template.format(part_number)
    
    # Step 5: Generate summary
    accessible_sites = [r for r in results['website_availability'] if r['status'] == 'accessible']
    sites_with_3d = [r for r in accessible_sites if r.get('has_3d_content', False)]
    successful_urls = [r for r in results['url_test_results'] if r['status'] == 'success']
    urls_with_models = [r for r in successful_urls if r.get('model_links')]
    
    results['summary'] = {
        'accessible_websites': len(accessible_sites),
        'websites_with_3d_content': len(sites_with_3d),
        'tested_urls': len(results['url_test_results']),
        'successful_url_tests': len(successful_urls),
        'urls_with_potential_models': len(urls_with_models),
        'total_potential_3d_links': sum(len(r.get('model_links', [])) for r in urls_with_models)
    }
    
    return results

def save_test_results(results):
    """Save test results to JSON file"""
    filename = f"manufacturer_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        log(f"✅ Test results saved to: {filename}")
        return filename
    except Exception as e:
        log(f"❌ Could not save results: {e}")
        return None

def print_test_summary(results):
    """Print a formatted summary of test results"""
    log("\n" + "=" * 60)
    log("📊 TEST SUMMARY")
    log("=" * 60)
    
    summary = results['summary']
    log(f"Manufacturer: {results['manufacturer']}")
    log(f"Part Number: {results['part_number']}")
    log(f"Test Time: {results['timestamp']}")
    log("")
    log(f"🌐 Accessible Websites: {summary['accessible_websites']}")
    log(f"🎯 Sites with 3D Content: {summary['websites_with_3d_content']}")
    log(f"🔗 URLs Tested: {summary['tested_urls']}")
    log(f"✅ Successful Tests: {summary['successful_url_tests']}")
    log(f"📦 URLs with Potential Models: {summary['urls_with_potential_models']}")
    log(f"🔍 Total Potential 3D Links: {summary['total_potential_3d_links']}")
    
    # Show best URLs
    urls_with_models = [r for r in results['url_test_results'] 
                       if r['status'] == 'success' and r.get('model_links')]
    
    if urls_with_models:
        log("\n🎯 BEST URLS FOR 3D MODELS:")
        for i, url_result in enumerate(urls_with_models[:5], 1):
            log(f"{i}. {url_result['url']}")
            log(f"   📦 {len(url_result['model_links'])} potential 3D links found")
            for link in url_result['model_links'][:3]:
                log(f"      → {link['text']} ({link['type']})")
    else:
        log("\n❌ No URLs with potential 3D models found")

# === MAIN FUNCTION ===
def test_manufacturer_3d_availability(manufacturer, part_number, save_results=True):
    """Main function to test manufacturer 3D model availability"""
    
    # Run comprehensive test
    results = comprehensive_manufacturer_test(manufacturer, part_number)
    
    # Print summary
    print_test_summary(results)
    
    # Save results if requested
    if save_results:
        save_test_results(results)
    
    return results

# === ENTRY POINT ===
if __name__ == "__main__":
    # Example usage - test multiple manufacturers
    test_cases = [
        ("Texas Instruments", "TPS7A4700RGW"),
        ("Analog Devices", "AD8606ARZ"),
        ("Microchip", "PIC16F877A"),
        ("STMicroelectronics", "STM32F103C8T6"),
        ("Murata", "GCM155R71H104KE02D")
    ]
    
    print("🚀 MANUFACTURER 3D MODEL AVAILABILITY TESTER")
    print("=" * 60)
    
    for manufacturer, part_number in test_cases:
        print(f"\n🎯 Testing: {manufacturer} / {part_number}")
        test_manufacturer_3d_availability(manufacturer, part_number)
        print("\n" + "="*60)
        time.sleep(2)  # Brief pause between tests
