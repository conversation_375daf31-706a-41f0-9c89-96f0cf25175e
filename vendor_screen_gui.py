#!/usr/bin/env python3
"""
3D Vendor Screen GUI - Shows current screen, URL, and continue button
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

class VendorScreenGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("3D Vendor Screen Tracker")
        self.root.geometry("900x700")
        self.root.configure(bg='white')
        
        # Current state
        self.current_vendor = ""
        self.current_screen = 0
        self.current_url = ""
        self.continue_pressed = False
        
        # Title
        self.title_label = tk.Label(self.root, text="3D Vendor Screen Tracker", 
                                   font=('Arial', 20, 'bold'), bg='white', fg='blue')
        self.title_label.pack(pady=20)
        
        # Vendor name
        self.vendor_label = tk.Label(self.root, text="Vendor: Not Started", 
                                    font=('Arial', 16, 'bold'), bg='white', fg='green')
        self.vendor_label.pack(pady=10)
        
        # Screen number and title
        self.screen_label = tk.Label(self.root, text="Screen: Not Started", 
                                    font=('Arial', 18, 'bold'), bg='lightblue', 
                                    relief='raised', bd=3)
        self.screen_label.pack(pady=10, padx=20, fill='x')
        
        # Current URL
        self.url_frame = tk.Frame(self.root, bg='white')
        self.url_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(self.url_frame, text="Current URL:", font=('Arial', 12, 'bold'), 
                bg='white').pack(anchor='w')
        
        self.url_label = tk.Label(self.url_frame, text="No URL", 
                                 font=('Arial', 11), bg='lightyellow', 
                                 relief='sunken', bd=2, anchor='w')
        self.url_label.pack(fill='x', pady=5)
        
        # What you should see
        self.see_frame = tk.Frame(self.root, bg='white')
        self.see_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        tk.Label(self.see_frame, text="What You Should See:", 
                font=('Arial', 14, 'bold'), bg='white', fg='red').pack(anchor='w')
        
        self.see_text = tk.Text(self.see_frame, height=8, font=('Arial', 12), 
                               bg='lightgreen', relief='sunken', bd=2, wrap='word')
        self.see_text.pack(fill='both', expand=True, pady=5)
        
        # Action description
        self.action_frame = tk.Frame(self.root, bg='white')
        self.action_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(self.action_frame, text="Current Action:", 
                font=('Arial', 14, 'bold'), bg='white', fg='purple').pack(anchor='w')
        
        self.action_label = tk.Label(self.action_frame, text="No action", 
                                    font=('Arial', 12), bg='lightcyan', 
                                    relief='sunken', bd=2, anchor='w')
        self.action_label.pack(fill='x', pady=5)
        
        # Continue button
        self.continue_button = tk.Button(self.root, text="CONTINUE TO NEXT SCREEN", 
                                        font=('Arial', 16, 'bold'), bg='orange', 
                                        fg='white', command=self.continue_clicked,
                                        relief='raised', bd=5)
        self.continue_button.pack(pady=20, padx=20, fill='x')
        
        # Status
        self.status_label = tk.Label(self.root, text="Ready - Waiting for vendor to start", 
                                    font=('Arial', 10), bg='white', fg='gray')
        self.status_label.pack(pady=5)
        
    def update_screen(self, vendor, screen_num, title, action, what_to_see, url=""):
        """Update the GUI with new screen info"""
        self.current_vendor = vendor.upper()
        self.current_screen = screen_num
        self.current_url = url
        self.continue_pressed = False
        
        # Update all labels
        self.vendor_label.config(text=f"Vendor: {self.current_vendor}")
        self.screen_label.config(text=f"Screen {screen_num}: {title}")
        self.url_label.config(text=url if url else "URL not available")
        self.action_label.config(text=action)
        
        # Update text area
        self.see_text.delete(1.0, tk.END)
        self.see_text.insert(1.0, what_to_see)
        
        # Update status
        self.status_label.config(text=f"Showing {vendor} Screen {screen_num} - Click CONTINUE when ready")
        
        # Enable continue button
        self.continue_button.config(state='normal', bg='orange')
        
        # Update display
        self.root.update()
        
    def continue_clicked(self):
        """Handle continue button click"""
        self.continue_pressed = True
        self.continue_button.config(state='disabled', bg='gray')
        self.status_label.config(text="Continuing to next screen...")
        self.root.update()
        
    def wait_for_continue(self):
        """Wait for user to click continue"""
        while not self.continue_pressed:
            self.root.update()
            time.sleep(0.1)
        return True
        
    def show_error(self, error_msg):
        """Show error message"""
        self.status_label.config(text=f"ERROR: {error_msg}", fg='red')
        self.root.update()
        
    def run(self):
        """Start the GUI"""
        self.root.mainloop()

# Global GUI instance
gui = None

def start_gui():
    """Start GUI in separate thread"""
    global gui
    gui = VendorScreenGUI()
    gui.run()

def get_gui():
    """Get GUI instance, start if needed"""
    global gui
    if gui is None:
        gui = VendorScreenGUI()
        # Start GUI in separate thread
        gui_thread = threading.Thread(target=gui.run, daemon=True)
        gui_thread.start()
        time.sleep(1)  # Let GUI start
    return gui

# Screen definitions for each vendor
SNAPEDA_SCREENS = {
    1: {"title": "Login Page", "action": "You are on SnapEDA login page", "see": "Login form with:\n- Email input field\n- Password input field\n- Login button\n- SnapEDA logo"},
    2: {"title": "Next: Type Credentials", "action": "Will type email and password in fields", "see": "NEXT: Email and password will be typed\nFields will fill with text\nEmail: <EMAIL>"},
    3: {"title": "Next: Click Login", "action": "Will click the login button", "see": "NEXT: Login button will be clicked\nPage will start processing\nLogin attempt will begin"},
    4: {"title": "Login Processing", "action": "Login button was clicked", "see": "Page is changing/loading\nURL may be changing\nLoading spinner or redirect happening"},
    5: {"title": "Login Success Page", "action": "After URL changed from login", "see": "New page loaded (not login page)\nURL changed to dashboard/home\nLogin successful - on main site"},
    6: {"title": "Welcome Screen (EXTRA 1)", "action": "Handling welcome/onboarding popup", "see": "Welcome popup appears with:\n- Welcome message\n- Skip/Continue/Close button\n- Overlay on main page"},
    7: {"title": "Survey Screen (EXTRA 2)", "action": "Handling survey/feedback popup", "see": "Survey popup appears with:\n- Feedback request\n- No thanks/Dismiss/Later button\n- Rating or survey form"},
    8: {"title": "Part Search", "action": "Navigating to part page or searching", "see": "Part search page OR\nDirect part page loading\nSearch box or part details"},
    9: {"title": "Part Page", "action": "Loading component details page", "see": "Component information:\n- Part number and details\n- Tabs (Overview, 3D Model, etc.)\n- Component image"},
    10: {"title": "3D Model Section", "action": "Finding 3D model download button", "see": "3D Model tab active\nDownload button visible\n3D preview or download options"},
    11: {"title": "Download Click", "action": "Clicking download button", "see": "Download button clicked\nMay show modal/popup\nDownload preparation"},
    12: {"title": "File Download", "action": "Monitoring download folder for file", "see": "File downloading\nFile appears in download folder\nDownload complete notification"}
}

if __name__ == "__main__":
    start_gui()
