#!/usr/bin/env python3
"""
SnapEDA Visible Test - Force browser to stay visible
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def test_snapeda_visible():
    """Test SnapEDA with visible browser"""
    print("🚀 Starting SnapEDA Visible Test...")
    
    # Create Chrome driver with NO minimization
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Download settings
    download_dir = os.path.join(os.getcwd(), "3d")
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    print("🖥️ Browser window should be VISIBLE")
    
    try:
        # Go to SnapEDA homepage
        print("📍 Going to SnapEDA homepage...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        print(f"✅ Current URL: {driver.current_url}")
        print(f"📄 Page title: {driver.title}")
        
        # Look for login links
        print("🔍 Looking for login links...")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        login_links = []
        
        for link in all_links:
            try:
                href = link.get_attribute('href') or ''
                text = link.text.strip()
                if 'login' in href.lower() or 'login' in text.lower():
                    login_links.append((text, href))
                    print(f"  🔗 Login link: '{text}' -> {href}")
            except:
                pass
        
        print(f"📊 Found {len(login_links)} login links")
        
        # Wait for user to see the page
        print("⏳ Browser will stay open for 30 seconds so you can see the page...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()
        print("🏁 Test complete")

if __name__ == "__main__":
    test_snapeda_visible()
