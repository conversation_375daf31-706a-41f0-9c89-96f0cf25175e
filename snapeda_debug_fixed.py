#!/usr/bin/env python3
"""
SnapEDA Debug Program - FIXED VERSION
=====================================
Uses correct login URL and credentials
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class SnapEDADebuggerFixed:
    def __init__(self):
        self.download_dir = os.path.join(os.getcwd(), "3d")
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
        
        # Load credentials
        self.credentials = self.load_credentials()
        print(f"🔑 DEBUG: Loaded credentials - Email: {self.credentials.get('email', 'NONE')}, Password: {'***' if self.credentials.get('password') else 'NONE'}")
        
    def load_credentials(self):
        """Load SnapEDA credentials from correct file"""
        try:
            with open('component_site_credentials.json', 'r') as f:
                data = json.load(f)
                snapeda_creds = data.get('SnapEDA', {})
                return {
                    'email': snapeda_creds.get('email', ''),
                    'password': snapeda_creds.get('password', '')
                }
        except Exception as e:
            print(f"⚠️ Could not load credentials: {e}")
        return {'email': '', 'password': ''}
    
    def create_driver(self):
        """Create Chrome driver with proper settings"""
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download settings
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    
    def test_login(self, driver):
        """Test SnapEDA login with correct URL"""
        print("🔐 DEBUG: Testing SnapEDA login with CORRECT URL...")
        
        try:
            # Use the CORRECT working login URL
            login_url = "https://www.snapeda.com/account/login/"
            print(f"🔗 Using CORRECT login URL: {login_url}")
            driver.get(login_url)
            time.sleep(3)
            
            print(f"📍 Current URL: {driver.current_url}")
            print(f"📄 Page title: {driver.title}")
            
            if "404" in driver.title or "not found" in driver.title.lower():
                print("❌ Login page not found")
                return False

            # Debug page structure
            print("🔍 DEBUG: Analyzing login page structure...")

            # Check for any overlays or modals
            overlays = driver.find_elements(By.XPATH, "//div[contains(@class, 'modal') or contains(@class, 'overlay') or contains(@class, 'popup')]")
            if overlays:
                print(f"⚠️ Found {len(overlays)} potential overlays/modals")
                for i, overlay in enumerate(overlays):
                    if overlay.is_displayed():
                        print(f"  Modal {i+1}: class='{overlay.get_attribute('class')}' displayed=True")

            # Find ALL input fields
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"🔍 Found {len(all_inputs)} input fields:")
            for i, inp in enumerate(all_inputs):
                try:
                    input_type = inp.get_attribute('type') or 'text'
                    input_name = inp.get_attribute('name') or 'no-name'
                    input_id = inp.get_attribute('id') or 'no-id'
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    print(f"  Input {i+1}: type='{input_type}' name='{input_name}' id='{input_id}' displayed={is_displayed} enabled={is_enabled}")
                except:
                    pass
            
            # Wait for and find email field
            email_field = None
            try:
                wait = WebDriverWait(driver, 10)
                email_field = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='email']")))
                print("✅ Found clickable email field")
            except:
                print("❌ No clickable email field found")
                return False

            # Wait for and find password field
            password_field = None
            try:
                password_field = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='password']")))
                print("✅ Found clickable password field")
            except:
                print("❌ No clickable password field found")
                return False
            
            # Enter credentials if available
            if self.credentials['email'] and self.credentials['password']:
                print(f"✅ Using credentials: {self.credentials['email']}")

                # Clear and enter email with retry
                try:
                    email_field.clear()
                    time.sleep(0.5)
                    email_field.send_keys(self.credentials['email'])
                    print("✅ Email entered")
                except Exception as e:
                    print(f"⚠️ Email entry failed: {e}")

                # Clear and enter password with retry
                try:
                    password_field.clear()
                    time.sleep(0.5)
                    password_field.send_keys(self.credentials['password'])
                    print("✅ Password entered")
                except Exception as e:
                    print(f"⚠️ Password entry failed: {e}")

                print("✅ Credentials entered")
                
                # Find and click login button
                login_buttons = driver.find_elements(By.XPATH, "//button | //input[@type='submit'] | //input[@type='button']")
                
                for button in login_buttons:
                    try:
                        if button.is_displayed():
                            button_text = button.text or button.get_attribute('value') or 'no-text'
                            print(f"🔍 Found button: '{button_text}'")
                            
                            # Look for login-related buttons
                            if any(word in button_text.lower() for word in ['login', 'sign in', 'submit']):
                                print(f"🎯 Clicking login button: '{button_text}'")
                                button.click()
                                time.sleep(5)
                                
                                # Check if login was successful
                                print(f"📍 After login URL: {driver.current_url}")
                                if "login" not in driver.current_url.lower():
                                    print("🎉 LOGIN SUCCESSFUL!")
                                    return True
                                else:
                                    print("⚠️ Still on login page")
                    except Exception as e:
                        print(f"⚠️ Button click failed: {e}")
                        continue
            else:
                print("❌ No credentials available")
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            
        return False
    
    def test_search(self, driver, part_number="GCM155R71H104KE02D", manufacturer="Murata"):
        """Test SnapEDA search with correct approach"""
        print(f"🔍 DEBUG: Testing search for {manufacturer} {part_number}...")
        
        try:
            # Try direct URL first
            direct_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
            print(f"🔗 Trying direct URL: {direct_url}")
            driver.get(direct_url)
            time.sleep(3)
            
            print(f"📍 Current URL: {driver.current_url}")
            print(f"📄 Page title: {driver.title}")
            
            # Check if we got a valid page
            if "404" in driver.title or "not found" in driver.page_source.lower():
                print("⚠️ Direct URL failed, trying search...")
                
                # Go to search
                search_url = f"https://www.snapeda.com/search?q={part_number}"
                print(f"🔗 Search URL: {search_url}")
                driver.get(search_url)
                time.sleep(3)
                
                # Look for search results
                search_results = driver.find_elements(By.XPATH, f"//a[contains(@href, '/parts/') and contains(text(), '{part_number}')]")
                print(f"🔍 Found {len(search_results)} search results")
                
                if search_results:
                    result = search_results[0]
                    result_text = result.text
                    result_href = result.get_attribute('href')
                    print(f"🎯 Clicking first result: '{result_text}' -> {result_href}")
                    result.click()
                    time.sleep(3)
                else:
                    print("❌ No search results found")
                    return False
            
            print(f"📍 Final URL: {driver.current_url}")
            
            # Debug page content
            self.debug_page_content(driver)
            
            return True
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return False
    
    def debug_page_content(self, driver):
        """Debug what's actually on the page"""
        print("\n🔍 DEBUG: Analyzing page content...")
        
        try:
            # Find all links with download/3d/model keywords
            all_links = driver.find_elements(By.TAG_NAME, "a")
            print(f"\n🔗 Found {len(all_links)} total links")
            
            relevant_links = []
            for i, link in enumerate(all_links[:30]):  # Check first 30 links
                try:
                    if link.is_displayed():
                        text = link.text.strip()
                        href = link.get_attribute('href') or ''
                        
                        # Check if link is relevant to 3D models
                        if any(keyword in text.lower() or keyword in href.lower() 
                               for keyword in ['3d', 'model', 'download']):
                            relevant_links.append((text, href))
                            print(f"  🎯 RELEVANT Link {i+1}: '{text}' -> {href}")
                except:
                    pass
            
            # Find all buttons
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"\n🔘 Found {len(all_buttons)} total buttons")
            
            for i, button in enumerate(all_buttons[:15]):  # Check first 15 buttons
                try:
                    if button.is_displayed():
                        text = button.text.strip()
                        class_attr = button.get_attribute('class') or ''
                        
                        if any(keyword in text.lower() or keyword in class_attr.lower() 
                               for keyword in ['3d', 'model', 'download']):
                            print(f"  🎯 RELEVANT Button {i+1}: '{text}' class='{class_attr}'")
                except:
                    pass
            
            return relevant_links
            
        except Exception as e:
            print(f"❌ Page analysis error: {e}")
            return []

def main():
    """Main debug function"""
    print("🚀🚀🚀 FIXED VERSION RUNNING - NEW PROGRAM 🚀🚀🚀")
    print("🚀 Starting SnapEDA Debug Program - FIXED VERSION...")
    
    debugger = SnapEDADebuggerFixed()
    driver = debugger.create_driver()
    
    try:
        # Test login
        if debugger.test_login(driver):
            print("\n" + "="*50)
            
            # Test search
            if debugger.test_search(driver):
                print("\n🎉 SnapEDA debug completed successfully!")
        
        print("\n🏁 Debug session complete")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
