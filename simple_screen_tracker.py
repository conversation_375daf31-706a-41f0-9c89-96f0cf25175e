#!/usr/bin/env python3
"""
Simple Screen Tracker - Shows actual browser state
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

class SimpleScreenTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Browser Screen Tracker")
        self.root.geometry("1000x600")
        self.root.configure(bg='white')
        
        self.continue_pressed = False
        
        # Title
        self.title_label = tk.Label(self.root, text="Browser Screen Tracker", 
                                   font=('Arial', 20, 'bold'), bg='white', fg='blue')
        self.title_label.pack(pady=20)
        
        # Current URL
        self.url_frame = tk.Frame(self.root, bg='white')
        self.url_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(self.url_frame, text="Current URL:", font=('Arial', 14, 'bold'), 
                bg='white').pack(anchor='w')
        
        self.url_label = tk.Label(self.url_frame, text="No URL", 
                                 font=('Arial', 12), bg='lightyellow', 
                                 relief='sunken', bd=2, anchor='w')
        self.url_label.pack(fill='x', pady=5)
        
        # Page Title
        self.title_frame = tk.Frame(self.root, bg='white')
        self.title_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(self.title_frame, text="Page Title:", font=('Arial', 14, 'bold'), 
                bg='white').pack(anchor='w')
        
        self.page_title_label = tk.Label(self.title_frame, text="No Title", 
                                        font=('Arial', 12), bg='lightcyan', 
                                        relief='sunken', bd=2, anchor='w')
        self.page_title_label.pack(fill='x', pady=5)
        
        # What you should see
        self.see_frame = tk.Frame(self.root, bg='white')
        self.see_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        tk.Label(self.see_frame, text="What You Should See:", 
                font=('Arial', 14, 'bold'), bg='white', fg='red').pack(anchor='w')
        
        self.see_text = tk.Text(self.see_frame, height=15, font=('Arial', 12), 
                               bg='lightgreen', relief='sunken', bd=2, wrap='word')
        self.see_text.pack(fill='both', expand=True, pady=5)
        
        # Continue button
        self.continue_button = tk.Button(self.root, text="CONTINUE", 
                                        font=('Arial', 16, 'bold'), bg='orange', 
                                        fg='white', command=self.continue_clicked,
                                        relief='raised', bd=5)
        self.continue_button.pack(pady=20, padx=20, fill='x')
        
        # Status
        self.status_label = tk.Label(self.root, text="Ready", 
                                    font=('Arial', 10), bg='white', fg='gray')
        self.status_label.pack(pady=5)
        
    def update_screen(self, url, page_title, what_to_see):
        """Update the GUI with current browser state"""
        self.continue_pressed = False
        
        # Update all labels
        self.url_label.config(text=url)
        self.page_title_label.config(text=page_title)
        
        # Update text area
        self.see_text.delete(1.0, tk.END)
        self.see_text.insert(1.0, what_to_see)
        
        # Update status
        self.status_label.config(text="Click CONTINUE when ready")
        
        # Enable continue button
        self.continue_button.config(state='normal', bg='orange')
        
        # Update display
        self.root.update()
        
    def continue_clicked(self):
        """Handle continue button click"""
        self.continue_pressed = True
        self.continue_button.config(state='disabled', bg='gray')
        self.status_label.config(text="Continuing...")
        self.root.update()
        
    def wait_for_continue(self):
        """Wait for user to click continue"""
        while not self.continue_pressed:
            self.root.update()
            time.sleep(0.1)
        return True
        
    def run(self):
        """Start the GUI"""
        self.root.mainloop()

# Global GUI instance
gui = None

def get_gui():
    """Get GUI instance, start if needed"""
    global gui
    if gui is None:
        gui = SimpleScreenTracker()
        # Start GUI in separate thread
        gui_thread = threading.Thread(target=gui.run, daemon=True)
        gui_thread.start()
        time.sleep(1)  # Let GUI start
    return gui

def describe_page(url, title):
    """Describe what should be on this page based on URL and title"""
    url_lower = url.lower()
    title_lower = title.lower()
    
    if 'login' in url_lower or 'login' in title_lower:
        return "LOGIN PAGE:\n- Email input field\n- Password input field\n- Login button\n- SnapEDA logo"
    
    elif 'home' in url_lower or 'dashboard' in url_lower:
        return "HOME/DASHBOARD PAGE:\n- Welcome message\n- Search box\n- Navigation menu\n- May have popup overlays"
    
    elif 'search' in url_lower:
        return "SEARCH PAGE:\n- Search results list\n- Part numbers and links\n- Filter options\n- Click on matching part"
    
    elif 'parts' in url_lower and 'view-part' in url_lower:
        return "PART DETAILS PAGE:\n- Component information\n- Tabs (Overview, 3D Model, etc.)\n- Download options\n- 3D model section"
    
    elif '404' in title_lower or 'not found' in title_lower:
        return "ERROR PAGE:\n- 404 Not Found\n- Part may not exist\n- Need to try search instead"
    
    else:
        return f"UNKNOWN PAGE:\n- URL: {url}\n- Title: {title}\n- Check what you see in browser"

if __name__ == "__main__":
    gui = SimpleScreenTracker()
    gui.run()
