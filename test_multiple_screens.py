#!/usr/bin/env python3
"""
Test multiple GUI screens to make sure they all appear
"""

from working_screen_gui import get_working_gui
import time

def test_multiple_screens():
    print("Testing multiple GUI screens...")
    
    # Get GUI
    gui = get_working_gui()
    print("GUI created")
    
    # Screen 1
    gui.update_screen("SnapEDA", 1, "First Screen", "http://test1.com", 
                     "SCREEN 1:\nThis is the first screen.\nClick CONTINUE to see screen 2.")
    print("Screen 1 shown - click CONTINUE")
    gui.wait_for_continue()
    print("Screen 1 continue clicked")
    
    # Screen 2  
    gui.update_screen("SnapEDA", 2, "Second Screen", "http://test2.com",
                     "SCREEN 2:\nThis is the second screen.\nClick CONTINUE to see screen 3.")
    print("Screen 2 shown - click CONTINUE")
    gui.wait_for_continue()
    print("Screen 2 continue clicked")
    
    # Screen 3
    gui.update_screen("SnapEDA", 3, "Third Screen", "http://test3.com",
                     "SCREEN 3:\nThis is the third screen.\nClick CONTINUE to see screen 4.")
    print("Screen 3 shown - click CONTINUE") 
    gui.wait_for_continue()
    print("Screen 3 continue clicked")
    
    # Screen 4
    gui.update_screen("SnapEDA", 4, "Fourth Screen", "http://test4.com",
                     "SCREEN 4:\nThis is the fourth screen.\nClick CONTINUE to finish test.")
    print("Screen 4 shown - click CONTINUE")
    gui.wait_for_continue()
    print("Screen 4 continue clicked")
    
    print("✅ All 4 screens worked! GUI is persistent.")

if __name__ == "__main__":
    test_multiple_screens()
