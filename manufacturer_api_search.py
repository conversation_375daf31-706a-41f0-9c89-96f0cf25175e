#!/usr/bin/env python3
"""
Manufacturer API Search Module
Searches manufacturer APIs for datasheet and product information
"""

import requests
import json
import os
from datetime import datetime
import time

class ManufacturerAPISearch:
    def __init__(self):
        self.session = requests.Session()
        self.credentials = {}
        self.base_urls = {
            'ti': 'https://api.ti.com',
            'analog_devices': 'https://api.analog.com',
            'microchip': 'https://api.microchip.com',
            'infineon': 'https://api.infineon.com',
            'nxp': 'https://api.nxp.com'
        }
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")
    
    def load_credentials(self):
        """Load manufacturer API credentials"""
        self.log("Loading manufacturer API credentials...")
        
        try:
            if os.path.exists('manufacturer_api_credentials.json'):
                with open('manufacturer_api_credentials.json', 'r') as f:
                    self.credentials = json.load(f)
                self.log("Manufacturer API credentials loaded", "SUCCESS")
                return True
            else:
                self.log("No manufacturer API credentials file found", "WARNING")
                return False
                
        except Exception as e:
            self.log(f"Error loading credentials: {e}", "ERROR")
            return False
    
    def search_ti_website(self, manufacturer, part_number):
        """Search Texas Instruments website for part information"""
        self.log(f"Searching TI website for {part_number}")

        try:
            # TI product search URL
            search_url = f"https://www.ti.com/product/{part_number}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }

            response = self.session.get(search_url, headers=headers, timeout=30)

            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for datasheet links
                datasheet_url = None
                product_url = search_url
                description = None
                package = None

                # Find datasheet link
                datasheet_links = soup.find_all('a', href=True)
                for link in datasheet_links:
                    href = link.get('href', '')
                    if 'datasheet' in href.lower() or '.pdf' in href.lower():
                        if href.startswith('http'):
                            datasheet_url = href
                        else:
                            datasheet_url = f"https://www.ti.com{href}"
                        break

                # Find description
                desc_elements = soup.find_all(['h1', 'h2', 'p'], class_=True)
                for elem in desc_elements:
                    if elem.text and len(elem.text) > 20 and len(elem.text) < 200:
                        description = elem.text.strip()
                        break

                # Find package information
                package_elements = soup.find_all(text=True)
                for text in package_elements:
                    if any(pkg in text.upper() for pkg in ['DIP', 'SOIC', 'QFP', 'BGA', 'TSSOP']):
                        package = text.strip()
                        break

                if datasheet_url or description:
                    self.log(f"TI website found product information", "SUCCESS")
                    return {
                        'success': True,
                        'source': 'Texas Instruments Website',
                        'datasheet_url': datasheet_url,
                        'product_url': product_url,
                        'description': description,
                        'package': package,
                        'manufacturer': 'Texas Instruments'
                    }
                else:
                    self.log("TI website: No product information found", "WARNING")
                    return None
            else:
                self.log(f"TI website error: {response.status_code}", "ERROR")
                return None

        except Exception as e:
            self.log(f"TI website search error: {e}", "ERROR")
            return None
    
    def search_analog_devices_website(self, manufacturer, part_number):
        """Search Analog Devices website for part information"""
        self.log(f"Searching Analog Devices website for {part_number}")

        try:
            # Analog Devices product search URL
            search_url = f"https://www.analog.com/en/products/{part_number}.html"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }

            response = self.session.get(search_url, headers=headers, timeout=30)

            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for datasheet links
                datasheet_url = None
                product_url = search_url
                description = None
                package = None

                # Find datasheet link
                datasheet_links = soup.find_all('a', href=True)
                for link in datasheet_links:
                    href = link.get('href', '')
                    text = link.text.lower()
                    if 'datasheet' in text or 'data sheet' in text or '.pdf' in href.lower():
                        if href.startswith('http'):
                            datasheet_url = href
                        else:
                            datasheet_url = f"https://www.analog.com{href}"
                        break

                # Find description
                desc_elements = soup.find_all(['h1', 'h2', 'p'], class_=True)
                for elem in desc_elements:
                    if elem.text and len(elem.text) > 20 and len(elem.text) < 200:
                        description = elem.text.strip()
                        break

                if datasheet_url or description:
                    self.log(f"Analog Devices website found product information", "SUCCESS")
                    return {
                        'success': True,
                        'source': 'Analog Devices Website',
                        'datasheet_url': datasheet_url,
                        'product_url': product_url,
                        'description': description,
                        'package': package,
                        'manufacturer': 'Analog Devices'
                    }
                else:
                    self.log("Analog Devices website: No product information found", "WARNING")
                    return None
            else:
                self.log(f"Analog Devices website error: {response.status_code}", "ERROR")
                return None

        except Exception as e:
            self.log(f"Analog Devices website search error: {e}", "ERROR")
            return None
    
    def search_manufacturer_api(self, manufacturer, part_number):
        """Search appropriate manufacturer API based on manufacturer name"""
        self.log(f"Starting manufacturer API search for {manufacturer} {part_number}")
        
        # Load credentials if not already loaded
        if not self.credentials:
            self.load_credentials()
        
        manufacturer_lower = manufacturer.lower()
        
        # Route to appropriate search based on manufacturer
        if 'texas instruments' in manufacturer_lower or 'ti' in manufacturer_lower:
            return self.search_ti_website(manufacturer, part_number)
        elif 'analog devices' in manufacturer_lower or 'adi' in manufacturer_lower:
            return self.search_analog_devices_website(manufacturer, part_number)
        elif 'microchip' in manufacturer_lower:
            self.log("Microchip API not yet implemented", "WARNING")
            return None
        elif 'infineon' in manufacturer_lower:
            self.log("Infineon API not yet implemented", "WARNING")
            return None
        elif 'nxp' in manufacturer_lower:
            self.log("NXP API not yet implemented", "WARNING")
            return None
        else:
            self.log(f"No API available for manufacturer: {manufacturer}", "WARNING")
            return None
    
    def create_sample_credentials_file(self):
        """Create a sample credentials file for manufacturer APIs"""
        sample_credentials = {
            "ti": {
                "api_key": "YOUR_TI_API_KEY_HERE",
                "base_url": "https://api.ti.com",
                "status": "inactive",
                "notes": "Get API key from https://api-portal.ti.com/"
            },
            "analog_devices": {
                "api_key": "YOUR_ADI_API_KEY_HERE",
                "base_url": "https://api.analog.com",
                "status": "inactive",
                "notes": "Get API key from Analog Devices developer portal"
            },
            "microchip": {
                "api_key": "YOUR_MICROCHIP_API_KEY_HERE",
                "base_url": "https://api.microchip.com",
                "status": "inactive",
                "notes": "Get API key from Microchip developer portal"
            }
        }
        
        try:
            with open('manufacturer_api_credentials.json', 'w') as f:
                json.dump(sample_credentials, f, indent=2)
            self.log("Created sample manufacturer API credentials file", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating credentials file: {e}", "ERROR")
            return False

def main():
    """Test the manufacturer API search"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test manufacturer API search')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    searcher = ManufacturerAPISearch()
    
    # Create sample credentials file if it doesn't exist
    if not os.path.exists('manufacturer_api_credentials.json'):
        searcher.create_sample_credentials_file()
        print("\n📝 Created sample credentials file: manufacturer_api_credentials.json")
        print("   Please add your actual API keys to test manufacturer APIs")
    
    result = searcher.search_manufacturer_api(args.manufacturer, args.part_number)
    
    if result:
        print(f"\n🎉 SUCCESS: Found part information!")
        print(f"   Source: {result['source']}")
        print(f"   Datasheet URL: {result.get('datasheet_url', 'Not available')}")
        print(f"   Product URL: {result.get('product_url', 'Not available')}")
        print(f"   Description: {result.get('description', 'Not available')}")
        print(f"   Package: {result.get('package', 'Not available')}")
    else:
        print(f"\n💥 FAILED: Could not find part information via manufacturer API")

if __name__ == "__main__":
    main()
