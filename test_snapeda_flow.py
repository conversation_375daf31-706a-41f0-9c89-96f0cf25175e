#!/usr/bin/env python3
"""
Test the correct SnapEDA login flow:
1. Click "Log In" link (first time)
2. Enter email and password
3. Click "Log In" button (second time)
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def test_snapeda_flow():
    print("🔍 Testing SnapEDA login flow...")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Go to SnapEDA homepage
        print("\n🔸 STEP 1: Loading SnapEDA homepage...")
        driver.get("https://www.snapeda.com/")
        time.sleep(3)
        print(f"✅ Homepage loaded: {driver.current_url}")
        
        # Step 2: Click FIRST "Log In" link
        print("\n🔸 STEP 2: Looking for FIRST 'Log In' link...")
        login_link = None
        
        # Look for login link
        login_selectors = [
            "//a[contains(text(), 'Log In')]",
            "//a[contains(@href, '/account/login')]"
        ]
        
        for selector in login_selectors:
            try:
                login_link = driver.find_element(By.XPATH, selector)
                if login_link.is_displayed():
                    text = login_link.text.strip()
                    href = login_link.get_attribute('href')
                    print(f"✅ Found FIRST login link: '{text}' -> {href}")
                    break
            except:
                continue
        
        if not login_link:
            print("❌ No login link found")
            return False
        
        # Click first login link
        print("🔸 Clicking FIRST 'Log In' link...")
        login_link.click()
        time.sleep(3)
        print(f"✅ After first click: {driver.current_url}")
        
        # Step 3: Fill email and password
        print("\n🔸 STEP 3: Filling email and password...")
        
        # Find and fill email
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.clear()
        email_field.send_keys("<EMAIL>")
        print("✅ Email filled")
        
        # Find and fill password
        password_field = driver.find_element(By.CSS_SELECTOR, "#id_password")
        password_field.clear()
        password_field.send_keys("Lennyai123#")
        print("✅ Password filled")
        
        # Step 4: Click SECOND "Log In" button (submit)
        print("\n🔸 STEP 4: Looking for SECOND 'Log In' button...")
        
        # Look for submit button
        submit_button = None
        submit_selectors = [
            "//button[contains(text(), 'Log In')]",
            "button[type='submit']",
            "input[type='submit']"
        ]
        
        for selector in submit_selectors:
            try:
                if selector.startswith("//"):
                    submit_button = driver.find_element(By.XPATH, selector)
                else:
                    submit_button = driver.find_element(By.CSS_SELECTOR, selector)
                
                if submit_button.is_displayed() and submit_button.is_enabled():
                    btn_text = submit_button.text or submit_button.get_attribute('value')
                    print(f"✅ Found SECOND login button: '{btn_text}'")
                    break
            except:
                continue
        
        if not submit_button:
            print("❌ No submit button found")
            return False
        
        # Click second login button
        print("🔸 Clicking SECOND 'Log In' button...")
        submit_button.click()
        time.sleep(5)
        print(f"✅ After submit: {driver.current_url}")
        
        # Step 5: Check if login worked
        print("\n🔸 STEP 5: Checking login result...")
        if "login" not in driver.current_url.lower():
            print("✅ LOGIN SUCCESS - redirected away from login page!")
            return True
        else:
            print("❌ Still on login page - login failed")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        print("\n🎯 Test complete - browser will stay open")
        input("Press Enter to close...")
        driver.quit()

if __name__ == "__main__":
    success = test_snapeda_flow()
    if success:
        print("✅ SnapEDA login flow works!")
    else:
        print("❌ SnapEDA login flow failed!")
