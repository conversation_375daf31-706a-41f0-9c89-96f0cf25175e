#!/usr/bin/env python3
"""
Test UltraLibrarian search functionality
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def test_ultralibrarian_search():
    """Test UltraLibrarian search without login"""
    print("🔍 Testing UltraLibrarian Search")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Load homepage
        print("🔸 Loading UltraLibrarian homepage...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        
        print(f"✅ Page loaded: {driver.title}")
        print(f"   URL: {driver.current_url}")
        
        # Step 2: Look for search elements
        print("\n🔸 Looking for search elements...")
        
        # Try different search selectors
        selectors = [
            "input[placeholder*='search' i]",
            "input[type='search']",
            "input[name*='search' i]",
            "input[id*='search' i]",
            "input[class*='search' i]",
            ".search input",
            "#search",
            "input[type='text']"
        ]
        
        search_elements = []
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"   ✅ Found {len(elements)} elements with selector: {selector}")
                    for i, elem in enumerate(elements):
                        try:
                            visible = elem.is_displayed()
                            enabled = elem.is_enabled()
                            placeholder = elem.get_attribute('placeholder') or ''
                            name = elem.get_attribute('name') or ''
                            id_attr = elem.get_attribute('id') or ''
                            
                            print(f"      {i+1}. Visible: {visible}, Enabled: {enabled}")
                            print(f"         Placeholder: '{placeholder}'")
                            print(f"         Name: '{name}', ID: '{id_attr}'")
                            
                            if visible and enabled:
                                search_elements.append((elem, selector, placeholder))
                        except Exception as e:
                            print(f"      {i+1}. Error reading element: {e}")
                else:
                    print(f"   ❌ No elements found with selector: {selector}")
            except Exception as e:
                print(f"   ❌ Error with selector {selector}: {e}")
        
        # Step 3: Try to search
        if search_elements:
            print(f"\n🔸 Found {len(search_elements)} usable search elements")
            
            # Use the first visible/enabled search element
            search_elem, selector_used, placeholder = search_elements[0]
            print(f"   Using element with selector: {selector_used}")
            print(f"   Placeholder: '{placeholder}'")
            
            try:
                print("\n🔸 Testing search with 'LM358N'...")
                search_elem.clear()
                search_elem.send_keys('LM358N')
                search_elem.send_keys(Keys.RETURN)
                time.sleep(5)
                
                print(f"✅ Search submitted")
                print(f"   Current URL: {driver.current_url}")
                print(f"   Page title: {driver.title}")
                
                # Check for results
                results = driver.find_elements(By.TAG_NAME, "a")
                result_count = 0
                for link in results:
                    try:
                        text = link.text.strip()
                        if text and 'LM358' in text.upper():
                            result_count += 1
                            if result_count <= 5:  # Show first 5 results
                                print(f"   Result {result_count}: {text}")
                    except:
                        continue
                
                if result_count > 0:
                    print(f"✅ Found {result_count} potential results")
                    return True
                else:
                    print("⚠️ No results found")
                    return False
                    
            except Exception as e:
                print(f"❌ Search failed: {e}")
                return False
        else:
            print("❌ No usable search elements found")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        driver.quit()
        print("\n🔒 Browser closed")

if __name__ == "__main__":
    success = test_ultralibrarian_search()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 UltraLibrarian search is working!")
    else:
        print("🚫 UltraLibrarian search has issues")
