#!/usr/bin/env python3
"""
SnapEDA GUI Test - Shows screen then asks user if it worked
"""

import os
import time
import tkinter as tk
from tkinter import messagebox
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def ask_user_gui(question):
    """Show GUI dialog asking user a question"""
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    result = messagebox.askyesno("SnapEDA Test", question)
    root.destroy()
    return result

def snapeda_gui_test():
    """Test SnapEDA with GUI feedback"""
    print("🚀 SnapEDA GUI Test Starting...")
    
    # Create visible Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    
    try:
        # Step 1: Go to login page
        print("1. Going to SnapEDA login page...")
        driver.get("https://www.snapeda.com/account/login/")
        time.sleep(3)
        
        # Ask user if login page appeared
        if not ask_user_gui("Did the SnapEDA login page appear?"):
            print("❌ User says login page did not appear")
            driver.quit()
            return
        
        # Step 2: Login
        print("2. Logging in...")
        try:
            email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
            password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")

            email_field.send_keys("<EMAIL>")
            password_field.send_keys("Lennyai123#")

            # Try multiple login button selectors
            login_selectors = [
                "//button[contains(text(), 'Log in')]",
                "//input[@type='submit']",
                "//button[@type='submit']",
                "//input[@value='Log in']",
                "//button[contains(@class, 'btn')]"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = driver.find_element(By.XPATH, selector)
                    if login_button.is_displayed():
                        print(f"✅ Found login button with selector: {selector}")
                        break
                except:
                    continue

            if not login_button:
                raise Exception("No login button found with any selector")
            login_button.click()
            time.sleep(5)
            print("✅ Login attempt completed")
        except Exception as e:
            error_msg = f"Login failed with error: {e}"
            print(f"❌ {error_msg}")
            ask_user_gui(f"Login Error: {error_msg}")
            driver.quit()
            return
        
        # Ask user if login worked
        if not ask_user_gui("Did the login work? (Are you now logged in?)"):
            print("❌ User says login failed")
            driver.quit()
            return
        
        # Step 3: Go to part page
        print("3. Going to part page...")
        part_url = "https://www.snapeda.com/parts/GCM155R71H104KE02D/Murata/view-part/"
        driver.get(part_url)
        time.sleep(3)
        
        # Ask user if part page appeared
        if not ask_user_gui("Did the part page appear with component info?"):
            print("❌ User says part page did not appear")
            driver.quit()
            return
        
        # Step 4: Click 3D Model tab
        print("4. Clicking 3D Model tab...")
        try:
            three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
            three_d_tab.click()
            time.sleep(3)
            print("✅ Clicked 3D Model tab")
        except Exception as e:
            print(f"❌ Could not click 3D Model tab: {e}")
        
        # Ask user if 3D model content appeared
        if not ask_user_gui("Did the 3D Model content appear after clicking the tab?"):
            print("❌ User says 3D Model content did not appear")
            print("🔧 Need to fix 3D Model tab clicking...")
            driver.quit()
            return
        
        # Step 5: Look for download button
        print("5. Looking for download button...")
        time.sleep(2)
        
        # Ask user what they see
        if ask_user_gui("Do you see a download button for the 3D model?"):
            print("✅ User sees download button!")
            print("🎉 SnapEDA test successful!")
        else:
            print("❌ User does not see download button")
            print("🔧 Need to fix download button detection...")
        
        # Keep browser open for final inspection
        ask_user_gui("Click OK to close the browser")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        ask_user_gui(f"Error occurred: {e}\nClick OK to close browser")
    finally:
        driver.quit()
        print("🏁 Test complete")

if __name__ == "__main__":
    snapeda_gui_test()
