#!/usr/bin/env python3
"""
Dialog Testing Program - Test ALL dialogs for multi-monitor positioning
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import sys
import os

class DialogTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Dialog Position Tester")
        self.root.geometry("600x800")
        self.root.configure(bg='#f0f0f0')
        
        # Track window position
        self.root.bind('<Configure>', self._on_window_configure)
        
        self.create_ui()
        
    def _on_window_configure(self, event):
        """Track main window position changes"""
        if event.widget == self.root:
            try:
                x = self.root.winfo_rootx()
                y = self.root.winfo_rooty()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                print(f"🏠 MAIN WINDOW: Position ({x}, {y}) Size {width}x{height}")
            except:
                pass
    
    def create_ui(self):
        """Create test UI"""
        title = tk.Label(self.root, text="DIALOG POSITION TESTER", 
                        font=('Arial', 16, 'bold'), bg='#f0f0f0')
        title.pack(pady=10)
        
        instruction = tk.Label(self.root, 
                              text="1. Move this window to your second screen\n2. Click buttons to test dialogs\n3. Report which dialogs appear on wrong screen",
                              font=('Arial', 10), bg='#f0f0f0', justify='left')
        instruction.pack(pady=10)
        
        # Test buttons
        self.create_test_buttons()
        
    def create_test_buttons(self):
        """Create test buttons for all dialog types"""
        frame = tk.Frame(self.root, bg='#f0f0f0')
        frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        tests = [
            ("1. Error Dialog", self.test_error_dialog),
            ("2. Info Dialog", self.test_info_dialog),
            ("3. Warning Dialog", self.test_warning_dialog),
            ("4. Yes/No Dialog", self.test_yesno_dialog),
            ("5. Yes/No/Cancel Dialog", self.test_yesnocancel_dialog),
            ("6. Input Dialog (askstring)", self.test_askstring_dialog),
            ("7. File Dialog", self.test_file_dialog),
            ("8. Custom Toplevel Dialog", self.test_custom_dialog),
            ("9. Excel Column Mapping", self.test_column_mapping),
            ("10. Part Number Correction", self.test_part_correction),
            ("11. Manual Entry Dialog", self.test_manual_entry),
            ("12. Alternative Parts Dialog", self.test_alternative_parts),
        ]
        
        for i, (text, command) in enumerate(tests):
            btn = tk.Button(frame, text=text, command=command,
                           font=('Arial', 10), width=30, height=2)
            btn.pack(pady=5)
    
    def _show_messagebox_on_main(self, msg_type, title, message, **kwargs):
        """Helper method with debug output"""
        print(f"\n📢 TESTING: {msg_type} - {title}")
        
        # Get main window position for debugging
        try:
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()
            print(f"📢 Main window at ({main_x}, {main_y}) size {main_width}x{main_height}")
        except Exception as e:
            print(f"📢 Could not get main window position: {e}")
        
        print(f"📢 Using parent=self.root for messagebox positioning")
        
        # Use the main window as parent
        if msg_type == 'info':
            result = messagebox.showinfo(title, message, parent=self.root, **kwargs)
        elif msg_type == 'error':
            result = messagebox.showerror(title, message, parent=self.root, **kwargs)
        elif msg_type == 'warning':
            result = messagebox.showwarning(title, message, parent=self.root, **kwargs)
        elif msg_type == 'yesno':
            result = messagebox.askyesno(title, message, parent=self.root, **kwargs)
        elif msg_type == 'yesnocancel':
            result = messagebox.askyesnocancel(title, message, parent=self.root, **kwargs)
        else:
            result = messagebox.showinfo(title, message, parent=self.root, **kwargs)
            
        print(f"📢 Messagebox result: {result}")
        return result
    
    def _show_askstring_on_main(self, title, prompt, **kwargs):
        """Helper for askstring with debug"""
        print(f"\n💬 TESTING: askstring - {title}")
        
        try:
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            print(f"💬 Main window at ({main_x}, {main_y})")
        except Exception as e:
            print(f"💬 Could not get main window position: {e}")
        
        print(f"💬 Using parent=self.root for askstring")
        result = simpledialog.askstring(title, prompt, parent=self.root, **kwargs)
        print(f"💬 Askstring result: {result}")
        return result
    
    def _position_dialog_on_main_window(self, dialog, width, height):
        """Position dialog centered on main window"""
        print(f"\n🔧 TESTING: Custom dialog positioning")
        print(f"🔧 Dialog size: {width}x{height}")
        
        try:
            dialog.transient(self.root)
            dialog.grab_set()
            
            dialog.update_idletasks()
            self.root.update_idletasks()
            
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()
            
            print(f"🔧 Main window position: ({main_x}, {main_y})")
            print(f"🔧 Main window size: {main_width}x{main_height}")
            
            x = main_x + (main_width - width) // 2
            y = main_y + (main_height - height) // 2
            
            print(f"🔧 Calculated dialog position: ({x}, {y})")
            
            geometry_str = f"{width}x{height}+{x}+{y}"
            dialog.geometry(geometry_str)
            print(f"🔧 Set dialog geometry: {geometry_str}")
            
            dialog.lift()
            dialog.focus_force()
            
            dialog.update_idletasks()
            final_x = dialog.winfo_rootx()
            final_y = dialog.winfo_rooty()
            print(f"🔧 Final dialog position: ({final_x}, {final_y})")
            
        except Exception as e:
            print(f"❌ Dialog positioning failed: {e}")
            dialog.geometry(f"{width}x{height}")
    
    # Test methods
    def test_error_dialog(self):
        self._show_messagebox_on_main("error", "Test Error", "This is a test error dialog")
    
    def test_info_dialog(self):
        self._show_messagebox_on_main("info", "Test Info", "This is a test info dialog")
    
    def test_warning_dialog(self):
        self._show_messagebox_on_main("warning", "Test Warning", "This is a test warning dialog")
    
    def test_yesno_dialog(self):
        result = self._show_messagebox_on_main("yesno", "Test Yes/No", "This is a test yes/no dialog")
        print(f"Result: {result}")
    
    def test_yesnocancel_dialog(self):
        result = self._show_messagebox_on_main("yesnocancel", "Test Yes/No/Cancel", "This is a test yes/no/cancel dialog")
        print(f"Result: {result}")
    
    def test_askstring_dialog(self):
        result = self._show_askstring_on_main("Test Input", "Enter some text:")
        print(f"Input result: {result}")
    
    def test_file_dialog(self):
        print(f"\n📁 TESTING: File dialog")
        try:
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            print(f"📁 Main window at ({main_x}, {main_y})")
        except:
            pass
        
        result = filedialog.askopenfilename(parent=self.root, title="Test File Dialog")
        print(f"📁 File dialog result: {result}")
    
    def test_custom_dialog(self):
        dialog = tk.Toplevel(self.root)
        dialog.title("Test Custom Dialog")
        dialog.configure(bg='#f0f0f0')
        
        label = tk.Label(dialog, text="This is a custom dialog", bg='#f0f0f0')
        label.pack(pady=20, padx=20)
        
        btn = tk.Button(dialog, text="Close", command=dialog.destroy)
        btn.pack(pady=10)
        
        self._position_dialog_on_main_window(dialog, 300, 150)
    
    def test_column_mapping(self):
        """Test Excel column mapping dialog"""
        print(f"\n📊 TESTING: Excel Column Mapping Dialog")
        
        dialog = tk.Toplevel(self.root)
        dialog.title("Column Mapping Test")
        dialog.configure(bg='#f0f0f0')
        
        label = tk.Label(dialog, text="Excel Column Mapping Dialog Test", bg='#f0f0f0')
        label.pack(pady=20, padx=20)
        
        btn = tk.Button(dialog, text="Close", command=dialog.destroy)
        btn.pack(pady=10)
        
        self._position_dialog_on_main_window(dialog, 400, 200)
    
    def test_part_correction(self):
        """Test part number correction dialog"""
        print(f"\n⚠️ TESTING: Part Number Correction Dialog")
        
        dialog = tk.Toplevel(self.root)
        dialog.title("Part Number Not Found")
        dialog.configure(bg='#f0f0f0')
        
        label = tk.Label(dialog, text="Part Number Correction Dialog Test", bg='#f0f0f0')
        label.pack(pady=20, padx=20)
        
        btn = tk.Button(dialog, text="Close", command=dialog.destroy)
        btn.pack(pady=10)
        
        self._position_dialog_on_main_window(dialog, 500, 350)
    
    def test_manual_entry(self):
        """Test manual entry dialog"""
        print(f"\n📝 TESTING: Manual Entry Dialog")
        
        dialog = tk.Toplevel(self.root)
        dialog.title("Manual Entry Test")
        dialog.configure(bg='#f0f0f0')
        
        label = tk.Label(dialog, text="Manual Entry Dialog Test", bg='#f0f0f0')
        label.pack(pady=20, padx=20)
        
        btn = tk.Button(dialog, text="Close", command=dialog.destroy)
        btn.pack(pady=10)
        
        self._position_dialog_on_main_window(dialog, 600, 400)
    
    def test_alternative_parts(self):
        """Test alternative parts dialog"""
        result = self._show_messagebox_on_main("yesno", "Alternate Part Numbers Found", 
                                             "Original part number 'TEST123' not found.\n\nFound alternate: TEST123-ALT\n\nAccept alternate?")
        print(f"Alternative parts result: {result}")
    
    def run(self):
        """Run the tester"""
        print("🚀 Dialog Position Tester Started")
        print("📍 Move the main window to your second screen and test dialogs")
        self.root.mainloop()

if __name__ == "__main__":
    tester = DialogTester()
    tester.run()
