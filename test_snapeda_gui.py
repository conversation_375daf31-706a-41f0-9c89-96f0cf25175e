#!/usr/bin/env python3
"""
Test SnapEDA with GUI - This will show you each screen step by step
"""

from snapeda_3d_finder import SnapEDA3DFinder
from working_screen_gui import get_working_gui

def test_snapeda_with_gui():
    """Test SnapEDA finder with GUI screens"""
    print("🧪 Testing SnapEDA with GUI screens...")
    
    # Create GUI first
    gui = get_working_gui()
    print("✅ GUI created")
    
    # Create SnapEDA finder
    finder = SnapEDA3DFinder()
    finder.gui = gui  # Assign GUI to finder
    print("✅ SnapEDA finder created with GUI")
    
    # Show initial screen
    gui.update_screen("SnapEDA", 0, "Starting Test", 
                     "about:blank", 
                     "STARTING SNAPEDA TEST:\n\n🎯 WHAT WE'RE TESTING:\n   - SnapEDA 3D model finder\n   - GUI screen tracking\n   - Smart waits instead of delays\n\n👀 YOU SHOULD SEE:\n   - This GUI window\n   - Chrome browser will open next\n   - Each step will be shown here\n\n🎯 WHAT HAPPENS NEXT:\n   → Chrome browser opens\n   → Navigate to SnapEDA homepage\n   → Show you each screen step by step")
    
    print("👆 Click CONTINUE to start the SnapEDA test...")
    gui.wait_for_continue()
    
    # Now start the actual SnapEDA process
    print("🚀 Starting SnapEDA finder...")
    try:
        result = finder.search_and_download("Texas Instruments", "LM358N", silent=False)
        if result:
            print(f"✅ SUCCESS: {result}")
        else:
            print("❌ FAILED: No result")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    print("🎉 Test completed!")

if __name__ == "__main__":
    test_snapeda_with_gui()
