#!/usr/bin/env python3
"""
Component Finder GUI - Visual Studio Style Interface

Features:
- Visual Studio-like dark theme and layout
- Dockable panels and professional appearance
- Menu system and toolbar
- Status bar with progress indicators
- Tabbed interface for multiple searches
- Tree view for organizing results
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog, font, Menu
import threading
import sys
import os

# Import the original ComponentFinderGUI class
sys.path.append('.')
from component_finder import ComponentFinder<PERSON><PERSON> as OriginalComponentFinder

class VSStyleComponentFinder:
    def __init__(self, root):
        self.root = root
        self.setup_vs_theme()
        self.setup_vs_layout()
        
        # Create the original component finder instance for functionality
        self.component_finder = OriginalComponentFinder(tk.Toplevel())
        self.component_finder.root.withdraw()  # Hide the original window
        
        # Connect the original functionality to our VS-style interface
        self.connect_functionality()
        
    def setup_vs_theme(self):
        """Setup Visual Studio-like dark theme"""
        self.root.title("Component Finder - Visual Studio Style")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#2d2d30')
        
        # VS Color scheme
        self.colors = {
            'bg_dark': '#2d2d30',           # Main background
            'bg_medium': '#3e3e42',         # Panel backgrounds
            'bg_light': '#4d4d50',          # Lighter panels
            'fg_primary': '#ffffff',        # Primary text
            'fg_secondary': '#cccccc',      # Secondary text
            'accent_blue': '#007acc',       # VS Blue accent
            'accent_green': '#608b4e',      # Success green
            'accent_red': '#f44747',        # Error red
            'accent_orange': '#ff8c00',     # Warning orange
            'border': '#464647',            # Border color
            'selection': '#094771'          # Selection color
        }
        
        # Configure ttk styles for VS theme
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure styles
        self.configure_vs_styles()
        
    def configure_vs_styles(self):
        """Configure ttk styles to match Visual Studio"""
        
        # Frame styles
        self.style.configure('VS.TFrame', 
                           background=self.colors['bg_medium'],
                           borderwidth=1,
                           relief='solid')
        
        # Label styles
        self.style.configure('VS.TLabel',
                           background=self.colors['bg_medium'],
                           foreground=self.colors['fg_primary'],
                           font=('Segoe UI', 9))
        
        self.style.configure('VSTitle.TLabel',
                           background=self.colors['bg_medium'],
                           foreground=self.colors['fg_primary'],
                           font=('Segoe UI', 11, 'bold'))
        
        # Button styles
        self.style.configure('VS.TButton',
                           background=self.colors['bg_light'],
                           foreground=self.colors['fg_primary'],
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))
        
        self.style.map('VS.TButton',
                      background=[('active', self.colors['accent_blue']),
                                ('pressed', self.colors['selection'])])
        
        # Entry styles
        self.style.configure('VS.TEntry',
                           fieldbackground=self.colors['bg_light'],
                           foreground=self.colors['fg_primary'],
                           borderwidth=1,
                           insertcolor=self.colors['fg_primary'])
        
        # Notebook (tabs) styles
        self.style.configure('VS.TNotebook',
                           background=self.colors['bg_medium'],
                           borderwidth=0)
        
        self.style.configure('VS.TNotebook.Tab',
                           background=self.colors['bg_dark'],
                           foreground=self.colors['fg_secondary'],
                           padding=[12, 8],
                           font=('Segoe UI', 9))
        
        self.style.map('VS.TNotebook.Tab',
                      background=[('selected', self.colors['bg_medium']),
                                ('active', self.colors['bg_light'])],
                      foreground=[('selected', self.colors['fg_primary'])])
        
        # Treeview styles
        self.style.configure('VS.Treeview',
                           background=self.colors['bg_light'],
                           foreground=self.colors['fg_primary'],
                           fieldbackground=self.colors['bg_light'],
                           borderwidth=0,
                           font=('Segoe UI', 9))
        
        self.style.configure('VS.Treeview.Heading',
                           background=self.colors['bg_medium'],
                           foreground=self.colors['fg_primary'],
                           font=('Segoe UI', 9, 'bold'))

        # Checkbutton styles
        self.style.configure('VS.TCheckbutton',
                           background=self.colors['bg_medium'],
                           foreground=self.colors['fg_primary'],
                           font=('Segoe UI', 9))
        
    def setup_vs_layout(self):
        """Setup Visual Studio-like layout with panels"""
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create main container
        main_container = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_container.pack(fill='both', expand=True)
        
        # Create toolbar
        self.create_toolbar(main_container)
        
        # Create main content area with panels
        content_frame = tk.Frame(main_container, bg=self.colors['bg_dark'])
        content_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Create paned window for resizable panels
        self.main_paned = tk.PanedWindow(content_frame, 
                                        orient='horizontal',
                                        bg=self.colors['bg_dark'],
                                        sashwidth=4,
                                        sashrelief='flat')
        self.main_paned.pack(fill='both', expand=True)
        
        # Create left panel (Explorer/Properties)
        self.create_left_panel()
        
        # Create center panel (Main work area)
        self.create_center_panel()
        
        # Create right panel (Output/Results)
        self.create_right_panel()
        
        # Create status bar
        self.create_status_bar(main_container)
        
    def create_menu_bar(self):
        """Create Visual Studio-style menu bar"""
        menubar = Menu(self.root, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Search", command=self.new_search)
        file_menu.add_command(label="Load Excel File", command=self.load_excel)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Edit menu
        edit_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Clear Results", command=self.clear_results)
        edit_menu.add_command(label="Settings", command=self.show_settings)
        
        # View menu
        view_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Toggle Explorer", command=self.toggle_explorer)
        view_menu.add_command(label="Toggle Output", command=self.toggle_output)
        
        # Tools menu
        tools_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Batch Search", command=self.batch_search)
        tools_menu.add_command(label="Export Results", command=self.export_results)
        
        # Help menu
        help_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
    def create_toolbar(self, parent):
        """Create Visual Studio-style toolbar"""
        toolbar_frame = tk.Frame(parent, bg=self.colors['bg_medium'], height=40)
        toolbar_frame.pack(fill='x', padx=2, pady=(2, 0))
        toolbar_frame.pack_propagate(False)
        
        # Toolbar buttons
        buttons = [
            ("🔍", "Search Component", self.start_search),
            ("📊", "Load Excel", self.load_excel),
            ("🔄", "Refresh", self.refresh_search),
            ("💾", "Save Results", self.save_results),
            ("⚙️", "Settings", self.show_settings),
        ]
        
        for icon, tooltip, command in buttons:
            btn = tk.Button(toolbar_frame, 
                          text=icon,
                          command=command,
                          bg=self.colors['bg_light'],
                          fg=self.colors['fg_primary'],
                          relief='flat',
                          padx=8,
                          pady=4,
                          font=('Segoe UI', 10))
            btn.pack(side='left', padx=2, pady=4)
            
            # Add tooltip (simplified)
            self.create_tooltip(btn, tooltip)
            
    def create_tooltip(self, widget, text):
        """Create a simple tooltip for widgets"""
        def on_enter(event):
            # Simple tooltip implementation
            pass
        def on_leave(event):
            pass
        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def create_left_panel(self):
        """Create left panel with Explorer and Properties"""
        left_frame = ttk.Frame(self.main_paned, style='VS.TFrame', width=300)
        self.main_paned.add(left_frame, minsize=250)

        # Create notebook for tabbed panels
        left_notebook = ttk.Notebook(left_frame, style='VS.TNotebook')
        left_notebook.pack(fill='both', expand=True, padx=2, pady=2)

        # Explorer tab
        explorer_frame = ttk.Frame(left_notebook, style='VS.TFrame')
        left_notebook.add(explorer_frame, text='Explorer')

        # Explorer title
        ttk.Label(explorer_frame, text='Component Explorer', style='VSTitle.TLabel').pack(pady=5)

        # Search history tree
        self.search_tree = ttk.Treeview(explorer_frame, style='VS.Treeview', height=15)
        self.search_tree.pack(fill='both', expand=True, padx=5, pady=5)

        # Configure tree columns
        self.search_tree['columns'] = ('Status', 'Files')
        self.search_tree.column('#0', width=200, minwidth=150)
        self.search_tree.column('Status', width=80, minwidth=60)
        self.search_tree.column('Files', width=60, minwidth=40)

        self.search_tree.heading('#0', text='Component', anchor='w')
        self.search_tree.heading('Status', text='Status', anchor='center')
        self.search_tree.heading('Files', text='Files', anchor='center')

        # Properties tab
        properties_frame = ttk.Frame(left_notebook, style='VS.TFrame')
        left_notebook.add(properties_frame, text='Properties')

        # Properties content
        ttk.Label(properties_frame, text='Component Properties', style='VSTitle.TLabel').pack(pady=5)

        # Properties text area
        self.properties_text = scrolledtext.ScrolledText(
            properties_frame,
            bg=self.colors['bg_light'],
            fg=self.colors['fg_primary'],
            insertbackground=self.colors['fg_primary'],
            font=('Consolas', 9),
            height=20
        )
        self.properties_text.pack(fill='both', expand=True, padx=5, pady=5)

    def create_center_panel(self):
        """Create center panel with main search interface"""
        center_frame = ttk.Frame(self.main_paned, style='VS.TFrame')
        self.main_paned.add(center_frame, minsize=600)

        # Create notebook for tabbed searches
        self.center_notebook = ttk.Notebook(center_frame, style='VS.TNotebook')
        self.center_notebook.pack(fill='both', expand=True, padx=2, pady=2)

        # Main search tab
        self.create_main_search_tab()

        # Add button to create new search tabs
        new_tab_frame = tk.Frame(center_frame, bg=self.colors['bg_medium'], height=30)
        new_tab_frame.pack(fill='x', pady=(0, 2))
        new_tab_frame.pack_propagate(False)

        ttk.Button(new_tab_frame, text='+ New Search',
                  style='VS.TButton',
                  command=self.new_search_tab).pack(side='left', padx=5, pady=2)

    def create_main_search_tab(self):
        """Create the main search tab"""
        search_frame = ttk.Frame(self.center_notebook, style='VS.TFrame')
        self.center_notebook.add(search_frame, text='Component Search')

        # Search input section
        input_frame = ttk.Frame(search_frame, style='VS.TFrame')
        input_frame.pack(fill='x', padx=10, pady=10)

        # Manufacturer input
        ttk.Label(input_frame, text='Manufacturer:', style='VS.TLabel').grid(row=0, column=0, sticky='w', pady=2)
        self.manufacturer_var = tk.StringVar(value="Texas Instruments")
        self.manufacturer_entry = ttk.Entry(input_frame, textvariable=self.manufacturer_var,
                                          style='VS.TEntry', width=30)
        self.manufacturer_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=2)

        # Part number input
        ttk.Label(input_frame, text='Part Number:', style='VS.TLabel').grid(row=1, column=0, sticky='w', pady=2)
        self.part_number_var = tk.StringVar(value="LP590722QDQNRQ1")
        self.part_number_entry = ttk.Entry(input_frame, textvariable=self.part_number_var,
                                         style='VS.TEntry', width=30)
        self.part_number_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=2)

        # Configure grid weights
        input_frame.columnconfigure(1, weight=1)

        # Search button
        search_btn_frame = ttk.Frame(search_frame, style='VS.TFrame')
        search_btn_frame.pack(fill='x', padx=10, pady=5)

        self.search_button = ttk.Button(search_btn_frame, text='🔍 Search Component',
                                       style='VS.TButton', command=self.start_search)
        self.search_button.pack(side='left')

        # Progress bar
        self.progress_var = tk.StringVar(value="Ready")
        self.progress_bar = ttk.Progressbar(search_btn_frame, mode='indeterminate')
        self.progress_bar.pack(side='right', padx=(10, 0))

        # Results section
        results_frame = ttk.Frame(search_frame, style='VS.TFrame')
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Results notebook
        self.results_notebook = ttk.Notebook(results_frame, style='VS.TNotebook')
        self.results_notebook.pack(fill='both', expand=True)

        # Files tab
        files_frame = ttk.Frame(self.results_notebook, style='VS.TFrame')
        self.results_notebook.add(files_frame, text='Files Found')

        # Files tree
        self.files_tree = ttk.Treeview(files_frame, style='VS.Treeview')
        self.files_tree.pack(fill='both', expand=True, padx=5, pady=5)

        self.files_tree['columns'] = ('Type', 'Size', 'Status')
        self.files_tree.column('#0', width=300, minwidth=200)
        self.files_tree.column('Type', width=100, minwidth=80)
        self.files_tree.column('Size', width=80, minwidth=60)
        self.files_tree.column('Status', width=100, minwidth=80)

        self.files_tree.heading('#0', text='File Name', anchor='w')
        self.files_tree.heading('Type', text='Type', anchor='center')
        self.files_tree.heading('Size', text='Size', anchor='center')
        self.files_tree.heading('Status', text='Status', anchor='center')

        # Details tab
        details_frame = ttk.Frame(self.results_notebook, style='VS.TFrame')
        self.results_notebook.add(details_frame, text='Component Details')

        # Details text area
        self.details_text = scrolledtext.ScrolledText(
            details_frame,
            bg=self.colors['bg_light'],
            fg=self.colors['fg_primary'],
            insertbackground=self.colors['fg_primary'],
            font=('Consolas', 9)
        )
        self.details_text.pack(fill='both', expand=True, padx=5, pady=5)

    def create_right_panel(self):
        """Create right panel with Output and Error List"""
        right_frame = ttk.Frame(self.main_paned, style='VS.TFrame', width=400)
        self.main_paned.add(right_frame, minsize=300)

        # Create notebook for output panels
        right_notebook = ttk.Notebook(right_frame, style='VS.TNotebook')
        right_notebook.pack(fill='both', expand=True, padx=2, pady=2)

        # Output tab
        output_frame = ttk.Frame(right_notebook, style='VS.TFrame')
        right_notebook.add(output_frame, text='Output')

        # Output text area (this will show the search progress)
        self.output_text = scrolledtext.ScrolledText(
            output_frame,
            bg=self.colors['bg_light'],
            fg=self.colors['fg_primary'],
            insertbackground=self.colors['fg_primary'],
            font=('Consolas', 9),
            state='disabled'
        )
        self.output_text.pack(fill='both', expand=True, padx=5, pady=5)

        # Error List tab
        error_frame = ttk.Frame(right_notebook, style='VS.TFrame')
        right_notebook.add(error_frame, text='Problems')

        # Error list tree
        self.error_tree = ttk.Treeview(error_frame, style='VS.Treeview')
        self.error_tree.pack(fill='both', expand=True, padx=5, pady=5)

        self.error_tree['columns'] = ('Type', 'Description')
        self.error_tree.column('#0', width=60, minwidth=50)
        self.error_tree.column('Type', width=80, minwidth=60)
        self.error_tree.column('Description', width=250, minwidth=200)

        self.error_tree.heading('#0', text='#', anchor='center')
        self.error_tree.heading('Type', text='Type', anchor='center')
        self.error_tree.heading('Description', text='Description', anchor='w')

        # Search Options tab
        options_frame = ttk.Frame(right_notebook, style='VS.TFrame')
        right_notebook.add(options_frame, text='Options')

        # Search options checkboxes
        ttk.Label(options_frame, text='Search Options', style='VSTitle.TLabel').pack(pady=5)

        self.search_options = {}
        options = [
            ('search_datasheet', 'Search for Datasheets', True),
            ('search_3d', 'Search for 3D Models', True),
            ('search_ultralibrarian', 'Search UltraLibrarian', True),
            ('search_snapmagic', 'Search SnapMagic', True),
            ('search_samacsys', 'Search SamacSys', True),
            ('auto_download', 'Auto Download Files', True),
            ('show_debug', 'Show Debug Info', False)
        ]

        for key, text, default in options:
            var = tk.BooleanVar(value=default)
            self.search_options[key] = var
            cb = ttk.Checkbutton(options_frame, text=text, variable=var, style='VS.TCheckbutton')
            cb.pack(anchor='w', padx=10, pady=2)

    def create_status_bar(self, parent):
        """Create Visual Studio-style status bar"""
        status_frame = tk.Frame(parent, bg=self.colors['accent_blue'], height=25)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # Status text
        self.status_var = tk.StringVar(value="Ready")
        status_label = tk.Label(status_frame,
                               textvariable=self.status_var,
                               bg=self.colors['accent_blue'],
                               fg='white',
                               font=('Segoe UI', 9))
        status_label.pack(side='left', padx=10, pady=2)

        # Progress indicator
        self.status_progress = ttk.Progressbar(status_frame, mode='determinate', length=200)
        self.status_progress.pack(side='right', padx=10, pady=2)

    def connect_functionality(self):
        """Connect the original component finder functionality to VS interface"""
        # Override the original GUI's add_comment method to display in our output
        original_add_comment = self.component_finder.add_comment

        def vs_add_comment(message):
            # Add to our VS-style output
            self.add_output_message(message)
            # Also call original for compatibility
            original_add_comment(message)

        self.component_finder.add_comment = vs_add_comment

        # Override status updates
        original_update_status = getattr(self.component_finder, 'update_status', lambda x: None)

        def vs_update_status(message):
            self.status_var.set(message)
            original_update_status(message)

        self.component_finder.update_status = vs_update_status

    def add_output_message(self, message):
        """Add message to the output panel"""
        self.output_text.config(state='normal')
        self.output_text.insert('end', f"{message}\n")
        self.output_text.see('end')
        self.output_text.config(state='disabled')
        self.root.update_idletasks()

    # Menu and toolbar command methods
    def new_search(self):
        """Start a new search"""
        self.clear_results()
        self.manufacturer_entry.focus()

    def new_search_tab(self):
        """Create a new search tab"""
        # For now, just focus on the current tab
        self.new_search()

    def load_excel(self):
        """Load Excel file for batch processing"""
        if hasattr(self.component_finder, 'load_excel_file'):
            self.component_finder.load_excel_file()
        else:
            messagebox.showinfo("Info", "Excel loading functionality will be implemented")

    def start_search(self):
        """Start the component search"""
        manufacturer = self.manufacturer_var.get().strip()
        part_number = self.part_number_var.get().strip()

        if not manufacturer or not part_number:
            messagebox.showerror("Error", "Please enter both manufacturer and part number")
            return

        # Clear previous results
        self.clear_results()

        # Update status
        self.status_var.set(f"Searching for {manufacturer} {part_number}...")
        self.progress_bar.start()

        # Start search in thread
        search_thread = threading.Thread(
            target=self.run_search,
            args=(manufacturer, part_number),
            daemon=True
        )
        search_thread.start()

    def run_search(self, manufacturer, part_number):
        """Run the actual search using the original component finder"""
        try:
            # Use the original component finder's search method
            self.component_finder.search_component(manufacturer, part_number)

            # Update UI when search completes
            self.root.after(0, self.search_completed)

        except Exception as e:
            error_msg = f"Search error: {str(e)}"
            self.root.after(0, lambda: self.add_output_message(error_msg))
            self.root.after(0, self.search_completed)

    def search_completed(self):
        """Called when search is completed"""
        self.progress_bar.stop()
        self.status_var.set("Search completed")

        # Update the search tree and files tree with results
        self.update_search_results()

    def update_search_results(self):
        """Update the results display"""
        manufacturer = self.manufacturer_var.get()
        part_number = self.part_number_var.get()

        # Add to search history tree
        search_item = self.search_tree.insert('', 'end',
                                            text=f"{manufacturer} {part_number}",
                                            values=('Complete', '0'))

        # Check for downloaded files and update display
        self.check_downloaded_files(search_item, manufacturer, part_number)

    def check_downloaded_files(self, search_item, manufacturer, part_number):
        """Check for downloaded files and update the display"""
        files_found = 0

        # Check datasheets folder
        datasheets_path = Path('datasheets')
        if datasheets_path.exists():
            for file in datasheets_path.glob(f"*{part_number}*"):
                self.files_tree.insert('', 'end',
                                     text=file.name,
                                     values=('Datasheet', self.format_file_size(file.stat().st_size), 'Downloaded'))
                files_found += 1

        # Check 3d folder
        models_path = Path('3d')
        if models_path.exists():
            for file in models_path.glob(f"*{part_number}*"):
                self.files_tree.insert('', 'end',
                                     text=file.name,
                                     values=('3D Model', self.format_file_size(file.stat().st_size), 'Downloaded'))
                files_found += 1

        # Update search tree with file count
        self.search_tree.item(search_item, values=('Complete', str(files_found)))

    def format_file_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes // 1024} KB"
        else:
            return f"{size_bytes // (1024 * 1024)} MB"

    def clear_results(self):
        """Clear all results"""
        # Clear trees
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)

        # Clear output
        self.output_text.config(state='normal')
        self.output_text.delete(1.0, 'end')
        self.output_text.config(state='disabled')

        # Clear details
        self.details_text.delete(1.0, 'end')

    def refresh_search(self):
        """Refresh the current search"""
        self.start_search()

    def save_results(self):
        """Save search results"""
        messagebox.showinfo("Info", "Save results functionality will be implemented")

    def show_settings(self):
        """Show settings dialog"""
        messagebox.showinfo("Info", "Settings dialog will be implemented")

    def toggle_explorer(self):
        """Toggle explorer panel visibility"""
        # Implementation for toggling panels
        pass

    def toggle_output(self):
        """Toggle output panel visibility"""
        # Implementation for toggling panels
        pass

    def batch_search(self):
        """Start batch search"""
        self.load_excel()

    def export_results(self):
        """Export search results"""
        messagebox.showinfo("Info", "Export functionality will be implemented")

    def show_about(self):
        """Show about dialog"""
        messagebox.showinfo("About",
                          "Component Finder - Visual Studio Style\n\n"
                          "Professional component search tool with\n"
                          "Visual Studio-like interface\n\n"
                          "Version 1.0")

def main():
    """Main function to run the VS-style Component Finder"""
    root = tk.Tk()
    app = VSStyleComponentFinder(root)
    root.mainloop()

if __name__ == "__main__":
    main()
