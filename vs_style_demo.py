#!/usr/bin/env python3
"""
Visual Studio Style Component Finder - Demo Version
Shows the interface layout and styling
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, Menu
from datetime import datetime

class VSStyleDemo:
    def __init__(self, root):
        self.root = root
        self.setup_demo()
        
    def setup_demo(self):
        """Setup the demo interface"""
        self.root.title("Component Finder - Visual Studio Style")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#2d2d30')
        
        # VS Colors
        self.colors = {
            'bg_dark': '#2d2d30',
            'bg_medium': '#3e3e42', 
            'bg_light': '#4d4d50',
            'fg_primary': '#ffffff',
            'fg_secondary': '#cccccc',
            'accent_blue': '#007acc',
            'accent_green': '#608b4e',
            'accent_red': '#f44747',
            'border': '#464647'
        }
        
        self.setup_styles()
        self.create_interface()
        self.populate_demo_data()
        
    def setup_styles(self):
        """Setup ttk styles"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure styles for VS theme
        self.style.configure('VS.TFrame', background=self.colors['bg_medium'])
        self.style.configure('VS.TLabel', background=self.colors['bg_medium'], 
                           foreground=self.colors['fg_primary'], font=('Segoe UI', 9))
        self.style.configure('VSTitle.TLabel', background=self.colors['bg_medium'],
                           foreground=self.colors['fg_primary'], font=('Segoe UI', 11, 'bold'))
        self.style.configure('VS.TButton', background=self.colors['bg_light'],
                           foreground=self.colors['fg_primary'], font=('Segoe UI', 9))
        self.style.configure('VS.TEntry', fieldbackground=self.colors['bg_light'],
                           foreground=self.colors['fg_primary'])
        self.style.configure('VS.TNotebook', background=self.colors['bg_medium'])
        self.style.configure('VS.TNotebook.Tab', background=self.colors['bg_dark'],
                           foreground=self.colors['fg_secondary'], padding=[12, 8])
        self.style.map('VS.TNotebook.Tab',
                      background=[('selected', self.colors['bg_medium'])],
                      foreground=[('selected', self.colors['fg_primary'])])
        self.style.configure('VS.Treeview', background=self.colors['bg_light'],
                           foreground=self.colors['fg_primary'], fieldbackground=self.colors['bg_light'])
        
    def create_interface(self):
        """Create the main interface"""
        # Menu bar
        menubar = Menu(self.root, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        self.root.config(menu=menubar)
        
        file_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Search")
        file_menu.add_command(label="Load Excel File")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        edit_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Clear Results")
        edit_menu.add_command(label="Settings")
        
        view_menu = Menu(menubar, tearoff=0, bg=self.colors['bg_medium'], fg=self.colors['fg_primary'])
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Toggle Explorer")
        view_menu.add_command(label="Toggle Output")
        
        # Main container
        main_container = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_container.pack(fill='both', expand=True)
        
        # Toolbar
        toolbar = tk.Frame(main_container, bg=self.colors['bg_medium'], height=40)
        toolbar.pack(fill='x', padx=2, pady=(2, 0))
        toolbar.pack_propagate(False)
        
        buttons = [("🔍", "Search"), ("📊", "Excel"), ("🔄", "Refresh"), ("💾", "Save"), ("⚙️", "Settings")]
        for icon, tooltip in buttons:
            btn = tk.Button(toolbar, text=icon, bg=self.colors['bg_light'], 
                          fg=self.colors['fg_primary'], relief='flat', padx=8, pady=4)
            btn.pack(side='left', padx=2, pady=4)
        
        # Content area
        content_frame = tk.Frame(main_container, bg=self.colors['bg_dark'])
        content_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Paned window
        paned = tk.PanedWindow(content_frame, orient='horizontal', bg=self.colors['bg_dark'], 
                              sashwidth=4, sashrelief='flat')
        paned.pack(fill='both', expand=True)
        
        # Left panel
        left_frame = ttk.Frame(paned, style='VS.TFrame', width=300)
        paned.add(left_frame, minsize=250)
        
        left_notebook = ttk.Notebook(left_frame, style='VS.TNotebook')
        left_notebook.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Explorer tab
        explorer_frame = ttk.Frame(left_notebook, style='VS.TFrame')
        left_notebook.add(explorer_frame, text='Explorer')
        
        ttk.Label(explorer_frame, text='Component Explorer', style='VSTitle.TLabel').pack(pady=5)
        
        self.search_tree = ttk.Treeview(explorer_frame, style='VS.Treeview', height=15)
        self.search_tree.pack(fill='both', expand=True, padx=5, pady=5)
        self.search_tree['columns'] = ('Status', 'Files')
        self.search_tree.column('#0', width=200)
        self.search_tree.column('Status', width=80)
        self.search_tree.column('Files', width=60)
        self.search_tree.heading('#0', text='Component')
        self.search_tree.heading('Status', text='Status')
        self.search_tree.heading('Files', text='Files')
        
        # Properties tab
        props_frame = ttk.Frame(left_notebook, style='VS.TFrame')
        left_notebook.add(props_frame, text='Properties')
        
        ttk.Label(props_frame, text='Component Properties', style='VSTitle.TLabel').pack(pady=5)
        self.props_text = scrolledtext.ScrolledText(props_frame, bg=self.colors['bg_light'],
                                                   fg=self.colors['fg_primary'], font=('Consolas', 9))
        self.props_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Center panel
        center_frame = ttk.Frame(paned, style='VS.TFrame')
        paned.add(center_frame, minsize=600)
        
        center_notebook = ttk.Notebook(center_frame, style='VS.TNotebook')
        center_notebook.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Main search tab
        search_frame = ttk.Frame(center_notebook, style='VS.TFrame')
        center_notebook.add(search_frame, text='Component Search')
        
        # Search inputs
        input_frame = ttk.Frame(search_frame, style='VS.TFrame')
        input_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(input_frame, text='Manufacturer:', style='VS.TLabel').grid(row=0, column=0, sticky='w', pady=2)
        self.mfg_entry = ttk.Entry(input_frame, style='VS.TEntry', width=30)
        self.mfg_entry.insert(0, "Texas Instruments")
        self.mfg_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=2)
        
        ttk.Label(input_frame, text='Part Number:', style='VS.TLabel').grid(row=1, column=0, sticky='w', pady=2)
        self.part_entry = ttk.Entry(input_frame, style='VS.TEntry', width=30)
        self.part_entry.insert(0, "LP590722QDQNRQ1")
        self.part_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=2)
        
        input_frame.columnconfigure(1, weight=1)
        
        # Search button
        btn_frame = ttk.Frame(search_frame, style='VS.TFrame')
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        search_btn = ttk.Button(btn_frame, text='🔍 Search Component', style='VS.TButton')
        search_btn.pack(side='left')
        
        progress = ttk.Progressbar(btn_frame, mode='indeterminate')
        progress.pack(side='right', padx=(10, 0))
        
        # Results area
        results_frame = ttk.Frame(search_frame, style='VS.TFrame')
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        results_notebook = ttk.Notebook(results_frame, style='VS.TNotebook')
        results_notebook.pack(fill='both', expand=True)
        
        # Files tab
        files_frame = ttk.Frame(results_notebook, style='VS.TFrame')
        results_notebook.add(files_frame, text='Files Found')
        
        self.files_tree = ttk.Treeview(files_frame, style='VS.Treeview')
        self.files_tree.pack(fill='both', expand=True, padx=5, pady=5)
        self.files_tree['columns'] = ('Type', 'Size', 'Status')
        self.files_tree.column('#0', width=300)
        self.files_tree.column('Type', width=100)
        self.files_tree.column('Size', width=80)
        self.files_tree.column('Status', width=100)
        self.files_tree.heading('#0', text='File Name')
        self.files_tree.heading('Type', text='Type')
        self.files_tree.heading('Size', text='Size')
        self.files_tree.heading('Status', text='Status')
        
        # Details tab
        details_frame = ttk.Frame(results_notebook, style='VS.TFrame')
        results_notebook.add(details_frame, text='Component Details')
        
        self.details_text = scrolledtext.ScrolledText(details_frame, bg=self.colors['bg_light'],
                                                     fg=self.colors['fg_primary'], font=('Consolas', 9))
        self.details_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Right panel
        right_frame = ttk.Frame(paned, style='VS.TFrame', width=400)
        paned.add(right_frame, minsize=300)
        
        right_notebook = ttk.Notebook(right_frame, style='VS.TNotebook')
        right_notebook.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Output tab
        output_frame = ttk.Frame(right_notebook, style='VS.TFrame')
        right_notebook.add(output_frame, text='Output')
        
        self.output_text = scrolledtext.ScrolledText(output_frame, bg=self.colors['bg_light'],
                                                    fg=self.colors['fg_primary'], font=('Consolas', 9))
        self.output_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Problems tab
        problems_frame = ttk.Frame(right_notebook, style='VS.TFrame')
        right_notebook.add(problems_frame, text='Problems')
        
        self.problems_tree = ttk.Treeview(problems_frame, style='VS.Treeview')
        self.problems_tree.pack(fill='both', expand=True, padx=5, pady=5)
        self.problems_tree['columns'] = ('Type', 'Description')
        self.problems_tree.heading('#0', text='#')
        self.problems_tree.heading('Type', text='Type')
        self.problems_tree.heading('Description', text='Description')
        
        # Status bar
        status_frame = tk.Frame(main_container, bg=self.colors['accent_blue'], height=25)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        status_label = tk.Label(status_frame, text="Ready - Visual Studio Style Component Finder",
                               bg=self.colors['accent_blue'], fg='white', font=('Segoe UI', 9))
        status_label.pack(side='left', padx=10, pady=2)
        
        status_progress = ttk.Progressbar(status_frame, mode='determinate', length=200)
        status_progress.pack(side='right', padx=10, pady=2)
        
    def populate_demo_data(self):
        """Add some demo data to show the interface"""
        # Add search history
        ti_item = self.search_tree.insert('', 'end', text='Texas Instruments LP590722QDQNRQ1', 
                                         values=('Complete', '2'))
        self.search_tree.insert(ti_item, 'end', text='Datasheet: lp590722qdqnrq1.pdf', 
                               values=('Downloaded', ''))
        self.search_tree.insert(ti_item, 'end', text='3D Model: LP590722QDQNRQ1.step', 
                               values=('Downloaded', ''))
        
        analog_item = self.search_tree.insert('', 'end', text='Analog Devices AD8065', 
                                             values=('Complete', '1'))
        self.search_tree.insert(analog_item, 'end', text='Datasheet: AD8065.pdf', 
                               values=('Downloaded', ''))
        
        # Add files found
        self.files_tree.insert('', 'end', text='lp590722qdqnrq1.pdf', 
                              values=('Datasheet', '2.1 MB', 'Downloaded'))
        self.files_tree.insert('', 'end', text='LP590722QDQNRQ1.step', 
                              values=('3D Model', '156 KB', 'Downloaded'))
        
        # Add component details
        details = """Component: LP590722QDQNRQ1
Manufacturer: Texas Instruments
Package: WSON-6
Description: 300mA Low-Dropout Linear Regulator
Voltage Input: 2.5V to 5.5V
Voltage Output: 1.2V to 4.5V
Current Output: 300mA
Temperature Range: -40°C to +125°C

Files Found:
✓ Datasheet: lp590722qdqnrq1.pdf (2.1 MB)
✓ 3D Model: LP590722QDQNRQ1.step (156 KB)

Search Sources:
✓ Digi-Key API
✓ Texas Instruments Website
✓ UltraLibrarian
"""
        self.details_text.insert('1.0', details)
        
        # Add properties
        properties = """Selected Component Properties:

Part Number: LP590722QDQNRQ1
Manufacturer: Texas Instruments
Category: Linear Regulators
Package: WSON-6
Pins: 6
Status: Active

Electrical Characteristics:
- Input Voltage: 2.5V - 5.5V
- Output Voltage: 1.2V - 4.5V  
- Output Current: 300mA
- Dropout Voltage: 300mV @ 300mA
- Quiescent Current: 45µA

Temperature Range: -40°C to +125°C
"""
        self.props_text.insert('1.0', properties)
        
        # Add output messages
        output_messages = [
            "🚀 Component Finder Started",
            "📡 Searching API Digi-Key website for Texas Instruments LP590722QDQNRQ1",
            "✅ Found 1 products on Digi-Key",
            "📥 Downloading datasheet from Texas Instruments",
            "✅ Downloaded: lp590722qdqnrq1.pdf (2.1 MB)",
            "🔍 Searching for 3D models...",
            "✅ Found 3D model on UltraLibrarian",
            "📥 Downloading 3D model...",
            "✅ Downloaded: LP590722QDQNRQ1.step (156 KB)",
            "🎉 Search completed successfully!"
        ]
        
        for msg in output_messages:
            self.output_text.insert('end', f"[{datetime.now().strftime('%H:%M:%S')}] {msg}\n")

def main():
    root = tk.Tk()
    app = VSStyleDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main()
