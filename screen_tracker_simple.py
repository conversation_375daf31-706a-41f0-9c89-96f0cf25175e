#!/usr/bin/env python3
"""
Simple Screen Tracker for 3D Vendors
Shows what screen we're on and what you should see
"""

import time

class ScreenTracker:
    def __init__(self, vendor_name):
        self.vendor = vendor_name.upper()
        self.screen_num = 0
        print(f"\n{'='*60}")
        print(f"🎯 {self.vendor} SCREEN TRACKER STARTED")
        print(f"{'='*60}")
        
    def show_screen(self, screen_num, title, description, what_to_see):
        """Show current screen info"""
        self.screen_num = screen_num
        print(f"\n📺 SCREEN {screen_num}: {title}")
        print(f"📝 Action: {description}")
        print(f"👀 You should see: {what_to_see}")
        print(f"⏰ Time: {time.strftime('%H:%M:%S')}")
        print("-" * 50)
        
    def wait_for_user(self, message="Press Enter when you see this screen..."):
        """Wait for user confirmation"""
        input(f"⏸️  {message}")
        
    def error_screen(self, error_msg):
        """Show error"""
        print(f"❌ ERROR on Screen {self.screen_num}: {error_msg}")

# SnapEDA Screen Definitions
SNAPEDA_SCREENS = {
    1: {
        "title": "Browser Launch",
        "description": "Opening Chrome and navigating to SnapEDA login",
        "what_to_see": "Chrome browser opens, SnapEDA login page loads"
    },
    2: {
        "title": "Login Page",
        "description": "Finding email and password fields",
        "what_to_see": "Email field, password field, login button visible"
    },
    3: {
        "title": "Enter Credentials", 
        "description": "Typing email and password",
        "what_to_see": "Email and password being typed in fields"
    },
    4: {
        "title": "Login Submit",
        "description": "Clicking login button",
        "what_to_see": "Login button clicked, page starts to change"
    },
    5: {
        "title": "Post-Login Redirect",
        "description": "Waiting for URL to change from login page",
        "what_to_see": "URL changes, may show loading or new page"
    },
    6: {
        "title": "Welcome Screen (EXTRA SCREEN 1)",
        "description": "Handling welcome/onboarding popup",
        "what_to_see": "Welcome popup with Skip/Continue/Close button"
    },
    7: {
        "title": "Survey Screen (EXTRA SCREEN 2)", 
        "description": "Handling survey/feedback popup",
        "what_to_see": "Survey popup with No thanks/Dismiss/Later button"
    },
    8: {
        "title": "Part Search",
        "description": "Navigating to part page or searching",
        "what_to_see": "Part search page or direct part page loading"
    },
    9: {
        "title": "Part Page",
        "description": "Loading component details page",
        "what_to_see": "Component info, tabs, 3D model section"
    },
    10: {
        "title": "3D Model Section",
        "description": "Finding 3D model download button",
        "what_to_see": "3D model tab active, download button visible"
    },
    11: {
        "title": "Download Click",
        "description": "Clicking download button",
        "what_to_see": "Download button clicked, may show modal/popup"
    },
    12: {
        "title": "File Download",
        "description": "Monitoring download folder for file",
        "what_to_see": "File appears in download folder"
    }
}

# UltraLibrarian Screen Definitions  
ULTRALIB_SCREENS = {
    1: {
        "title": "Browser Launch",
        "description": "Opening Chrome and navigating to UltraLibrarian",
        "what_to_see": "Chrome opens, UltraLibrarian homepage loads"
    },
    2: {
        "title": "Homepage",
        "description": "Finding search box",
        "what_to_see": "Search box visible on homepage"
    },
    3: {
        "title": "Search Submit",
        "description": "Entering part number and searching",
        "what_to_see": "Part number typed, search button clicked"
    },
    4: {
        "title": "Search Results",
        "description": "Loading search results page",
        "what_to_see": "List of matching parts displayed"
    },
    5: {
        "title": "Part Selection",
        "description": "Clicking on correct part",
        "what_to_see": "Clicking on part link in results"
    },
    6: {
        "title": "Part Details",
        "description": "Loading component information page",
        "what_to_see": "Part details, specifications, download options"
    },
    7: {
        "title": "Download Options",
        "description": "Finding CAD model download section",
        "what_to_see": "CAD downloads section with format options"
    },
    8: {
        "title": "Format Selection",
        "description": "Selecting STEP file format",
        "what_to_see": "STEP format selected, download button ready"
    },
    9: {
        "title": "Download Start",
        "description": "Clicking download button",
        "what_to_see": "Download starts, file begins downloading"
    },
    10: {
        "title": "Download Complete",
        "description": "File saved to download folder",
        "what_to_see": "File appears in download folder"
    }
}

# SamacSys Screen Definitions
SAMACSYS_SCREENS = {
    1: {
        "title": "Browser Launch", 
        "description": "Opening Chrome and navigating to SamacSys",
        "what_to_see": "Chrome opens, SamacSys homepage loads"
    },
    2: {
        "title": "Search Page",
        "description": "Finding part number search box",
        "what_to_see": "Search box visible, ready for part number"
    },
    3: {
        "title": "Search Results",
        "description": "Loading component search results",
        "what_to_see": "List of matching components"
    },
    4: {
        "title": "Part Page",
        "description": "Loading component details page",
        "what_to_see": "Component info, downloads section"
    },
    5: {
        "title": "CAD Downloads",
        "description": "Finding 3D model download section",
        "what_to_see": "CAD downloads area with file format options"
    },
    6: {
        "title": "STEP Download",
        "description": "Clicking STEP file download",
        "what_to_see": "STEP download link clicked"
    },
    7: {
        "title": "Download Processing",
        "description": "File preparation and download",
        "what_to_see": "Download processing, file being prepared"
    },
    8: {
        "title": "Download Complete",
        "description": "File saved to download folder", 
        "what_to_see": "File appears in download folder"
    }
}

def create_tracker(vendor):
    """Create screen tracker for vendor"""
    return ScreenTracker(vendor)

def get_screens(vendor):
    """Get screen definitions for vendor"""
    if vendor.lower() == 'snapeda':
        return SNAPEDA_SCREENS
    elif vendor.lower() == 'ultralibrarian':
        return ULTRALIB_SCREENS
    elif vendor.lower() == 'samacsys':
        return SAMACSYS_SCREENS
    else:
        return {}
