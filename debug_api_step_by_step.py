#!/usr/bin/env python3
"""
Debug API step by step - show exactly what's happening
"""

import requests
import json
from digikey_datasheet_improved import DigikeyDatasheetFinder

def debug_api_step_by_step():
    print("🔍 DEBUG API STEP BY STEP")
    print("=" * 50)
    
    # Step 1: Get API response
    print("📍 Step 1: Getting API response...")
    finder = DigikeyDatasheetFinder()
    
    if not finder.load_credentials():
        print("❌ Failed to load credentials")
        return
    
    result = finder.search_part('Texas Instruments', 'LM358N')
    if not result:
        print("❌ API returned no results")
        return
    
    datasheet_url = result.get('DatasheetUrl')
    print(f"✅ API returned URL: {datasheet_url}")
    print(f"URL type: {type(datasheet_url)}")
    print(f"URL length: {len(datasheet_url) if datasheet_url else 0}")
    
    # Step 2: Test the exact URL
    print(f"\n📍 Step 2: Testing exact API URL...")
    print(f"Making request to: {datasheet_url}")
    
    try:
        response = requests.get(datasheet_url, timeout=60)
        print(f"✅ Response status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"Content-Length: {response.headers.get('content-length', 'unknown')}")
        print(f"Response URL: {response.url}")
        print(f"History: {[r.url for r in response.history]}")
        
        # Check first 100 bytes
        content_start = response.content[:100]
        print(f"First 100 bytes: {content_start}")
        
        if content_start.startswith(b'%PDF'):
            print("✅ Response is PDF!")
        elif b'<html' in content_start.lower():
            print("❌ Response is HTML")
        else:
            print("❓ Unknown content type")
            
        # Save for inspection
        with open('debug_response.txt', 'wb') as f:
            f.write(response.content)
        print("💾 Saved response to debug_response.txt")
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    print(f"\n📍 Step 3: Manual verification...")
    print(f"You can now:")
    print(f"1. Copy this URL and test in browser: {datasheet_url}")
    print(f"2. Check debug_response.txt to see what we got")
    print(f"3. Compare with what you get manually")

if __name__ == "__main__":
    debug_api_step_by_step()
