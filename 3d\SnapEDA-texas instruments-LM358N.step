ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-09-19T05:36:41',(''),(''),
  'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('DIP_standard','DIP_standard','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23,#27,#31,#35),#39);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,3.81));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,0.));
#27 = AXIS2_PLACEMENT_3D('',#28,#29,#30);
#28 = CARTESIAN_POINT('',(0.,0.,0.));
#29 = DIRECTION('',(0.,0.,1.));
#30 = DIRECTION('',(1.,0.,0.));
#31 = AXIS2_PLACEMENT_3D('',#32,#33,#34);
#32 = CARTESIAN_POINT('',(0.,0.,0.));
#33 = DIRECTION('',(0.,0.,1.));
#34 = DIRECTION('',(1.,0.,0.));
#35 = AXIS2_PLACEMENT_3D('',#36,#37,#38);
#36 = CARTESIAN_POINT('',(0.,0.,0.));
#37 = DIRECTION('',(0.,0.,1.));
#38 = DIRECTION('',(1.,0.,0.));
#39 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#43)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#40,#41,#42)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#40 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#41 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#42 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#43 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#40,
  'distance_accuracy_value','confusion accuracy');
#44 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#45 = SHAPE_DEFINITION_REPRESENTATION(#46,#52);
#46 = PRODUCT_DEFINITION_SHAPE('','',#47);
#47 = PRODUCT_DEFINITION('design','',#48,#51);
#48 = PRODUCT_DEFINITION_FORMATION('','',#49);
#49 = PRODUCT('Body','Body','',(#50));
#50 = PRODUCT_CONTEXT('',#2,'mechanical');
#51 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#52 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#53),#440);
#53 = MANIFOLD_SOLID_BREP('',#54);
#54 = CLOSED_SHELL('',(#55,#95,#126,#157,#188,#219,#243,#267,#289,#313,
    #341,#365,#382,#394,#406,#431));
#55 = ADVANCED_FACE('',(#56),#90,.T.);
#56 = FACE_BOUND('',#57,.T.);
#57 = EDGE_LOOP('',(#58,#68,#76,#84));
#58 = ORIENTED_EDGE('',*,*,#59,.F.);
#59 = EDGE_CURVE('',#60,#62,#64,.T.);
#60 = VERTEX_POINT('',#61);
#61 = CARTESIAN_POINT('',(3.175,2.6110385,4.795));
#62 = VERTEX_POINT('',#63);
#63 = CARTESIAN_POINT('',(3.175,2.9769615,4.795));
#64 = LINE('',#65,#66);
#65 = CARTESIAN_POINT('',(3.175,1.064703880616E-15,4.795));
#66 = VECTOR('',#67,1.);
#67 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#68 = ORIENTED_EDGE('',*,*,#69,.F.);
#69 = EDGE_CURVE('',#70,#60,#72,.T.);
#70 = VERTEX_POINT('',#71);
#71 = CARTESIAN_POINT('',(3.175,2.6110385,-4.795));
#72 = LINE('',#73,#74);
#73 = CARTESIAN_POINT('',(3.175,2.6110385,-4.424177570981));
#74 = VECTOR('',#75,1.);
#75 = DIRECTION('',(0.,2.22044604925E-16,1.));
#76 = ORIENTED_EDGE('',*,*,#77,.T.);
#77 = EDGE_CURVE('',#70,#78,#80,.T.);
#78 = VERTEX_POINT('',#79);
#79 = CARTESIAN_POINT('',(3.175,2.9769615,-4.795));
#80 = LINE('',#81,#82);
#81 = CARTESIAN_POINT('',(3.175,-1.064703880616E-15,-4.795));
#82 = VECTOR('',#83,1.);
#83 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#84 = ORIENTED_EDGE('',*,*,#85,.T.);
#85 = EDGE_CURVE('',#78,#62,#86,.T.);
#86 = LINE('',#87,#88);
#87 = CARTESIAN_POINT('',(3.175,2.9769615,-4.424177570981));
#88 = VECTOR('',#89,1.);
#89 = DIRECTION('',(0.,2.22044604925E-16,1.));
#90 = PLANE('',#91);
#91 = AXIS2_PLACEMENT_3D('',#92,#93,#94);
#92 = CARTESIAN_POINT('',(3.175,-1.064703880616E-15,-4.795));
#93 = DIRECTION('',(1.,0.,0.));
#94 = DIRECTION('',(0.,2.22044604925E-16,1.));
#95 = ADVANCED_FACE('',(#96),#121,.T.);
#96 = FACE_BOUND('',#97,.T.);
#97 = EDGE_LOOP('',(#98,#99,#107,#115));
#98 = ORIENTED_EDGE('',*,*,#59,.T.);
#99 = ORIENTED_EDGE('',*,*,#100,.T.);
#100 = EDGE_CURVE('',#62,#101,#103,.T.);
#101 = VERTEX_POINT('',#102);
#102 = CARTESIAN_POINT('',(-3.175,2.9769615,4.795));
#103 = LINE('',#104,#105);
#104 = CARTESIAN_POINT('',(3.175,2.9769615,4.795));
#105 = VECTOR('',#106,1.);
#106 = DIRECTION('',(-1.,0.,0.));
#107 = ORIENTED_EDGE('',*,*,#108,.F.);
#108 = EDGE_CURVE('',#109,#101,#111,.T.);
#109 = VERTEX_POINT('',#110);
#110 = CARTESIAN_POINT('',(-3.175,2.6110385,4.795));
#111 = LINE('',#112,#113);
#112 = CARTESIAN_POINT('',(-3.175,1.064703880616E-15,4.795));
#113 = VECTOR('',#114,1.);
#114 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#115 = ORIENTED_EDGE('',*,*,#116,.F.);
#116 = EDGE_CURVE('',#60,#109,#117,.T.);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(3.175,2.6110385,4.795));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(-1.,0.,0.));
#121 = PLANE('',#122);
#122 = AXIS2_PLACEMENT_3D('',#123,#124,#125);
#123 = CARTESIAN_POINT('',(3.175,1.064703880616E-15,4.795));
#124 = DIRECTION('',(0.,2.22044604925E-16,1.));
#125 = DIRECTION('',(-1.,0.,0.));
#126 = ADVANCED_FACE('',(#127),#152,.T.);
#127 = FACE_BOUND('',#128,.F.);
#128 = EDGE_LOOP('',(#129,#137,#138,#146));
#129 = ORIENTED_EDGE('',*,*,#130,.F.);
#130 = EDGE_CURVE('',#78,#131,#133,.T.);
#131 = VERTEX_POINT('',#132);
#132 = CARTESIAN_POINT('',(2.804177570981,5.08,-4.424177570981));
#133 = LINE('',#134,#135);
#134 = CARTESIAN_POINT('',(2.896653172612,4.555544801711,-4.516653172612
    ));
#135 = VECTOR('',#136,1.);
#136 = DIRECTION('',(-0.171087869746,0.970287525248,0.171087869746));
#137 = ORIENTED_EDGE('',*,*,#85,.T.);
#138 = ORIENTED_EDGE('',*,*,#139,.T.);
#139 = EDGE_CURVE('',#62,#140,#142,.T.);
#140 = VERTEX_POINT('',#141);
#141 = CARTESIAN_POINT('',(2.804177570981,5.08,4.424177570981));
#142 = LINE('',#143,#144);
#143 = CARTESIAN_POINT('',(2.860088422013,4.762913807024,4.480088422013)
  );
#144 = VECTOR('',#145,1.);
#145 = DIRECTION('',(-0.171087869746,0.970287525248,-0.171087869746));
#146 = ORIENTED_EDGE('',*,*,#147,.F.);
#147 = EDGE_CURVE('',#131,#140,#148,.T.);
#148 = LINE('',#149,#150);
#149 = CARTESIAN_POINT('',(2.804177570981,5.08,-4.424177570981));
#150 = VECTOR('',#151,1.);
#151 = DIRECTION('',(0.,2.22044604925E-16,1.));
#152 = PLANE('',#153);
#153 = AXIS2_PLACEMENT_3D('',#154,#155,#156);
#154 = CARTESIAN_POINT('',(2.989588785491,4.02848075,-4.424177570981));
#155 = DIRECTION('',(0.984807753012,0.173648177667,-1.110978533537E-17)
  );
#156 = DIRECTION('',(2.7030861068E-17,-2.17278334807E-16,-1.));
#157 = ADVANCED_FACE('',(#158),#183,.F.);
#158 = FACE_BOUND('',#159,.T.);
#159 = EDGE_LOOP('',(#160,#168,#169,#177));
#160 = ORIENTED_EDGE('',*,*,#161,.F.);
#161 = EDGE_CURVE('',#70,#162,#164,.T.);
#162 = VERTEX_POINT('',#163);
#163 = CARTESIAN_POINT('',(2.804177570981,0.508,-4.424177570981));
#164 = LINE('',#165,#166);
#165 = CARTESIAN_POINT('',(2.896653172612,1.032455198289,-4.516653172612
    ));
#166 = VECTOR('',#167,1.);
#167 = DIRECTION('',(-0.171087869746,-0.970287525248,0.171087869746));
#168 = ORIENTED_EDGE('',*,*,#69,.T.);
#169 = ORIENTED_EDGE('',*,*,#170,.T.);
#170 = EDGE_CURVE('',#60,#171,#173,.T.);
#171 = VERTEX_POINT('',#172);
#172 = CARTESIAN_POINT('',(2.804177570981,0.508,4.424177570981));
#173 = LINE('',#174,#175);
#174 = CARTESIAN_POINT('',(2.860088422013,0.825086192976,4.480088422013)
  );
#175 = VECTOR('',#176,1.);
#176 = DIRECTION('',(-0.171087869746,-0.970287525248,-0.171087869746));
#177 = ORIENTED_EDGE('',*,*,#178,.F.);
#178 = EDGE_CURVE('',#162,#171,#179,.T.);
#179 = LINE('',#180,#181);
#180 = CARTESIAN_POINT('',(2.804177570981,0.508,-4.424177570981));
#181 = VECTOR('',#182,1.);
#182 = DIRECTION('',(0.,2.22044604925E-16,1.));
#183 = PLANE('',#184);
#184 = AXIS2_PLACEMENT_3D('',#185,#186,#187);
#185 = CARTESIAN_POINT('',(2.989588785491,1.55951925,-4.424177570981));
#186 = DIRECTION('',(-0.984807753012,0.173648177667,3.080907905024E-18)
  );
#187 = DIRECTION('',(-4.100596579176E-17,-2.148141467859E-16,-1.));
#188 = ADVANCED_FACE('',(#189),#214,.T.);
#189 = FACE_BOUND('',#190,.T.);
#190 = EDGE_LOOP('',(#191,#192,#200,#208));
#191 = ORIENTED_EDGE('',*,*,#77,.F.);
#192 = ORIENTED_EDGE('',*,*,#193,.F.);
#193 = EDGE_CURVE('',#194,#70,#196,.T.);
#194 = VERTEX_POINT('',#195);
#195 = CARTESIAN_POINT('',(-3.175,2.6110385,-4.795));
#196 = LINE('',#197,#198);
#197 = CARTESIAN_POINT('',(-3.175,2.6110385,-4.795));
#198 = VECTOR('',#199,1.);
#199 = DIRECTION('',(1.,0.,0.));
#200 = ORIENTED_EDGE('',*,*,#201,.T.);
#201 = EDGE_CURVE('',#194,#202,#204,.T.);
#202 = VERTEX_POINT('',#203);
#203 = CARTESIAN_POINT('',(-3.175,2.9769615,-4.795));
#204 = LINE('',#205,#206);
#205 = CARTESIAN_POINT('',(-3.175,-1.064703880616E-15,-4.795));
#206 = VECTOR('',#207,1.);
#207 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#208 = ORIENTED_EDGE('',*,*,#209,.T.);
#209 = EDGE_CURVE('',#202,#78,#210,.T.);
#210 = LINE('',#211,#212);
#211 = CARTESIAN_POINT('',(-3.175,2.9769615,-4.795));
#212 = VECTOR('',#213,1.);
#213 = DIRECTION('',(1.,0.,0.));
#214 = PLANE('',#215);
#215 = AXIS2_PLACEMENT_3D('',#216,#217,#218);
#216 = CARTESIAN_POINT('',(-3.175,-1.064703880616E-15,-4.795));
#217 = DIRECTION('',(0.,-2.22044604925E-16,-1.));
#218 = DIRECTION('',(1.,0.,0.));
#219 = ADVANCED_FACE('',(#220),#238,.T.);
#220 = FACE_BOUND('',#221,.F.);
#221 = EDGE_LOOP('',(#222,#223,#224,#232));
#222 = ORIENTED_EDGE('',*,*,#139,.F.);
#223 = ORIENTED_EDGE('',*,*,#100,.T.);
#224 = ORIENTED_EDGE('',*,*,#225,.T.);
#225 = EDGE_CURVE('',#101,#226,#228,.T.);
#226 = VERTEX_POINT('',#227);
#227 = CARTESIAN_POINT('',(-2.804177570981,5.08,4.424177570981));
#228 = LINE('',#229,#230);
#229 = CARTESIAN_POINT('',(-2.902080355244,4.524765719522,4.522080355244
    ));
#230 = VECTOR('',#231,1.);
#231 = DIRECTION('',(0.171087869746,0.970287525248,-0.171087869746));
#232 = ORIENTED_EDGE('',*,*,#233,.F.);
#233 = EDGE_CURVE('',#140,#226,#234,.T.);
#234 = LINE('',#235,#236);
#235 = CARTESIAN_POINT('',(3.175,5.08,4.424177570981));
#236 = VECTOR('',#237,1.);
#237 = DIRECTION('',(-1.,0.,0.));
#238 = PLANE('',#239);
#239 = AXIS2_PLACEMENT_3D('',#240,#241,#242);
#240 = CARTESIAN_POINT('',(3.175,4.02848075,4.609588785491));
#241 = DIRECTION('',(0.,0.173648177667,0.984807753012));
#242 = DIRECTION('',(1.,0.,0.));
#243 = ADVANCED_FACE('',(#244),#262,.F.);
#244 = FACE_BOUND('',#245,.T.);
#245 = EDGE_LOOP('',(#246,#254,#255,#256));
#246 = ORIENTED_EDGE('',*,*,#247,.F.);
#247 = EDGE_CURVE('',#171,#248,#250,.T.);
#248 = VERTEX_POINT('',#249);
#249 = CARTESIAN_POINT('',(-2.804177570981,0.508,4.424177570981));
#250 = LINE('',#251,#252);
#251 = CARTESIAN_POINT('',(3.175,0.508,4.424177570981));
#252 = VECTOR('',#253,1.);
#253 = DIRECTION('',(-1.,0.,0.));
#254 = ORIENTED_EDGE('',*,*,#170,.F.);
#255 = ORIENTED_EDGE('',*,*,#116,.T.);
#256 = ORIENTED_EDGE('',*,*,#257,.T.);
#257 = EDGE_CURVE('',#109,#248,#258,.T.);
#258 = LINE('',#259,#260);
#259 = CARTESIAN_POINT('',(-2.902080355244,1.063234280478,4.522080355244
    ));
#260 = VECTOR('',#261,1.);
#261 = DIRECTION('',(0.171087869746,-0.970287525248,-0.171087869746));
#262 = PLANE('',#263);
#263 = AXIS2_PLACEMENT_3D('',#264,#265,#266);
#264 = CARTESIAN_POINT('',(3.175,1.55951925,4.609588785491));
#265 = DIRECTION('',(7.095346620196E-18,0.173648177667,-0.984807753012)
  );
#266 = DIRECTION('',(1.,-1.232094010512E-18,6.987552361878E-18));
#267 = ADVANCED_FACE('',(#268),#284,.T.);
#268 = FACE_BOUND('',#269,.T.);
#269 = EDGE_LOOP('',(#270,#271,#277,#278));
#270 = ORIENTED_EDGE('',*,*,#201,.F.);
#271 = ORIENTED_EDGE('',*,*,#272,.F.);
#272 = EDGE_CURVE('',#109,#194,#273,.T.);
#273 = LINE('',#274,#275);
#274 = CARTESIAN_POINT('',(-3.175,2.6110385,4.795));
#275 = VECTOR('',#276,1.);
#276 = DIRECTION('',(0.,-2.22044604925E-16,-1.));
#277 = ORIENTED_EDGE('',*,*,#108,.T.);
#278 = ORIENTED_EDGE('',*,*,#279,.T.);
#279 = EDGE_CURVE('',#101,#202,#280,.T.);
#280 = LINE('',#281,#282);
#281 = CARTESIAN_POINT('',(-3.175,2.9769615,4.795));
#282 = VECTOR('',#283,1.);
#283 = DIRECTION('',(0.,-2.22044604925E-16,-1.));
#284 = PLANE('',#285);
#285 = AXIS2_PLACEMENT_3D('',#286,#287,#288);
#286 = CARTESIAN_POINT('',(-3.175,1.064703880616E-15,4.795));
#287 = DIRECTION('',(-1.,0.,0.));
#288 = DIRECTION('',(0.,-2.22044604925E-16,-1.));
#289 = ADVANCED_FACE('',(#290),#308,.T.);
#290 = FACE_BOUND('',#291,.F.);
#291 = EDGE_LOOP('',(#292,#300,#306,#307));
#292 = ORIENTED_EDGE('',*,*,#293,.F.);
#293 = EDGE_CURVE('',#294,#131,#296,.T.);
#294 = VERTEX_POINT('',#295);
#295 = CARTESIAN_POINT('',(-2.804177570981,5.08,-4.424177570981));
#296 = LINE('',#297,#298);
#297 = CARTESIAN_POINT('',(-3.175,5.08,-4.424177570981));
#298 = VECTOR('',#299,1.);
#299 = DIRECTION('',(1.,0.,0.));
#300 = ORIENTED_EDGE('',*,*,#301,.F.);
#301 = EDGE_CURVE('',#202,#294,#302,.T.);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(-2.854661239382,4.793692889214,
    -4.474661239382));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(0.171087869746,0.970287525248,0.171087869746));
#306 = ORIENTED_EDGE('',*,*,#209,.T.);
#307 = ORIENTED_EDGE('',*,*,#130,.T.);
#308 = PLANE('',#309);
#309 = AXIS2_PLACEMENT_3D('',#310,#311,#312);
#310 = CARTESIAN_POINT('',(-3.175,4.02848075,-4.609588785491));
#311 = DIRECTION('',(7.095346620196E-18,0.173648177667,-0.984807753012)
  );
#312 = DIRECTION('',(-1.,1.232094010512E-18,-6.987552361878E-18));
#313 = ADVANCED_FACE('',(#314,#325),#336,.T.);
#314 = FACE_BOUND('',#315,.T.);
#315 = EDGE_LOOP('',(#316,#317,#323,#324));
#316 = ORIENTED_EDGE('',*,*,#293,.F.);
#317 = ORIENTED_EDGE('',*,*,#318,.F.);
#318 = EDGE_CURVE('',#226,#294,#319,.T.);
#319 = LINE('',#320,#321);
#320 = CARTESIAN_POINT('',(-2.804177570981,5.08,4.795));
#321 = VECTOR('',#322,1.);
#322 = DIRECTION('',(0.,-2.22044604925E-16,-1.));
#323 = ORIENTED_EDGE('',*,*,#233,.F.);
#324 = ORIENTED_EDGE('',*,*,#147,.F.);
#325 = FACE_BOUND('',#326,.T.);
#326 = EDGE_LOOP('',(#327));
#327 = ORIENTED_EDGE('',*,*,#328,.F.);
#328 = EDGE_CURVE('',#329,#329,#331,.T.);
#329 = VERTEX_POINT('',#330);
#330 = CARTESIAN_POINT('',(-1.852083333333,5.08,-3.736666666667));
#331 = CIRCLE('',#332,0.264583333333);
#332 = AXIS2_PLACEMENT_3D('',#333,#334,#335);
#333 = CARTESIAN_POINT('',(-2.116666666667,5.08,-3.736666666667));
#334 = DIRECTION('',(-0.,1.,2.22044604925E-16));
#335 = DIRECTION('',(1.,0.,0.));
#336 = PLANE('',#337);
#337 = AXIS2_PLACEMENT_3D('',#338,#339,#340);
#338 = CARTESIAN_POINT('',(1.114402032246E-16,5.08,-8.881784197001E-16)
  );
#339 = DIRECTION('',(0.,1.,0.));
#340 = DIRECTION('',(0.,0.,1.));
#341 = ADVANCED_FACE('',(#342),#360,.F.);
#342 = FACE_BOUND('',#343,.T.);
#343 = EDGE_LOOP('',(#344,#352,#358,#359));
#344 = ORIENTED_EDGE('',*,*,#345,.F.);
#345 = EDGE_CURVE('',#346,#162,#348,.T.);
#346 = VERTEX_POINT('',#347);
#347 = CARTESIAN_POINT('',(-2.804177570981,0.508,-4.424177570981));
#348 = LINE('',#349,#350);
#349 = CARTESIAN_POINT('',(-3.175,0.508,-4.424177570981));
#350 = VECTOR('',#351,1.);
#351 = DIRECTION('',(1.,0.,0.));
#352 = ORIENTED_EDGE('',*,*,#353,.F.);
#353 = EDGE_CURVE('',#194,#346,#354,.T.);
#354 = LINE('',#355,#356);
#355 = CARTESIAN_POINT('',(-2.854661239382,0.794307110786,
    -4.474661239382));
#356 = VECTOR('',#357,1.);
#357 = DIRECTION('',(0.171087869746,-0.970287525248,0.171087869746));
#358 = ORIENTED_EDGE('',*,*,#193,.T.);
#359 = ORIENTED_EDGE('',*,*,#161,.T.);
#360 = PLANE('',#361);
#361 = AXIS2_PLACEMENT_3D('',#362,#363,#364);
#362 = CARTESIAN_POINT('',(-3.175,1.55951925,-4.609588785491));
#363 = DIRECTION('',(0.,0.173648177667,0.984807753012));
#364 = DIRECTION('',(-1.,-0.,-0.));
#365 = ADVANCED_FACE('',(#366),#377,.F.);
#366 = FACE_BOUND('',#367,.F.);
#367 = EDGE_LOOP('',(#368,#369,#375,#376));
#368 = ORIENTED_EDGE('',*,*,#345,.F.);
#369 = ORIENTED_EDGE('',*,*,#370,.F.);
#370 = EDGE_CURVE('',#248,#346,#371,.T.);
#371 = LINE('',#372,#373);
#372 = CARTESIAN_POINT('',(-2.804177570981,0.508,4.795));
#373 = VECTOR('',#374,1.);
#374 = DIRECTION('',(0.,-2.22044604925E-16,-1.));
#375 = ORIENTED_EDGE('',*,*,#247,.F.);
#376 = ORIENTED_EDGE('',*,*,#178,.F.);
#377 = PLANE('',#378);
#378 = AXIS2_PLACEMENT_3D('',#379,#380,#381);
#379 = CARTESIAN_POINT('',(1.114402032246E-16,0.508,0.));
#380 = DIRECTION('',(0.,1.,0.));
#381 = DIRECTION('',(0.,0.,1.));
#382 = ADVANCED_FACE('',(#383),#389,.T.);
#383 = FACE_BOUND('',#384,.F.);
#384 = EDGE_LOOP('',(#385,#386,#387,#388));
#385 = ORIENTED_EDGE('',*,*,#318,.F.);
#386 = ORIENTED_EDGE('',*,*,#225,.F.);
#387 = ORIENTED_EDGE('',*,*,#279,.T.);
#388 = ORIENTED_EDGE('',*,*,#301,.T.);
#389 = PLANE('',#390);
#390 = AXIS2_PLACEMENT_3D('',#391,#392,#393);
#391 = CARTESIAN_POINT('',(-2.989588785491,4.02848075,4.795));
#392 = DIRECTION('',(-0.984807753012,0.173648177667,-4.014438715172E-18)
  );
#393 = DIRECTION('',(3.401841342988E-17,2.160462407964E-16,1.));
#394 = ADVANCED_FACE('',(#395),#401,.F.);
#395 = FACE_BOUND('',#396,.T.);
#396 = EDGE_LOOP('',(#397,#398,#399,#400));
#397 = ORIENTED_EDGE('',*,*,#257,.F.);
#398 = ORIENTED_EDGE('',*,*,#272,.T.);
#399 = ORIENTED_EDGE('',*,*,#353,.T.);
#400 = ORIENTED_EDGE('',*,*,#370,.F.);
#401 = PLANE('',#402);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(-2.989588785491,1.55951925,4.795));
#404 = DIRECTION('',(0.984807753012,0.173648177667,-4.014438715172E-18)
  );
#405 = DIRECTION('',(-3.401841342988E-17,2.160462407964E-16,1.));
#406 = ADVANCED_FACE('',(#407),#426,.F.);
#407 = FACE_BOUND('',#408,.F.);
#408 = EDGE_LOOP('',(#409,#417,#424,#425));
#409 = ORIENTED_EDGE('',*,*,#410,.T.);
#410 = EDGE_CURVE('',#329,#411,#413,.T.);
#411 = VERTEX_POINT('',#412);
#412 = CARTESIAN_POINT('',(-1.852083333333,4.6228,-3.736666666667));
#413 = LINE('',#414,#415);
#414 = CARTESIAN_POINT('',(-1.852083333333,5.08,-3.736666666667));
#415 = VECTOR('',#416,1.);
#416 = DIRECTION('',(0.,-1.,-2.22044604925E-16));
#417 = ORIENTED_EDGE('',*,*,#418,.T.);
#418 = EDGE_CURVE('',#411,#411,#419,.T.);
#419 = CIRCLE('',#420,0.264583333333);
#420 = AXIS2_PLACEMENT_3D('',#421,#422,#423);
#421 = CARTESIAN_POINT('',(-2.116666666667,4.6228,-3.736666666667));
#422 = DIRECTION('',(-0.,1.,2.22044604925E-16));
#423 = DIRECTION('',(1.,0.,0.));
#424 = ORIENTED_EDGE('',*,*,#410,.F.);
#425 = ORIENTED_EDGE('',*,*,#328,.F.);
#426 = CYLINDRICAL_SURFACE('',#427,0.264583333333);
#427 = AXIS2_PLACEMENT_3D('',#428,#429,#430);
#428 = CARTESIAN_POINT('',(-2.116666666667,5.08,-3.736666666667));
#429 = DIRECTION('',(0.,1.,2.22044604925E-16));
#430 = DIRECTION('',(1.,0.,0.));
#431 = ADVANCED_FACE('',(#432),#435,.T.);
#432 = FACE_BOUND('',#433,.T.);
#433 = EDGE_LOOP('',(#434));
#434 = ORIENTED_EDGE('',*,*,#418,.T.);
#435 = PLANE('',#436);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(-2.116666666667,4.6228,-3.736666666667));
#438 = DIRECTION('',(0.,1.,0.));
#439 = DIRECTION('',(0.,0.,1.));
#440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#444)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#441,#442,#443)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#441 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#442 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#443 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#444 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#441,
  'distance_accuracy_value','confusion accuracy');
#445 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#446,#448);
#446 = ( REPRESENTATION_RELATIONSHIP('','',#52,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#447) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#447 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#448 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#449
  );
#449 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9499','Body','',#5,#47,$);
#450 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#49));
#451 = SHAPE_DEFINITION_REPRESENTATION(#452,#458);
#452 = PRODUCT_DEFINITION_SHAPE('','',#453);
#453 = PRODUCT_DEFINITION('design','',#454,#457);
#454 = PRODUCT_DEFINITION_FORMATION('','',#455);
#455 = PRODUCT('Body001','Body001','',(#456));
#456 = PRODUCT_CONTEXT('',#2,'mechanical');
#457 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#458 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#459),#842);
#459 = MANIFOLD_SOLID_BREP('',#460);
#460 = CLOSED_SHELL('',(#461,#526,#557,#582,#606,#662,#686,#710,#759,
    #779,#796,#813,#830));
#461 = ADVANCED_FACE('',(#462),#521,.F.);
#462 = FACE_BOUND('',#463,.F.);
#463 = EDGE_LOOP('',(#464,#474,#482,#490,#498,#506,#514));
#464 = ORIENTED_EDGE('',*,*,#465,.T.);
#465 = EDGE_CURVE('',#466,#468,#470,.T.);
#466 = VERTEX_POINT('',#467);
#467 = CARTESIAN_POINT('',(3.787038500007,2.9769615,-0.3375));
#468 = VERTEX_POINT('',#469);
#469 = CARTESIAN_POINT('',(3.175,2.9769615,-0.3375));
#470 = LINE('',#471,#472);
#471 = CARTESIAN_POINT('',(4.152961500007,2.9769615,-0.3375));
#472 = VECTOR('',#473,1.);
#473 = DIRECTION('',(-1.,0.,0.));
#474 = ORIENTED_EDGE('',*,*,#475,.T.);
#475 = EDGE_CURVE('',#468,#476,#478,.T.);
#476 = VERTEX_POINT('',#477);
#477 = CARTESIAN_POINT('',(3.175,2.6110385,-0.3375));
#478 = LINE('',#479,#480);
#479 = CARTESIAN_POINT('',(3.175,2.9769615,-0.3375));
#480 = VECTOR('',#481,1.);
#481 = DIRECTION('',(0.,-1.,0.));
#482 = ORIENTED_EDGE('',*,*,#483,.T.);
#483 = EDGE_CURVE('',#476,#484,#486,.T.);
#484 = VERTEX_POINT('',#485);
#485 = CARTESIAN_POINT('',(3.787038499993,2.6110385,-0.3375));
#486 = LINE('',#487,#488);
#487 = CARTESIAN_POINT('',(3.175,2.6110385,-0.3375));
#488 = VECTOR('',#489,1.);
#489 = DIRECTION('',(1.,0.,0.));
#490 = ORIENTED_EDGE('',*,*,#491,.T.);
#491 = EDGE_CURVE('',#484,#492,#494,.T.);
#492 = VERTEX_POINT('',#493);
#493 = CARTESIAN_POINT('',(3.787038500007,0.,-0.3375));
#494 = LINE('',#495,#496);
#495 = CARTESIAN_POINT('',(3.787038499993,2.6110385,-0.3375));
#496 = VECTOR('',#497,1.);
#497 = DIRECTION('',(0.,-1.,0.));
#498 = ORIENTED_EDGE('',*,*,#499,.F.);
#499 = EDGE_CURVE('',#500,#492,#502,.T.);
#500 = VERTEX_POINT('',#501);
#501 = CARTESIAN_POINT('',(4.152961500007,0.,-0.3375));
#502 = LINE('',#503,#504);
#503 = CARTESIAN_POINT('',(4.152961500007,0.,-0.3375));
#504 = VECTOR('',#505,1.);
#505 = DIRECTION('',(-1.,-0.,-0.));
#506 = ORIENTED_EDGE('',*,*,#507,.T.);
#507 = EDGE_CURVE('',#500,#508,#510,.T.);
#508 = VERTEX_POINT('',#509);
#509 = CARTESIAN_POINT('',(4.152961500007,2.6110385,-0.3375));
#510 = LINE('',#511,#512);
#511 = CARTESIAN_POINT('',(4.152961500007,-3.,-0.3375));
#512 = VECTOR('',#513,1.);
#513 = DIRECTION('',(0.,1.,0.));
#514 = ORIENTED_EDGE('',*,*,#515,.F.);
#515 = EDGE_CURVE('',#466,#508,#516,.T.);
#516 = CIRCLE('',#517,0.365923);
#517 = AXIS2_PLACEMENT_3D('',#518,#519,#520);
#518 = CARTESIAN_POINT('',(3.787038500007,2.6110385,-0.3375));
#519 = DIRECTION('',(-0.,-0.,-1.));
#520 = DIRECTION('',(0.,-1.,0.));
#521 = PLANE('',#522);
#522 = AXIS2_PLACEMENT_3D('',#523,#524,#525);
#523 = CARTESIAN_POINT('',(3.910868572265,0.235368572262,-0.3375));
#524 = DIRECTION('',(0.,0.,1.));
#525 = DIRECTION('',(1.,0.,0.));
#526 = ADVANCED_FACE('',(#527),#552,.F.);
#527 = FACE_BOUND('',#528,.F.);
#528 = EDGE_LOOP('',(#529,#530,#538,#546));
#529 = ORIENTED_EDGE('',*,*,#465,.F.);
#530 = ORIENTED_EDGE('',*,*,#531,.T.);
#531 = EDGE_CURVE('',#466,#532,#534,.T.);
#532 = VERTEX_POINT('',#533);
#533 = CARTESIAN_POINT('',(3.787038500007,2.9769615,0.3375));
#534 = LINE('',#535,#536);
#535 = CARTESIAN_POINT('',(3.787038500007,2.9769615,-0.3375));
#536 = VECTOR('',#537,1.);
#537 = DIRECTION('',(0.,0.,1.));
#538 = ORIENTED_EDGE('',*,*,#539,.T.);
#539 = EDGE_CURVE('',#532,#540,#542,.T.);
#540 = VERTEX_POINT('',#541);
#541 = CARTESIAN_POINT('',(3.175,2.9769615,0.3375));
#542 = LINE('',#543,#544);
#543 = CARTESIAN_POINT('',(4.152961500007,2.9769615,0.3375));
#544 = VECTOR('',#545,1.);
#545 = DIRECTION('',(-1.,0.,0.));
#546 = ORIENTED_EDGE('',*,*,#547,.F.);
#547 = EDGE_CURVE('',#468,#540,#548,.T.);
#548 = LINE('',#549,#550);
#549 = CARTESIAN_POINT('',(3.175,2.9769615,-0.3375));
#550 = VECTOR('',#551,1.);
#551 = DIRECTION('',(0.,0.,1.));
#552 = PLANE('',#553);
#553 = AXIS2_PLACEMENT_3D('',#554,#555,#556);
#554 = CARTESIAN_POINT('',(4.152961500007,2.9769615,-0.3375));
#555 = DIRECTION('',(0.,-1.,0.));
#556 = DIRECTION('',(-1.,0.,0.));
#557 = ADVANCED_FACE('',(#558),#577,.T.);
#558 = FACE_BOUND('',#559,.F.);
#559 = EDGE_LOOP('',(#560,#561,#569,#576));
#560 = ORIENTED_EDGE('',*,*,#515,.T.);
#561 = ORIENTED_EDGE('',*,*,#562,.T.);
#562 = EDGE_CURVE('',#508,#563,#565,.T.);
#563 = VERTEX_POINT('',#564);
#564 = CARTESIAN_POINT('',(4.152961500007,2.6110385,0.3375));
#565 = LINE('',#566,#567);
#566 = CARTESIAN_POINT('',(4.152961500007,2.6110385,-0.3375));
#567 = VECTOR('',#568,1.);
#568 = DIRECTION('',(0.,0.,1.));
#569 = ORIENTED_EDGE('',*,*,#570,.F.);
#570 = EDGE_CURVE('',#532,#563,#571,.T.);
#571 = CIRCLE('',#572,0.365923);
#572 = AXIS2_PLACEMENT_3D('',#573,#574,#575);
#573 = CARTESIAN_POINT('',(3.787038500007,2.6110385,0.3375));
#574 = DIRECTION('',(-0.,-0.,-1.));
#575 = DIRECTION('',(0.,-1.,0.));
#576 = ORIENTED_EDGE('',*,*,#531,.F.);
#577 = CYLINDRICAL_SURFACE('',#578,0.365923);
#578 = AXIS2_PLACEMENT_3D('',#579,#580,#581);
#579 = CARTESIAN_POINT('',(3.787038500007,2.6110385,-0.3375));
#580 = DIRECTION('',(0.,0.,1.));
#581 = DIRECTION('',(0.,1.,0.));
#582 = ADVANCED_FACE('',(#583),#601,.F.);
#583 = FACE_BOUND('',#584,.F.);
#584 = EDGE_LOOP('',(#585,#586,#594,#600));
#585 = ORIENTED_EDGE('',*,*,#547,.T.);
#586 = ORIENTED_EDGE('',*,*,#587,.T.);
#587 = EDGE_CURVE('',#540,#588,#590,.T.);
#588 = VERTEX_POINT('',#589);
#589 = CARTESIAN_POINT('',(3.175,2.6110385,0.3375));
#590 = LINE('',#591,#592);
#591 = CARTESIAN_POINT('',(3.175,2.9769615,0.3375));
#592 = VECTOR('',#593,1.);
#593 = DIRECTION('',(0.,-1.,0.));
#594 = ORIENTED_EDGE('',*,*,#595,.F.);
#595 = EDGE_CURVE('',#476,#588,#596,.T.);
#596 = LINE('',#597,#598);
#597 = CARTESIAN_POINT('',(3.175,2.6110385,-0.3375));
#598 = VECTOR('',#599,1.);
#599 = DIRECTION('',(0.,0.,1.));
#600 = ORIENTED_EDGE('',*,*,#475,.F.);
#601 = PLANE('',#602);
#602 = AXIS2_PLACEMENT_3D('',#603,#604,#605);
#603 = CARTESIAN_POINT('',(3.175,2.9769615,-0.3375));
#604 = DIRECTION('',(1.,0.,0.));
#605 = DIRECTION('',(0.,-1.,0.));
#606 = ADVANCED_FACE('',(#607),#657,.F.);
#607 = FACE_BOUND('',#608,.F.);
#608 = EDGE_LOOP('',(#609,#610,#618,#626,#634,#642,#650,#656));
#609 = ORIENTED_EDGE('',*,*,#507,.F.);
#610 = ORIENTED_EDGE('',*,*,#611,.T.);
#611 = EDGE_CURVE('',#500,#612,#614,.T.);
#612 = VERTEX_POINT('',#613);
#613 = CARTESIAN_POINT('',(4.152961500007,0.,-0.225));
#614 = LINE('',#615,#616);
#615 = CARTESIAN_POINT('',(4.152961500007,0.,-0.3375));
#616 = VECTOR('',#617,1.);
#617 = DIRECTION('',(0.,0.,1.));
#618 = ORIENTED_EDGE('',*,*,#619,.T.);
#619 = EDGE_CURVE('',#612,#620,#622,.T.);
#620 = VERTEX_POINT('',#621);
#621 = CARTESIAN_POINT('',(4.152961500007,-3.,-0.225));
#622 = LINE('',#623,#624);
#623 = CARTESIAN_POINT('',(4.152961500007,0.,-0.225));
#624 = VECTOR('',#625,1.);
#625 = DIRECTION('',(0.,-1.,0.));
#626 = ORIENTED_EDGE('',*,*,#627,.T.);
#627 = EDGE_CURVE('',#620,#628,#630,.T.);
#628 = VERTEX_POINT('',#629);
#629 = CARTESIAN_POINT('',(4.152961500007,-3.,0.225));
#630 = LINE('',#631,#632);
#631 = CARTESIAN_POINT('',(4.152961500007,-3.,-0.3375));
#632 = VECTOR('',#633,1.);
#633 = DIRECTION('',(0.,0.,1.));
#634 = ORIENTED_EDGE('',*,*,#635,.F.);
#635 = EDGE_CURVE('',#636,#628,#638,.T.);
#636 = VERTEX_POINT('',#637);
#637 = CARTESIAN_POINT('',(4.152961500007,0.,0.225));
#638 = LINE('',#639,#640);
#639 = CARTESIAN_POINT('',(4.152961500007,0.,0.225));
#640 = VECTOR('',#641,1.);
#641 = DIRECTION('',(0.,-1.,0.));
#642 = ORIENTED_EDGE('',*,*,#643,.F.);
#643 = EDGE_CURVE('',#644,#636,#646,.T.);
#644 = VERTEX_POINT('',#645);
#645 = CARTESIAN_POINT('',(4.152961500007,0.,0.3375));
#646 = LINE('',#647,#648);
#647 = CARTESIAN_POINT('',(4.152961500007,0.,0.3375));
#648 = VECTOR('',#649,1.);
#649 = DIRECTION('',(0.,0.,-1.));
#650 = ORIENTED_EDGE('',*,*,#651,.T.);
#651 = EDGE_CURVE('',#644,#563,#652,.T.);
#652 = LINE('',#653,#654);
#653 = CARTESIAN_POINT('',(4.152961500007,-3.,0.3375));
#654 = VECTOR('',#655,1.);
#655 = DIRECTION('',(0.,1.,0.));
#656 = ORIENTED_EDGE('',*,*,#562,.F.);
#657 = PLANE('',#658);
#658 = AXIS2_PLACEMENT_3D('',#659,#660,#661);
#659 = CARTESIAN_POINT('',(4.152961500007,-3.,-0.3375));
#660 = DIRECTION('',(-1.,0.,0.));
#661 = DIRECTION('',(0.,1.,0.));
#662 = ADVANCED_FACE('',(#663),#681,.F.);
#663 = FACE_BOUND('',#664,.F.);
#664 = EDGE_LOOP('',(#665,#666,#674,#680));
#665 = ORIENTED_EDGE('',*,*,#595,.T.);
#666 = ORIENTED_EDGE('',*,*,#667,.T.);
#667 = EDGE_CURVE('',#588,#668,#670,.T.);
#668 = VERTEX_POINT('',#669);
#669 = CARTESIAN_POINT('',(3.787038499993,2.6110385,0.3375));
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(3.175,2.6110385,0.3375));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(1.,0.,0.));
#674 = ORIENTED_EDGE('',*,*,#675,.F.);
#675 = EDGE_CURVE('',#484,#668,#676,.T.);
#676 = LINE('',#677,#678);
#677 = CARTESIAN_POINT('',(3.787038499993,2.6110385,-0.3375));
#678 = VECTOR('',#679,1.);
#679 = DIRECTION('',(0.,0.,1.));
#680 = ORIENTED_EDGE('',*,*,#483,.F.);
#681 = PLANE('',#682);
#682 = AXIS2_PLACEMENT_3D('',#683,#684,#685);
#683 = CARTESIAN_POINT('',(3.175,2.6110385,-0.3375));
#684 = DIRECTION('',(0.,1.,0.));
#685 = DIRECTION('',(1.,0.,0.));
#686 = ADVANCED_FACE('',(#687),#705,.F.);
#687 = FACE_BOUND('',#688,.F.);
#688 = EDGE_LOOP('',(#689,#690,#698,#704));
#689 = ORIENTED_EDGE('',*,*,#499,.T.);
#690 = ORIENTED_EDGE('',*,*,#691,.T.);
#691 = EDGE_CURVE('',#492,#692,#694,.T.);
#692 = VERTEX_POINT('',#693);
#693 = CARTESIAN_POINT('',(3.787038500007,0.,-0.225));
#694 = LINE('',#695,#696);
#695 = CARTESIAN_POINT('',(3.787038500007,0.,-0.3375));
#696 = VECTOR('',#697,1.);
#697 = DIRECTION('',(0.,0.,1.));
#698 = ORIENTED_EDGE('',*,*,#699,.F.);
#699 = EDGE_CURVE('',#612,#692,#700,.T.);
#700 = LINE('',#701,#702);
#701 = CARTESIAN_POINT('',(4.152961500007,0.,-0.225));
#702 = VECTOR('',#703,1.);
#703 = DIRECTION('',(-1.,-0.,-0.));
#704 = ORIENTED_EDGE('',*,*,#611,.F.);
#705 = PLANE('',#706);
#706 = AXIS2_PLACEMENT_3D('',#707,#708,#709);
#707 = CARTESIAN_POINT('',(4.152961500007,0.,-0.3375));
#708 = DIRECTION('',(0.,1.,0.));
#709 = DIRECTION('',(0.,0.,1.));
#710 = ADVANCED_FACE('',(#711),#754,.F.);
#711 = FACE_BOUND('',#712,.F.);
#712 = EDGE_LOOP('',(#713,#714,#715,#723,#731,#739,#747,#753));
#713 = ORIENTED_EDGE('',*,*,#491,.F.);
#714 = ORIENTED_EDGE('',*,*,#675,.T.);
#715 = ORIENTED_EDGE('',*,*,#716,.T.);
#716 = EDGE_CURVE('',#668,#717,#719,.T.);
#717 = VERTEX_POINT('',#718);
#718 = CARTESIAN_POINT('',(3.787038500007,0.,0.3375));
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(3.787038499993,2.6110385,0.3375));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(0.,-1.,0.));
#723 = ORIENTED_EDGE('',*,*,#724,.T.);
#724 = EDGE_CURVE('',#717,#725,#727,.T.);
#725 = VERTEX_POINT('',#726);
#726 = CARTESIAN_POINT('',(3.787038500007,0.,0.225));
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(3.787038500007,0.,0.3375));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(0.,0.,-1.));
#731 = ORIENTED_EDGE('',*,*,#732,.T.);
#732 = EDGE_CURVE('',#725,#733,#735,.T.);
#733 = VERTEX_POINT('',#734);
#734 = CARTESIAN_POINT('',(3.787038500007,-3.,0.225));
#735 = LINE('',#736,#737);
#736 = CARTESIAN_POINT('',(3.787038500007,0.,0.225));
#737 = VECTOR('',#738,1.);
#738 = DIRECTION('',(0.,-1.,0.));
#739 = ORIENTED_EDGE('',*,*,#740,.F.);
#740 = EDGE_CURVE('',#741,#733,#743,.T.);
#741 = VERTEX_POINT('',#742);
#742 = CARTESIAN_POINT('',(3.787038500007,-3.,-0.225));
#743 = LINE('',#744,#745);
#744 = CARTESIAN_POINT('',(3.787038499993,-3.,-0.3375));
#745 = VECTOR('',#746,1.);
#746 = DIRECTION('',(0.,0.,1.));
#747 = ORIENTED_EDGE('',*,*,#748,.F.);
#748 = EDGE_CURVE('',#692,#741,#749,.T.);
#749 = LINE('',#750,#751);
#750 = CARTESIAN_POINT('',(3.787038500007,0.,-0.225));
#751 = VECTOR('',#752,1.);
#752 = DIRECTION('',(0.,-1.,0.));
#753 = ORIENTED_EDGE('',*,*,#691,.F.);
#754 = PLANE('',#755);
#755 = AXIS2_PLACEMENT_3D('',#756,#757,#758);
#756 = CARTESIAN_POINT('',(3.787038499993,2.6110385,-0.3375));
#757 = DIRECTION('',(1.,0.,0.));
#758 = DIRECTION('',(0.,-1.,0.));
#759 = ADVANCED_FACE('',(#760),#774,.T.);
#760 = FACE_BOUND('',#761,.T.);
#761 = EDGE_LOOP('',(#762,#763,#764,#765,#766,#772,#773));
#762 = ORIENTED_EDGE('',*,*,#539,.T.);
#763 = ORIENTED_EDGE('',*,*,#587,.T.);
#764 = ORIENTED_EDGE('',*,*,#667,.T.);
#765 = ORIENTED_EDGE('',*,*,#716,.T.);
#766 = ORIENTED_EDGE('',*,*,#767,.F.);
#767 = EDGE_CURVE('',#644,#717,#768,.T.);
#768 = LINE('',#769,#770);
#769 = CARTESIAN_POINT('',(4.152961500007,0.,0.3375));
#770 = VECTOR('',#771,1.);
#771 = DIRECTION('',(-1.,-0.,-0.));
#772 = ORIENTED_EDGE('',*,*,#651,.T.);
#773 = ORIENTED_EDGE('',*,*,#570,.F.);
#774 = PLANE('',#775);
#775 = AXIS2_PLACEMENT_3D('',#776,#777,#778);
#776 = CARTESIAN_POINT('',(3.910868572265,0.235368572262,0.3375));
#777 = DIRECTION('',(0.,0.,1.));
#778 = DIRECTION('',(1.,0.,0.));
#779 = ADVANCED_FACE('',(#780),#791,.F.);
#780 = FACE_BOUND('',#781,.F.);
#781 = EDGE_LOOP('',(#782,#783,#784,#790));
#782 = ORIENTED_EDGE('',*,*,#699,.T.);
#783 = ORIENTED_EDGE('',*,*,#748,.T.);
#784 = ORIENTED_EDGE('',*,*,#785,.F.);
#785 = EDGE_CURVE('',#620,#741,#786,.T.);
#786 = LINE('',#787,#788);
#787 = CARTESIAN_POINT('',(4.152961500007,-3.,-0.225));
#788 = VECTOR('',#789,1.);
#789 = DIRECTION('',(-1.,-0.,-0.));
#790 = ORIENTED_EDGE('',*,*,#619,.F.);
#791 = PLANE('',#792);
#792 = AXIS2_PLACEMENT_3D('',#793,#794,#795);
#793 = CARTESIAN_POINT('',(4.152961500007,0.,-0.225));
#794 = DIRECTION('',(0.,0.,1.));
#795 = DIRECTION('',(0.,-1.,0.));
#796 = ADVANCED_FACE('',(#797),#808,.T.);
#797 = FACE_BOUND('',#798,.T.);
#798 = EDGE_LOOP('',(#799,#800,#801,#807));
#799 = ORIENTED_EDGE('',*,*,#767,.T.);
#800 = ORIENTED_EDGE('',*,*,#724,.T.);
#801 = ORIENTED_EDGE('',*,*,#802,.F.);
#802 = EDGE_CURVE('',#636,#725,#803,.T.);
#803 = LINE('',#804,#805);
#804 = CARTESIAN_POINT('',(4.152961500007,0.,0.225));
#805 = VECTOR('',#806,1.);
#806 = DIRECTION('',(-1.,-0.,-0.));
#807 = ORIENTED_EDGE('',*,*,#643,.F.);
#808 = PLANE('',#809);
#809 = AXIS2_PLACEMENT_3D('',#810,#811,#812);
#810 = CARTESIAN_POINT('',(4.152961500007,0.,0.3375));
#811 = DIRECTION('',(0.,-1.,0.));
#812 = DIRECTION('',(0.,0.,-1.));
#813 = ADVANCED_FACE('',(#814),#825,.F.);
#814 = FACE_BOUND('',#815,.F.);
#815 = EDGE_LOOP('',(#816,#817,#818,#824));
#816 = ORIENTED_EDGE('',*,*,#785,.T.);
#817 = ORIENTED_EDGE('',*,*,#740,.T.);
#818 = ORIENTED_EDGE('',*,*,#819,.F.);
#819 = EDGE_CURVE('',#628,#733,#820,.T.);
#820 = LINE('',#821,#822);
#821 = CARTESIAN_POINT('',(4.152961500007,-3.,0.225));
#822 = VECTOR('',#823,1.);
#823 = DIRECTION('',(-1.,-0.,-0.));
#824 = ORIENTED_EDGE('',*,*,#627,.F.);
#825 = PLANE('',#826);
#826 = AXIS2_PLACEMENT_3D('',#827,#828,#829);
#827 = CARTESIAN_POINT('',(3.787038499993,-3.,-0.3375));
#828 = DIRECTION('',(0.,1.,0.));
#829 = DIRECTION('',(1.,0.,0.));
#830 = ADVANCED_FACE('',(#831),#837,.T.);
#831 = FACE_BOUND('',#832,.T.);
#832 = EDGE_LOOP('',(#833,#834,#835,#836));
#833 = ORIENTED_EDGE('',*,*,#802,.T.);
#834 = ORIENTED_EDGE('',*,*,#732,.T.);
#835 = ORIENTED_EDGE('',*,*,#819,.F.);
#836 = ORIENTED_EDGE('',*,*,#635,.F.);
#837 = PLANE('',#838);
#838 = AXIS2_PLACEMENT_3D('',#839,#840,#841);
#839 = CARTESIAN_POINT('',(4.152961500007,0.,0.225));
#840 = DIRECTION('',(0.,0.,1.));
#841 = DIRECTION('',(0.,-1.,0.));
#842 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#846)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#843,#844,#845)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#843 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#844 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#845 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#846 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#843,
  'distance_accuracy_value','confusion accuracy');
#847 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#848,#850);
#848 = ( REPRESENTATION_RELATIONSHIP('','',#458,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#849) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#849 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#850 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#851
  );
#851 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9500','Body001','',#5,#453,$);
#852 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#455));
#853 = SHAPE_DEFINITION_REPRESENTATION(#854,#860);
#854 = PRODUCT_DEFINITION_SHAPE('','',#855);
#855 = PRODUCT_DEFINITION('design','',#856,#859);
#856 = PRODUCT_DEFINITION_FORMATION('','',#857);
#857 = PRODUCT('Body001 (Mirror #1)','Body001 (Mirror #1)','',(#858));
#858 = PRODUCT_CONTEXT('',#2,'mechanical');
#859 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#860 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#861),#1244);
#861 = MANIFOLD_SOLID_BREP('',#862);
#862 = CLOSED_SHELL('',(#863,#928,#959,#984,#1008,#1064,#1088,#1112,
    #1161,#1181,#1198,#1215,#1232));
#863 = ADVANCED_FACE('',(#864),#923,.F.);
#864 = FACE_BOUND('',#865,.T.);
#865 = EDGE_LOOP('',(#866,#876,#884,#892,#900,#908,#916));
#866 = ORIENTED_EDGE('',*,*,#867,.T.);
#867 = EDGE_CURVE('',#868,#870,#872,.T.);
#868 = VERTEX_POINT('',#869);
#869 = CARTESIAN_POINT('',(-3.787038500007,2.9769615,3.4725));
#870 = VERTEX_POINT('',#871);
#871 = CARTESIAN_POINT('',(-3.175,2.9769615,3.4725));
#872 = LINE('',#873,#874);
#873 = CARTESIAN_POINT('',(-4.152961500007,2.9769615,3.4725));
#874 = VECTOR('',#875,1.);
#875 = DIRECTION('',(1.,0.,0.));
#876 = ORIENTED_EDGE('',*,*,#877,.T.);
#877 = EDGE_CURVE('',#870,#878,#880,.T.);
#878 = VERTEX_POINT('',#879);
#879 = CARTESIAN_POINT('',(-3.175,2.6110385,3.4725));
#880 = LINE('',#881,#882);
#881 = CARTESIAN_POINT('',(-3.175,2.9769615,3.4725));
#882 = VECTOR('',#883,1.);
#883 = DIRECTION('',(-0.,-1.,-0.));
#884 = ORIENTED_EDGE('',*,*,#885,.T.);
#885 = EDGE_CURVE('',#878,#886,#888,.T.);
#886 = VERTEX_POINT('',#887);
#887 = CARTESIAN_POINT('',(-3.787038499993,2.6110385,3.4725));
#888 = LINE('',#889,#890);
#889 = CARTESIAN_POINT('',(-3.175,2.6110385,3.4725));
#890 = VECTOR('',#891,1.);
#891 = DIRECTION('',(-1.,-0.,-0.));
#892 = ORIENTED_EDGE('',*,*,#893,.T.);
#893 = EDGE_CURVE('',#886,#894,#896,.T.);
#894 = VERTEX_POINT('',#895);
#895 = CARTESIAN_POINT('',(-3.787038500007,0.,3.4725));
#896 = LINE('',#897,#898);
#897 = CARTESIAN_POINT('',(-3.787038499993,2.6110385,3.4725));
#898 = VECTOR('',#899,1.);
#899 = DIRECTION('',(-0.,-1.,-0.));
#900 = ORIENTED_EDGE('',*,*,#901,.F.);
#901 = EDGE_CURVE('',#902,#894,#904,.T.);
#902 = VERTEX_POINT('',#903);
#903 = CARTESIAN_POINT('',(-4.152961500007,0.,3.4725));
#904 = LINE('',#905,#906);
#905 = CARTESIAN_POINT('',(-4.152961500007,0.,3.4725));
#906 = VECTOR('',#907,1.);
#907 = DIRECTION('',(1.,0.,0.));
#908 = ORIENTED_EDGE('',*,*,#909,.T.);
#909 = EDGE_CURVE('',#902,#910,#912,.T.);
#910 = VERTEX_POINT('',#911);
#911 = CARTESIAN_POINT('',(-4.152961500007,2.6110385,3.4725));
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(-4.152961500007,-3.,3.4725));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(0.,1.,0.));
#916 = ORIENTED_EDGE('',*,*,#917,.F.);
#917 = EDGE_CURVE('',#868,#910,#918,.T.);
#918 = CIRCLE('',#919,0.365923);
#919 = AXIS2_PLACEMENT_3D('',#920,#921,#922);
#920 = CARTESIAN_POINT('',(-3.787038500007,2.6110385,3.4725));
#921 = DIRECTION('',(0.,-0.,1.));
#922 = DIRECTION('',(-0.,-1.,-0.));
#923 = PLANE('',#924);
#924 = AXIS2_PLACEMENT_3D('',#925,#926,#927);
#925 = CARTESIAN_POINT('',(-3.910868572265,0.235368572262,3.4725));
#926 = DIRECTION('',(0.,0.,1.));
#927 = DIRECTION('',(1.,0.,0.));
#928 = ADVANCED_FACE('',(#929),#954,.F.);
#929 = FACE_BOUND('',#930,.T.);
#930 = EDGE_LOOP('',(#931,#932,#940,#948));
#931 = ORIENTED_EDGE('',*,*,#867,.F.);
#932 = ORIENTED_EDGE('',*,*,#933,.T.);
#933 = EDGE_CURVE('',#868,#934,#936,.T.);
#934 = VERTEX_POINT('',#935);
#935 = CARTESIAN_POINT('',(-3.787038500007,2.9769615,4.1475));
#936 = LINE('',#937,#938);
#937 = CARTESIAN_POINT('',(-3.787038500007,2.9769615,3.4725));
#938 = VECTOR('',#939,1.);
#939 = DIRECTION('',(-0.,-0.,1.));
#940 = ORIENTED_EDGE('',*,*,#941,.T.);
#941 = EDGE_CURVE('',#934,#942,#944,.T.);
#942 = VERTEX_POINT('',#943);
#943 = CARTESIAN_POINT('',(-3.175,2.9769615,4.1475));
#944 = LINE('',#945,#946);
#945 = CARTESIAN_POINT('',(-4.152961500007,2.9769615,4.1475));
#946 = VECTOR('',#947,1.);
#947 = DIRECTION('',(1.,0.,0.));
#948 = ORIENTED_EDGE('',*,*,#949,.F.);
#949 = EDGE_CURVE('',#870,#942,#950,.T.);
#950 = LINE('',#951,#952);
#951 = CARTESIAN_POINT('',(-3.175,2.9769615,3.4725));
#952 = VECTOR('',#953,1.);
#953 = DIRECTION('',(0.,0.,1.));
#954 = PLANE('',#955);
#955 = AXIS2_PLACEMENT_3D('',#956,#957,#958);
#956 = CARTESIAN_POINT('',(-4.152961500007,2.9769615,3.4725));
#957 = DIRECTION('',(-0.,-1.,-0.));
#958 = DIRECTION('',(-1.,0.,0.));
#959 = ADVANCED_FACE('',(#960),#979,.T.);
#960 = FACE_BOUND('',#961,.T.);
#961 = EDGE_LOOP('',(#962,#963,#971,#978));
#962 = ORIENTED_EDGE('',*,*,#917,.T.);
#963 = ORIENTED_EDGE('',*,*,#964,.T.);
#964 = EDGE_CURVE('',#910,#965,#967,.T.);
#965 = VERTEX_POINT('',#966);
#966 = CARTESIAN_POINT('',(-4.152961500007,2.6110385,4.1475));
#967 = LINE('',#968,#969);
#968 = CARTESIAN_POINT('',(-4.152961500007,2.6110385,3.4725));
#969 = VECTOR('',#970,1.);
#970 = DIRECTION('',(-0.,-0.,1.));
#971 = ORIENTED_EDGE('',*,*,#972,.F.);
#972 = EDGE_CURVE('',#934,#965,#973,.T.);
#973 = CIRCLE('',#974,0.365923);
#974 = AXIS2_PLACEMENT_3D('',#975,#976,#977);
#975 = CARTESIAN_POINT('',(-3.787038500007,2.6110385,4.1475));
#976 = DIRECTION('',(0.,-0.,1.));
#977 = DIRECTION('',(-0.,-1.,-0.));
#978 = ORIENTED_EDGE('',*,*,#933,.F.);
#979 = CYLINDRICAL_SURFACE('',#980,0.365923);
#980 = AXIS2_PLACEMENT_3D('',#981,#982,#983);
#981 = CARTESIAN_POINT('',(-3.787038500007,2.6110385,3.4725));
#982 = DIRECTION('',(-0.,-0.,1.));
#983 = DIRECTION('',(-0.,1.,-0.));
#984 = ADVANCED_FACE('',(#985),#1003,.F.);
#985 = FACE_BOUND('',#986,.T.);
#986 = EDGE_LOOP('',(#987,#988,#996,#1002));
#987 = ORIENTED_EDGE('',*,*,#949,.T.);
#988 = ORIENTED_EDGE('',*,*,#989,.T.);
#989 = EDGE_CURVE('',#942,#990,#992,.T.);
#990 = VERTEX_POINT('',#991);
#991 = CARTESIAN_POINT('',(-3.175,2.6110385,4.1475));
#992 = LINE('',#993,#994);
#993 = CARTESIAN_POINT('',(-3.175,2.9769615,4.1475));
#994 = VECTOR('',#995,1.);
#995 = DIRECTION('',(-0.,-1.,-0.));
#996 = ORIENTED_EDGE('',*,*,#997,.F.);
#997 = EDGE_CURVE('',#878,#990,#998,.T.);
#998 = LINE('',#999,#1000);
#999 = CARTESIAN_POINT('',(-3.175,2.6110385,3.4725));
#1000 = VECTOR('',#1001,1.);
#1001 = DIRECTION('',(0.,0.,1.));
#1002 = ORIENTED_EDGE('',*,*,#877,.F.);
#1003 = PLANE('',#1004);
#1004 = AXIS2_PLACEMENT_3D('',#1005,#1006,#1007);
#1005 = CARTESIAN_POINT('',(-3.175,2.9769615,3.4725));
#1006 = DIRECTION('',(-1.,-0.,-0.));
#1007 = DIRECTION('',(0.,1.,0.));
#1008 = ADVANCED_FACE('',(#1009),#1059,.F.);
#1009 = FACE_BOUND('',#1010,.T.);
#1010 = EDGE_LOOP('',(#1011,#1012,#1020,#1028,#1036,#1044,#1052,#1058));
#1011 = ORIENTED_EDGE('',*,*,#909,.F.);
#1012 = ORIENTED_EDGE('',*,*,#1013,.T.);
#1013 = EDGE_CURVE('',#902,#1014,#1016,.T.);
#1014 = VERTEX_POINT('',#1015);
#1015 = CARTESIAN_POINT('',(-4.152961500007,0.,3.585));
#1016 = LINE('',#1017,#1018);
#1017 = CARTESIAN_POINT('',(-4.152961500007,0.,3.4725));
#1018 = VECTOR('',#1019,1.);
#1019 = DIRECTION('',(0.,0.,1.));
#1020 = ORIENTED_EDGE('',*,*,#1021,.T.);
#1021 = EDGE_CURVE('',#1014,#1022,#1024,.T.);
#1022 = VERTEX_POINT('',#1023);
#1023 = CARTESIAN_POINT('',(-4.152961500007,-3.,3.585));
#1024 = LINE('',#1025,#1026);
#1025 = CARTESIAN_POINT('',(-4.152961500007,0.,3.585));
#1026 = VECTOR('',#1027,1.);
#1027 = DIRECTION('',(-0.,-1.,-0.));
#1028 = ORIENTED_EDGE('',*,*,#1029,.T.);
#1029 = EDGE_CURVE('',#1022,#1030,#1032,.T.);
#1030 = VERTEX_POINT('',#1031);
#1031 = CARTESIAN_POINT('',(-4.152961500007,-3.,4.035));
#1032 = LINE('',#1033,#1034);
#1033 = CARTESIAN_POINT('',(-4.152961500007,-3.,3.4725));
#1034 = VECTOR('',#1035,1.);
#1035 = DIRECTION('',(0.,0.,1.));
#1036 = ORIENTED_EDGE('',*,*,#1037,.F.);
#1037 = EDGE_CURVE('',#1038,#1030,#1040,.T.);
#1038 = VERTEX_POINT('',#1039);
#1039 = CARTESIAN_POINT('',(-4.152961500007,0.,4.035));
#1040 = LINE('',#1041,#1042);
#1041 = CARTESIAN_POINT('',(-4.152961500007,0.,4.035));
#1042 = VECTOR('',#1043,1.);
#1043 = DIRECTION('',(-0.,-1.,-0.));
#1044 = ORIENTED_EDGE('',*,*,#1045,.F.);
#1045 = EDGE_CURVE('',#1046,#1038,#1048,.T.);
#1046 = VERTEX_POINT('',#1047);
#1047 = CARTESIAN_POINT('',(-4.152961500007,0.,4.1475));
#1048 = LINE('',#1049,#1050);
#1049 = CARTESIAN_POINT('',(-4.152961500007,0.,4.1475));
#1050 = VECTOR('',#1051,1.);
#1051 = DIRECTION('',(-0.,-0.,-1.));
#1052 = ORIENTED_EDGE('',*,*,#1053,.T.);
#1053 = EDGE_CURVE('',#1046,#965,#1054,.T.);
#1054 = LINE('',#1055,#1056);
#1055 = CARTESIAN_POINT('',(-4.152961500007,-3.,4.1475));
#1056 = VECTOR('',#1057,1.);
#1057 = DIRECTION('',(0.,1.,0.));
#1058 = ORIENTED_EDGE('',*,*,#964,.F.);
#1059 = PLANE('',#1060);
#1060 = AXIS2_PLACEMENT_3D('',#1061,#1062,#1063);
#1061 = CARTESIAN_POINT('',(-4.152961500007,-3.,3.4725));
#1062 = DIRECTION('',(1.,0.,0.));
#1063 = DIRECTION('',(0.,-1.,0.));
#1064 = ADVANCED_FACE('',(#1065),#1083,.F.);
#1065 = FACE_BOUND('',#1066,.T.);
#1066 = EDGE_LOOP('',(#1067,#1068,#1076,#1082));
#1067 = ORIENTED_EDGE('',*,*,#997,.T.);
#1068 = ORIENTED_EDGE('',*,*,#1069,.T.);
#1069 = EDGE_CURVE('',#990,#1070,#1072,.T.);
#1070 = VERTEX_POINT('',#1071);
#1071 = CARTESIAN_POINT('',(-3.787038499993,2.6110385,4.1475));
#1072 = LINE('',#1073,#1074);
#1073 = CARTESIAN_POINT('',(-3.175,2.6110385,4.1475));
#1074 = VECTOR('',#1075,1.);
#1075 = DIRECTION('',(-1.,-0.,-0.));
#1076 = ORIENTED_EDGE('',*,*,#1077,.F.);
#1077 = EDGE_CURVE('',#886,#1070,#1078,.T.);
#1078 = LINE('',#1079,#1080);
#1079 = CARTESIAN_POINT('',(-3.787038499993,2.6110385,3.4725));
#1080 = VECTOR('',#1081,1.);
#1081 = DIRECTION('',(0.,0.,1.));
#1082 = ORIENTED_EDGE('',*,*,#885,.F.);
#1083 = PLANE('',#1084);
#1084 = AXIS2_PLACEMENT_3D('',#1085,#1086,#1087);
#1085 = CARTESIAN_POINT('',(-3.175,2.6110385,3.4725));
#1086 = DIRECTION('',(0.,1.,0.));
#1087 = DIRECTION('',(1.,0.,0.));
#1088 = ADVANCED_FACE('',(#1089),#1107,.F.);
#1089 = FACE_BOUND('',#1090,.T.);
#1090 = EDGE_LOOP('',(#1091,#1092,#1100,#1106));
#1091 = ORIENTED_EDGE('',*,*,#901,.T.);
#1092 = ORIENTED_EDGE('',*,*,#1093,.T.);
#1093 = EDGE_CURVE('',#894,#1094,#1096,.T.);
#1094 = VERTEX_POINT('',#1095);
#1095 = CARTESIAN_POINT('',(-3.787038500007,0.,3.585));
#1096 = LINE('',#1097,#1098);
#1097 = CARTESIAN_POINT('',(-3.787038500007,0.,3.4725));
#1098 = VECTOR('',#1099,1.);
#1099 = DIRECTION('',(0.,0.,1.));
#1100 = ORIENTED_EDGE('',*,*,#1101,.F.);
#1101 = EDGE_CURVE('',#1014,#1094,#1102,.T.);
#1102 = LINE('',#1103,#1104);
#1103 = CARTESIAN_POINT('',(-4.152961500007,0.,3.585));
#1104 = VECTOR('',#1105,1.);
#1105 = DIRECTION('',(1.,0.,0.));
#1106 = ORIENTED_EDGE('',*,*,#1013,.F.);
#1107 = PLANE('',#1108);
#1108 = AXIS2_PLACEMENT_3D('',#1109,#1110,#1111);
#1109 = CARTESIAN_POINT('',(-4.152961500007,0.,3.4725));
#1110 = DIRECTION('',(0.,1.,0.));
#1111 = DIRECTION('',(0.,0.,-1.));
#1112 = ADVANCED_FACE('',(#1113),#1156,.F.);
#1113 = FACE_BOUND('',#1114,.T.);
#1114 = EDGE_LOOP('',(#1115,#1116,#1117,#1125,#1133,#1141,#1149,#1155));
#1115 = ORIENTED_EDGE('',*,*,#893,.F.);
#1116 = ORIENTED_EDGE('',*,*,#1077,.T.);
#1117 = ORIENTED_EDGE('',*,*,#1118,.T.);
#1118 = EDGE_CURVE('',#1070,#1119,#1121,.T.);
#1119 = VERTEX_POINT('',#1120);
#1120 = CARTESIAN_POINT('',(-3.787038500007,0.,4.1475));
#1121 = LINE('',#1122,#1123);
#1122 = CARTESIAN_POINT('',(-3.787038499993,2.6110385,4.1475));
#1123 = VECTOR('',#1124,1.);
#1124 = DIRECTION('',(-0.,-1.,-0.));
#1125 = ORIENTED_EDGE('',*,*,#1126,.T.);
#1126 = EDGE_CURVE('',#1119,#1127,#1129,.T.);
#1127 = VERTEX_POINT('',#1128);
#1128 = CARTESIAN_POINT('',(-3.787038500007,0.,4.035));
#1129 = LINE('',#1130,#1131);
#1130 = CARTESIAN_POINT('',(-3.787038500007,0.,4.1475));
#1131 = VECTOR('',#1132,1.);
#1132 = DIRECTION('',(-0.,-0.,-1.));
#1133 = ORIENTED_EDGE('',*,*,#1134,.T.);
#1134 = EDGE_CURVE('',#1127,#1135,#1137,.T.);
#1135 = VERTEX_POINT('',#1136);
#1136 = CARTESIAN_POINT('',(-3.787038500007,-3.,4.035));
#1137 = LINE('',#1138,#1139);
#1138 = CARTESIAN_POINT('',(-3.787038500007,0.,4.035));
#1139 = VECTOR('',#1140,1.);
#1140 = DIRECTION('',(-0.,-1.,-0.));
#1141 = ORIENTED_EDGE('',*,*,#1142,.F.);
#1142 = EDGE_CURVE('',#1143,#1135,#1145,.T.);
#1143 = VERTEX_POINT('',#1144);
#1144 = CARTESIAN_POINT('',(-3.787038500007,-3.,3.585));
#1145 = LINE('',#1146,#1147);
#1146 = CARTESIAN_POINT('',(-3.787038499993,-3.,3.4725));
#1147 = VECTOR('',#1148,1.);
#1148 = DIRECTION('',(0.,0.,1.));
#1149 = ORIENTED_EDGE('',*,*,#1150,.F.);
#1150 = EDGE_CURVE('',#1094,#1143,#1151,.T.);
#1151 = LINE('',#1152,#1153);
#1152 = CARTESIAN_POINT('',(-3.787038500007,0.,3.585));
#1153 = VECTOR('',#1154,1.);
#1154 = DIRECTION('',(-0.,-1.,-0.));
#1155 = ORIENTED_EDGE('',*,*,#1093,.F.);
#1156 = PLANE('',#1157);
#1157 = AXIS2_PLACEMENT_3D('',#1158,#1159,#1160);
#1158 = CARTESIAN_POINT('',(-3.787038499993,2.6110385,3.4725));
#1159 = DIRECTION('',(-1.,-0.,-0.));
#1160 = DIRECTION('',(0.,1.,0.));
#1161 = ADVANCED_FACE('',(#1162),#1176,.T.);
#1162 = FACE_BOUND('',#1163,.F.);
#1163 = EDGE_LOOP('',(#1164,#1165,#1166,#1167,#1168,#1174,#1175));
#1164 = ORIENTED_EDGE('',*,*,#941,.T.);
#1165 = ORIENTED_EDGE('',*,*,#989,.T.);
#1166 = ORIENTED_EDGE('',*,*,#1069,.T.);
#1167 = ORIENTED_EDGE('',*,*,#1118,.T.);
#1168 = ORIENTED_EDGE('',*,*,#1169,.F.);
#1169 = EDGE_CURVE('',#1046,#1119,#1170,.T.);
#1170 = LINE('',#1171,#1172);
#1171 = CARTESIAN_POINT('',(-4.152961500007,0.,4.1475));
#1172 = VECTOR('',#1173,1.);
#1173 = DIRECTION('',(1.,0.,0.));
#1174 = ORIENTED_EDGE('',*,*,#1053,.T.);
#1175 = ORIENTED_EDGE('',*,*,#972,.F.);
#1176 = PLANE('',#1177);
#1177 = AXIS2_PLACEMENT_3D('',#1178,#1179,#1180);
#1178 = CARTESIAN_POINT('',(-3.910868572265,0.235368572262,4.1475));
#1179 = DIRECTION('',(0.,0.,1.));
#1180 = DIRECTION('',(1.,0.,0.));
#1181 = ADVANCED_FACE('',(#1182),#1193,.F.);
#1182 = FACE_BOUND('',#1183,.T.);
#1183 = EDGE_LOOP('',(#1184,#1185,#1186,#1192));
#1184 = ORIENTED_EDGE('',*,*,#1101,.T.);
#1185 = ORIENTED_EDGE('',*,*,#1150,.T.);
#1186 = ORIENTED_EDGE('',*,*,#1187,.F.);
#1187 = EDGE_CURVE('',#1022,#1143,#1188,.T.);
#1188 = LINE('',#1189,#1190);
#1189 = CARTESIAN_POINT('',(-4.152961500007,-3.,3.585));
#1190 = VECTOR('',#1191,1.);
#1191 = DIRECTION('',(1.,0.,0.));
#1192 = ORIENTED_EDGE('',*,*,#1021,.F.);
#1193 = PLANE('',#1194);
#1194 = AXIS2_PLACEMENT_3D('',#1195,#1196,#1197);
#1195 = CARTESIAN_POINT('',(-4.152961500007,0.,3.585));
#1196 = DIRECTION('',(0.,0.,1.));
#1197 = DIRECTION('',(0.,1.,0.));
#1198 = ADVANCED_FACE('',(#1199),#1210,.T.);
#1199 = FACE_BOUND('',#1200,.F.);
#1200 = EDGE_LOOP('',(#1201,#1202,#1203,#1209));
#1201 = ORIENTED_EDGE('',*,*,#1169,.T.);
#1202 = ORIENTED_EDGE('',*,*,#1126,.T.);
#1203 = ORIENTED_EDGE('',*,*,#1204,.F.);
#1204 = EDGE_CURVE('',#1038,#1127,#1205,.T.);
#1205 = LINE('',#1206,#1207);
#1206 = CARTESIAN_POINT('',(-4.152961500007,0.,4.035));
#1207 = VECTOR('',#1208,1.);
#1208 = DIRECTION('',(1.,0.,0.));
#1209 = ORIENTED_EDGE('',*,*,#1045,.F.);
#1210 = PLANE('',#1211);
#1211 = AXIS2_PLACEMENT_3D('',#1212,#1213,#1214);
#1212 = CARTESIAN_POINT('',(-4.152961500007,0.,4.1475));
#1213 = DIRECTION('',(-0.,-1.,-0.));
#1214 = DIRECTION('',(0.,0.,1.));
#1215 = ADVANCED_FACE('',(#1216),#1227,.F.);
#1216 = FACE_BOUND('',#1217,.T.);
#1217 = EDGE_LOOP('',(#1218,#1219,#1220,#1226));
#1218 = ORIENTED_EDGE('',*,*,#1187,.T.);
#1219 = ORIENTED_EDGE('',*,*,#1142,.T.);
#1220 = ORIENTED_EDGE('',*,*,#1221,.F.);
#1221 = EDGE_CURVE('',#1030,#1135,#1222,.T.);
#1222 = LINE('',#1223,#1224);
#1223 = CARTESIAN_POINT('',(-4.152961500007,-3.,4.035));
#1224 = VECTOR('',#1225,1.);
#1225 = DIRECTION('',(1.,0.,0.));
#1226 = ORIENTED_EDGE('',*,*,#1029,.F.);
#1227 = PLANE('',#1228);
#1228 = AXIS2_PLACEMENT_3D('',#1229,#1230,#1231);
#1229 = CARTESIAN_POINT('',(-3.787038499993,-3.,3.4725));
#1230 = DIRECTION('',(0.,1.,0.));
#1231 = DIRECTION('',(1.,0.,0.));
#1232 = ADVANCED_FACE('',(#1233),#1239,.T.);
#1233 = FACE_BOUND('',#1234,.F.);
#1234 = EDGE_LOOP('',(#1235,#1236,#1237,#1238));
#1235 = ORIENTED_EDGE('',*,*,#1204,.T.);
#1236 = ORIENTED_EDGE('',*,*,#1134,.T.);
#1237 = ORIENTED_EDGE('',*,*,#1221,.F.);
#1238 = ORIENTED_EDGE('',*,*,#1037,.F.);
#1239 = PLANE('',#1240);
#1240 = AXIS2_PLACEMENT_3D('',#1241,#1242,#1243);
#1241 = CARTESIAN_POINT('',(-4.152961500007,0.,4.035));
#1242 = DIRECTION('',(0.,0.,1.));
#1243 = DIRECTION('',(0.,1.,0.));
#1244 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1248)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1245,#1246,#1247)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1245 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1246 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1247 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1248 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1245,
  'distance_accuracy_value','confusion accuracy');
#1249 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1250,#1252);
#1250 = ( REPRESENTATION_RELATIONSHIP('','',#860,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1251) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1251 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1252 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1253);
#1253 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9501','Body001 (Mirror #1)','',
  #5,#855,$);
#1254 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#857));
#1255 = SHAPE_DEFINITION_REPRESENTATION(#1256,#1262);
#1256 = PRODUCT_DEFINITION_SHAPE('','',#1257);
#1257 = PRODUCT_DEFINITION('design','',#1258,#1261);
#1258 = PRODUCT_DEFINITION_FORMATION('','',#1259);
#1259 = PRODUCT('Array','Array','',(#1260));
#1260 = PRODUCT_CONTEXT('',#2,'mechanical');
#1261 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1262 = SHAPE_REPRESENTATION('',(#11,#1263,#1267,#1271,#1275),#1279);
#1263 = AXIS2_PLACEMENT_3D('',#1264,#1265,#1266);
#1264 = CARTESIAN_POINT('',(0.,0.,0.));
#1265 = DIRECTION('',(0.,0.,1.));
#1266 = DIRECTION('',(1.,0.,0.));
#1267 = AXIS2_PLACEMENT_3D('',#1268,#1269,#1270);
#1268 = CARTESIAN_POINT('',(0.,0.,-2.54));
#1269 = DIRECTION('',(0.,0.,1.));
#1270 = DIRECTION('',(1.,0.,0.));
#1271 = AXIS2_PLACEMENT_3D('',#1272,#1273,#1274);
#1272 = CARTESIAN_POINT('',(0.,0.,-5.08));
#1273 = DIRECTION('',(0.,0.,1.));
#1274 = DIRECTION('',(1.,0.,0.));
#1275 = AXIS2_PLACEMENT_3D('',#1276,#1277,#1278);
#1276 = CARTESIAN_POINT('',(0.,0.,-7.62));
#1277 = DIRECTION('',(0.,0.,1.));
#1278 = DIRECTION('',(1.,0.,0.));
#1279 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1283)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1280,#1281,#1282)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1280 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1281 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1282 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1283 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1280,
  'distance_accuracy_value','confusion accuracy');
#1284 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1285,#1287);
#1285 = ( REPRESENTATION_RELATIONSHIP('','',#1262,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1286) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1286 = ITEM_DEFINED_TRANSFORMATION('','',#11,#27);
#1287 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1288);
#1288 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9506','Array','',#5,#1257,$);
#1289 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1259));
#1290 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1291,#1293);
#1291 = ( REPRESENTATION_RELATIONSHIP('','',#860,#1262) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1292) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1292 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1263);
#1293 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1294);
#1294 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9502','0','',#1257,#855,$);
#1295 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1296,#1298);
#1296 = ( REPRESENTATION_RELATIONSHIP('','',#860,#1262) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1297) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1297 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1267);
#1298 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1299);
#1299 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9503','1','',#1257,#855,$);
#1300 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1301,#1303);
#1301 = ( REPRESENTATION_RELATIONSHIP('','',#860,#1262) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1302) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1302 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1271);
#1303 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1304);
#1304 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9504','2','',#1257,#855,$);
#1305 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1306,#1308);
#1306 = ( REPRESENTATION_RELATIONSHIP('','',#860,#1262) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1307) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1307 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1275);
#1308 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1309);
#1309 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9505','3','',#1257,#855,$);
#1310 = SHAPE_DEFINITION_REPRESENTATION(#1311,#1317);
#1311 = PRODUCT_DEFINITION_SHAPE('','',#1312);
#1312 = PRODUCT_DEFINITION('design','',#1313,#1316);
#1313 = PRODUCT_DEFINITION_FORMATION('','',#1314);
#1314 = PRODUCT('Array001','Array001','',(#1315));
#1315 = PRODUCT_CONTEXT('',#2,'mechanical');
#1316 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1317 = SHAPE_REPRESENTATION('',(#11,#1318,#1322,#1326,#1330),#1334);
#1318 = AXIS2_PLACEMENT_3D('',#1319,#1320,#1321);
#1319 = CARTESIAN_POINT('',(0.,0.,3.81));
#1320 = DIRECTION('',(0.,0.,1.));
#1321 = DIRECTION('',(1.,0.,0.));
#1322 = AXIS2_PLACEMENT_3D('',#1323,#1324,#1325);
#1323 = CARTESIAN_POINT('',(0.,0.,1.27));
#1324 = DIRECTION('',(0.,0.,1.));
#1325 = DIRECTION('',(1.,0.,0.));
#1326 = AXIS2_PLACEMENT_3D('',#1327,#1328,#1329);
#1327 = CARTESIAN_POINT('',(0.,0.,-1.27));
#1328 = DIRECTION('',(0.,0.,1.));
#1329 = DIRECTION('',(1.,0.,0.));
#1330 = AXIS2_PLACEMENT_3D('',#1331,#1332,#1333);
#1331 = CARTESIAN_POINT('',(0.,0.,-3.81));
#1332 = DIRECTION('',(0.,0.,1.));
#1333 = DIRECTION('',(1.,0.,0.));
#1334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1338)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1335,#1336,#1337)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1335 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1336 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1337 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1338 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1335,
  'distance_accuracy_value','confusion accuracy');
#1339 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1340,#1342);
#1340 = ( REPRESENTATION_RELATIONSHIP('','',#1317,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1341) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1341 = ITEM_DEFINED_TRANSFORMATION('','',#11,#31);
#1342 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1343);
#1343 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9511','Array001','',#5,#1312,$);
#1344 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1314));
#1345 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1346,#1348);
#1346 = ( REPRESENTATION_RELATIONSHIP('','',#458,#1317) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1347) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1347 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1318);
#1348 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1349);
#1349 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9507','0','',#1312,#453,$);
#1350 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1351,#1353);
#1351 = ( REPRESENTATION_RELATIONSHIP('','',#458,#1317) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1352) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1352 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1322);
#1353 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1354);
#1354 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9508','1','',#1312,#453,$);
#1355 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1356,#1358);
#1356 = ( REPRESENTATION_RELATIONSHIP('','',#458,#1317) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1357) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1357 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1326);
#1358 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1359);
#1359 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9509','2','',#1312,#453,$);
#1360 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1361,#1363);
#1361 = ( REPRESENTATION_RELATIONSHIP('','',#458,#1317) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1362) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1362 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1330);
#1363 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1364);
#1364 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9510','3','',#1312,#453,$);
#1365 = SHAPE_DEFINITION_REPRESENTATION(#1366,#1372);
#1366 = PRODUCT_DEFINITION_SHAPE('','',#1367);
#1367 = PRODUCT_DEFINITION('design','',#1368,#1371);
#1368 = PRODUCT_DEFINITION_FORMATION('','',#1369);
#1369 = PRODUCT('Body002','Body002','',(#1370));
#1370 = PRODUCT_CONTEXT('',#2,'mechanical');
#1371 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1372 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1373),#1426);
#1373 = MANIFOLD_SOLID_BREP('',#1374);
#1374 = CLOSED_SHELL('',(#1375,#1408,#1417));
#1375 = ADVANCED_FACE('',(#1376),#1403,.T.);
#1376 = FACE_BOUND('',#1377,.F.);
#1377 = EDGE_LOOP('',(#1378,#1388,#1395,#1396));
#1378 = ORIENTED_EDGE('',*,*,#1379,.T.);
#1379 = EDGE_CURVE('',#1380,#1382,#1384,.T.);
#1380 = VERTEX_POINT('',#1381);
#1381 = CARTESIAN_POINT('',(-2.38125,4.6228,-3.736666666667));
#1382 = VERTEX_POINT('',#1383);
#1383 = CARTESIAN_POINT('',(-2.38125,5.08,-3.736666666667));
#1384 = LINE('',#1385,#1386);
#1385 = CARTESIAN_POINT('',(-2.38125,4.6228,-3.736666666667));
#1386 = VECTOR('',#1387,1.);
#1387 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#1388 = ORIENTED_EDGE('',*,*,#1389,.T.);
#1389 = EDGE_CURVE('',#1382,#1382,#1390,.T.);
#1390 = CIRCLE('',#1391,0.264583333333);
#1391 = AXIS2_PLACEMENT_3D('',#1392,#1393,#1394);
#1392 = CARTESIAN_POINT('',(-2.116666666667,5.08,-3.736666666667));
#1393 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#1394 = DIRECTION('',(-1.,0.,0.));
#1395 = ORIENTED_EDGE('',*,*,#1379,.F.);
#1396 = ORIENTED_EDGE('',*,*,#1397,.F.);
#1397 = EDGE_CURVE('',#1380,#1380,#1398,.T.);
#1398 = CIRCLE('',#1399,0.264583333333);
#1399 = AXIS2_PLACEMENT_3D('',#1400,#1401,#1402);
#1400 = CARTESIAN_POINT('',(-2.116666666667,4.6228,-3.736666666667));
#1401 = DIRECTION('',(0.,1.,-2.22044604925E-16));
#1402 = DIRECTION('',(-1.,0.,0.));
#1403 = CYLINDRICAL_SURFACE('',#1404,0.264583333333);
#1404 = AXIS2_PLACEMENT_3D('',#1405,#1406,#1407);
#1405 = CARTESIAN_POINT('',(-2.116666666667,4.6228,-3.736666666667));
#1406 = DIRECTION('',(0.,-1.,2.22044604925E-16));
#1407 = DIRECTION('',(-1.,0.,0.));
#1408 = ADVANCED_FACE('',(#1409),#1412,.F.);
#1409 = FACE_BOUND('',#1410,.F.);
#1410 = EDGE_LOOP('',(#1411));
#1411 = ORIENTED_EDGE('',*,*,#1397,.T.);
#1412 = PLANE('',#1413);
#1413 = AXIS2_PLACEMENT_3D('',#1414,#1415,#1416);
#1414 = CARTESIAN_POINT('',(-2.116666666667,4.6228,-3.736666666667));
#1415 = DIRECTION('',(0.,1.,0.));
#1416 = DIRECTION('',(0.,0.,1.));
#1417 = ADVANCED_FACE('',(#1418),#1421,.T.);
#1418 = FACE_BOUND('',#1419,.T.);
#1419 = EDGE_LOOP('',(#1420));
#1420 = ORIENTED_EDGE('',*,*,#1389,.T.);
#1421 = PLANE('',#1422);
#1422 = AXIS2_PLACEMENT_3D('',#1423,#1424,#1425);
#1423 = CARTESIAN_POINT('',(-2.116666666667,5.08,-3.736666666667));
#1424 = DIRECTION('',(0.,1.,0.));
#1425 = DIRECTION('',(0.,0.,1.));
#1426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1430)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1427,#1428,#1429)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1427 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1428 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1429 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1430 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1427,
  'distance_accuracy_value','confusion accuracy');
#1431 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1432,#1434);
#1432 = ( REPRESENTATION_RELATIONSHIP('','',#1372,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1433) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1433 = ITEM_DEFINED_TRANSFORMATION('','',#11,#35);
#1434 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1435);
#1435 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9512','Body002','',#5,#1367,$);
#1436 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1369));
#1437 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1438),#1426);
#1438 = STYLED_ITEM('color',(#1439),#1373);
#1439 = PRESENTATION_STYLE_ASSIGNMENT((#1440,#1446));
#1440 = SURFACE_STYLE_USAGE(.BOTH.,#1441);
#1441 = SURFACE_SIDE_STYLE('',(#1442));
#1442 = SURFACE_STYLE_FILL_AREA(#1443);
#1443 = FILL_AREA_STYLE('',(#1444));
#1444 = FILL_AREA_STYLE_COLOUR('',#1445);
#1445 = DRAUGHTING_PRE_DEFINED_COLOUR('white');
#1446 = CURVE_STYLE('',#1447,POSITIVE_LENGTH_MEASURE(0.1),#1448);
#1447 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1448 = COLOUR_RGB('',9.803921802644E-02,9.803921802644E-02,
  9.803921802644E-02);
#1449 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1450),#1244);
#1450 = STYLED_ITEM('color',(#1451),#861);
#1451 = PRESENTATION_STYLE_ASSIGNMENT((#1452,#1458));
#1452 = SURFACE_STYLE_USAGE(.BOTH.,#1453);
#1453 = SURFACE_SIDE_STYLE('',(#1454));
#1454 = SURFACE_STYLE_FILL_AREA(#1455);
#1455 = FILL_AREA_STYLE('',(#1456));
#1456 = FILL_AREA_STYLE_COLOUR('',#1457);
#1457 = COLOUR_RGB('',0.800000010877,0.800000010877,0.800000010877);
#1458 = CURVE_STYLE('',#1459,POSITIVE_LENGTH_MEASURE(0.1),#1448);
#1459 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1460 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1461),#842);
#1461 = STYLED_ITEM('color',(#1462),#459);
#1462 = PRESENTATION_STYLE_ASSIGNMENT((#1463,#1468));
#1463 = SURFACE_STYLE_USAGE(.BOTH.,#1464);
#1464 = SURFACE_SIDE_STYLE('',(#1465));
#1465 = SURFACE_STYLE_FILL_AREA(#1466);
#1466 = FILL_AREA_STYLE('',(#1467));
#1467 = FILL_AREA_STYLE_COLOUR('',#1457);
#1468 = CURVE_STYLE('',#1469,POSITIVE_LENGTH_MEASURE(0.1),#1448);
#1469 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1470 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1471),#440);
#1471 = STYLED_ITEM('color',(#1472),#53);
#1472 = PRESENTATION_STYLE_ASSIGNMENT((#1473,#1479));
#1473 = SURFACE_STYLE_USAGE(.BOTH.,#1474);
#1474 = SURFACE_SIDE_STYLE('',(#1475));
#1475 = SURFACE_STYLE_FILL_AREA(#1476);
#1476 = FILL_AREA_STYLE('',(#1477));
#1477 = FILL_AREA_STYLE_COLOUR('',#1478);
#1478 = COLOUR_RGB('',0.196078429142,0.196078429142,0.196078429142);
#1479 = CURVE_STYLE('',#1480,POSITIVE_LENGTH_MEASURE(0.1),#1448);
#1480 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
