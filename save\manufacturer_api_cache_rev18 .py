#!/usr/bin/env python3
"""
Manufacturer API Cache System
Stores and retrieves previously found manufacturer API endpoints and 3D model sources
"""

import json
import os
from datetime import datetime

class ManufacturerAPICache:
    def __init__(self):
        self.cache_file = "manufacturer_api_cache.json"
        self.cache = self.load_cache()
    
    def load_cache(self):
        """Load the manufacturer API cache from file"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    return json.load(f)
            else:
                return {
                    "datasheet_apis": {},
                    "3d_model_apis": {},
                    "last_updated": datetime.now().isoformat()
                }
        except Exception as e:
            print(f"Error loading API cache: {e}")
            return {
                "datasheet_apis": {},
                "3d_model_apis": {},
                "last_updated": datetime.now().isoformat()
            }
    
    def save_cache(self):
        """Save the manufacturer API cache to file"""
        try:
            self.cache["last_updated"] = datetime.now().isoformat()
            with open(self.cache_file, 'w') as f:
                json.dump(self.cache, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving API cache: {e}")
            return False
    
    def add_datasheet_api(self, manufacturer, api_info):
        """Add a manufacturer's datasheet API information"""
        manufacturer_key = manufacturer.lower().strip()
        self.cache["datasheet_apis"][manufacturer_key] = {
            "manufacturer": manufacturer,
            "api_endpoint": api_info.get("api_endpoint"),
            "search_pattern": api_info.get("search_pattern"),
            "api_key_required": api_info.get("api_key_required", False),
            "success_rate": api_info.get("success_rate", 0),
            "last_used": datetime.now().isoformat(),
            "notes": api_info.get("notes", "")
        }
        self.save_cache()
    
    def add_3d_model_api(self, manufacturer, api_info):
        """Add a manufacturer's 3D model API information"""
        manufacturer_key = manufacturer.lower().strip()
        self.cache["3d_model_apis"][manufacturer_key] = {
            "manufacturer": manufacturer,
            "api_endpoint": api_info.get("api_endpoint"),
            "search_pattern": api_info.get("search_pattern"),
            "api_key_required": api_info.get("api_key_required", False),
            "success_rate": api_info.get("success_rate", 0),
            "last_used": datetime.now().isoformat(),
            "notes": api_info.get("notes", "")
        }
        self.save_cache()
    
    def get_datasheet_api(self, manufacturer):
        """Get cached datasheet API information for a manufacturer"""
        manufacturer_key = manufacturer.lower().strip()
        return self.cache["datasheet_apis"].get(manufacturer_key)
    
    def get_3d_model_api(self, manufacturer):
        """Get cached 3D model API information for a manufacturer"""
        manufacturer_key = manufacturer.lower().strip()
        return self.cache["3d_model_apis"].get(manufacturer_key)
    
    def update_success_rate(self, manufacturer, api_type, success):
        """Update the success rate for a manufacturer's API"""
        manufacturer_key = manufacturer.lower().strip()
        
        if api_type == "datasheet" and manufacturer_key in self.cache["datasheet_apis"]:
            api_info = self.cache["datasheet_apis"][manufacturer_key]
            current_rate = api_info.get("success_rate", 0)
            # Simple success rate calculation (could be more sophisticated)
            new_rate = (current_rate + (100 if success else 0)) / 2
            api_info["success_rate"] = new_rate
            api_info["last_used"] = datetime.now().isoformat()
            
        elif api_type == "3d_model" and manufacturer_key in self.cache["3d_model_apis"]:
            api_info = self.cache["3d_model_apis"][manufacturer_key]
            current_rate = api_info.get("success_rate", 0)
            new_rate = (current_rate + (100 if success else 0)) / 2
            api_info["success_rate"] = new_rate
            api_info["last_used"] = datetime.now().isoformat()
        
        self.save_cache()
    
    def create_sample_cache(self):
        """Create a sample cache with common manufacturer APIs"""
        sample_cache = {
            "datasheet_apis": {
                "texas instruments": {
                    "manufacturer": "Texas Instruments",
                    "api_endpoint": "https://www.ti.com/product/{part_number}",
                    "search_pattern": "direct_product_page",
                    "api_key_required": False,
                    "success_rate": 85,
                    "last_used": datetime.now().isoformat(),
                    "notes": "Direct product page access, reliable datasheet links"
                },
                "analog devices": {
                    "manufacturer": "Analog Devices",
                    "api_endpoint": "https://www.analog.com/en/products/{part_number}.html",
                    "search_pattern": "direct_product_page",
                    "api_key_required": False,
                    "success_rate": 80,
                    "last_used": datetime.now().isoformat(),
                    "notes": "Direct product page access"
                },
                "microchip": {
                    "manufacturer": "Microchip",
                    "api_endpoint": "https://www.microchip.com/en-us/product/{part_number}",
                    "search_pattern": "direct_product_page",
                    "api_key_required": False,
                    "success_rate": 75,
                    "last_used": datetime.now().isoformat(),
                    "notes": "Direct product page access"
                }
            },
            "3d_model_apis": {
                "texas instruments": {
                    "manufacturer": "Texas Instruments",
                    "api_endpoint": "https://www.ti.com/product/{part_number}",
                    "search_pattern": "product_page_3d_links",
                    "api_key_required": False,
                    "success_rate": 60,
                    "last_used": datetime.now().isoformat(),
                    "notes": "Some products have 3D models on product pages"
                },
                "analog devices": {
                    "manufacturer": "Analog Devices",
                    "api_endpoint": "https://www.analog.com/en/products/{part_number}.html",
                    "search_pattern": "product_page_3d_links",
                    "api_key_required": False,
                    "success_rate": 50,
                    "last_used": datetime.now().isoformat(),
                    "notes": "Limited 3D model availability"
                }
            },
            "last_updated": datetime.now().isoformat()
        }
        
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(sample_cache, f, indent=2)
            print(f"✅ Created sample manufacturer API cache: {self.cache_file}")
            self.cache = sample_cache
            return True
        except Exception as e:
            print(f"❌ Error creating sample cache: {e}")
            return False
    
    def list_cached_apis(self):
        """List all cached manufacturer APIs"""
        print("\n📋 Cached Manufacturer APIs:")
        print("=" * 50)
        
        print("\n📄 Datasheet APIs:")
        for manufacturer, info in self.cache["datasheet_apis"].items():
            success_rate = info.get("success_rate", 0)
            print(f"   • {info['manufacturer']}: {success_rate:.1f}% success rate")
            print(f"     Endpoint: {info['api_endpoint']}")
        
        print("\n🎯 3D Model APIs:")
        for manufacturer, info in self.cache["3d_model_apis"].items():
            success_rate = info.get("success_rate", 0)
            print(f"   • {info['manufacturer']}: {success_rate:.1f}% success rate")
            print(f"     Endpoint: {info['api_endpoint']}")

def main():
    """Test the manufacturer API cache"""
    cache = ManufacturerAPICache()
    
    # Create sample cache if it doesn't exist
    if not os.path.exists(cache.cache_file):
        cache.create_sample_cache()
    
    # List cached APIs
    cache.list_cached_apis()
    
    # Test getting API info
    print(f"\n🧪 Testing API retrieval:")
    ti_datasheet_api = cache.get_datasheet_api("Texas Instruments")
    if ti_datasheet_api:
        print(f"✅ Found TI datasheet API: {ti_datasheet_api['api_endpoint']}")
    else:
        print(f"❌ No TI datasheet API found")
    
    ti_3d_api = cache.get_3d_model_api("Texas Instruments")
    if ti_3d_api:
        print(f"✅ Found TI 3D model API: {ti_3d_api['api_endpoint']}")
    else:
        print(f"❌ No TI 3D model API found")

if __name__ == "__main__":
    main()
