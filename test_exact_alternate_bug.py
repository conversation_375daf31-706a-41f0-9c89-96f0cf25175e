#!/usr/bin/env python3
"""
Test Exact Alternate Part Bug - With Real Calling Context
This reproduces the exact bug by simulating the API search flow
"""

import tkinter as tk
from tkinter import messagebox

class ExactAlternateBugTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Exact Alternate Bug Test - Real Context")
        self.root.geometry("700x600")
        
        # Mock the exact same attributes as component_finder
        self.vs_colors = {
            'form_bg': '#f0f0f0',
            'control_bg': 'white', 
            'text_color': 'black',
            'button_bg': '#e1e1e1',
            'accent_blue': '#0078d4'
        }
        
        # Mock the show_alternative_dialog setting
        self.show_alternative_dialog = tk.BooleanVar(value=True)
        
        self.create_interface()
    
    def create_interface(self):
        """Create test interface"""
        tk.Label(self.root, text="Exact Alternate Bug Test", 
                font=('Arial', 16, 'bold')).pack(pady=10)
        
        tk.Label(self.root, text="This simulates the EXACT calling context from the API search\n"
                                "that causes the alternate part bug",
                justify=tk.CENTER).pack(pady=10)
        
        tk.Button(self.root, text="Simulate Digi-Key API Search with Alternate", 
                 command=self.simulate_digikey_search, width=40).pack(pady=10)
        
        tk.Button(self.root, text="Quit", command=self.root.quit, width=40).pack(pady=10)
        
        # Log display
        self.log_text = tk.Text(self.root, height=25, width=80)
        self.log_text.pack(pady=10, fill=tk.BOTH, expand=True)
        
        self.add_comment("Exact Alternate Bug Test Ready")
        self.add_comment("This will simulate the exact API search flow that causes the bug")
    
    def add_comment(self, message):
        """Mock add_comment function"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def _show_messagebox_on_main(self, msg_type, title, message, **kwargs):
        """Mock messagebox function"""
        if msg_type == 'yesno':
            return messagebox.askyesno(title, message, parent=self.root, **kwargs)
        else:
            return messagebox.showinfo(title, message, parent=self.root, **kwargs)
    
    def show_alternate_part_acceptance_dialog(self, manufacturer, original_part, alternates):
        """EXACT COPY of alternate part dialog from component_finder.py"""
        try:
            if not alternates:
                return None

            # Create message with alternates
            alternate_list = "\n".join([f"  • {alt}" for alt in alternates[:5]])

            self.add_comment(f"🔄 DEBUG: Showing alternate part numbers dialog")
            response = self._show_messagebox_on_main("yesno",
                "Alternate Part Numbers Found",
                f"Original part number '{original_part}' not found for {manufacturer}.\n\n"
                f"Found these alternate part numbers:\n{alternate_list}\n\n"
                f"Would you like to accept one of these alternates?\n\n"
                f"YES = Select an alternate\n"
                f"NO = Continue with original part number"
            )

            if response:  # YES - Select alternate
                if len(alternates) == 1:
                    # Only one alternate, ask for confirmation
                    confirm = self._show_messagebox_on_main("yesno",
                        "Confirm Alternate",
                        f"Use '{alternates[0]}' instead of '{original_part}'?"
                    )
                    if confirm:
                        self.add_comment(f"✅ Accepted alternate part number: {original_part} → {alternates[0]}")
                        return alternates[0]
                else:
                    # Multiple alternates - create selection dialog
                    choice_dialog = tk.Toplevel(self.root)
                    choice_dialog.title("Select Alternate Part Number")
                    choice_dialog.geometry("500x400")
                    choice_dialog.configure(bg=self.vs_colors['form_bg'])
                    choice_dialog.transient(self.root)
                    choice_dialog.grab_set()

                    selected_part = tk.StringVar()

                    # Title
                    title_label = tk.Label(choice_dialog,
                                         text=f"Select Alternate for {original_part}",
                                         font=('Microsoft Sans Serif', 10, 'bold'),
                                         bg=self.vs_colors['form_bg'],
                                         fg=self.vs_colors['text_color'])
                    title_label.pack(pady=10)

                    # Listbox
                    listbox_frame = tk.Frame(choice_dialog, bg=self.vs_colors['form_bg'])
                    listbox_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                    listbox = tk.Listbox(listbox_frame,
                                       font=('Microsoft Sans Serif', 9),
                                       bg=self.vs_colors['control_bg'],
                                       fg=self.vs_colors['text_color'],
                                       selectbackground=self.vs_colors['accent_blue'],
                                       selectforeground='white')
                    scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
                    listbox.config(yscrollcommand=scrollbar.set)
                    scrollbar.config(command=listbox.yview)

                    for alt in alternates:
                        listbox.insert(tk.END, alt)

                    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                    # Buttons
                    button_frame = tk.Frame(choice_dialog, bg=self.vs_colors['form_bg'])
                    button_frame.pack(pady=10)

                    def on_select():
                        selection = listbox.curselection()
                        if selection:
                            selected_part.set(alternates[selection[0]])
                            choice_dialog.destroy()

                    def on_cancel():
                        selected_part.set("")
                        choice_dialog.destroy()

                    tk.Button(button_frame, text="Select", command=on_select,
                             bg=self.vs_colors['button_bg'], fg=self.vs_colors['text_color'],
                             width=10).pack(side=tk.LEFT, padx=5)

                    tk.Button(button_frame, text="Cancel", command=on_cancel,
                             bg=self.vs_colors['button_bg'], fg=self.vs_colors['text_color'],
                             width=10).pack(side=tk.LEFT, padx=5)

                    # Wait for dialog to close
                    choice_dialog.wait_window()

                    chosen_part = selected_part.get()
                    if chosen_part:
                        self.add_comment(f"✅ Accepted alternate part number: {original_part} → {chosen_part}")
                        return chosen_part

            # If we get here, user either said NO or cancelled
            self.add_comment(f"⏭️ Continuing with original part number: {original_part}")
            return None

        except Exception as e:
            self.add_comment(f"⚠️ Error in alternate part dialog: {str(e)[:50]}")
            return None
    
    def simulate_digikey_search(self):
        """Simulate the EXACT Digi-Key API search flow that causes the bug"""
        self.add_comment("\n" + "="*60)
        self.add_comment("SIMULATING DIGI-KEY API SEARCH WITH ALTERNATE PART")
        self.add_comment("="*60)
        
        # Mock API search parameters
        manufacturer = "Texas Instruments"
        part_number = "LM358N"  # What user searched for
        api_part_number = "LM358AN"  # What API found (different)
        
        self.add_comment(f"🔍 Searching for: {manufacturer} {part_number}")
        self.add_comment(f"🔍 Digi-Key API found: {api_part_number}")
        self.add_comment(f"❌ Part number mismatch detected")
        
        # This is the EXACT code from component_finder.py lines 3404-3419
        if api_part_number and part_number.upper() not in api_part_number.upper():
            self.add_comment(f"   ❌ Digi-Key API found {api_part_number} but looking for {part_number}")

            # Check if alternative dialog is enabled
            if hasattr(self, 'show_alternative_dialog') and not self.show_alternative_dialog.get():
                self.add_comment(f"   ⚠️ Alternative dialog disabled - continuing with original part: {part_number}")
                self.add_comment(f"   🔄 Moving to Mouser to search for exact part number...")
                return None

            # Offer alternate part to user (unless in silent mode)
            if not (hasattr(self, 'silent_mode') and self.silent_mode):
                self.add_comment(f"🔄 CALLING show_alternate_part_acceptance_dialog...")
                accepted_alternate = self.show_alternate_part_acceptance_dialog(
                    manufacturer, part_number, [api_part_number]
                )
                self.add_comment(f"🔄 RETURNED FROM show_alternate_part_acceptance_dialog: '{accepted_alternate}'")
                
                if accepted_alternate:
                    # User accepted the alternate, update the search
                    part_number = accepted_alternate  # THIS IS THE CRITICAL LINE
                    self.add_comment(f"   ✅ Using accepted alternate: {accepted_alternate}")
                    self.add_comment(f"   🔄 Updated part_number variable to: {part_number}")
                    # Continue with the API result using the accepted alternate
                    self.add_comment(f"   ✅ SHOULD CONTINUE WITH SEARCH USING: {part_number}")
                    self.add_comment(f"   🎉 SUCCESS - No more dialogs should appear!")
                else:
                    # User rejected alternate, try Mouser
                    self.add_comment(f"   🔄 Moving to Mouser to search for exact part number...")
                    return None
            else:
                # In silent mode, try Mouser
                self.add_comment(f"   🔄 Moving to Mouser to search for exact part number...")
                return None
        
        self.add_comment(f"\n🎯 FINAL RESULT: part_number = '{part_number}'")
        self.add_comment(f"🎯 If bug exists, another dialog might appear asking to select alternate again")
    
    def run(self):
        """Run the test"""
        self.root.mainloop()

def main():
    print("🐛 EXACT ALTERNATE PART BUG TEST")
    print("=" * 50)
    print("This reproduces the exact calling context that causes the bug")
    
    test = ExactAlternateBugTest()
    test.run()

if __name__ == "__main__":
    main()
