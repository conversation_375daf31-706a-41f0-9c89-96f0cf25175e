{"format": 1, "restore": {"E:\\Python\\web-get-files\\vbasic\\WinFormsApp1\\WinFormsApp1.vbproj": {}}, "projects": {"E:\\Python\\web-get-files\\vbasic\\WinFormsApp1\\WinFormsApp1.vbproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Python\\web-get-files\\vbasic\\WinFormsApp1\\WinFormsApp1.vbproj", "projectName": "WinFormsApp1", "projectPath": "E:\\Python\\web-get-files\\vbasic\\WinFormsApp1\\WinFormsApp1.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Python\\web-get-files\\vbasic\\WinFormsApp1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.20, 8.0.20]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.20, 8.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.20, 8.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.20, 8.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}