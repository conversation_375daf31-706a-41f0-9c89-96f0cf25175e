@echo off
echo ========================================
echo COMPONENT FINDER RESTORE SYSTEM
echo ========================================
echo.

REM List available backup versions
echo Available backup versions in save directory:
for %%i in (save\*_rev*.py) do echo - %%~ni
for %%i in (save\*_rev*.json) do echo - %%~ni
for %%i in (save\*_rev*.bat) do echo - %%~ni

echo.
set /p VERSION=Enter version number to restore from (e.g., 6):

REM Check if backup files exist for this version
if not exist "save\component_finder_rev%VERSION%.py" (
    echo Error: No backup files found for version %VERSION%!
    pause
    exit /b
)

echo.
echo WARNING: This will overwrite current files with backup files from version %VERSION%!
echo.
set /p CONFIRM=Are you sure you want to restore from version %VERSION%? (y/N):

if /i not "%CONFIRM%"=="y" (
    echo Restore cancelled.
    pause
    exit /b
)

echo.
echo Restoring from backup version: %VERSION%

REM Restore core files
if exist "save\component_finder_rev%VERSION%.py" copy "save\component_finder_rev%VERSION%.py" "component_finder.py" >nul
if exist "save\datasheet_finder_rev%VERSION%.py" copy "save\datasheet_finder_rev%VERSION%.py" "datasheet_finder.py" >nul
if exist "save\digikey_datasheet_improved_rev%VERSION%.py" copy "save\digikey_datasheet_improved_rev%VERSION%.py" "digikey_datasheet_improved.py" >nul
if exist "save\mouser_datasheet_improved_rev%VERSION%.py" copy "save\mouser_datasheet_improved_rev%VERSION%.py" "mouser_datasheet_improved.py" >nul
if exist "save\pdf_parser_rev%VERSION%.py" copy "save\pdf_parser_rev%VERSION%.py" "pdf_parser.py" >nul
if exist "save\rs_components_scraper_rev%VERSION%.py" copy "save\rs_components_scraper_rev%VERSION%.py" "rs_components_scraper.py" >nul
if exist "save\step_finder_rev%VERSION%.py" copy "save\step_finder_rev%VERSION%.py" "step_finder.py" >nul
if exist "save\samacsys_credentials_rev%VERSION%.py" copy "save\samacsys_credentials_rev%VERSION%.py" "samacsys_credentials.py" >nul

REM Restore 3D Finder modules
if exist "save\external_3d_finder_rev%VERSION%.py" copy "save\external_3d_finder_rev%VERSION%.py" "external_3d_finder.py" >nul
if exist "save\ultralibrarian_3d_finder_rev%VERSION%.py" copy "save\ultralibrarian_3d_finder_rev%VERSION%.py" "ultralibrarian_3d_finder.py" >nul
if exist "save\snapeda_3d_finder_working_rev%VERSION%.py" copy "save\snapeda_3d_finder_working_rev%VERSION%.py" "snapeda_3d_finder_working.py" >nul

REM Restore API and utility modules
if exist "save\manufacturer_api_cache_rev%VERSION%.py" copy "save\manufacturer_api_cache_rev%VERSION%.py" "manufacturer_api_cache.py" >nul
if exist "save\manufacturer_api_search_rev%VERSION%.py" copy "save\manufacturer_api_search_rev%VERSION%.py" "manufacturer_api_search.py" >nul
if exist "save\smart_wait_utils_rev%VERSION%.py" copy "save\smart_wait_utils_rev%VERSION%.py" "smart_wait_utils.py" >nul
if exist "save\auto_fix_component_finder_rev%VERSION%.py" copy "save\auto_fix_component_finder_rev%VERSION%.py" "auto_fix_component_finder.py" >nul

REM Restore configuration files
if exist "save\component_site_credentials_rev%VERSION%.json" copy "save\component_site_credentials_rev%VERSION%.json" "component_site_credentials.json" >nul
if exist "save\digikey_api_credentials_rev%VERSION%.json" copy "save\digikey_api_credentials_rev%VERSION%.json" "digikey_api_credentials.json" >nul
if exist "save\samacsys_credentials_rev%VERSION%.json" copy "save\samacsys_credentials_rev%VERSION%.json" "samacsys_credentials.json" >nul
if exist "save\manufacturer_knowledge_rev%VERSION%.json" copy "save\manufacturer_knowledge_rev%VERSION%.json" "manufacturer_knowledge.json" >nul
if exist "save\manufacturer_websites_rev%VERSION%.json" copy "save\manufacturer_websites_rev%VERSION%.json" "manufacturer_websites.json" >nul
if exist "save\manufacturer_api_credentials_rev%VERSION%.json" copy "save\manufacturer_api_credentials_rev%VERSION%.json" "manufacturer_api_credentials.json" >nul
if exist "save\mouser_api_credentials_rev%VERSION%.json" copy "save\mouser_api_credentials_rev%VERSION%.json" "mouser_api_credentials.json" >nul
if exist "save\manufacturer_api_cache_rev%VERSION%.json" copy "save\manufacturer_api_cache_rev%VERSION%.json" "manufacturer_api_cache.json" >nul

REM Restore external scripts
if exist "save\ultralibrarian_3d_finder_rev%VERSION%.py" copy "save\ultralibrarian_3d_finder_rev%VERSION%.py" "save\ultralibrarian_3d_finder.py" >nul
if exist "save\samacsys_3d_finder_rev%VERSION%.py" copy "save\samacsys_3d_finder_rev%VERSION%.py" "save\samacsys_3d_finder.py" >nul
if exist "save\snapeda_3d_finder_final_rev%VERSION%.py" copy "save\snapeda_3d_finder_final_rev%VERSION%.py" "save\snapeda_3d_finder_final.py" >nul

REM Restore batch files
if exist "save\run_component_finder_rev%VERSION%.bat" copy "save\run_component_finder_rev%VERSION%.bat" "run_component_finder.bat" >nul
if exist "save\install_rev%VERSION%.bat" copy "save\install_rev%VERSION%.bat" "install.bat" >nul

REM Restore documentation
if exist "save\README_rev%VERSION%.md" copy "save\README_rev%VERSION%.md" "README.md" >nul
if exist "save\COMPONENT_FINDER_WORKFLOW_rev%VERSION%.txt" copy "save\COMPONENT_FINDER_WORKFLOW_rev%VERSION%.txt" "COMPONENT_FINDER_WORKFLOW.txt" >nul
if exist "save\ESSENTIAL_FILES_LIST_rev%VERSION%.txt" copy "save\ESSENTIAL_FILES_LIST_rev%VERSION%.txt" "ESSENTIAL_FILES_LIST.txt" >nul

REM Restore help files directory
if exist "save\help_files_rev%VERSION%" (
    if exist "help_files" rmdir /s /q "help_files" >nul
    xcopy "save\help_files_rev%VERSION%\*.*" "help_files\" /E /I /Q >nul
)

REM Restore emoji images directory
if exist "save\emoji_images_rev%VERSION%" (
    if exist "emoji_images" rmdir /s /q "emoji_images" >nul
    xcopy "save\emoji_images_rev%VERSION%\*.*" "emoji_images\" /E /I /Q >nul
)

REM Restore runtime CSV files
if exist "save\actual-web-site-xref_rev%VERSION%.csv" copy "save\actual-web-site-xref_rev%VERSION%.csv" "actual-web-site-xref.csv" >nul
if exist "save\3d_model_downloads_rev%VERSION%.csv" copy "save\3d_model_downloads_rev%VERSION%.csv" "3d\3d_model_downloads.csv" >nul

REM Restore version file
if exist "save\version_rev%VERSION%.txt" copy "save\version_rev%VERSION%.txt" "version.txt" >nul

echo.
echo ✅ Restore completed successfully!
echo.
echo Files restored from version: %VERSION%
echo   - Core Python modules
echo   - 3D Finder modules
echo   - API and utility modules
echo   - Configuration files (*.json)
echo   - External 3D scripts
echo   - Batch files
echo   - Documentation files
echo   - Help files directory
echo   - Emoji images directory
echo   - Runtime CSV files (search logs and website mappings)
echo   - Version control file
echo.
pause
