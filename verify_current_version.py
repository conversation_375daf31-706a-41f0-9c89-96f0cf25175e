#!/usr/bin/env python3
"""
Verify Current Version of Component Finder
Check if the enhanced 3D search and multi-monitor fixes are present
"""

import os
import sys

def check_enhanced_3d_finder():
    """Check if enhanced 3D finder exists"""
    if os.path.exists('enhanced_3d_finder.py'):
        print("✅ enhanced_3d_finder.py exists")
        return True
    else:
        print("❌ enhanced_3d_finder.py missing")
        return False

def check_component_finder_integration():
    """Check if component_finder.py has the enhanced integration"""
    try:
        with open('component_finder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('Enhanced 3D Search', 'from enhanced_3d_finder import find_enhanced_3d_model'),
            ('Multi-monitor Fix', '_ensure_primary_monitor'),
            ('Dialog Positioning', 'same screen as main window'),
            ('Enhanced Search Call', '✅ Downloaded 3D model:')
        ]
        
        results = []
        for name, pattern in checks:
            if pattern in content:
                print(f"✅ {name}: Found")
                results.append(True)
            else:
                print(f"❌ {name}: Missing")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ Error checking component_finder.py: {e}")
        return False

def check_old_external_tester():
    """Check if old external tester is still being used"""
    try:
        with open('component_finder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        old_patterns = [
            'external_manufacturer_3d_tester',
            'Analyzing.*website for 3D model availability',
            'Found.*URLs with potential 3D models',
            'Best URLs for manual checking'
        ]
        
        found_old = []
        for pattern in old_patterns:
            if pattern in content:
                found_old.append(pattern)
        
        if found_old:
            print("⚠️ Old external tester code still present:")
            for pattern in found_old:
                print(f"   - {pattern}")
            return True
        else:
            print("✅ No old external tester code found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking for old code: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 VERIFYING CURRENT COMPONENT FINDER VERSION")
    print("=" * 50)
    
    # Check files
    enhanced_ok = check_enhanced_3d_finder()
    
    print("\n🔍 CHECKING COMPONENT_FINDER.PY INTEGRATION")
    print("=" * 50)
    integration_ok = check_component_finder_integration()
    
    print("\n🔍 CHECKING FOR OLD CODE")
    print("=" * 50)
    has_old_code = check_old_external_tester()
    
    print("\n📊 SUMMARY")
    print("=" * 50)
    
    if enhanced_ok and integration_ok and not has_old_code:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Enhanced 3D search is properly integrated")
        print("✅ Multi-monitor fixes are present")
        print("✅ No old code conflicts")
        print("\n💡 If you're still seeing old behavior, try:")
        print("   1. Close all Component Finder windows")
        print("   2. Run: python component_finder.py")
        print("   3. The new version should work correctly")
    else:
        print("⚠️ ISSUES FOUND!")
        if not enhanced_ok:
            print("❌ Enhanced 3D finder missing")
        if not integration_ok:
            print("❌ Integration incomplete")
        if has_old_code:
            print("❌ Old code still present")
        print("\n💡 The system may need to be updated")

if __name__ == "__main__":
    main()
