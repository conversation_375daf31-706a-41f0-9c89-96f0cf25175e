@echo off
echo 🚀 3D Model Discovery Tester
echo ============================
echo.
echo Choose test type:
echo 1. Quick manufacturer test (fast)
echo 2. Integrated test (existing + new)
echo 3. Comprehensive test (multiple parts)
echo.

set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Running quick manufacturer test...
    python test_manufacturer_3d_discovery.py
) else if "%choice%"=="2" (
    echo.
    echo Running integrated test...
    python integrated_3d_finder_test.py
) else if "%choice%"=="3" (
    echo.
    echo Running comprehensive test...
    python test_manufacturer_3d_discovery.py
) else (
    echo Invalid choice. Please run again and choose 1, 2, or 3.
)

echo.
pause
