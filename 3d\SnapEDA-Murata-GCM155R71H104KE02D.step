ISO-10303-21;
HEADER;
FILE_DESCRIPTION (( 'STEP AP214' ),
    '1' );
FILE_NAME ('10.50.550.25.STEP',
    '2025-05-18T14:20:35',
    ( 'LENOVO' ),
    ( '' ),
    'SwSTEP 2.0',
    'SolidWorks 2021',
    '' );
FILE_SCHEMA (( 'AUTOMOTIVE_DESIGN' ));
ENDSEC;

DATA;
#1 = VERTEX_POINT ( 'NONE', #540 ) ;
#2 = AXIS2_PLACEMENT_3D ( 'NONE', #1051, #1061, #1145 ) ;
#3 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#4 = ORIENTED_EDGE ( 'NONE', *, *, #1252, .F. ) ;
#5 = FACE_OUTER_BOUND ( 'NONE', #380, .T. ) ;
#6 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#7 = EDGE_CURVE ( 'NONE', #483, #606, #1067, .T. ) ;
#8 = VERTEX_POINT ( 'NONE', #1177 ) ;
#9 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#10 = EDGE_CURVE ( 'NONE', #33, #1372, #136, .T. ) ;
#11 = PLANE ( 'NONE',  #1223 ) ;
#12 = SURFACE_STYLE_USAGE ( .BOTH. , #193 ) ;
#13 = ORIENTED_EDGE ( 'NONE', *, *, #669, .F. ) ;
#14 = LINE ( 'NONE', #521, #491 ) ;
#15 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#16 = ORIENTED_EDGE ( 'NONE', *, *, #1077, .F. ) ;
#17 = LINE ( 'NONE', #736, #485 ) ;
#18 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#19 = ORIENTED_EDGE ( 'NONE', *, *, #218, .T. ) ;
#20 = FILL_AREA_STYLE_COLOUR ( '', #683 ) ;
#21 = VERTEX_POINT ( 'NONE', #396 ) ;
#22 = LINE ( 'NONE', #1370, #948 ) ;
#23 = AXIS2_PLACEMENT_3D ( 'NONE', #983, #535, #716 ) ;
#24 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#25 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#26 = ORIENTED_EDGE ( 'NONE', *, *, #449, .T. ) ;
#27 = VECTOR ( 'NONE', #261, 1000.000000000000000 ) ;
#28 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#29 = ORIENTED_EDGE ( 'NONE', *, *, #1331, .F. ) ;
#30 = LINE ( 'NONE', #831, #955 ) ;
#31 = AXIS2_PLACEMENT_3D ( 'NONE', #1039, #168, #753 ) ;
#32 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#33 = VERTEX_POINT ( 'NONE', #1100 ) ;
#34 = EDGE_CURVE ( 'NONE', #839, #483, #107, .T. ) ;
#35 = ORIENTED_EDGE ( 'NONE', *, *, #1124, .T. ) ;
#36 = PRODUCT_DEFINITION_CONTEXT ( 'detailed design', #915, 'design' ) ;
#37 = ORIENTED_EDGE ( 'NONE', *, *, #1199, .T. ) ;
#38 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#39 = ORIENTED_EDGE ( 'NONE', *, *, #190, .T. ) ;
#40 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02500000000000000139, -0.2249999999999999500 ) ) ;
#41 = LINE ( 'NONE', #699, #529 ) ;
#42 = EDGE_CURVE ( 'NONE', #770, #479, #1078, .T. ) ;
#43 = ORIENTED_EDGE ( 'NONE', *, *, #201, .T. ) ;
#44 = AXIS2_PLACEMENT_3D ( 'NONE', #1397, #1068, #823 ) ;
#45 = SURFACE_SIDE_STYLE ('',( #576 ) ) ;
#46 = ORIENTED_EDGE ( 'NONE', *, *, #222, .T. ) ;
#47 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #932 ), #187 ) ;
#48 = VERTEX_POINT ( 'NONE', #854 ) ;
#49 = VECTOR ( 'NONE', #744, 1000.000000000000000 ) ;
#50 = ORIENTED_EDGE ( 'NONE', *, *, #1204, .T. ) ;
#51 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#52 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#53 = ORIENTED_EDGE ( 'NONE', *, *, #245, .F. ) ;
#54 = LINE ( 'NONE', #615, #70 ) ;
#55 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#56 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#57 = ORIENTED_EDGE ( 'NONE', *, *, #1209, .T. ) ;
#58 = VECTOR ( 'NONE', #463, 1000.000000000000000 ) ;
#59 = ORIENTED_EDGE ( 'NONE', *, *, #1204, .F. ) ;
#60 = ORIENTED_EDGE ( 'NONE', *, *, #1279, .F. ) ;
#61 = LINE ( 'NONE', #1193, #49 ) ;
#62 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#63 = SURFACE_STYLE_USAGE ( .BOTH. , #81 ) ;
#64 = EDGE_LOOP ( 'NONE', ( #412, #77, #1312, #1233 ) ) ;
#65 = AXIS2_PLACEMENT_3D ( 'NONE', #703, #1208, #607 ) ;
#66 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#67 = ORIENTED_EDGE ( 'NONE', *, *, #888, .F. ) ;
#68 = EDGE_CURVE ( 'NONE', #559, #21, #599, .T. ) ;
#69 = ORIENTED_EDGE ( 'NONE', *, *, #449, .F. ) ;
#70 = VECTOR ( 'NONE', #1304, 1000.000000000000000 ) ;
#71 = ADVANCED_FACE ( 'NONE', ( #612 ), #584, .T. ) ;
#72 = ORIENTED_EDGE ( 'NONE', *, *, #1362, .T. ) ;
#73 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#74 = VERTEX_POINT ( 'NONE', #1129 ) ;
#75 = FACE_OUTER_BOUND ( 'NONE', #416, .T. ) ;
#76 = ADVANCED_FACE ( 'NONE', ( #1049 ), #1180, .F. ) ;
#77 = ORIENTED_EDGE ( 'NONE', *, *, #757, .T. ) ;
#78 = SURFACE_STYLE_FILL_AREA ( #603 ) ;
#79 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #339 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #574, #1205, #214 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#80 = AXIS2_PLACEMENT_3D ( 'NONE', #1200, #750, #735 ) ;
#81 = SURFACE_SIDE_STYLE ('',( #135 ) ) ;
#82 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#83 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#84 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.008333333333333333218, 0.2249999999024217545 ) ) ;
#85 = EDGE_CURVE ( 'NONE', #374, #1372, #1396, .T. ) ;
#86 = ADVANCED_FACE ( 'NONE', ( #1386 ), #945, .T. ) ;
#87 = ORIENTED_EDGE ( 'NONE', *, *, #713, .T. ) ;
#88 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#89 = ORIENTED_EDGE ( 'NONE', *, *, #704, .T. ) ;
#90 = CIRCLE ( 'NONE', #1356, 0.02500000000000001180 ) ;
#91 = LINE ( 'NONE', #1219, #619 ) ;
#92 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#93 = ORIENTED_EDGE ( 'NONE', *, *, #1152, .T. ) ;
#94 = CYLINDRICAL_SURFACE ( 'NONE', #96, 0.02500000000000001180 ) ;
#95 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.008333333333333296788, 0.2249999999999998945 ) ) ;
#96 = AXIS2_PLACEMENT_3D ( 'NONE', #1087, #725, #843 ) ;
#97 = ADVANCED_FACE ( 'NONE', ( #442 ), #992, .T. ) ;
#98 = ORIENTED_EDGE ( 'NONE', *, *, #973, .T. ) ;
#99 = VECTOR ( 'NONE', #1254, 1000.000000000000000 ) ;
#100 = VERTEX_POINT ( 'NONE', #1123 ) ;
#101 = AXIS2_PLACEMENT_3D ( 'NONE', #733, #1414, #720 ) ;
#102 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#103 = EDGE_LOOP ( 'NONE', ( #13, #510, #541, #766 ) ) ;
#104 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#105 = VERTEX_POINT ( 'NONE', #25 ) ;
#106 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.02499999999999995628, 0.2249999999566318354 ) ) ;
#107 = CIRCLE ( 'NONE', #418, 0.02500000000000001180 ) ;
#108 = FACE_OUTER_BOUND ( 'NONE', #1263, .T. ) ;
#109 = ADVANCED_FACE ( 'NONE', ( #457 ), #908, .T. ) ;
#110 = ORIENTED_EDGE ( 'NONE', *, *, #1209, .F. ) ;
#111 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#112 = PLANE ( 'NONE',  #1319 ) ;
#113 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#114 = DIRECTION ( 'NONE',  ( -1.084202172485504681E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#115 = EDGE_CURVE ( 'NONE', #954, #950, #937, .T. ) ;
#116 = EDGE_LOOP ( 'NONE', ( #1025, #788, #1010 ) ) ;
#117 = LINE ( 'NONE', #228, #134 ) ;
#118 = AXIS2_PLACEMENT_3D ( 'NONE', #1118, #1257, #281 ) ;
#119 = CIRCLE ( 'NONE', #710, 0.02500000000000001180 ) ;
#120 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#121 = SURFACE_STYLE_FILL_AREA ( #1007 ) ;
#122 = LINE ( 'NONE', #370, #99 ) ;
#123 = EDGE_CURVE ( 'NONE', #349, #559, #1401, .T. ) ;
#124 = ORIENTED_EDGE ( 'NONE', *, *, #1316, .T. ) ;
#125 = DIRECTION ( 'NONE',  ( -1.084202172485504681E-16, -0.000000000000000000, -1.000000000000000000 ) ) ;
#126 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#127 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#128 = FACE_OUTER_BOUND ( 'NONE', #547, .T. ) ;
#129 = ORIENTED_EDGE ( 'NONE', *, *, #552, .T. ) ;
#130 = LINE ( 'NONE', #1154, #137 ) ;
#131 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#132 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#133 = VERTEX_POINT ( 'NONE', #892 ) ;
#134 = VECTOR ( 'NONE', #1327, 1000.000000000000000 ) ;
#135 = SURFACE_STYLE_FILL_AREA ( #771 ) ;
#136 = CIRCLE ( 'NONE', #262, 0.02500000000000001180 ) ;
#137 = VECTOR ( 'NONE', #814, 1000.000000000000000 ) ;
#138 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#139 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02500000000000000139, 0.2249999999999999500 ) ) ;
#140 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#141 = PRODUCT ( '10.50.550.25', '10.50.550.25', '', ( #626 ) ) ;
#142 = CIRCLE ( 'NONE', #242, 0.02500000000000001180 ) ;
#143 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #1240 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #260, #738, #844 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#144 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#145 = VECTOR ( 'NONE', #904, 1000.000000000000000 ) ;
#146 = EDGE_CURVE ( 'NONE', #105, #1258, #437, .T. ) ;
#147 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#148 = EDGE_CURVE ( 'NONE', #617, #954, #429, .T. ) ;
#149 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#150 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#151 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.2249999999999999500 ) ) ;
#152 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#153 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#154 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#155 = STYLED_ITEM ( 'NONE', ( #1041 ), #755 ) ;
#156 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#157 = STYLED_ITEM ( 'NONE', ( #582 ), #232 ) ;
#158 = EDGE_CURVE ( 'NONE', #408, #1018, #1392, .T. ) ;
#159 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#160 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02500000000000000139, -0.2249999999999999500 ) ) ;
#161 = FILL_AREA_STYLE_COLOUR ( '', #1412 ) ;
#162 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#163 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE ( 'ANY', '', #141, .NOT_KNOWN. ) ;
#164 = EDGE_CURVE ( 'NONE', #617, #1258, #934, .T. ) ;
#165 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#166 = AXIS2_PLACEMENT_3D ( 'NONE', #1101, #951, #1365 ) ;
#167 = PRESENTATION_STYLE_ASSIGNMENT (( #666 ) ) ;
#168 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#169 = ADVANCED_FACE ( 'NONE', ( #1380 ), #1408, .T. ) ;
#170 = AXIS2_PLACEMENT_3D ( 'NONE', #1133, #1024, #1115 ) ;
#171 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.000000000000000000, -0.2249999999999999500 ) ) ;
#172 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', 'automotive_design', 1998, #1211 ) ;
#173 = DIRECTION ( 'NONE',  ( 8.673617379884035472E-17, -0.000000000000000000, 1.000000000000000000 ) ) ;
#174 = ADVANCED_FACE ( 'NONE', ( #941 ), #432, .T. ) ;
#175 = ADVANCED_FACE ( 'NONE', ( #462 ), #344, .F. ) ;
#176 = EDGE_CURVE ( 'NONE', #887, #306, #590, .T. ) ;
#177 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#178 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #952 ), #922 ) ;
#179 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.000000000000000000, 0.2249999999999999500 ) ) ;
#180 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #1114 ), #1206 ) ;
#181 = ADVANCED_FACE ( 'NONE', ( #1057 ), #605, .T. ) ;
#182 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#183 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#184 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#185 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#186 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995628, 0.2416666666666666408 ) ) ;
#187 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #916 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1320, #761, #906 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#188 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#189 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#190 = EDGE_CURVE ( 'NONE', #306, #349, #119, .T. ) ;
#191 = FILL_AREA_STYLE_COLOUR ( '', #1332 ) ;
#192 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, 0.000000000000000000 ) ) ;
#193 = SURFACE_SIDE_STYLE ('',( #423 ) ) ;
#194 = ADVANCED_FACE ( 'NONE', ( #1082 ), #362, .F. ) ;
#195 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#196 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #155 ), #79 ) ;
#197 = AXIS2_PLACEMENT_3D ( 'NONE', #177, #255, #938 ) ;
#198 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#199 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#200 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -1.084202172485504681E-16 ) ) ;
#201 = EDGE_CURVE ( 'NONE', #466, #897, #90, .T. ) ;
#202 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #674, 'distance_accuracy_value', 'NONE');
#203 = ADVANCED_FACE ( 'NONE', ( #1097 ), #966, .F. ) ;
#204 = FILL_AREA_STYLE_COLOUR ( '', #1281 ) ;
#205 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#206 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#207 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#208 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#209 = FILL_AREA_STYLE ('',( #204 ) ) ;
#210 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#211 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#212 = AXIS2_PLACEMENT_3D ( 'NONE', #1335, #649, #711 ) ;
#213 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#214 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#215 = FILL_AREA_STYLE ('',( #20 ) ) ;
#216 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#217 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#218 = EDGE_CURVE ( 'NONE', #606, #1020, #1079, .T. ) ;
#219 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#220 = AXIS2_PLACEMENT_3D ( 'NONE', #144, #221, #461 ) ;
#221 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#222 = EDGE_CURVE ( 'NONE', #21, #48, #614, .T. ) ;
#223 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#224 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#225 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#226 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#227 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#228 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.008333333333333333218, -0.2250000000433680647 ) ) ;
#229 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #1178 ) ) ;
#230 = EDGE_CURVE ( 'NONE', #315, #617, #91, .T. ) ;
#231 = AXIS2_PLACEMENT_3D ( 'NONE', #363, #224, #455 ) ;
#232 = MANIFOLD_SOLID_BREP ( 'Mirror2', #270 ) ;
#233 = EDGE_CURVE ( 'NONE', #266, #133, #1299, .T. ) ;
#234 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#235 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -1.084202172485504681E-16 ) ) ;
#236 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#237 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1163, 'distance_accuracy_value', 'NONE');
#238 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#239 = ADVANCED_BREP_SHAPE_REPRESENTATION ( '10.50.550.25', ( #1390, #232, #498, #680 ), #500 ) ;
#240 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#241 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#242 = AXIS2_PLACEMENT_3D ( 'NONE', #1355, #1371, #236 ) ;
#243 = ADVANCED_FACE ( 'NONE', ( #781 ), #330, .T. ) ;
#244 = AXIS2_PLACEMENT_3D ( 'NONE', #754, #1137, #156 ) ;
#245 = EDGE_CURVE ( 'NONE', #762, #893, #1303, .T. ) ;
#246 = ADVANCED_FACE ( 'NONE', ( #789 ), #802, .T. ) ;
#247 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#248 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#249 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#250 = STYLED_ITEM ( 'NONE', ( #580 ), #989 ) ;
#251 = ADVANCED_FACE ( 'NONE', ( #792 ), #495, .T. ) ;
#252 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#253 = AXIS2_PLACEMENT_3D ( 'NONE', #165, #235, #627 ) ;
#254 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#255 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#256 = ADVANCED_FACE ( 'NONE', ( #1297 ), #350, .T. ) ;
#257 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#258 = ADVANCED_FACE ( 'NONE', ( #806 ), #953, .F. ) ;
#259 = AXIS2_PLACEMENT_3D ( 'NONE', #1089, #173, #507 ) ;
#260 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#261 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#262 = AXIS2_PLACEMENT_3D ( 'NONE', #1283, #991, #531 ) ;
#263 = ADVANCED_FACE ( 'NONE', ( #816 ), #1268, .T. ) ;
#264 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5416666666666667407, -0.2250000000433680647 ) ) ;
#265 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#266 = VERTEX_POINT ( 'NONE', #1112 ) ;
#267 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#268 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#269 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, 0.2499999999999999445 ) ) ;
#270 = CLOSED_SHELL ( 'NONE', ( #203, #708, #501, #658, #635, #258, #181, #246, #1405, #601, #1153, #1038, #791, #782, #256, #243, #486, #375 ) ) ;
#271 = ORIENTED_EDGE ( 'NONE', *, *, #698, .F. ) ;
#272 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#273 = EDGE_LOOP ( 'NONE', ( #795, #1235, #289, #16 ) ) ;
#274 = ORIENTED_EDGE ( 'NONE', *, *, #115, .T. ) ;
#275 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #928 ), #634 ) ;
#276 = EDGE_LOOP ( 'NONE', ( #963, #623, #882, #4 ) ) ;
#277 = ORIENTED_EDGE ( 'NONE', *, *, #409, .T. ) ;
#278 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #1135 ), #1158 ) ;
#279 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995975, -0.2416666666666665853 ) ) ;
#280 = EDGE_LOOP ( 'NONE', ( #277, #900, #1287, #764 ) ) ;
#281 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#282 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.008333333333333333218, -0.2250000000000000056 ) ) ;
#283 = EDGE_CURVE ( 'NONE', #939, #770, #285, .T. ) ;
#284 = ORIENTED_EDGE ( 'NONE', *, *, #949, .F. ) ;
#285 = LINE ( 'NONE', #1229, #809 ) ;
#286 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#287 = ORIENTED_EDGE ( 'NONE', *, *, #698, .T. ) ;
#288 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#289 = ORIENTED_EDGE ( 'NONE', *, *, #1107, .F. ) ;
#290 = PLANE ( 'NONE',  #593 ) ;
#291 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, 0.2499999999999999445 ) ) ;
#292 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#293 = ORIENTED_EDGE ( 'NONE', *, *, #1183, .F. ) ;
#294 = VECTOR ( 'NONE', #1182, 1000.000000000000000 ) ;
#295 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#296 = EDGE_LOOP ( 'NONE', ( #995, #1291, #1246, #1253 ) ) ;
#297 = LINE ( 'NONE', #896, #342 ) ;
#298 = AXIS2_PLACEMENT_3D ( 'NONE', #1238, #1267, #1413 ) ;
#299 = ORIENTED_EDGE ( 'NONE', *, *, #1013, .T. ) ;
#300 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#301 = CYLINDRICAL_SURFACE ( 'NONE', #231, 0.02500000000000001180 ) ;
#302 = ORIENTED_EDGE ( 'NONE', *, *, #176, .F. ) ;
#303 = AXIS2_PLACEMENT_3D ( 'NONE', #616, #1357, #371 ) ;
#304 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.2249999999999999500 ) ) ;
#305 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#306 = VERTEX_POINT ( 'NONE', #269 ) ;
#307 = EDGE_CURVE ( 'NONE', #1258, #936, #1286, .T. ) ;
#308 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#309 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995628, 0.2499999999999999445 ) ) ;
#310 = EDGE_LOOP ( 'NONE', ( #974, #811, #1225 ) ) ;
#311 = VECTOR ( 'NONE', #219, 1000.000000000000000 ) ;
#312 = ORIENTED_EDGE ( 'NONE', *, *, #1169, .T. ) ;
#313 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.000000000000000000, 0.000000000000000000 ) ) ;
#314 = ADVANCED_FACE ( 'NONE', ( #778 ), #884, .T. ) ;
#315 = VERTEX_POINT ( 'NONE', #670 ) ;
#316 = ORIENTED_EDGE ( 'NONE', *, *, #1362, .F. ) ;
#317 = CIRCLE ( 'NONE', #1055, 0.02500000000000001180 ) ;
#318 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#319 = EDGE_LOOP ( 'NONE', ( #1353, #470, #971, #1314 ) ) ;
#320 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -8.673617379884036705E-17 ) ) ;
#321 = ORIENTED_EDGE ( 'NONE', *, *, #467, .T. ) ;
#322 = EDGE_CURVE ( 'NONE', #1, #765, #1273, .T. ) ;
#323 = FACE_OUTER_BOUND ( 'NONE', #1086, .T. ) ;
#324 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, -0.2416666666666665853 ) ) ;
#325 = EDGE_LOOP ( 'NONE', ( #902, #1272, #1364, #980, #439, #338, #50, #523 ) ) ;
#326 = ORIENTED_EDGE ( 'NONE', *, *, #34, .T. ) ;
#327 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #250 ) ) ;
#328 = ORIENTED_EDGE ( 'NONE', *, *, #552, .F. ) ;
#329 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#330 = SPHERICAL_SURFACE ( 'NONE', #2, 0.02500000000000000139 ) ;
#331 = EDGE_CURVE ( 'NONE', #266, #760, #333, .T. ) ;
#332 = ORIENTED_EDGE ( 'NONE', *, *, #146, .T. ) ;
#333 = CIRCLE ( 'NONE', #1005, 0.02500000000000001180 ) ;
#334 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#335 = DIRECTION ( 'NONE',  ( 1.084202172485504681E-16, -0.000000000000000000, -1.000000000000000000 ) ) ;
#336 = ORIENTED_EDGE ( 'NONE', *, *, #331, .F. ) ;
#337 = SURFACE_STYLE_FILL_AREA ( #426 ) ;
#338 = ORIENTED_EDGE ( 'NONE', *, *, #146, .F. ) ;
#339 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #574, 'distance_accuracy_value', 'NONE');
#340 = VERTEX_POINT ( 'NONE', #282 ) ;
#341 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#342 = VECTOR ( 'NONE', #468, 1000.000000000000000 ) ;
#343 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', 'automotive_design', 1998, #915 ) ;
#344 = PLANE ( 'NONE',  #259 ) ;
#345 = ORIENTED_EDGE ( 'NONE', *, *, #68, .F. ) ;
#346 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, 0.2499999999999999445 ) ) ;
#347 = EDGE_CURVE ( 'NONE', #936, #74, #317, .T. ) ;
#348 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#349 = VERTEX_POINT ( 'NONE', #1131 ) ;
#350 = CYLINDRICAL_SURFACE ( 'NONE', #571, 0.02500000000000001180 ) ;
#351 = VERTEX_POINT ( 'NONE', #1313 ) ;
#352 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #932 ) ) ;
#353 = LINE ( 'NONE', #1179, #1318 ) ;
#354 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#355 = EDGE_CURVE ( 'NONE', #887, #349, #1289, .T. ) ;
#356 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -8.673617379884036705E-17 ) ) ;
#357 = LINE ( 'NONE', #769, #848 ) ;
#358 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#359 = ORIENTED_EDGE ( 'NONE', *, *, #1160, .F. ) ;
#360 = FACE_OUTER_BOUND ( 'NONE', #997, .T. ) ;
#361 = CYLINDRICAL_SURFACE ( 'NONE', #80, 0.02500000000000001180 ) ;
#362 = PLANE ( 'NONE',  #387 ) ;
#363 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#364 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #250 ), #1139 ) ;
#365 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#366 = STYLED_ITEM ( 'NONE', ( #390 ), #498 ) ;
#367 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#368 = PRESENTATION_STYLE_ASSIGNMENT (( #448 ) ) ;
#369 = EDGE_CURVE ( 'NONE', #1372, #817, #773, .T. ) ;
#370 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, 0.2499999999999999445 ) ) ;
#371 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#372 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#373 = FILL_AREA_STYLE_COLOUR ( '', #898 ) ;
#374 = VERTEX_POINT ( 'NONE', #248 ) ;
#375 = ADVANCED_FACE ( 'NONE', ( #1261 ), #301, .T. ) ;
#376 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#377 = ORIENTED_EDGE ( 'NONE', *, *, #123, .F. ) ;
#378 = LINE ( 'NONE', #1262, #1394 ) ;
#379 = FACE_OUTER_BOUND ( 'NONE', #296, .T. ) ;
#380 = EDGE_LOOP ( 'NONE', ( #990, #299, #72, #392 ) ) ;
#381 = FACE_OUTER_BOUND ( 'NONE', #830, .T. ) ;
#382 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#383 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #1108 ), #1070 ) ;
#384 = VERTEX_POINT ( 'NONE', #1321 ) ;
#385 = CYLINDRICAL_SURFACE ( 'NONE', #1269, 0.01666666666666664562 ) ;
#386 = VECTOR ( 'NONE', #1302, 1000.000000000000000 ) ;
#387 = AXIS2_PLACEMENT_3D ( 'NONE', #382, #1151, #700 ) ;
#388 = ORIENTED_EDGE ( 'NONE', *, *, #445, .F. ) ;
#389 = FACE_OUTER_BOUND ( 'NONE', #561, .T. ) ;
#390 = PRESENTATION_STYLE_ASSIGNMENT (( #743 ) ) ;
#391 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#392 = ORIENTED_EDGE ( 'NONE', *, *, #1149, .T. ) ;
#393 = EDGE_CURVE ( 'NONE', #559, #48, #826, .T. ) ;
#394 = CYLINDRICAL_SURFACE ( 'NONE', #1110, 0.02500000000000001180 ) ;
#395 = CLOSED_SHELL ( 'NONE', ( #1167, #194, #641, #1170, #251, #1385, #516, #808, #71, #174, #86, #1064, #946, #1176, #97, #263, #169, #553 ) ) ;
#396 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, 0.2499999999999999445 ) ) ;
#397 = FACE_OUTER_BOUND ( 'NONE', #821, .T. ) ;
#398 = EDGE_CURVE ( 'NONE', #488, #813, #1292, .T. ) ;
#399 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#400 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#401 = EDGE_LOOP ( 'NONE', ( #1278, #93, #1285, #968, #1274, #1415, #785, #1419 ) ) ;
#402 = VECTOR ( 'NONE', #225, 1000.000000000000000 ) ;
#403 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1284, 'distance_accuracy_value', 'NONE');
#404 = EDGE_LOOP ( 'NONE', ( #564, #1351, #1306, #621, #1407, #1301, #1410, #430 ) ) ;
#405 = LINE ( 'NONE', #551, #894 ) ;
#406 = FACE_OUTER_BOUND ( 'NONE', #1265, .T. ) ;
#407 = DIRECTION ( 'NONE',  ( 1.084202172485504681E-16, -0.000000000000000000, -1.000000000000000000 ) ) ;
#408 = VERTEX_POINT ( 'NONE', #324 ) ;
#409 = EDGE_CURVE ( 'NONE', #1, #893, #297, .T. ) ;
#410 = LINE ( 'NONE', #1168, #1400 ) ;
#411 = VECTOR ( 'NONE', #1195, 1000.000000000000000 ) ;
#412 = ORIENTED_EDGE ( 'NONE', *, *, #888, .T. ) ;
#413 = STYLED_ITEM ( 'NONE', ( #927 ), #1008 ) ;
#414 = DIRECTION ( 'NONE',  ( 1.084202172485504681E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#415 = SURFACE_STYLE_USAGE ( .BOTH. , #881 ) ;
#416 = EDGE_LOOP ( 'NONE', ( #1349, #554, #923, #987 ) ) ;
#417 = CYLINDRICAL_SURFACE ( 'NONE', #1132, 0.02500000000000001180 ) ;
#418 = AXIS2_PLACEMENT_3D ( 'NONE', #111, #1161, #265 ) ;
#419 = EDGE_LOOP ( 'NONE', ( #446, #832, #1305, #60 ) ) ;
#420 = EDGE_LOOP ( 'NONE', ( #1030, #910, #1363, #1231, #532, #1336, #422, #43 ) ) ;
#421 = VECTOR ( 'NONE', #697, 1000.000000000000000 ) ;
#422 = ORIENTED_EDGE ( 'NONE', *, *, #873, .T. ) ;
#423 = SURFACE_STYLE_FILL_AREA ( #443 ) ;
#424 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#425 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#426 = FILL_AREA_STYLE ('',( #740 ) ) ;
#427 = ORIENTED_EDGE ( 'NONE', *, *, #355, .F. ) ;
#428 = CIRCLE ( 'NONE', #1430, 0.02500000000000001180 ) ;
#429 = CIRCLE ( 'NONE', #31, 0.02500000000000001180 ) ;
#430 = ORIENTED_EDGE ( 'NONE', *, *, #973, .F. ) ;
#431 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#432 = SPHERICAL_SURFACE ( 'NONE', #1027, 0.02500000000000000139 ) ;
#433 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#434 = FILL_AREA_STYLE_COLOUR ( '', #1037 ) ;
#435 = ORIENTED_EDGE ( 'NONE', *, *, #1169, .F. ) ;
#436 = LINE ( 'NONE', #1277, #1391 ) ;
#437 = CIRCLE ( 'NONE', #741, 0.02500000000000001180 ) ;
#438 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #1121 ), #143 ) ;
#439 = ORIENTED_EDGE ( 'NONE', *, *, #164, .T. ) ;
#440 = SURFACE_STYLE_FILL_AREA ( #723 ) ;
#441 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#442 = FACE_OUTER_BOUND ( 'NONE', #1241, .T. ) ;
#443 = FILL_AREA_STYLE ('',( #1398 ) ) ;
#444 = ORIENTED_EDGE ( 'NONE', *, *, #961, .T. ) ;
#445 = EDGE_CURVE ( 'NONE', #340, #794, #1255, .T. ) ;
#446 = ORIENTED_EDGE ( 'NONE', *, *, #355, .T. ) ;
#447 = VECTOR ( 'NONE', #210, 1000.000000000000000 ) ;
#448 = SURFACE_STYLE_USAGE ( .BOTH. , #875 ) ;
#449 = EDGE_CURVE ( 'NONE', #897, #887, #1276, .T. ) ;
#450 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#451 = ORIENTED_EDGE ( 'NONE', *, *, #1183, .T. ) ;
#452 = EDGE_LOOP ( 'NONE', ( #388, #1059, #575, #604 ) ) ;
#453 = CIRCLE ( 'NONE', #819, 0.01666666666666667337 ) ;
#454 = FILL_AREA_STYLE ('',( #161 ) ) ;
#455 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#456 = ORIENTED_EDGE ( 'NONE', *, *, #638, .T. ) ;
#457 = FACE_OUTER_BOUND ( 'NONE', #452, .T. ) ;
#458 = EDGE_LOOP ( 'NONE', ( #793, #1016, #302, #69 ) ) ;
#459 = CIRCLE ( 'NONE', #1043, 0.02500000000000001180 ) ;
#460 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #155 ) ) ;
#461 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#462 = FACE_OUTER_BOUND ( 'NONE', #1256, .T. ) ;
#463 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#464 = ORIENTED_EDGE ( 'NONE', *, *, #398, .T. ) ;
#465 = AXIS2_PLACEMENT_3D ( 'NONE', #482, #249, #1212 ) ;
#466 = VERTEX_POINT ( 'NONE', #309 ) ;
#467 = EDGE_CURVE ( 'NONE', #950, #1367, #1264, .T. ) ;
#468 = DIRECTION ( 'NONE',  ( -1.084202172485504681E-16, -0.000000000000000000, -1.000000000000000000 ) ) ;
#469 = EDGE_LOOP ( 'NONE', ( #53, #805, #435, #868 ) ) ;
#470 = ORIENTED_EDGE ( 'NONE', *, *, #1226, .T. ) ;
#471 = ORIENTED_EDGE ( 'NONE', *, *, #701, .T. ) ;
#472 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#473 = VERTEX_POINT ( 'NONE', #685 ) ;
#474 = ORIENTED_EDGE ( 'NONE', *, *, #1226, .F. ) ;
#475 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #928 ) ) ;
#476 = ORIENTED_EDGE ( 'NONE', *, *, #148, .T. ) ;
#477 = CYLINDRICAL_SURFACE ( 'NONE', #1330, 0.02500000000000001180 ) ;
#478 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#479 = VERTEX_POINT ( 'NONE', #1422 ) ;
#480 = LINE ( 'NONE', #715, #972 ) ;
#481 = AXIS2_PLACEMENT_3D ( 'NONE', #241, #1347, #188 ) ;
#482 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#483 = VERTEX_POINT ( 'NONE', #286 ) ;
#484 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#485 = VECTOR ( 'NONE', #818, 1000.000000000000000 ) ;
#486 = ADVANCED_FACE ( 'NONE', ( #323 ), #812, .T. ) ;
#487 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#488 = VERTEX_POINT ( 'NONE', #1033 ) ;
#489 = LINE ( 'NONE', #1402, #1404 ) ;
#490 = ORIENTED_EDGE ( 'NONE', *, *, #85, .T. ) ;
#491 = VECTOR ( 'NONE', #425, 1000.000000000000000 ) ;
#492 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#493 = ORIENTED_EDGE ( 'NONE', *, *, #577, .F. ) ;
#494 = VECTOR ( 'NONE', #308, 1000.000000000000000 ) ;
#495 = PLANE ( 'NONE',  #1275 ) ;
#496 = ORIENTED_EDGE ( 'NONE', *, *, #158, .T. ) ;
#497 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#498 = MANIFOLD_SOLID_BREP ( 'Boss-Extrude2', #967 ) ;
#499 = ORIENTED_EDGE ( 'NONE', *, *, #829, .T. ) ;
#500 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #403 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1284, #126, #737 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#501 = ADVANCED_FACE ( 'NONE', ( #852 ), #998, .F. ) ;
#502 = AXIS2_PLACEMENT_3D ( 'NONE', #880, #24, #9 ) ;
#503 = ORIENTED_EDGE ( 'NONE', *, *, #230, .T. ) ;
#504 = EDGE_CURVE ( 'NONE', #1298, #48, #1326, .T. ) ;
#505 = ORIENTED_EDGE ( 'NONE', *, *, #1331, .T. ) ;
#506 = CIRCLE ( 'NONE', #1294, 0.02500000000000001180 ) ;
#507 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -8.673617379884035472E-17 ) ) ;
#508 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#509 = EDGE_CURVE ( 'NONE', #765, #21, #1354, .T. ) ;
#510 = ORIENTED_EDGE ( 'NONE', *, *, #369, .F. ) ;
#511 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#512 = ORIENTED_EDGE ( 'NONE', *, *, #7, .T. ) ;
#513 = SURFACE_STYLE_FILL_AREA ( #758 ) ;
#514 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#515 = ORIENTED_EDGE ( 'NONE', *, *, #958, .F. ) ;
#516 = ADVANCED_FACE ( 'NONE', ( #381 ), #361, .T. ) ;
#517 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#518 = EDGE_LOOP ( 'NONE', ( #549, #444, #293, #1236 ) ) ;
#519 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#520 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#521 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.000000000000000000, 0.000000000000000000 ) ) ;
#522 = AXIS2_PLACEMENT_3D ( 'NONE', #391, #850, #1084 ) ;
#523 = ORIENTED_EDGE ( 'NONE', *, *, #34, .F. ) ;
#524 = FILL_AREA_STYLE ('',( #373 ) ) ;
#525 = ORIENTED_EDGE ( 'NONE', *, *, #1279, .T. ) ;
#526 = LINE ( 'NONE', #313, #58 ) ;
#527 = AXIS2_PLACEMENT_3D ( 'NONE', #38, #597, #688 ) ;
#528 = ORIENTED_EDGE ( 'NONE', *, *, #935, .T. ) ;
#529 = VECTOR ( 'NONE', #62, 1000.000000000000000 ) ;
#530 = EDGE_CURVE ( 'NONE', #1340, #939, #864, .T. ) ;
#531 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#532 = ORIENTED_EDGE ( 'NONE', *, *, #409, .F. ) ;
#533 = LINE ( 'NONE', #234, #573 ) ;
#534 = EDGE_LOOP ( 'NONE', ( #35, #1250, #37, #321 ) ) ;
#535 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#536 = ADVANCED_FACE ( 'NONE', ( #406 ), #1329, .T. ) ;
#537 = EDGE_LOOP ( 'NONE', ( #1266, #528, #336 ) ) ;
#538 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#539 = ORIENTED_EDGE ( 'NONE', *, *, #1047, .T. ) ;
#540 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#541 = ORIENTED_EDGE ( 'NONE', *, *, #85, .F. ) ;
#542 = CIRCLE ( 'NONE', #565, 0.01666666666666662133 ) ;
#543 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.02499999999999995628, -0.2250000000433680647 ) ) ;
#544 = AXIS2_PLACEMENT_3D ( 'NONE', #160, #365, #775 ) ;
#545 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#546 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#547 = EDGE_LOOP ( 'NONE', ( #943, #776, #129, #1004 ) ) ;
#548 = AXIS2_PLACEMENT_3D ( 'NONE', #556, #292, #1196 ) ;
#549 = ORIENTED_EDGE ( 'NONE', *, *, #692, .F. ) ;
#550 = VECTOR ( 'NONE', #1210, 1000.000000000000000 ) ;
#551 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#552 = EDGE_CURVE ( 'NONE', #1348, #936, #1359, .T. ) ;
#553 = ADVANCED_FACE ( 'NONE', ( #360 ), #394, .T. ) ;
#554 = ORIENTED_EDGE ( 'NONE', *, *, #115, .F. ) ;
#555 = FACE_OUTER_BOUND ( 'NONE', #404, .T. ) ;
#556 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#557 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#558 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#559 = VERTEX_POINT ( 'NONE', #354 ) ;
#560 = FACE_OUTER_BOUND ( 'NONE', #1379, .T. ) ;
#561 = EDGE_LOOP ( 'NONE', ( #332, #890, #1393, #505 ) ) ;
#562 = LINE ( 'NONE', #877, #1011 ) ;
#563 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#564 = ORIENTED_EDGE ( 'NONE', *, *, #158, .F. ) ;
#565 = AXIS2_PLACEMENT_3D ( 'NONE', #1144, #1334, #288 ) ;
#566 = LINE ( 'NONE', #591, #550 ) ;
#567 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#568 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#569 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5250000000000000222, -0.2416666666666666408 ) ) ;
#570 = CIRCLE ( 'NONE', #718, 0.02500000000000001180 ) ;
#571 = AXIS2_PLACEMENT_3D ( 'NONE', #223, #695, #772 ) ;
#572 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#573 = VECTOR ( 'NONE', #689, 1000.000000000000000 ) ;
#574 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#575 = ORIENTED_EDGE ( 'NONE', *, *, #727, .T. ) ;
#576 = SURFACE_STYLE_FILL_AREA ( #1213 ) ;
#577 = EDGE_CURVE ( 'NONE', #1075, #8, #1341, .T. ) ;
#578 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#579 = EDGE_LOOP ( 'NONE', ( #1032, #312, #493 ) ) ;
#580 = PRESENTATION_STYLE_ASSIGNMENT (( #620 ) ) ;
#581 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, 0.2499999999999999445 ) ) ;
#582 = PRESENTATION_STYLE_ASSIGNMENT (( #63 ) ) ;
#583 = SURFACE_STYLE_USAGE ( .BOTH. , #663 ) ;
#584 = CYLINDRICAL_SURFACE ( 'NONE', #803, 0.02500000000000001180 ) ;
#585 = VECTOR ( 'NONE', #1142, 1000.000000000000000 ) ;
#586 = ORIENTED_EDGE ( 'NONE', *, *, #673, .T. ) ;
#587 = CIRCLE ( 'NONE', #646, 0.02500000000000001180 ) ;
#588 = PRODUCT_DEFINITION ( 'UNKNOWN', '', #163, #36 ) ;
#589 = PLANE ( 'NONE',  #999 ) ;
#590 = CIRCLE ( 'NONE', #303, 0.02500000000000001180 ) ;
#591 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -0.2249999999999999500 ) ) ;
#592 = EDGE_LOOP ( 'NONE', ( #783, #67, #345, #377 ) ) ;
#593 = AXIS2_PLACEMENT_3D ( 'NONE', #1028, #441, #578 ) ;
#594 = ORIENTED_EDGE ( 'NONE', *, *, #1252, .T. ) ;
#595 = FILL_AREA_STYLE ('',( #1006 ) ) ;
#596 = SURFACE_SIDE_STYLE ('',( #1417 ) ) ;
#597 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#598 = AXIS2_PLACEMENT_3D ( 'NONE', #1237, #1029, #1103 ) ;
#599 = CIRCLE ( 'NONE', #1411, 0.02500000000000001180 ) ;
#600 = FACE_OUTER_BOUND ( 'NONE', #592, .T. ) ;
#601 = ADVANCED_FACE ( 'NONE', ( #1323 ), #857, .T. ) ;
#602 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#603 = FILL_AREA_STYLE ('',( #625 ) ) ;
#604 = ORIENTED_EDGE ( 'NONE', *, *, #701, .F. ) ;
#605 = CYLINDRICAL_SURFACE ( 'NONE', #197, 0.02500000000000001180 ) ;
#606 = VERTEX_POINT ( 'NONE', #1307 ) ;
#607 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#608 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.02499999999999995628, -0.2250000000000000056 ) ) ;
#609 = PRESENTATION_STYLE_ASSIGNMENT (( #415 ) ) ;
#610 = LINE ( 'NONE', #66, #1080 ) ;
#611 = AXIS2_PLACEMENT_3D ( 'NONE', #240, #356, #636 ) ;
#612 = FACE_OUTER_BOUND ( 'NONE', #458, .T. ) ;
#613 = CIRCLE ( 'NONE', #611, 0.01666666666666667337 ) ;
#614 = CIRCLE ( 'NONE', #212, 0.02500000000000001180 ) ;
#615 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#616 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02500000000000000139, 0.2249999999999999500 ) ) ;
#617 = VERTEX_POINT ( 'NONE', #732 ) ;
#618 = AXIS2_PLACEMENT_3D ( 'NONE', #1184, #1191, #18 ) ;
#619 = VECTOR ( 'NONE', #694, 1000.000000000000000 ) ;
#620 = SURFACE_STYLE_USAGE ( .BOTH. , #45 ) ;
#621 = ORIENTED_EDGE ( 'NONE', *, *, #638, .F. ) ;
#622 = EDGE_CURVE ( 'NONE', #813, #33, #861, .T. ) ;
#623 = ORIENTED_EDGE ( 'NONE', *, *, #283, .T. ) ;
#624 = LINE ( 'NONE', #919, #1096 ) ;
#625 = FILL_AREA_STYLE_COLOUR ( '', #519 ) ;
#626 = PRODUCT_CONTEXT ( 'NONE', #1211, 'mechanical' ) ;
#627 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#628 = LINE ( 'NONE', #1048, #1099 ) ;
#629 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#630 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#631 = LINE ( 'NONE', #84, #145 ) ;
#632 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#633 = FACE_OUTER_BOUND ( 'NONE', #319, .T. ) ;
#634 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #656 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1046, #1035, #1134 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#635 = ADVANCED_FACE ( 'NONE', ( #379 ), #290, .T. ) ;
#636 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#637 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #977 ) ) ;
#638 = EDGE_CURVE ( 'NONE', #479, #384, #874, .T. ) ;
#639 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#640 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -1.084202172485504681E-16 ) ) ;
#641 = ADVANCED_FACE ( 'NONE', ( #1350 ), #112, .F. ) ;
#642 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#643 = LINE ( 'NONE', #581, #654 ) ;
#644 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -1.084202172485504681E-16 ) ) ;
#645 = CYLINDRICAL_SURFACE ( 'NONE', #1090, 0.02500000000000001180 ) ;
#646 = AXIS2_PLACEMENT_3D ( 'NONE', #511, #644, #956 ) ;
#647 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.02499999999999995628, 0.2249999999999998945 ) ) ;
#648 = EDGE_CURVE ( 'NONE', #897, #907, #353, .T. ) ;
#649 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#650 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#651 = EDGE_CURVE ( 'NONE', #473, #408, #851, .T. ) ;
#652 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02500000000000000139, 0.2249999999999999500 ) ) ;
#653 = FACE_OUTER_BOUND ( 'NONE', #917, .T. ) ;
#654 = VECTOR ( 'NONE', #1296, 1000.000000000000000 ) ;
#655 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#656 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1046, 'distance_accuracy_value', 'NONE');
#657 = AXIS2_PLACEMENT_3D ( 'NONE', #40, #431, #152 ) ;
#658 = ADVANCED_FACE ( 'NONE', ( #1309 ), #751, .F. ) ;
#659 = EDGE_CURVE ( 'NONE', #794, #1293, #357, .T. ) ;
#660 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#661 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5250000000000000222, 0.2499999999999999445 ) ) ;
#662 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#663 = SURFACE_SIDE_STYLE ('',( #337 ) ) ;
#664 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5250000000000000222, 0.2416666666666665575 ) ) ;
#665 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#666 = SURFACE_STYLE_USAGE ( .BOTH. , #746 ) ;
#667 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#668 = AXIS2_PLACEMENT_3D ( 'NONE', #268, #655, #721 ) ;
#669 = EDGE_CURVE ( 'NONE', #817, #1348, #562, .T. ) ;
#670 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#671 = AXIS2_PLACEMENT_3D ( 'NONE', #1189, #1198, #114 ) ;
#672 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#673 = EDGE_CURVE ( 'NONE', #950, #315, #1017, .T. ) ;
#674 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#675 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#676 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#677 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#678 = FILL_AREA_STYLE ('',( #729 ) ) ;
#679 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#680 = AXIS2_PLACEMENT_3D ( 'NONE', #563, #227, #52 ) ;
#681 = AXIS2_PLACEMENT_3D ( 'NONE', #1201, #1317, #1050 ) ;
#682 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#683 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#684 = AXIS2_PLACEMENT_3D ( 'NONE', #15, #208, #140 ) ;
#685 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995628, -0.2416666666666665575 ) ) ;
#686 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#687 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#688 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#689 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#690 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #237 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1163, #827, #189 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#691 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#692 = EDGE_CURVE ( 'NONE', #1348, #74, #570, .T. ) ;
#693 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#694 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#695 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#696 = SURFACE_STYLE_FILL_AREA ( #215 ) ;
#697 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#698 = EDGE_CURVE ( 'NONE', #133, #907, #61, .T. ) ;
#699 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#700 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#701 = EDGE_CURVE ( 'NONE', #794, #473, #1054, .T. ) ;
#702 = AXIS2_PLACEMENT_3D ( 'NONE', #608, #691, #763 ) ;
#703 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02500000000000000139, -0.2249999999999999500 ) ) ;
#704 = EDGE_CURVE ( 'NONE', #1020, #839, #566, .T. ) ;
#705 = ADVANCED_FACE ( 'NONE', ( #555 ), #914, .F. ) ;
#706 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, 0.2499999999999999445 ) ) ;
#707 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#708 = ADVANCED_FACE ( 'NONE', ( #75 ), #924, .F. ) ;
#709 = AXIS2_PLACEMENT_3D ( 'NONE', #1378, #6, #1290 ) ;
#710 = AXIS2_PLACEMENT_3D ( 'NONE', #247, #796, #1217 ) ;
#711 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#712 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.02499999999999995628, 0.2416666666666665853 ) ) ;
#713 = EDGE_CURVE ( 'NONE', #33, #1020, #526, .T. ) ;
#714 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#715 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5250000000000000222, 0.2416666666232984984 ) ) ;
#716 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#717 = EDGE_CURVE ( 'NONE', #762, #100, #54, .T. ) ;
#718 = AXIS2_PLACEMENT_3D ( 'NONE', #226, #211, #329 ) ;
#719 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #962 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #3, #1014, #572 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#720 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#721 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#722 = SURFACE_SIDE_STYLE ('',( #1361 ) ) ;
#723 = FILL_AREA_STYLE ('',( #191 ) ) ;
#724 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#725 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#726 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5416666666666667407, 0.2249999999999998945 ) ) ;
#727 = EDGE_CURVE ( 'NONE', #384, #473, #542, .T. ) ;
#728 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #952 ) ) ;
#729 = FILL_AREA_STYLE_COLOUR ( '', #879 ) ;
#730 = SURFACE_SIDE_STYLE ('',( #440 ) ) ;
#731 = EDGE_CURVE ( 'NONE', #936, #315, #533, .T. ) ;
#732 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#733 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.008333333333333333218, 0.2249999999566318354 ) ) ;
#734 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#735 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#736 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5416666666666667407, -0.2250000000975781456 ) ) ;
#737 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#738 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#739 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#740 = FILL_AREA_STYLE_COLOUR ( '', #1248 ) ;
#741 = AXIS2_PLACEMENT_3D ( 'NONE', #238, #487, #687 ) ;
#742 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #358, 'distance_accuracy_value', 'NONE');
#743 = SURFACE_STYLE_USAGE ( .BOTH. , #899 ) ;
#744 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#745 = EDGE_CURVE ( 'NONE', #48, #1, #41, .T. ) ;
#746 = SURFACE_SIDE_STYLE ('',( #1102 ) ) ;
#747 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#748 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#749 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#750 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#751 = PLANE ( 'NONE',  #1148 ) ;
#752 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #1108 ) ) ;
#753 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#754 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#755 = ADVANCED_FACE ( 'NONE', ( #560 ), #976, .F. ) ;
#756 = AXIS2_PLACEMENT_3D ( 'NONE', #139, #675, #113 ) ;
#757 = EDGE_CURVE ( 'NONE', #306, #466, #122, .T. ) ;
#758 = FILL_AREA_STYLE ('',( #434 ) ) ;
#759 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#760 = VERTEX_POINT ( 'NONE', #517 ) ;
#761 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#762 = VERTEX_POINT ( 'NONE', #492 ) ;
#763 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#764 = ORIENTED_EDGE ( 'NONE', *, *, #745, .T. ) ;
#765 = VERTEX_POINT ( 'NONE', #706 ) ;
#766 = ORIENTED_EDGE ( 'NONE', *, *, #961, .F. ) ;
#767 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#768 = VECTOR ( 'NONE', #734, 1000.000000000000000 ) ;
#769 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.02499999999999995975, -0.2416666666666666408 ) ) ;
#770 = VERTEX_POINT ( 'NONE', #186 ) ;
#771 = FILL_AREA_STYLE ('',( #1227 ) ) ;
#772 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#773 = LINE ( 'NONE', #1119, #294 ) ;
#774 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #202 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #674, #1427, #853 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#775 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#776 = ORIENTED_EDGE ( 'NONE', *, *, #669, .T. ) ;
#777 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#778 = FACE_OUTER_BOUND ( 'NONE', #401, .T. ) ;
#779 = ORIENTED_EDGE ( 'NONE', *, *, #393, .F. ) ;
#780 = AXIS2_PLACEMENT_3D ( 'NONE', #543, #747, #267 ) ;
#781 = FACE_OUTER_BOUND ( 'NONE', #1045, .T. ) ;
#782 = ADVANCED_FACE ( 'NONE', ( #108 ), #1066, .T. ) ;
#783 = ORIENTED_EDGE ( 'NONE', *, *, #190, .F. ) ;
#784 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.02499999999999995628, -0.2416666667100346721 ) ) ;
#785 = ORIENTED_EDGE ( 'NONE', *, *, #445, .T. ) ;
#786 = PRESENTATION_STYLE_ASSIGNMENT (( #583 ) ) ;
#787 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#788 = ORIENTED_EDGE ( 'NONE', *, *, #849, .T. ) ;
#789 = FACE_OUTER_BOUND ( 'NONE', #534, .T. ) ;
#790 = ORIENTED_EDGE ( 'NONE', *, *, #222, .F. ) ;
#791 = ADVANCED_FACE ( 'NONE', ( #128 ), #1069, .T. ) ;
#792 = FACE_OUTER_BOUND ( 'NONE', #280, .T. ) ;
#793 = ORIENTED_EDGE ( 'NONE', *, *, #201, .F. ) ;
#794 = VERTEX_POINT ( 'NONE', #965 ) ;
#795 = ORIENTED_EDGE ( 'NONE', *, *, #393, .T. ) ;
#796 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#797 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#798 = AXIS2_PLACEMENT_3D ( 'NONE', #1126, #1324, #51 ) ;
#799 = VECTOR ( 'NONE', #1202, 1000.000000000000000 ) ;
#800 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.000000000000000000, -0.2249999999999999500 ) ) ;
#801 = ORIENTED_EDGE ( 'NONE', *, *, #1187, .F. ) ;
#802 = CYLINDRICAL_SURFACE ( 'NONE', #1166, 0.02500000000000001180 ) ;
#803 = AXIS2_PLACEMENT_3D ( 'NONE', #102, #149, #1310 ) ;
#804 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #889, 'distance_accuracy_value', 'NONE');
#805 = ORIENTED_EDGE ( 'NONE', *, *, #1149, .F. ) ;
#806 = FACE_OUTER_BOUND ( 'NONE', #1270, .T. ) ;
#807 = ORIENTED_EDGE ( 'NONE', *, *, #1222, .T. ) ;
#808 = ADVANCED_FACE ( 'NONE', ( #600 ), #94, .T. ) ;
#809 = VECTOR ( 'NONE', #1174, 1000.000000000000000 ) ;
#810 = EDGE_LOOP ( 'NONE', ( #59, #29, #801, #1224 ) ) ;
#811 = ORIENTED_EDGE ( 'NONE', *, *, #10, .T. ) ;
#812 = SPHERICAL_SURFACE ( 'NONE', #657, 0.02500000000000000139 ) ;
#813 = VERTEX_POINT ( 'NONE', #1109 ) ;
#814 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#815 = ORIENTED_EDGE ( 'NONE', *, *, #622, .F. ) ;
#816 = FACE_OUTER_BOUND ( 'NONE', #579, .T. ) ;
#817 = VERTEX_POINT ( 'NONE', #1162 ) ;
#818 = DIRECTION ( 'NONE',  ( -1.084202172485504681E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#819 = AXIS2_PLACEMENT_3D ( 'NONE', #1230, #1387, #1181 ) ;
#820 = EDGE_CURVE ( 'NONE', #1092, #770, #1072, .T. ) ;
#821 = EDGE_LOOP ( 'NONE', ( #957, #1389, #98, #474 ) ) ;
#822 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#823 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#824 = EDGE_LOOP ( 'NONE', ( #911, #807, #539, #836 ) ) ;
#825 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #1190 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #749, #558, #677 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#826 = CIRCLE ( 'NONE', #527, 0.02500000000000001180 ) ;
#827 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#828 = ORIENTED_EDGE ( 'NONE', *, *, #1160, .T. ) ;
#829 = EDGE_CURVE ( 'NONE', #33, #1367, #1093, .T. ) ;
#830 = EDGE_LOOP ( 'NONE', ( #1034, #1360, #790, #1040 ) ) ;
#831 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5250000000000000222, -0.2416666667100346999 ) ) ;
#832 = ORIENTED_EDGE ( 'NONE', *, *, #1047, .F. ) ;
#833 = ORIENTED_EDGE ( 'NONE', *, *, #704, .F. ) ;
#834 = ORIENTED_EDGE ( 'NONE', *, *, #648, .F. ) ;
#835 = VECTOR ( 'NONE', #450, 1000.000000000000000 ) ;
#836 = ORIENTED_EDGE ( 'NONE', *, *, #123, .T. ) ;
#837 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #413 ), #690 ) ;
#838 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#839 = VERTEX_POINT ( 'NONE', #171 ) ;
#840 = VECTOR ( 'NONE', #1315, 1000.000000000000000 ) ;
#841 = AXIS2_PLACEMENT_3D ( 'NONE', #870, #200, #855 ) ;
#842 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#843 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#844 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#845 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.008333333333333296788, 0.2249999999566318354 ) ) ;
#846 = EDGE_CURVE ( 'NONE', #985, #1092, #613, .T. ) ;
#847 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1164, 'distance_accuracy_value', 'NONE');
#848 = VECTOR ( 'NONE', #1026, 1000.000000000000000 ) ;
#849 = EDGE_CURVE ( 'NONE', #817, #315, #142, .T. ) ;
#850 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#851 = LINE ( 'NONE', #279, #835 ) ;
#852 = FACE_OUTER_BOUND ( 'NONE', #325, .T. ) ;
#853 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#854 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#855 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#856 = EDGE_CURVE ( 'NONE', #954, #488, #1104, .T. ) ;
#857 = SPHERICAL_SURFACE ( 'NONE', #502, 0.02500000000000000139 ) ;
#858 = AXIS2_PLACEMENT_3D ( 'NONE', #1060, #1140, #153 ) ;
#859 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#860 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.000000000000000000, -0.2249999999999999500 ) ) ;
#861 = LINE ( 'NONE', #304, #1337 ) ;
#862 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #742 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #358, #182, #925 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#863 = CYLINDRICAL_SURFACE ( 'NONE', #1120, 0.01666666666666667337 ) ;
#864 = LINE ( 'NONE', #664, #840 ) ;
#865 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1172, 'distance_accuracy_value', 'NONE');
#866 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#867 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#868 = ORIENTED_EDGE ( 'NONE', *, *, #1343, .F. ) ;
#869 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02500000000000000139, -0.2249999999999999500 ) ) ;
#870 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#871 = ORIENTED_EDGE ( 'NONE', *, *, #820, .T. ) ;
#872 = LINE ( 'NONE', #1395, #447 ) ;
#873 = EDGE_CURVE ( 'NONE', #765, #466, #1088, .T. ) ;
#874 = LINE ( 'NONE', #1128, #411 ) ;
#875 = SURFACE_SIDE_STYLE ('',( #895 ) ) ;
#876 = ORIENTED_EDGE ( 'NONE', *, *, #1013, .F. ) ;
#877 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#878 = ORIENTED_EDGE ( 'NONE', *, *, #530, .F. ) ;
#879 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#880 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#881 = SURFACE_SIDE_STYLE ('',( #121 ) ) ;
#882 = ORIENTED_EDGE ( 'NONE', *, *, #42, .T. ) ;
#883 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5250000000000000222, 0.2416666666666665575 ) ) ;
#884 = PLANE ( 'NONE',  #671 ) ;
#885 = ORIENTED_EDGE ( 'NONE', *, *, #577, .T. ) ;
#886 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #366 ) ) ;
#887 = VERTEX_POINT ( 'NONE', #179 ) ;
#888 = EDGE_CURVE ( 'NONE', #21, #306, #610, .T. ) ;
#889 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#890 = ORIENTED_EDGE ( 'NONE', *, *, #307, .T. ) ;
#891 = PRESENTATION_STYLE_ASSIGNMENT (( #1098 ) ) ;
#892 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.000000000000000000, -0.2249999999999999500 ) ) ;
#893 = VERTEX_POINT ( 'NONE', #707 ) ;
#894 = VECTOR ( 'NONE', #257, 1000.000000000000000 ) ;
#895 = SURFACE_STYLE_FILL_AREA ( #678 ) ;
#896 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#897 = VERTEX_POINT ( 'NONE', #1232 ) ;
#898 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#899 = SURFACE_SIDE_STYLE ('',( #1328 ) ) ;
#900 = ORIENTED_EDGE ( 'NONE', *, *, #1343, .T. ) ;
#901 = SURFACE_SIDE_STYLE ('',( #78 ) ) ;
#902 = ORIENTED_EDGE ( 'NONE', *, *, #1058, .F. ) ;
#903 = FACE_OUTER_BOUND ( 'NONE', #276, .T. ) ;
#904 = DIRECTION ( 'NONE',  ( 1.084202172485504681E-16, 0.000000000000000000, -1.000000000000000000 ) ) ;
#905 = AXIS2_PLACEMENT_3D ( 'NONE', #652, #88, #254 ) ;
#906 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#907 = VERTEX_POINT ( 'NONE', #800 ) ;
#908 = CYLINDRICAL_SURFACE ( 'NONE', #780, 0.01666666666666664562 ) ;
#909 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #1135 ) ) ;
#910 = ORIENTED_EDGE ( 'NONE', *, *, #958, .T. ) ;
#911 = ORIENTED_EDGE ( 'NONE', *, *, #1077, .T. ) ;
#912 = EDGE_CURVE ( 'NONE', #340, #384, #117, .T. ) ;
#913 = VECTOR ( 'NONE', #335, 1000.000000000000000 ) ;
#914 = PLANE ( 'NONE',  #681 ) ;
#915 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#916 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1320, 'distance_accuracy_value', 'NONE');
#917 = EDGE_LOOP ( 'NONE', ( #464, #920, #499, #1429 ) ) ;
#918 = VECTOR ( 'NONE', #199, 1000.000000000000000 ) ;
#919 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#920 = ORIENTED_EDGE ( 'NONE', *, *, #622, .T. ) ;
#921 = ORIENTED_EDGE ( 'NONE', *, *, #68, .T. ) ;
#922 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #804 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #889, #154, #1325 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#923 = ORIENTED_EDGE ( 'NONE', *, *, #856, .T. ) ;
#924 = PLANE ( 'NONE',  #858 ) ;
#925 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#926 = ORIENTED_EDGE ( 'NONE', *, *, #651, .T. ) ;
#927 = PRESENTATION_STYLE_ASSIGNMENT (( #1388 ) ) ;
#928 = STYLED_ITEM ( 'NONE', ( #1052 ), #705 ) ;
#929 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5250000000000000222, 0.2249999999566318354 ) ) ;
#930 = ORIENTED_EDGE ( 'NONE', *, *, #347, .F. ) ;
#931 = PRESENTATION_STYLE_ASSIGNMENT (( #12 ) ) ;
#932 = STYLED_ITEM ( 'NONE', ( #1421 ), #175 ) ;
#933 = ORIENTED_EDGE ( 'NONE', *, *, #912, .F. ) ;
#934 = LINE ( 'NONE', #56, #913 ) ;
#935 = EDGE_CURVE ( 'NONE', #133, #760, #1056, .T. ) ;
#936 = VERTEX_POINT ( 'NONE', #1147 ) ;
#937 = LINE ( 'NONE', #661, #1369 ) ;
#938 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#939 = VERTEX_POINT ( 'NONE', #712 ) ;
#940 = CIRCLE ( 'NONE', #118, 0.02500000000000001180 ) ;
#941 = FACE_OUTER_BOUND ( 'NONE', #1044, .T. ) ;
#942 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#943 = ORIENTED_EDGE ( 'NONE', *, *, #849, .F. ) ;
#944 = LINE ( 'NONE', #195, #27 ) ;
#945 = SPHERICAL_SURFACE ( 'NONE', #756, 0.02500000000000000139 ) ;
#946 = ADVANCED_FACE ( 'NONE', ( #1062 ), #1085, .T. ) ;
#947 = EDGE_LOOP ( 'NONE', ( #476, #274, #586, #503 ) ) ;
#948 = VECTOR ( 'NONE', #545, 1000.000000000000000 ) ;
#949 = EDGE_CURVE ( 'NONE', #351, #1018, #130, .T. ) ;
#950 = VERTEX_POINT ( 'NONE', #346 ) ;
#951 = DIRECTION ( 'NONE',  ( -8.673617379884035472E-17, -5.421010862427520937E-17, -1.000000000000000000 ) ) ;
#952 = STYLED_ITEM ( 'NONE', ( #1146 ), #239 ) ;
#953 = PLANE ( 'NONE',  #684 ) ;
#954 = VERTEX_POINT ( 'NONE', #1220 ) ;
#955 = VECTOR ( 'NONE', #376, 1000.000000000000000 ) ;
#956 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#957 = ORIENTED_EDGE ( 'NONE', *, *, #1152, .F. ) ;
#958 = EDGE_CURVE ( 'NONE', #907, #100, #587, .T. ) ;
#959 = EDGE_LOOP ( 'NONE', ( #525, #287, #834, #26 ) ) ;
#960 = SPHERICAL_SURFACE ( 'NONE', #905, 0.02500000000000000139 ) ;
#961 = EDGE_CURVE ( 'NONE', #1348, #374, #624, .T. ) ;
#962 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #3, 'distance_accuracy_value', 'NONE');
#963 = ORIENTED_EDGE ( 'NONE', *, *, #1136, .F. ) ;
#964 = FACE_OUTER_BOUND ( 'NONE', #824, .T. ) ;
#965 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.02499999999999995628, -0.2416666666666666130 ) ) ;
#966 = PLANE ( 'NONE',  #1216 ) ;
#967 = CLOSED_SHELL ( 'NONE', ( #314, #705, #175, #109, #76, #1074, #755, #989, #1008, #536 ) ) ;
#968 = ORIENTED_EDGE ( 'NONE', *, *, #530, .T. ) ;
#969 = VECTOR ( 'NONE', #994, 1000.000000000000000 ) ;
#970 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5416666666666667407, -0.2249999999999999500 ) ) ;
#971 = ORIENTED_EDGE ( 'NONE', *, *, #846, .T. ) ;
#972 = VECTOR ( 'NONE', #150, 1000.000000000000000 ) ;
#973 = EDGE_CURVE ( 'NONE', #1018, #985, #628, .T. ) ;
#974 = ORIENTED_EDGE ( 'NONE', *, *, #829, .F. ) ;
#975 = CIRCLE ( 'NONE', #668, 0.02500000000000001180 ) ;
#976 = PLANE ( 'NONE',  #166 ) ;
#977 = STYLED_ITEM ( 'NONE', ( #786 ), #314 ) ;
#978 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#979 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#980 = ORIENTED_EDGE ( 'NONE', *, *, #148, .F. ) ;
#981 = LINE ( 'NONE', #399, #1416 ) ;
#982 = PRODUCT_DEFINITION_SHAPE ( 'NONE', 'NONE',  #588 ) ;
#983 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5250000000000000222, -0.2250000000000000056 ) ) ;
#984 = EDGE_CURVE ( 'NONE', #1259, #340, #631, .T. ) ;
#985 = VERTEX_POINT ( 'NONE', #1218 ) ;
#986 = LINE ( 'NONE', #748, #494 ) ;
#987 = ORIENTED_EDGE ( 'NONE', *, *, #1373, .F. ) ;
#988 = LINE ( 'NONE', #1130, #996 ) ;
#989 = ADVANCED_FACE ( 'NONE', ( #633 ), #863, .T. ) ;
#990 = ORIENTED_EDGE ( 'NONE', *, *, #717, .T. ) ;
#991 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#992 = CYLINDRICAL_SURFACE ( 'NONE', #548, 0.02500000000000001180 ) ;
#993 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#994 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#995 = ORIENTED_EDGE ( 'NONE', *, *, #164, .F. ) ;
#996 = VECTOR ( 'NONE', #1197, 1000.000000000000000 ) ;
#997 = EDGE_LOOP ( 'NONE', ( #885, #316, #1002, #1423 ) ) ;
#998 = PLANE ( 'NONE',  #170 ) ;
#999 = AXIS2_PLACEMENT_3D ( 'NONE', #433, #686, #676 ) ;
#1000 = EDGE_CURVE ( 'NONE', #1293, #351, #1251, .T. ) ;
#1001 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, 0.2416666666666666130 ) ) ;
#1002 = ORIENTED_EDGE ( 'NONE', *, *, #331, .T. ) ;
#1003 = FACE_OUTER_BOUND ( 'NONE', #310, .T. ) ;
#1004 = ORIENTED_EDGE ( 'NONE', *, *, #731, .T. ) ;
#1005 = AXIS2_PLACEMENT_3D ( 'NONE', #1031, #1021, #82 ) ;
#1006 = FILL_AREA_STYLE_COLOUR ( '', #162 ) ;
#1007 = FILL_AREA_STYLE ('',( #1188 ) ) ;
#1008 = ADVANCED_FACE ( 'NONE', ( #397 ), #1424, .F. ) ;
#1009 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1010 = ORIENTED_EDGE ( 'NONE', *, *, #673, .F. ) ;
#1011 = VECTOR ( 'NONE', #679, 1000.000000000000000 ) ;
#1012 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1013 = EDGE_CURVE ( 'NONE', #100, #266, #872, .T. ) ;
#1014 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1015 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1016 = ORIENTED_EDGE ( 'NONE', *, *, #757, .F. ) ;
#1017 = CIRCLE ( 'NONE', #798, 0.02500000000000001180 ) ;
#1018 = VERTEX_POINT ( 'NONE', #970 ) ;
#1019 = VECTOR ( 'NONE', #300, 1000.000000000000000 ) ;
#1020 = VERTEX_POINT ( 'NONE', #860 ) ;
#1021 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1022 = ORIENTED_EDGE ( 'NONE', *, *, #218, .F. ) ;
#1023 = AXIS2_PLACEMENT_3D ( 'NONE', #192, #660, #639 ) ;
#1024 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#1025 = ORIENTED_EDGE ( 'NONE', *, *, #1199, .F. ) ;
#1026 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1027 = AXIS2_PLACEMENT_3D ( 'NONE', #1342, #1053, #632 ) ;
#1028 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.5500000000000000444, 0.000000000000000000 ) ) ;
#1029 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -8.673617379884036705E-17 ) ) ;
#1030 = ORIENTED_EDGE ( 'NONE', *, *, #648, .T. ) ;
#1031 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#1032 = ORIENTED_EDGE ( 'NONE', *, *, #1107, .T. ) ;
#1033 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.02499999999999995628, 0.2499999999999999445 ) ) ;
#1034 = ORIENTED_EDGE ( 'NONE', *, *, #322, .F. ) ;
#1035 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1036 = ORIENTED_EDGE ( 'NONE', *, *, #1000, .F. ) ;
#1037 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#1038 = ADVANCED_FACE ( 'NONE', ( #389 ), #417, .T. ) ;
#1039 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1040 = ORIENTED_EDGE ( 'NONE', *, *, #509, .F. ) ;
#1041 = PRESENTATION_STYLE_ASSIGNMENT (( #1346 ) ) ;
#1042 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#1043 = AXIS2_PLACEMENT_3D ( 'NONE', #104, #739, #184 ) ;
#1044 = EDGE_LOOP ( 'NONE', ( #921, #46, #779 ) ) ;
#1045 = EDGE_LOOP ( 'NONE', ( #328, #1243, #930 ) ) ;
#1046 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1047 = EDGE_CURVE ( 'NONE', #760, #349, #410, .T. ) ;
#1048 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5416666666666667407, -0.2249999999999999500 ) ) ;
#1049 = FACE_OUTER_BOUND ( 'NONE', #1383, .T. ) ;
#1050 = DIRECTION ( 'NONE',  ( -8.673617379884036705E-17, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1051 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#1052 = PRESENTATION_STYLE_ASSIGNMENT (( #1215 ) ) ;
#1053 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1054 = LINE ( 'NONE', #784, #1019 ) ;
#1055 = AXIS2_PLACEMENT_3D ( 'NONE', #183, #642, #1418 ) ;
#1056 = CIRCLE ( 'NONE', #544, 0.02500000000000001180 ) ;
#1057 = FACE_OUTER_BOUND ( 'NONE', #947, .T. ) ;
#1058 = EDGE_CURVE ( 'NONE', #813, #839, #378, .T. ) ;
#1059 = ORIENTED_EDGE ( 'NONE', *, *, #912, .T. ) ;
#1060 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5500000000000000444, 0.2499999999999999445 ) ) ;
#1061 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#1062 = FACE_OUTER_BOUND ( 'NONE', #273, .T. ) ;
#1063 = ORIENTED_EDGE ( 'NONE', *, *, #984, .F. ) ;
#1064 = ADVANCED_FACE ( 'NONE', ( #1368 ), #1376, .T. ) ;
#1065 = VECTOR ( 'NONE', #724, 1000.000000000000000 ) ;
#1066 = CYLINDRICAL_SURFACE ( 'NONE', #44, 0.02500000000000001180 ) ;
#1067 = LINE ( 'NONE', #842, #1065 ) ;
#1068 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1069 = CYLINDRICAL_SURFACE ( 'NONE', #1023, 0.02500000000000001180 ) ;
#1070 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #847 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1164, #1409, #1185 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#1071 = EDGE_LOOP ( 'NONE', ( #1399, #39, #427 ) ) ;
#1072 = LINE ( 'NONE', #1150, #1083 ) ;
#1073 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #157 ), #862 ) ;
#1074 = ADVANCED_FACE ( 'NONE', ( #903 ), #385, .T. ) ;
#1075 = VERTEX_POINT ( 'NONE', #132 ) ;
#1076 = VECTOR ( 'NONE', #318, 1000.000000000000000 ) ;
#1077 = EDGE_CURVE ( 'NONE', #559, #1075, #405, .T. ) ;
#1078 = CIRCLE ( 'NONE', #598, 0.01666666666666667337 ) ;
#1079 = CIRCLE ( 'NONE', #481, 0.02500000000000001180 ) ;
#1080 = VECTOR ( 'NONE', #497, 1000.000000000000000 ) ;
#1081 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#1082 = FACE_OUTER_BOUND ( 'NONE', #64, .T. ) ;
#1083 = VECTOR ( 'NONE', #1171, 1000.000000000000000 ) ;
#1084 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1085 = CYLINDRICAL_SURFACE ( 'NONE', #1203, 0.02500000000000001180 ) ;
#1086 = EDGE_LOOP ( 'NONE', ( #1022, #451, #110 ) ) ;
#1087 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#1088 = LINE ( 'NONE', #787, #585 ) ;
#1089 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.02499999999999995975, -0.2416666667100346999 ) ) ;
#1090 = AXIS2_PLACEMENT_3D ( 'NONE', #216, #305, #1122 ) ;
#1091 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#1092 = VERTEX_POINT ( 'NONE', #1001 ) ;
#1093 = CIRCLE ( 'NONE', #298, 0.02500000000000001180 ) ;
#1094 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1095 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #977 ), #825 ) ;
#1096 = VECTOR ( 'NONE', #1081, 1000.000000000000000 ) ;
#1097 = FACE_OUTER_BOUND ( 'NONE', #103, .T. ) ;
#1098 = SURFACE_STYLE_USAGE ( .BOTH. , #730 ) ;
#1099 = VECTOR ( 'NONE', #759, 1000.000000000000000 ) ;
#1100 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.000000000000000000, 0.2249999999999999500 ) ) ;
#1101 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5250000000000000222, 0.2416666666232984984 ) ) ;
#1102 = SURFACE_STYLE_FILL_AREA ( #595 ) ;
#1103 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1104 = LINE ( 'NONE', #73, #1076 ) ;
#1105 = FACE_OUTER_BOUND ( 'NONE', #959, .T. ) ;
#1106 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1107 = EDGE_CURVE ( 'NONE', #1075, #1298, #1374, .T. ) ;
#1108 = STYLED_ITEM ( 'NONE', ( #891 ), #1074 ) ;
#1109 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.000000000000000000, 0.2249999999999999500 ) ) ;
#1110 = AXIS2_PLACEMENT_3D ( 'NONE', #822, #1345, #693 ) ;
#1111 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1112 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#1113 = LINE ( 'NONE', #28, #1116 ) ;
#1114 = STYLED_ITEM ( 'NONE', ( #609 ), #76 ) ;
#1115 = DIRECTION ( 'NONE',  ( -1.084202172485504681E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1116 = VECTOR ( 'NONE', #424, 1000.000000000000000 ) ;
#1117 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#1118 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#1119 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#1120 = AXIS2_PLACEMENT_3D ( 'NONE', #929, #1245, #1094 ) ;
#1121 = STYLED_ITEM ( 'NONE', ( #931 ), #109 ) ;
#1122 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1123 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#1124 = EDGE_CURVE ( 'NONE', #1367, #1372, #428, .T. ) ;
#1125 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1126 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1127 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#1128 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.008333333333333333218, 0.2249999999999999500 ) ) ;
#1129 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#1130 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#1131 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#1132 = AXIS2_PLACEMENT_3D ( 'NONE', #205, #1015, #1106 ) ;
#1133 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#1134 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#1135 = STYLED_ITEM ( 'NONE', ( #368 ), #536 ) ;
#1136 = EDGE_CURVE ( 'NONE', #939, #1259, #1384, .T. ) ;
#1137 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#1138 = SURFACE_SIDE_STYLE ('',( #513 ) ) ;
#1139 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #1244 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #55, #484, #630 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#1140 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1141 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #157 ) ) ;
#1142 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#1143 = SURFACE_STYLE_USAGE ( .BOTH. , #596 ) ;
#1144 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995628, -0.2249999999999999500 ) ) ;
#1145 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1146 = PRESENTATION_STYLE_ASSIGNMENT (( #1280 ) ) ;
#1147 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#1148 = AXIS2_PLACEMENT_3D ( 'NONE', #159, #341, #478 ) ;
#1149 = EDGE_CURVE ( 'NONE', #8, #762, #436, .T. ) ;
#1150 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, 0.2416666666666666130 ) ) ;
#1151 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1152 = EDGE_CURVE ( 'NONE', #351, #1234, #17, .T. ) ;
#1153 = ADVANCED_FACE ( 'NONE', ( #1003 ), #960, .T. ) ;
#1154 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5416666666666667407, -0.2250000000433680647 ) ) ;
#1155 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#1156 = AXIS2_PLACEMENT_3D ( 'NONE', #1091, #1271, #557 ) ;
#1157 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, 0.000000000000000000 ) ) ;
#1158 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #1333 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1125, #859, #32 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#1159 = SHAPE_DEFINITION_REPRESENTATION ( #982, #239 ) ;
#1160 = EDGE_CURVE ( 'NONE', #1293, #408, #30, .T. ) ;
#1161 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#1162 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1163 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1164 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1165 = AXIS2_PLACEMENT_3D ( 'NONE', #264, #1249, #665 ) ;
#1166 = AXIS2_PLACEMENT_3D ( 'NONE', #1186, #1127, #629 ) ;
#1167 = ADVANCED_FACE ( 'NONE', ( #964 ), #589, .F. ) ;
#1168 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#1169 = EDGE_CURVE ( 'NONE', #1298, #8, #975, .T. ) ;
#1170 = ADVANCED_FACE ( 'NONE', ( #5 ), #1242, .F. ) ;
#1171 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 5.421010862427520937E-17 ) ) ;
#1172 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1173 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5500000000000000444, -0.2249999999999999500 ) ) ;
#1174 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#1175 = SURFACE_SIDE_STYLE ('',( #696 ) ) ;
#1176 = ADVANCED_FACE ( 'NONE', ( #1426 ), #477, .T. ) ;
#1177 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#1178 = STYLED_ITEM ( 'NONE', ( #167 ), #1390 ) ;
#1179 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.000000000000000000, -0.2499999999999999445 ) ) ;
#1180 = PLANE ( 'NONE',  #101 ) ;
#1181 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1182 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#1183 = EDGE_CURVE ( 'NONE', #606, #374, #940, .T. ) ;
#1184 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5250000000000000222, -0.2250000000433680647 ) ) ;
#1185 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#1186 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#1187 = EDGE_CURVE ( 'NONE', #606, #74, #489, .T. ) ;
#1188 = FILL_AREA_STYLE_COLOUR ( '', #602 ) ;
#1189 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#1190 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #749, 'distance_accuracy_value', 'NONE');
#1191 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#1192 = EDGE_CURVE ( 'NONE', #1234, #1340, #453, .T. ) ;
#1193 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -0.2249999999999999500 ) ) ;
#1194 = AXIS2_PLACEMENT_3D ( 'NONE', #213, #295, #1012 ) ;
#1195 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1196 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1197 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1198 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#1199 = EDGE_CURVE ( 'NONE', #817, #950, #459, .T. ) ;
#1200 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1201 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#1202 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#1203 = AXIS2_PLACEMENT_3D ( 'NONE', #1157, #334, #942 ) ;
#1204 = EDGE_CURVE ( 'NONE', #105, #483, #944, .T. ) ;
#1205 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1206 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #865 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1172, #1111, #568 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#1207 = FILL_AREA_STYLE_COLOUR ( '', #147 ) ;
#1208 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1209 = EDGE_CURVE ( 'NONE', #1020, #374, #506, .T. ) ;
#1210 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#1211 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#1212 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1213 = FILL_AREA_STYLE ('',( #1207 ) ) ;
#1214 = AXIS2_PLACEMENT_3D ( 'NONE', #647, #1117, #714 ) ;
#1215 = SURFACE_STYLE_USAGE ( .BOTH. , #901 ) ;
#1216 = AXIS2_PLACEMENT_3D ( 'NONE', #1239, #92, #1366 ) ;
#1217 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1218 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.5416666666666667407, 0.2249999999999999500 ) ) ;
#1219 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.5500000000000000444, 0.2249999999999999500 ) ) ;
#1220 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000000, 0.5250000000000000222, 0.2499999999999999445 ) ) ;
#1221 = PRODUCT_RELATED_PRODUCT_CATEGORY ( 'part', '', ( #141 ) ) ;
#1222 = EDGE_CURVE ( 'NONE', #1075, #760, #981, .T. ) ;
#1223 = AXIS2_PLACEMENT_3D ( 'NONE', #1308, #682, #567 ) ;
#1224 = ORIENTED_EDGE ( 'NONE', *, *, #7, .F. ) ;
#1225 = ORIENTED_EDGE ( 'NONE', *, *, #1124, .F. ) ;
#1226 = EDGE_CURVE ( 'NONE', #1234, #985, #22, .T. ) ;
#1227 = FILL_AREA_STYLE_COLOUR ( '', #131 ) ;
#1228 = EDGE_LOOP ( 'NONE', ( #326, #512, #19, #89 ) ) ;
#1229 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.02499999999999995628, 0.2416666666232985261 ) ) ;
#1230 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5250000000000000222, 0.2249999999999998945 ) ) ;
#1231 = ORIENTED_EDGE ( 'NONE', *, *, #245, .T. ) ;
#1232 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.000000000000000000, 0.2249999999999999500 ) ) ;
#1233 = ORIENTED_EDGE ( 'NONE', *, *, #509, .T. ) ;
#1234 = VERTEX_POINT ( 'NONE', #726 ) ;
#1235 = ORIENTED_EDGE ( 'NONE', *, *, #504, .F. ) ;
#1236 = ORIENTED_EDGE ( 'NONE', *, *, #1187, .T. ) ;
#1237 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#1238 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02500000000000000139, 0.2249999999999999500 ) ) ;
#1239 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#1240 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #260, 'distance_accuracy_value', 'NONE');
#1241 = EDGE_LOOP ( 'NONE', ( #515, #271, #1425, #876 ) ) ;
#1242 = PLANE ( 'NONE',  #1156 ) ;
#1243 = ORIENTED_EDGE ( 'NONE', *, *, #692, .T. ) ;
#1244 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #55, 'distance_accuracy_value', 'NONE');
#1245 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#1246 = ORIENTED_EDGE ( 'NONE', *, *, #731, .F. ) ;
#1247 = AXIS2_PLACEMENT_3D ( 'NONE', #777, #320, #348 ) ;
#1248 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#1249 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#1250 = ORIENTED_EDGE ( 'NONE', *, *, #369, .T. ) ;
#1251 = CIRCLE ( 'NONE', #23, 0.01666666666666667337 ) ;
#1252 = EDGE_CURVE ( 'NONE', #1259, #479, #1420, .T. ) ;
#1253 = ORIENTED_EDGE ( 'NONE', *, *, #307, .F. ) ;
#1254 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#1255 = CIRCLE ( 'NONE', #702, 0.01666666666666662133 ) ;
#1256 = EDGE_LOOP ( 'NONE', ( #1358, #471, #926, #359 ) ) ;
#1257 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#1258 = VERTEX_POINT ( 'NONE', #1173 ) ;
#1259 = VERTEX_POINT ( 'NONE', #95 ) ;
#1260 = ORIENTED_EDGE ( 'NONE', *, *, #713, .F. ) ;
#1261 = FACE_OUTER_BOUND ( 'NONE', #518, .T. ) ;
#1262 = CARTESIAN_POINT ( 'NONE',  ( 0.2500000000000000555, 0.000000000000000000, -0.2499999999999999445 ) ) ;
#1263 = EDGE_LOOP ( 'NONE', ( #1282, #87, #57, #490 ) ) ;
#1264 = LINE ( 'NONE', #1042, #311 ) ;
#1265 = EDGE_LOOP ( 'NONE', ( #1036, #828, #496, #284 ) ) ;
#1266 = ORIENTED_EDGE ( 'NONE', *, *, #233, .T. ) ;
#1267 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#1268 = SPHERICAL_SURFACE ( 'NONE', #1339, 0.02500000000000000139 ) ;
#1269 = AXIS2_PLACEMENT_3D ( 'NONE', #106, #120, #838 ) ;
#1270 = EDGE_LOOP ( 'NONE', ( #1260, #815, #1344, #833 ) ) ;
#1271 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1272 = ORIENTED_EDGE ( 'NONE', *, *, #398, .F. ) ;
#1273 = CIRCLE ( 'NONE', #841, 0.02500000000000001180 ) ;
#1274 = ORIENTED_EDGE ( 'NONE', *, *, #1136, .T. ) ;
#1275 = AXIS2_PLACEMENT_3D ( 'NONE', #1338, #472, #797 ) ;
#1276 = LINE ( 'NONE', #151, #799 ) ;
#1277 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000555, 0.5250000000000000222, -0.2499999999999999445 ) ) ;
#1278 = ORIENTED_EDGE ( 'NONE', *, *, #1000, .T. ) ;
#1279 = EDGE_CURVE ( 'NONE', #887, #133, #14, .T. ) ;
#1280 = SURFACE_STYLE_USAGE ( .BOTH. , #1175 ) ;
#1281 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#1282 = ORIENTED_EDGE ( 'NONE', *, *, #10, .F. ) ;
#1283 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#1284 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1285 = ORIENTED_EDGE ( 'NONE', *, *, #1192, .T. ) ;
#1286 = LINE ( 'NONE', #400, #768 ) ;
#1287 = ORIENTED_EDGE ( 'NONE', *, *, #504, .T. ) ;
#1288 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.5250000000000000222, 0.2499999999999999445 ) ) ;
#1289 = CIRCLE ( 'NONE', #1194, 0.02500000000000001180 ) ;
#1290 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1291 = ORIENTED_EDGE ( 'NONE', *, *, #230, .F. ) ;
#1292 = CIRCLE ( 'NONE', #244, 0.02500000000000001180 ) ;
#1293 = VERTEX_POINT ( 'NONE', #569 ) ;
#1294 = AXIS2_PLACEMENT_3D ( 'NONE', #869, #252, #207 ) ;
#1295 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1296 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1297 = FACE_OUTER_BOUND ( 'NONE', #1228, .T. ) ;
#1298 = VERTEX_POINT ( 'NONE', #650 ) ;
#1299 = CIRCLE ( 'NONE', #465, 0.02500000000000001180 ) ;
#1300 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #366 ), #719 ) ;
#1301 = ORIENTED_EDGE ( 'NONE', *, *, #820, .F. ) ;
#1302 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#1303 = CIRCLE ( 'NONE', #253, 0.02500000000000001180 ) ;
#1304 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#1305 = ORIENTED_EDGE ( 'NONE', *, *, #935, .F. ) ;
#1306 = ORIENTED_EDGE ( 'NONE', *, *, #727, .F. ) ;
#1307 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#1308 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1309 = FACE_OUTER_BOUND ( 'NONE', #810, .T. ) ;
#1310 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1311 = CARTESIAN_POINT ( 'NONE',  ( 0.5000000000000000000, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#1312 = ORIENTED_EDGE ( 'NONE', *, *, #873, .F. ) ;
#1313 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999940882844, 0.5416666666666667407, -0.2250000000000000056 ) ) ;
#1314 = ORIENTED_EDGE ( 'NONE', *, *, #1316, .F. ) ;
#1315 = DIRECTION ( 'NONE',  ( -5.877471754111437540E-33, -1.000000000000000000, 5.421010862427520937E-17 ) ) ;
#1316 = EDGE_CURVE ( 'NONE', #1340, #1092, #480, .T. ) ;
#1317 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -8.673617379884036705E-17 ) ) ;
#1318 = VECTOR ( 'NONE', #125, 1000.000000000000000 ) ;
#1319 = AXIS2_PLACEMENT_3D ( 'NONE', #206, #198, #414 ) ;
#1320 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1321 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.008333333333333333218, -0.2249999999999999500 ) ) ;
#1322 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #1114 ) ) ;
#1323 = FACE_OUTER_BOUND ( 'NONE', #116, .T. ) ;
#1324 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#1325 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#1326 = LINE ( 'NONE', #272, #402 ) ;
#1327 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#1328 = SURFACE_STYLE_FILL_AREA ( #209 ) ;
#1329 = CYLINDRICAL_SURFACE ( 'NONE', #618, 0.01666666666666664562 ) ;
#1330 = AXIS2_PLACEMENT_3D ( 'NONE', #979, #1009, #662 ) ;
#1331 = EDGE_CURVE ( 'NONE', #74, #105, #986, .T. ) ;
#1332 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#1333 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1125, 'distance_accuracy_value', 'NONE');
#1334 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -8.673617379884036705E-17 ) ) ;
#1335 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1336 = ORIENTED_EDGE ( 'NONE', *, *, #322, .T. ) ;
#1337 = VECTOR ( 'NONE', #138, 1000.000000000000000 ) ;
#1338 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.5500000000000000444, 0.000000000000000000 ) ) ;
#1339 = AXIS2_PLACEMENT_3D ( 'NONE', #1155, #508, #546 ) ;
#1340 = VERTEX_POINT ( 'NONE', #883 ) ;
#1341 = CIRCLE ( 'NONE', #1406, 0.02500000000000001180 ) ;
#1342 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1343 = EDGE_CURVE ( 'NONE', #893, #1298, #988, .T. ) ;
#1344 = ORIENTED_EDGE ( 'NONE', *, *, #1058, .T. ) ;
#1345 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#1346 = SURFACE_STYLE_USAGE ( .BOTH. , #1138 ) ;
#1347 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1348 = VERTEX_POINT ( 'NONE', #1311 ) ;
#1349 = ORIENTED_EDGE ( 'NONE', *, *, #467, .F. ) ;
#1350 = FACE_OUTER_BOUND ( 'NONE', #420, .T. ) ;
#1351 = ORIENTED_EDGE ( 'NONE', *, *, #651, .F. ) ;
#1352 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #413 ) ) ;
#1353 = ORIENTED_EDGE ( 'NONE', *, *, #1192, .F. ) ;
#1354 = LINE ( 'NONE', #1288, #386 ) ;
#1355 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5250000000000000222, 0.2249999999999999500 ) ) ;
#1356 = AXIS2_PLACEMENT_3D ( 'NONE', #1382, #640, #520 ) ;
#1357 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1358 = ORIENTED_EDGE ( 'NONE', *, *, #659, .F. ) ;
#1359 = CIRCLE ( 'NONE', #220, 0.02500000000000001180 ) ;
#1360 = ORIENTED_EDGE ( 'NONE', *, *, #745, .F. ) ;
#1361 = SURFACE_STYLE_FILL_AREA ( #524 ) ;
#1362 = EDGE_CURVE ( 'NONE', #266, #8, #1113, .T. ) ;
#1363 = ORIENTED_EDGE ( 'NONE', *, *, #717, .F. ) ;
#1364 = ORIENTED_EDGE ( 'NONE', *, *, #856, .F. ) ;
#1365 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 8.673617379884035472E-17 ) ) ;
#1366 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1367 = VERTEX_POINT ( 'NONE', #291 ) ;
#1368 = FACE_OUTER_BOUND ( 'NONE', #469, .T. ) ;
#1369 = VECTOR ( 'NONE', #993, 1000.000000000000000 ) ;
#1370 = CARTESIAN_POINT ( 'NONE',  ( 500000.0000000000582, 0.5416666666666667407, 0.2249999999566318354 ) ) ;
#1371 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1372 = VERTEX_POINT ( 'NONE', #867 ) ;
#1373 = EDGE_CURVE ( 'NONE', #1367, #488, #643, .T. ) ;
#1374 = CIRCLE ( 'NONE', #709, 0.02500000000000001180 ) ;
#1375 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#1376 = CYLINDRICAL_SURFACE ( 'NONE', #522, 0.02500000000000001180 ) ;
#1377 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #1178 ), #774 ) ;
#1378 = CARTESIAN_POINT ( 'NONE',  ( -0.4749999999999999778, 0.5250000000000000222, -0.2249999999999999500 ) ) ;
#1379 = EDGE_LOOP ( 'NONE', ( #878, #124, #871, #1403 ) ) ;
#1380 = FACE_OUTER_BOUND ( 'NONE', #537, .T. ) ;
#1381 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #1121 ) ) ;
#1382 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.02499999999999995628, 0.2249999999999999500 ) ) ;
#1383 = EDGE_LOOP ( 'NONE', ( #1063, #594, #456, #933 ) ) ;
#1384 = CIRCLE ( 'NONE', #1214, 0.01666666666666667337 ) ;
#1385 = ADVANCED_FACE ( 'NONE', ( #1105 ), #11, .F. ) ;
#1386 = FACE_OUTER_BOUND ( 'NONE', #1071, .T. ) ;
#1387 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 1.084202172485504681E-16 ) ) ;
#1388 = SURFACE_STYLE_USAGE ( .BOTH. , #722 ) ;
#1389 = ORIENTED_EDGE ( 'NONE', *, *, #949, .T. ) ;
#1390 = MANIFOLD_SOLID_BREP ( 'Fillet1', #395 ) ;
#1391 = VECTOR ( 'NONE', #672, 1000.000000000000000 ) ;
#1392 = CIRCLE ( 'NONE', #1247, 0.01666666666666667337 ) ;
#1393 = ORIENTED_EDGE ( 'NONE', *, *, #347, .T. ) ;
#1394 = VECTOR ( 'NONE', #407, 1000.000000000000000 ) ;
#1395 = CARTESIAN_POINT ( 'NONE',  ( -0.5000000000000000000, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#1396 = LINE ( 'NONE', #978, #918 ) ;
#1397 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.02499999999999995628, -0.2499999999999999445 ) ) ;
#1398 = FILL_AREA_STYLE_COLOUR ( '', #1375 ) ;
#1399 = ORIENTED_EDGE ( 'NONE', *, *, #176, .T. ) ;
#1400 = VECTOR ( 'NONE', #538, 1000.000000000000000 ) ;
#1401 = LINE ( 'NONE', #667, #421 ) ;
#1402 = CARTESIAN_POINT ( 'NONE',  ( 0.4749999999999999778, 0.5500000000000000444, -0.2499999999999999445 ) ) ;
#1403 = ORIENTED_EDGE ( 'NONE', *, *, #283, .F. ) ;
#1404 = VECTOR ( 'NONE', #83, 1000.000000000000000 ) ;
#1405 = ADVANCED_FACE ( 'NONE', ( #653 ), #645, .T. ) ;
#1406 = AXIS2_PLACEMENT_3D ( 'NONE', #767, #866, #1295 ) ;
#1407 = ORIENTED_EDGE ( 'NONE', *, *, #42, .F. ) ;
#1408 = SPHERICAL_SURFACE ( 'NONE', #65, 0.02500000000000000139 ) ;
#1409 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1410 = ORIENTED_EDGE ( 'NONE', *, *, #846, .F. ) ;
#1411 = AXIS2_PLACEMENT_3D ( 'NONE', #127, #1428, #217 ) ;
#1412 = COLOUR_RGB ( '',0.4862745098039215619, 0.2352941176470588203, 0.01568627450980392135 ) ;
#1413 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1414 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1415 = ORIENTED_EDGE ( 'NONE', *, *, #984, .T. ) ;
#1416 = VECTOR ( 'NONE', #372, 1000.000000000000000 ) ;
#1417 = SURFACE_STYLE_FILL_AREA ( #454 ) ;
#1418 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1419 = ORIENTED_EDGE ( 'NONE', *, *, #659, .T. ) ;
#1420 = LINE ( 'NONE', #845, #969 ) ;
#1421 = PRESENTATION_STYLE_ASSIGNMENT (( #1143 ) ) ;
#1422 = CARTESIAN_POINT ( 'NONE',  ( -0.2500000000000000000, 0.008333333333333296788, 0.2249999999999999500 ) ) ;
#1423 = ORIENTED_EDGE ( 'NONE', *, *, #1222, .F. ) ;
#1424 = PLANE ( 'NONE',  #1165 ) ;
#1425 = ORIENTED_EDGE ( 'NONE', *, *, #233, .F. ) ;
#1426 = FACE_OUTER_BOUND ( 'NONE', #419, .T. ) ;
#1427 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1428 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#1429 = ORIENTED_EDGE ( 'NONE', *, *, #1373, .T. ) ;
#1430 = AXIS2_PLACEMENT_3D ( 'NONE', #367, #514, #185 ) ;
ENDSEC;
END-ISO-10303-21;
