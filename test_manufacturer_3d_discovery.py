#!/usr/bin/env python3
"""
Test Manufacturer 3D Model Discovery
Quick test script to validate manufacturer website 3D model availability
"""

import os
import sys
import time
from manufacturer_3d_tester import test_manufacturer_3d_availability

def run_quick_test():
    """Run a quick test with a known good part"""
    print("🚀 QUICK MANUFACTURER 3D DISCOVERY TEST")
    print("=" * 50)
    
    # Test with a well-known part that should have 3D models
    manufacturer = "Texas Instruments"
    part_number = "LM358N"
    
    print(f"🎯 Testing: {manufacturer} / {part_number}")
    print("   This part is known to have 3D models available")
    print("   Testing manufacturer website discovery...")
    print()
    
    # Run the test
    results = test_manufacturer_3d_availability(manufacturer, part_number)
    
    # Quick analysis
    summary = results['summary']
    
    print("\n" + "="*50)
    print("🎯 QUICK ANALYSIS")
    print("="*50)
    
    if summary['websites_with_3d_content'] > 0:
        print("✅ Manufacturer website has 3D content sections")
    else:
        print("⚠️ No obvious 3D content found on main website")
    
    if summary['urls_with_potential_models'] > 0:
        print(f"✅ Found {summary['urls_with_potential_models']} URLs with potential 3D models")
        print(f"   Total potential links: {summary['total_potential_3d_links']}")
    else:
        print("❌ No URLs with potential 3D models found")
    
    # Show comparison with existing system
    print("\n🔄 COMPARISON WITH EXISTING SYSTEM:")
    print("   Your existing system has these 3D finders:")
    print("   • SnapEDA (working)")
    print("   • UltraLibrarian (working)")  
    print("   • SamacSys (working)")
    print("   • Manufacturer direct (this test)")
    
    # Check if we have existing downloads
    if os.path.exists('3d'):
        existing_files = [f for f in os.listdir('3d') if f.endswith('.step')]
        ti_files = [f for f in existing_files if 'texas' in f.lower() and 'lm358' in f.lower()]
        
        if ti_files:
            print(f"\n📁 EXISTING DOWNLOADS FOR THIS PART:")
            for f in ti_files:
                print(f"   ✅ {f}")
        else:
            print(f"\n📁 No existing downloads found for this part")
    
    return results

def run_comprehensive_test():
    """Run comprehensive test with multiple manufacturers"""
    print("🚀 COMPREHENSIVE MANUFACTURER 3D DISCOVERY TEST")
    print("=" * 60)
    
    # Test cases - mix of known good and challenging manufacturers
    test_cases = [
        ("Texas Instruments", "LM358N", "Known good - should have 3D models"),
        ("Analog Devices", "AD8606ARZ", "Major manufacturer - likely has models"),
        ("Murata", "GCM155R71H104KE02D", "Component manufacturer - may have CAD"),
        ("STMicroelectronics", "STM32F103C8T6", "MCU manufacturer - should have models"),
        ("Vishay", "1N4148", "Discrete component - may be challenging")
    ]
    
    all_results = []
    
    for i, (manufacturer, part_number, description) in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ TEST {i}/5: {manufacturer} / {part_number}")
        print(f"   {description}")
        print("-" * 50)
        
        try:
            results = test_manufacturer_3d_availability(manufacturer, part_number)
            all_results.append(results)
            
            # Brief summary
            summary = results['summary']
            if summary['urls_with_potential_models'] > 0:
                print(f"   ✅ Found {summary['urls_with_potential_models']} promising URLs")
            else:
                print(f"   ❌ No promising URLs found")
                
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            
        if i < len(test_cases):
            print("   ⏳ Waiting 3 seconds before next test...")
            time.sleep(3)
    
    # Overall summary
    print("\n" + "="*60)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("="*60)
    
    successful_tests = len(all_results)
    total_promising_urls = sum(r['summary']['urls_with_potential_models'] for r in all_results)
    total_3d_links = sum(r['summary']['total_potential_3d_links'] for r in all_results)
    
    print(f"✅ Successful tests: {successful_tests}/{len(test_cases)}")
    print(f"🔗 Total promising URLs found: {total_promising_urls}")
    print(f"📦 Total potential 3D links: {total_3d_links}")
    
    # Best performers
    best_results = sorted(all_results, 
                         key=lambda x: x['summary']['urls_with_potential_models'], 
                         reverse=True)
    
    print(f"\n🏆 BEST PERFORMERS:")
    for i, result in enumerate(best_results[:3], 1):
        urls_count = result['summary']['urls_with_potential_models']
        if urls_count > 0:
            print(f"{i}. {result['manufacturer']} - {urls_count} promising URLs")
    
    return all_results

def main():
    """Main function with menu"""
    print("🎯 MANUFACTURER 3D MODEL DISCOVERY TESTER")
    print("=" * 50)
    print("Choose test type:")
    print("1. Quick test (single part - fast)")
    print("2. Comprehensive test (5 parts - slower)")
    print("3. Custom test (enter your own part)")
    print("4. Exit")
    
    while True:
        try:
            choice = input("\nEnter choice (1-4): ").strip()
            
            if choice == '1':
                run_quick_test()
                break
            elif choice == '2':
                run_comprehensive_test()
                break
            elif choice == '3':
                manufacturer = input("Enter manufacturer name: ").strip()
                part_number = input("Enter part number: ").strip()
                
                if manufacturer and part_number:
                    print(f"\n🎯 Testing: {manufacturer} / {part_number}")
                    test_manufacturer_3d_availability(manufacturer, part_number)
                else:
                    print("❌ Please enter both manufacturer and part number")
                    continue
                break
            elif choice == '4':
                print("👋 Goodbye!")
                sys.exit(0)
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
