#!/usr/bin/env python3
"""
Direct SnapEDA test with GUI - Fixed version
"""

import sys
import os

def main():
    print("🚀 Starting direct SnapEDA test...")
    
    try:
        # Import GUI
        from working_screen_gui import get_working_gui
        print("✅ GUI imported")
        
        # Create GUI
        gui = get_working_gui()
        print("✅ GUI created")
        
        # Import SnapEDA finder
        from snapeda_3d_finder import find_3d_model
        print("✅ SnapEDA finder imported")
        
        # Show start screen
        gui.update_screen("SnapEDA", 0, "Starting Test", "about:blank",
                         "STARTING SNAPEDA TEST:\n\n🎯 TESTING:\n- SnapEDA 3D model finder\n- GUI screen tracking\n- Smart waits\n\n👀 WHAT HAPPENS:\n- Chrome browser opens\n- Each step shown in GUI\n- Click CONTINUE for each step\n\nClick CONTINUE to start...")
        print("✅ Start screen shown")
        gui.wait_for_continue()
        print("✅ Continue clicked - starting SnapEDA...")
        
        # Run SnapEDA finder
        result = find_3d_model("Texas Instruments", "LM358N", silent=False)
        
        if result:
            print(f"✅ SUCCESS: {result}")
        else:
            print("❌ FAILED")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
