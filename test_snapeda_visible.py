#!/usr/bin/env python3
"""
Test SnapEDA with VISIBLE browser so you can see what's happening
"""

import sys
import os
import time

# Add the save directory to path to import the finder
sys.path.append('save')

def test_snapeda_visible():
    print("🎯 TESTING SNAPEDA WITH VISIBLE BROWSER")
    print("=" * 60)
    
    manufacturer = "Texas Instruments"
    part_number = "LM358N"
    
    print(f"🔍 Testing part: {manufacturer} {part_number}")
    print(f"   Running with VISIBLE browser so you can see the screen")
    print(f"   <PERSON><PERSON><PERSON> will stay open for inspection")
    
    try:
        from snapeda_3d_finder_final import find_3d_model as snapeda_find
        
        print(f"\n🚀 Starting SnapEDA test with visible browser...")
        print(f"   The browser window will open and you can watch the process")
        
        start_time = time.time()
        # Run with silent=False to show the browser
        result = snapeda_find(manufacturer, part_number, silent=False)
        end_time = time.time()
        
        print(f"\n📊 RESULT:")
        if result:
            print(f"✅ SUCCESS: {result} ({end_time-start_time:.1f}s)")
        else:
            print(f"❌ FAILED ({end_time-start_time:.1f}s)")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Check downloaded files
    if os.path.exists('3d'):
        files = os.listdir('3d')
        step_files = [f for f in files if f.endswith('.step')]
        if step_files:
            print(f"\n📁 DOWNLOADED STEP FILES:")
            for f in step_files:
                print(f"   ✅ {f}")
        else:
            print(f"\n📁 No new STEP files found")
    else:
        print(f"\n📁 3d directory not found")
    
    print(f"\n🔍 Test complete!")

if __name__ == "__main__":
    test_snapeda_visible()
