@echo off
echo ========================================
echo COMPONENT FINDER BACKUP SYSTEM
echo ========================================

REM Read current version number
set /p VERSION=<version.txt

echo Current version: %VERSION%
echo Creating backup files with _rev%VERSION% suffix in save directory

echo.
echo Backing up essential files...

REM Core program files
if exist "component_finder.py" copy "component_finder.py" "save\component_finder_rev%VERSION%.py" >nul
if exist "datasheet_finder.py" copy "datasheet_finder.py" "save\datasheet_finder_rev%VERSION%.py" >nul
if exist "digikey_datasheet_improved.py" copy "digikey_datasheet_improved.py" "save\digikey_datasheet_improved_rev%VERSION%.py" >nul
if exist "mouser_datasheet_improved.py" copy "mouser_datasheet_improved.py" "save\mouser_datasheet_improved_rev%VERSION%.py" >nul
if exist "pdf_parser.py" copy "pdf_parser.py" "save\pdf_parser_rev%VERSION%.py" >nul
if exist "rs_components_scraper.py" copy "rs_components_scraper.py" "save\rs_components_scraper_rev%VERSION%.py" >nul
if exist "step_finder.py" copy "step_finder.py" "save\step_finder_rev%VERSION%.py" >nul
if exist "samacsys_credentials.py" copy "samacsys_credentials.py" "save\samacsys_credentials_rev%VERSION%.py" >nul

REM 3D Finder modules (now in root directory)
if exist "external_3d_finder.py" copy "external_3d_finder.py" "save\external_3d_finder_rev%VERSION%.py" >nul
if exist "ultralibrarian_3d_finder.py" copy "ultralibrarian_3d_finder.py" "save\ultralibrarian_3d_finder_rev%VERSION%.py" >nul
if exist "samacsys_3d_finder.py" copy "samacsys_3d_finder.py" "save\samacsys_3d_finder_rev%VERSION%.py" >nul
if exist "snapeda_3d_finder.py" copy "snapeda_3d_finder.py" "save\snapeda_3d_finder_rev%VERSION%.py" >nul
if exist "snapeda_3d_finder_working.py" copy "snapeda_3d_finder_working.py" "save\snapeda_3d_finder_working_rev%VERSION%.py" >nul

REM API and utility modules
if exist "manufacturer_api_cache.py" copy "manufacturer_api_cache.py" "save\manufacturer_api_cache_rev%VERSION%.py" >nul
if exist "manufacturer_api_search.py" copy "manufacturer_api_search.py" "save\manufacturer_api_search_rev%VERSION%.py" >nul
if exist "smart_wait_utils.py" copy "smart_wait_utils.py" "save\smart_wait_utils_rev%VERSION%.py" >nul
if exist "auto_fix_component_finder.py" copy "auto_fix_component_finder.py" "save\auto_fix_component_finder_rev%VERSION%.py" >nul

REM Configuration files
if exist "component_site_credentials.json" copy "component_site_credentials.json" "save\component_site_credentials_rev%VERSION%.json" >nul
if exist "digikey_api_credentials.json" copy "digikey_api_credentials.json" "save\digikey_api_credentials_rev%VERSION%.json" >nul
if exist "samacsys_credentials.json" copy "samacsys_credentials.json" "save\samacsys_credentials_rev%VERSION%.json" >nul
if exist "manufacturer_knowledge.json" copy "manufacturer_knowledge.json" "save\manufacturer_knowledge_rev%VERSION%.json" >nul
if exist "manufacturer_websites.json" copy "manufacturer_websites.json" "save\manufacturer_websites_rev%VERSION%.json" >nul
if exist "manufacturer_api_credentials.json" copy "manufacturer_api_credentials.json" "save\manufacturer_api_credentials_rev%VERSION%.json" >nul
if exist "mouser_api_credentials.json" copy "mouser_api_credentials.json" "save\mouser_api_credentials_rev%VERSION%.json" >nul
if exist "manufacturer_api_cache.json" copy "manufacturer_api_cache.json" "save\manufacturer_api_cache_rev%VERSION%.json" >nul

REM Essential external scripts (already in save directory, just create versioned copies)
if exist "save\ultralibrarian_3d_finder.py" copy "save\ultralibrarian_3d_finder.py" "save\ultralibrarian_3d_finder_rev%VERSION%.py" >nul
if exist "save\samacsys_3d_finder.py" copy "save\samacsys_3d_finder.py" "save\samacsys_3d_finder_rev%VERSION%.py" >nul
if exist "save\snapeda_3d_finder_final.py" copy "save\snapeda_3d_finder_final.py" "save\snapeda_3d_finder_final_rev%VERSION%.py" >nul
if exist "save\snapeda_3d_finder_working.py" copy "save\snapeda_3d_finder_working.py" "save\snapeda_3d_finder_working_rev%VERSION%.py" >nul

REM Additional important files that might exist
if exist "working_screen_gui.py" copy "working_screen_gui.py" "save\working_screen_gui_rev%VERSION%.py" >nul
if exist "vendor_screen_gui.py" copy "vendor_screen_gui.py" "save\vendor_screen_gui_rev%VERSION%.py" >nul
if exist "component_finder_vs_style.py" copy "component_finder_vs_style.py" "save\component_finder_vs_style_rev%VERSION%.py" >nul

REM Batch files
if exist "run_component_finder.bat" copy "run_component_finder.bat" "save\run_component_finder_rev%VERSION%.bat" >nul
if exist "install.bat" copy "install.bat" "save\install_rev%VERSION%.bat" >nul

REM Documentation
if exist "README.md" copy "README.md" "save\README_rev%VERSION%.md" >nul
if exist "COMPONENT_FINDER_WORKFLOW.txt" copy "COMPONENT_FINDER_WORKFLOW.txt" "save\COMPONENT_FINDER_WORKFLOW_rev%VERSION%.txt" >nul
if exist "ESSENTIAL_FILES_LIST.txt" copy "ESSENTIAL_FILES_LIST.txt" "save\ESSENTIAL_FILES_LIST_rev%VERSION%.txt" >nul

REM Help files directory (required for GUI) - Skip copying to avoid errors
REM if not exist "save\help_files_rev%VERSION%" mkdir "save\help_files_rev%VERSION%" >nul
REM if exist "help_files" xcopy "help_files\*.*" "save\help_files_rev%VERSION%\" /E /I /Q >nul

REM Emoji images directory (required for GUI) - Skip copying to avoid errors
REM if not exist "save\emoji_images_rev%VERSION%" mkdir "save\emoji_images_rev%VERSION%" >nul
REM if exist "emoji_images" xcopy "emoji_images\*.*" "save\emoji_images_rev%VERSION%\" /E /I /Q >nul

REM Runtime CSV files (important data)
if exist "actual-web-site-xref.csv" copy "actual-web-site-xref.csv" "save\actual-web-site-xref_rev%VERSION%.csv" >nul
if exist "3d\3d_model_downloads.csv" copy "3d\3d_model_downloads.csv" "save\3d_model_downloads_rev%VERSION%.csv" >nul

REM Copy version file
if exist "version.txt" copy "version.txt" "save\version_rev%VERSION%.txt" >nul

echo.
echo [SUCCESS] Backup created successfully in save directory with _rev%VERSION% suffix
echo.
echo Files backed up:
echo   - Core Python modules (component_finder.py, datasheet_finder.py, etc.)
echo   - 3D Finder modules (external_3d_finder.py, ultralibrarian_3d_finder.py, samacsys_3d_finder.py, snapeda_3d_finder.py, etc.)
echo   - API and utility modules (manufacturer_api_*.py, smart_wait_utils.py, etc.)
echo   - Configuration files (*.json)
echo   - External 3D scripts (save\*_3d_finder*.py)
echo   - GUI modules (working_screen_gui.py, vendor_screen_gui.py, etc.)
echo   - Batch files (*.bat)
echo   - Documentation (README.md, WORKFLOW.txt, ESSENTIAL_FILES_LIST.txt)
echo   - Help files directory (help_files\) - SKIPPED to avoid errors
echo   - Emoji images directory (emoji_images\) - SKIPPED to avoid errors
echo   - Runtime CSV files (actual-web-site-xref.csv, 3d_model_downloads.csv)
echo   - Version control (version.txt)
echo.

REM Increment version number for next backup
set /a NEW_VERSION=%VERSION%+1
echo %NEW_VERSION% > version.txt

echo [SUCCESS] Version incremented to: %NEW_VERSION%
echo.
echo Backup complete!
pause
