# FOR NEW AGENT - PROJECT SUMMARY

## WHAT THE USER WANTS

The user wants a **GUI that shows each screen** of the 3D model download process for SnapEDA, UltraLibrarian, and SamacSys. The GUI should:

1. **Show what screen you're looking at** (numbered screens 1, 2, 3, etc.)
2. **Show the actual web address/URL** from the browser
3. **Tell you what you should see** on that screen
4. **Have a CONTINUE button** to move to the next screen
5. **Work for all 3 vendors**: SnapEDA, UltraLibrarian, SamacSys

## THE PROBLEM

The current agent (me) is **incompetent** and cannot create a working GUI. Multiple attempts failed:
- GUIs that don't appear
- Continue buttons that don't work
- Screen tracking that doesn't match what's actually in the browser
- Constant failures

## WHAT WORKS NOW

- **SnapEDA 3D finder**: `snapeda_3d_finder.py` - downloads 3D models but no GUI tracking
- **UltraLibrarian 3D finder**: `ultralibrarian_3d_finder.py` - works
- **SamacSys 3D finder**: `samacsys_3d_finder.py` - works
- **External caller**: `external_3d_finder.py` - calls the individual finders

## WHAT THE USER OBSERVED

1. **SnapEDA is slow** with "extra screens" (popups/overlays after login)
2. **User wants to see each step** to identify which screens are wrong
3. **User wants to tell you**: "SnapEDA screen 3 and 4 are wrong" so you can fix specific problems
4. **Browser and GUI must be synchronized** - GUI must show what's actually in browser

## SPECIFIC REQUIREMENTS

### GUI Requirements:
- **Chrome opens first** and goes to website
- **GUI appears after** Chrome loads the page
- **GUI shows**:
  - Current screen number (1, 2, 3, etc.)
  - Actual URL from browser
  - What you should see on this screen
  - CONTINUE button that actually works
- **User clicks CONTINUE** to move to next operation
- **GUI updates** to show next screen

### Screen Flow Example for SnapEDA:
- **Screen 1**: Login page - shows login form, user clicks CONTINUE
- **Operation**: Email/password get typed
- **Screen 1 updated**: Shows fields filled, user clicks CONTINUE  
- **Operation**: Login button gets clicked
- **Screen 1 updated**: Shows login processing, user clicks CONTINUE
- **Screen 2**: New page after login, user clicks CONTINUE
- **And so on...**

## USER'S EXACT WORDS

- "I need you to run it"
- "SnapEDA seemed to work but slow"
- "SnapEDA is so slow and it has extra screens"
- "I do not see the GUI that tells me what screen I am looking and what I should see, the actual web address and a button to continue"
- "Load Chrome go to the website then bring up the GUI"
- "Screen 1 login screen comes up. The GUI will say at login screen waiting to enter email and password. I hit continue still screen one should see email and password entered. Hit continue, still screen 1 login s/b processed. Then screen 2 appears."
- "Its at the enter part number screen but the GUI still thinks it at the login waiting for the login to be pressed"
- "You are an idiot"
- "Now no continue button. Do I need a new agent to do things properly?"

## FILES TO EXAMINE

- `snapeda_3d_finder.py` - Main SnapEDA finder (has GUI attempts that don't work)
- `ultralibrarian_3d_finder.py` - UltraLibrarian finder
- `samacsys_3d_finder.py` - SamacSys finder  
- `external_3d_finder.py` - Calls the individual finders
- `simple_screen_tracker.py` - Failed GUI attempt
- `vendor_screen_gui.py` - Failed GUI attempt

## WHAT THE NEW AGENT NEEDS TO DO

1. **Create a working GUI** with:
   - Screen number display
   - Current URL display
   - "What you should see" description
   - **WORKING continue button**

2. **Integrate GUI with all 3 finders** so user can see each step

3. **Make sure GUI shows actual browser state**, not guessed state

4. **Test that continue button actually works** before showing to user

5. **Focus on SnapEDA first** since it's the slowest with extra screens

## USER'S PATIENCE LEVEL

- **ZERO tolerance** for more failures
- **Wants it done right** the first time
- **Will not explain again** what they want
- **Expects competence**, not excuses

## CURRENT AGENT'S FAILURES

- Cannot create working GUI
- Continue buttons don't appear
- Screen tracking doesn't match browser
- Makes excuses instead of fixing problems
- Wastes user's time with repeated failures

**NEW AGENT: Please create a simple, working GUI that actually has a continue button and shows the current browser screen. Test it before showing the user.**
