#!/usr/bin/env python3
"""
Debug what login buttons are available on SnapEDA login page
"""

import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_driver():
    options = Options()
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def main():
    print("🔍 Debugging SnapEDA login button...")
    
    driver = setup_driver()
    wait = WebDriverWait(driver, 15)
    
    try:
        # Go to login page
        driver.get("https://www.snapeda.com/account/login/")
        
        # Wait for page to load
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "#id_username")))
        
        # Fill email and password
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        
        print("✅ Filled email and password")
        
        # Find all buttons on the page
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\n📋 Found {len(all_buttons)} buttons:")
        for i, btn in enumerate(all_buttons):
            try:
                text = btn.text.strip()
                btn_type = btn.get_attribute("type")
                btn_class = btn.get_attribute("class")
                visible = btn.is_displayed()
                enabled = btn.is_enabled()
                print(f"  Button {i+1}: text='{text}' type='{btn_type}' class='{btn_class}' visible={visible} enabled={enabled}")
            except:
                print(f"  Button {i+1}: Error reading button")
        
        # Find all input elements
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"\n📋 Found {len(all_inputs)} input elements:")
        for i, inp in enumerate(all_inputs):
            try:
                inp_type = inp.get_attribute("type")
                inp_value = inp.get_attribute("value")
                inp_class = inp.get_attribute("class")
                visible = inp.is_displayed()
                enabled = inp.is_enabled()
                if inp_type in ["submit", "button"]:
                    print(f"  Input {i+1}: type='{inp_type}' value='{inp_value}' class='{inp_class}' visible={visible} enabled={enabled}")
            except:
                pass
        
        print("\n⏸️ Page loaded. Check the browser to see the login button, then press Enter...")
        input("Press Enter to continue...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
