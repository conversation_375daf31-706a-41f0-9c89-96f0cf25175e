#!/usr/bin/env python3
"""
SnapEDA 3D Model Finder - Improved version with better error handling
Addresses common issues like element not interactable, timing problems, etc.
"""

import os
import sys
import time
import csv
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys

def log_to_csv(manufacturer, part_number, source, step_filename):
    """Log the download information to CSV file - prevents duplicates"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")

    # Check for duplicates by reading existing entries
    if os.path.exists(csv_filename):
        try:
            with open(csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if (row['MANUFACTURER'].lower() == manufacturer.lower() and
                        row['PART_NUMBER'].lower() == part_number.lower() and
                        row['SOURCE'].lower() == source.lower()):
                        print(f"ℹ️ Entry already exists in CSV: {manufacturer}, {part_number}, {source}")
                        return
        except Exception as e:
            print(f"⚠️ Could not read CSV for duplicate check: {e}")

    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)

    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header if file is new
            if not file_exists:
                writer.writeheader()
                print(f"✅ Created new CSV file: {csv_filename}")

            # Write the data
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': source,
                'STEP_FILE_NAME': step_filename
            })

            print(f"✅ Logged to CSV: {manufacturer}, {part_number}, {source}, {step_filename}")

    except Exception as e:
        print(f"⚠️ Could not write to CSV: {e}")

class SnapEDAImproved3DFinder:
    def __init__(self):
        self.driver = None
        self.wait = None
    
    def setup_driver(self, silent=False):
        """Setup Chrome driver with download preferences and better options"""
        chrome_options = Options()

        # Configure for silent mode
        if silent:
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
        else:
            chrome_options.add_argument("--start-maximized")

        # Better automation detection avoidance
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-extensions")
        
        # Download preferences
        download_dir = os.path.abspath('3d')
        os.makedirs(download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # Set user agent to avoid detection
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    
    def wait_and_click(self, driver, element, timeout=10):
        """Wait for element to be clickable and click it with multiple strategies"""
        try:
            # Strategy 1: Wait for element to be clickable
            wait = WebDriverWait(driver, timeout)
            clickable_element = wait.until(EC.element_to_be_clickable(element))
            clickable_element.click()
            return True
        except:
            try:
                # Strategy 2: Scroll to element and click
                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                element.click()
                return True
            except:
                try:
                    # Strategy 3: JavaScript click
                    driver.execute_script("arguments[0].click();", element)
                    return True
                except:
                    try:
                        # Strategy 4: ActionChains click
                        actions = ActionChains(driver)
                        actions.move_to_element(element).click().perform()
                        return True
                    except:
                        return False
    
    def safe_find_element(self, driver, by, value, timeout=10):
        """Safely find element with wait"""
        try:
            wait = WebDriverWait(driver, timeout)
            return wait.until(EC.presence_of_element_located((by, value)))
        except:
            return None
    
    def safe_send_keys(self, element, text, clear_first=True):
        """Safely send keys to element"""
        try:
            if clear_first:
                element.clear()
            element.send_keys(text)
            return True
        except:
            return False

    def login_to_snapeda(self, driver, part_number):
        """Login to SnapEDA with improved error handling"""
        print("🔐 Logging into SnapEDA...")

        try:
            # Load credentials
            import json
            try:
                with open('component_site_credentials.json', 'r') as f:
                    creds = json.load(f)
                    snapeda_creds = creds.get('snapeda', {})
                    email = snapeda_creds.get('email', '')
                    password = snapeda_creds.get('password', '')
            except:
                # Fallback to hardcoded credentials
                email = "<EMAIL>"
                password = "Lennyai123#"
                print("⚠️ Using fallback credentials")

            if not email or not password:
                print("❌ No credentials available")
                return False

            # Go to SnapEDA homepage first
            print("🔸 Loading SnapEDA homepage...")
            driver.get("https://www.snapeda.com/")
            time.sleep(3)

            # Find and click login link
            print("🔸 Looking for login link...")
            login_selectors = [
                "//a[contains(@href, 'login')]",
                "//a[contains(text(), 'Log In')]",
                "//a[contains(text(), 'Login')]",
                ".login-link",
                "#login-link"
            ]
            
            login_link = None
            for selector in login_selectors:
                try:
                    if selector.startswith("//"):
                        login_link = self.safe_find_element(driver, By.XPATH, selector, 5)
                    else:
                        login_link = self.safe_find_element(driver, By.CSS_SELECTOR, selector, 5)
                    
                    if login_link and login_link.is_displayed():
                        break
                except:
                    continue
            
            if not login_link:
                print("❌ Could not find login link")
                return False
            
            print("✅ Found login link, clicking...")
            if not self.wait_and_click(driver, login_link):
                print("❌ Could not click login link")
                return False
            
            time.sleep(3)

            # Fill in search field first (if available)
            print("🔸 Looking for search field...")
            search_selectors = [
                "input[name='q']",
                "input[placeholder*='search']",
                "#search-input",
                ".search-input"
            ]
            
            for selector in search_selectors:
                try:
                    search_field = self.safe_find_element(driver, By.CSS_SELECTOR, selector, 3)
                    if search_field and search_field.is_displayed():
                        print(f"✅ Found search field, entering part number...")
                        if self.safe_send_keys(search_field, part_number):
                            print(f"✅ Entered part number: {part_number}")
                        break
                except:
                    continue

            # Fill login form
            print("🔸 Looking for email field...")
            email_selectors = [
                "#id_username",
                "input[name='username']",
                "input[type='email']",
                "input[placeholder*='email']"
            ]
            
            email_field = None
            for selector in email_selectors:
                try:
                    email_field = self.safe_find_element(driver, By.CSS_SELECTOR, selector, 5)
                    if email_field and email_field.is_displayed():
                        break
                except:
                    continue
            
            if not email_field:
                print("❌ Could not find email field")
                return False
            
            print("✅ Found email field, filling...")
            if not self.safe_send_keys(email_field, email):
                print("❌ Could not fill email field")
                return False

            # Fill password field
            print("🔸 Looking for password field...")
            password_selectors = [
                "input[type='password']",
                "#id_password",
                "input[name='password']"
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.safe_find_element(driver, By.CSS_SELECTOR, selector, 5)
                    if password_field and password_field.is_displayed():
                        break
                except:
                    continue
            
            if not password_field:
                print("❌ Could not find password field")
                return False
            
            print("✅ Found password field, filling...")
            if not self.safe_send_keys(password_field, password):
                print("❌ Could not fill password field")
                return False

            # Submit form
            print("🔸 Looking for submit button...")
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "//button[contains(text(), 'Log In')]",
                "//button[contains(text(), 'Login')]",
                "//input[contains(@value, 'Log In')]"
            ]
            
            submitted = False
            for selector in submit_selectors:
                try:
                    if selector.startswith("//"):
                        submit_btn = self.safe_find_element(driver, By.XPATH, selector, 3)
                    else:
                        submit_btn = self.safe_find_element(driver, By.CSS_SELECTOR, selector, 3)
                    
                    if submit_btn and submit_btn.is_displayed():
                        print(f"✅ Found submit button, clicking...")
                        if self.wait_and_click(driver, submit_btn):
                            submitted = True
                            break
                except:
                    continue
            
            # Try alternative submission methods
            if not submitted:
                print("🔸 Trying alternative submission methods...")
                try:
                    # Try pressing Enter on password field
                    password_field.send_keys(Keys.RETURN)
                    submitted = True
                    print("✅ Submitted via Enter key")
                except:
                    try:
                        # Try form submission
                        form = driver.find_element(By.TAG_NAME, "form")
                        form.submit()
                        submitted = True
                        print("✅ Submitted via form.submit()")
                    except:
                        pass
            
            if not submitted:
                print("❌ Could not submit login form")
                return False
            
            # Wait for login to complete
            print("🔸 Waiting for login to complete...")
            time.sleep(5)
            
            # Check if login was successful
            current_url = driver.current_url.lower()
            if "login" not in current_url or "dashboard" in current_url or "account" in current_url:
                print("✅ Login appears successful")
                return True
            else:
                print(f"❌ Login may have failed - current URL: {driver.current_url}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False

    def search_and_download(self, manufacturer, part_number, silent=False):
        """Search for part and download 3D model with improved handling"""
        if not silent:
            print(f"\n🎯 SNAPEDA 3D MODEL FINDER (IMPROVED)")
            print("=" * 60)
            print(f"🔍 Looking for: {manufacturer} {part_number}")
        
        driver = self.setup_driver(silent=silent)
        if not driver:
            return None
        
        self.wait = WebDriverWait(driver, 20)
        
        try:
            # Login to SnapEDA
            if not self.login_to_snapeda(driver, part_number):
                return None
            
            # Navigate to part page
            part_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
            print(f"🔸 Navigating to part page...")
            driver.get(part_url)
            time.sleep(5)
            
            # Look for 3D Model section/tab
            print(f"🔸 Looking for 3D Model section...")
            three_d_selectors = [
                "//li[text()='3D Model']",
                "//a[text()='3D Model']",
                "//span[text()='3D Model']",
                "//div[contains(@class, '3d-model')]",
                "//button[contains(text(), '3D Model')]",
                ".tab-3d-model",
                "#tab-3d-model"
            ]
            
            three_d_element = None
            for selector in three_d_selectors:
                try:
                    if selector.startswith("//"):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for elem in elements:
                        if elem.is_displayed():
                            three_d_element = elem
                            print(f"✅ Found 3D Model element: {elem.text}")
                            break
                    
                    if three_d_element:
                        break
                except:
                    continue
            
            if three_d_element:
                print(f"🔸 Clicking 3D Model section...")
                if not self.wait_and_click(driver, three_d_element):
                    print("❌ Could not click 3D Model section")
                    return None
                time.sleep(3)
            
            # Look for download button
            print(f"🔸 Looking for download button...")
            download_selectors = [
                "//a[contains(@class, '3D-model-download')]",
                "//a[contains(text(), 'Download 3D Model')]",
                "//button[contains(text(), 'Download')]",
                "//a[contains(@href, 'download')]",
                ".download-3d-btn",
                ".btn-download",
                "#download-3d-btn"
            ]
            
            download_button = None
            for selector in download_selectors:
                try:
                    if selector.startswith("//"):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for elem in elements:
                        if elem.is_displayed() and ("download" in elem.text.lower() or "3d" in elem.text.lower()):
                            download_button = elem
                            print(f"✅ Found download button: {elem.text}")
                            break
                    
                    if download_button:
                        break
                except:
                    continue
            
            if not download_button:
                print("❌ Could not find download button")
                return None
            
            # Monitor downloads before clicking
            download_dir = os.path.abspath('3d')
            before_files = set(os.listdir(download_dir)) if os.path.exists(download_dir) else set()
            
            # Click download button
            print(f"🔸 Clicking download button...")
            if not self.wait_and_click(driver, download_button):
                print("❌ Could not click download button")
                return None
            
            # Monitor for downloads
            print(f"📁 Monitoring downloads for 30 seconds...")
            for i in range(30):
                time.sleep(1)
                if os.path.exists(download_dir):
                    current_files = set(os.listdir(download_dir))
                    new_files = current_files - before_files
                    
                    if new_files:
                        print(f"🎉 NEW FILE DOWNLOADED after {i+1} seconds!")
                        for f in sorted(new_files):
                            if f.endswith('.step') or f.endswith('.stp'):
                                file_path = os.path.join(download_dir, f)
                                final_name = f"SnapEDA-{manufacturer}-{part_number}.step"
                                final_path = os.path.join(download_dir, final_name)
                                
                                # Rename file
                                if os.path.exists(file_path):
                                    if os.path.exists(final_path):
                                        os.remove(final_path)
                                    os.rename(file_path, final_path)
                                    print(f"✅ Downloaded and renamed: {final_name}")
                                    
                                    # Log to CSV
                                    log_to_csv(manufacturer, part_number, "SnapEDA", final_name)
                                    
                                    return final_path
                        break
                
                if i % 5 == 0 and i > 0:
                    print(f"   ⏳ {i+1}/30 seconds...")
            
            print(f"❌ No STEP file downloaded")
            return None
            
        except Exception as e:
            print(f"❌ Error during search and download: {e}")
            import traceback
            traceback.print_exc()
            return None
        
        finally:
            if driver:
                driver.quit()

def find_3d_model(manufacturer, part_number, silent=False):
    """Main function to find and download 3D model"""
    finder = SnapEDAImproved3DFinder()
    result = finder.search_and_download(manufacturer, part_number, silent=silent)
    
    if result:
        print(f"🎉 SUCCESS: {result} downloaded!")
        return result
    else:
        print(f"❌ FAILED: No 3D model obtained for {part_number}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python snapeda_3d_finder_improved.py <manufacturer> <part_number>")
        print("Example: python snapeda_3d_finder_improved.py \"Texas Instruments\" LM358N")
        sys.exit(1)
    
    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    
    find_3d_model(manufacturer, part_number)
