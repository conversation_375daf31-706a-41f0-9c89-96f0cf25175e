#!/usr/bin/env python3
"""
GUI Demo Launcher - Simple script to test the working GUI
"""

import sys
import os

def main():
    print("🎯 SnapEDA GUI Demo Launcher")
    print("=" * 40)
    
    print("\nChoose what to test:")
    print("1. Test GUI only (no browser)")
    print("2. Test GUI with SnapEDA browser (full demo)")
    print("3. Exit")
    
    choice = input("\nEnter choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        print("\n🧪 Testing GUI only...")
        test_gui_only()
    elif choice == "2":
        print("\n🌐 Testing GUI with SnapEDA browser...")
        test_gui_with_browser()
    elif choice == "3":
        print("👋 Goodbye!")
        return
    else:
        print("❌ Invalid choice. Please enter 1, 2, or 3.")
        main()

def test_gui_only():
    """Test just the GUI without browser"""
    try:
        from working_screen_gui import get_working_gui
        
        print("🖥️ Creating GUI...")
        gui = get_working_gui()
        
        print("✅ GUI created successfully!")
        print("📺 The GUI window should now be visible")
        print("👆 Click the CONTINUE button to test each screen...")
        
        # Test screen 1
        gui.update_screen("SnapEDA", 1, "Test Screen 1", 
                         "https://www.snapeda.com/login", 
                         "GUI TEST SCREEN 1:\n" +
                         "✅ This is a test of the GUI\n" +
                         "👀 You should see:\n" +
                         "  - This window with screen info\n" +
                         "  - Screen number: 1\n" +
                         "  - Vendor: SnapEDA\n" +
                         "  - Working CONTINUE button\n\n" +
                         "🔄 Click CONTINUE to test screen 2")
        gui.wait_for_continue()
        
        # Test screen 2
        gui.update_screen("UltraLibrarian", 2, "Test Screen 2", 
                         "https://www.ultralibrarian.com/search", 
                         "GUI TEST SCREEN 2:\n" +
                         "✅ Vendor changed to UltraLibrarian\n" +
                         "👀 You should see:\n" +
                         "  - Screen number: 2\n" +
                         "  - Vendor: ULTRALIBRARIAN\n" +
                         "  - Different URL displayed\n" +
                         "  - Continue button still works\n\n" +
                         "🔄 Click CONTINUE for final test")
        gui.wait_for_continue()
        
        # Test screen 3
        gui.update_screen("SamacSys", 3, "Test Complete", 
                         "https://www.samacsys.com/", 
                         "GUI TEST COMPLETE:\n" +
                         "✅ All GUI features working!\n" +
                         "👀 Final test results:\n" +
                         "  - ✅ Screen numbers update\n" +
                         "  - ✅ Vendor names change\n" +
                         "  - ✅ URLs display correctly\n" +
                         "  - ✅ Continue button responds\n" +
                         "  - ✅ Text content updates\n\n" +
                         "🎉 GUI is ready for browser integration!")
        gui.wait_for_continue()
        
        print("\n🎉 GUI TEST COMPLETED SUCCESSFULLY!")
        print("The GUI is working perfectly and ready to use with browsers.")
        
    except Exception as e:
        print(f"❌ Error testing GUI: {e}")
        print("Make sure all required files are present.")

def test_gui_with_browser():
    """Test GUI with actual browser integration"""
    try:
        from snapeda_gui_integrated import find_3d_model_with_gui
        
        print("🌐 Starting browser integration test...")
        print("This will:")
        print("  1. Open Chrome browser")
        print("  2. Show GUI with each screen")
        print("  3. Navigate through SnapEDA login process")
        print("  4. Demonstrate all GUI features")
        
        confirm = input("\nProceed with browser test? (y/n): ").strip().lower()
        if confirm != 'y':
            print("Test cancelled.")
            return
        
        print("\n🚀 Starting browser + GUI test...")
        result = find_3d_model_with_gui("Texas Instruments", "LM358N")
        
        if result:
            print(f"\n✅ Browser + GUI test completed: {result}")
        else:
            print("\n❌ Browser + GUI test failed")
            
    except Exception as e:
        print(f"❌ Error with browser test: {e}")
        print("Make sure Chrome is installed and all dependencies are available.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check that all required files are present.")
