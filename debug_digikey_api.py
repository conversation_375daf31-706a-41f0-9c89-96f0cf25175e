#!/usr/bin/env python3
"""
Debug script to test Digi-Key API and see why datasheet extraction is failing
for part GCM155R71H104KE02D (Murata)
"""

import sys
import json
from pprint import pprint

def debug_digikey_api():
    """Debug the Digi-Key API response for GCM155R71H104KE02D"""
    
    print("🔍 DEBUG: Testing Digi-Key API for GCM155R71H104KE02D")
    print("=" * 60)
    
    manufacturer = "Murata"
    part_number = "GCM155R71H104KE02D"
    
    try:
        # Import the API module
        from digikey_datasheet_improved import DigikeyDatasheetFinder
        
        print(f"📡 Testing API search for: {manufacturer} {part_number}")
        
        # Create API finder instance
        api_finder = DigikeyDatasheetFinder()
        
        # Load credentials
        print("🔑 Loading API credentials...")
        if not api_finder.load_credentials():
            print("❌ ERROR: Digi-Key API credentials not available")
            print("   Check if digikey_credentials.json exists")
            return False
        
        print("✅ API credentials loaded successfully")
        
        # Search using API
        print(f"🔍 Searching for part: {part_number}")
        api_results = api_finder.search_part(manufacturer, part_number)
        
        if not api_results:
            print("❌ ERROR: API returned no results")
            return False
            
        if not isinstance(api_results, dict):
            print(f"❌ ERROR: API returned unexpected type: {type(api_results)}")
            print(f"   Result: {api_results}")
            return False
            
        print("✅ API returned results!")
        print(f"📊 Result type: {type(api_results)}")
        
        # Show all available fields
        print("\n📋 ALL AVAILABLE FIELDS IN API RESPONSE:")
        print("-" * 40)
        all_fields = list(api_results.keys())
        for i, field in enumerate(sorted(all_fields), 1):
            print(f"{i:2d}. {field}")
        
        print(f"\n📊 Total fields: {len(all_fields)}")
        
        # Check manufacturer info
        print("\n🏭 MANUFACTURER INFORMATION:")
        print("-" * 30)
        if 'Manufacturer' in api_results:
            mfg_data = api_results['Manufacturer']
            print(f"Manufacturer field type: {type(mfg_data)}")
            if isinstance(mfg_data, dict):
                print("Manufacturer sub-fields:")
                for key, value in mfg_data.items():
                    print(f"  {key}: {value}")
            else:
                print(f"Manufacturer value: {mfg_data}")
        else:
            print("❌ No 'Manufacturer' field found")
            
        # Check part number info
        print("\n🔢 PART NUMBER INFORMATION:")
        print("-" * 30)
        part_fields = ['ManufacturerProductNumber', 'PartNumber', 'ProductNumber']
        for field in part_fields:
            if field in api_results:
                print(f"{field}: {api_results[field]}")
            else:
                print(f"{field}: NOT FOUND")
        
        # Check datasheet fields - THIS IS THE KEY PART
        print("\n📄 DATASHEET FIELDS (THE IMPORTANT PART):")
        print("-" * 40)
        datasheet_fields = [
            'DatasheetUrl', 'PrimaryDatasheet', 'Datasheet', 'DataSheet',
            'DocumentUrl', 'Documents', 'ProductDocuments', 'TechnicalDocuments'
        ]
        
        found_datasheet_fields = []
        for field in datasheet_fields:
            if field in api_results:
                value = api_results[field]
                print(f"✅ {field}: {value}")
                found_datasheet_fields.append((field, value))
            else:
                print(f"❌ {field}: NOT FOUND")
        
        # Show any field that contains 'sheet', 'pdf', or 'document'
        print("\n🔍 FIELDS CONTAINING 'SHEET', 'PDF', OR 'DOCUMENT':")
        print("-" * 50)
        for field, value in api_results.items():
            field_lower = field.lower()
            if any(keyword in field_lower for keyword in ['sheet', 'pdf', 'document', 'url']):
                print(f"📋 {field}: {value}")
        
        # Try to extract datasheet URL using current logic
        print("\n🔧 TESTING CURRENT DATASHEET EXTRACTION LOGIC:")
        print("-" * 50)
        datasheet_url = None
        
        if 'DatasheetUrl' in api_results and api_results['DatasheetUrl']:
            datasheet_url = api_results['DatasheetUrl']
            print(f"✅ Found via DatasheetUrl: {datasheet_url}")
        elif 'PrimaryDatasheet' in api_results and api_results['PrimaryDatasheet']:
            datasheet_url = api_results['PrimaryDatasheet']
            print(f"✅ Found via PrimaryDatasheet: {datasheet_url}")
        else:
            print("❌ No datasheet URL found using current logic")
            
        if datasheet_url:
            print(f"\n🎯 EXTRACTED DATASHEET URL: {datasheet_url}")
            print(f"   URL type: {type(datasheet_url)}")
            print(f"   URL length: {len(str(datasheet_url))}")
        else:
            print("\n❌ FAILED TO EXTRACT DATASHEET URL")
            print("   This is why the search is failing!")
            
        # Show raw JSON for manual inspection
        print("\n📝 RAW API RESPONSE (first 1000 chars):")
        print("-" * 40)
        raw_json = json.dumps(api_results, indent=2)
        print(raw_json[:1000])
        if len(raw_json) > 1000:
            print(f"... (truncated, total length: {len(raw_json)} chars)")
            
        return True
        
    except ImportError as e:
        print(f"❌ ERROR: Cannot import digikey_datasheet_improved: {e}")
        print("   Make sure the module exists in the current directory")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Digi-Key API Debug Tool")
    print("=" * 60)
    
    success = debug_digikey_api()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Debug completed successfully")
        print("   Check the output above to see why datasheet extraction is failing")
    else:
        print("❌ Debug failed")
        print("   Check the error messages above")
