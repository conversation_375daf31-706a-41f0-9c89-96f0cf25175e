========================================
COMPONENT FINDER - ESSENTIAL FILES LIST
========================================

CORE PROGRAM FILES (Required):
- component_finder.py          (Main GUI application)
- datasheet_finder.py          (Datasheet search module)
- digikey_datasheet_improved.py (Digi-Key API integration)
- mouser_datasheet_improved.py  (Mouser API integration)
- pdf_parser.py                (PDF processing)
- rs_components_scraper.py     (RS Components scraper)
- step_finder.py               (STEP file finder)

CONFIGURATION FILES (Required):
- component_site_credentials.json (Website login credentials)
- digikey_api_credentials.json    (Digi-Key API keys)
- samacsys_credentials.json       (SamacSys login credentials)
- manufacturer_knowledge.json     (Learned manufacturer patterns)
- manufacturer_websites.json      (Known manufacturer websites)

EXTERNAL 3D SCRIPTS (Required):
- save\ultralibrarian_3d_finder.py (UltraLibrarian 3D downloader)
- save\samacsys_3d_finder.py       (SamacSys 3D downloader)
- save\snapeda_3d_finder_final.py  (SnapEDA 3D downloader)

BATCH FILES (Convenience):
- run_component_finder.bat     (Program launcher)
- install.bat                  (Dependency installer)
- backup.bat                   (Backup system)
- cleanup_unneeded_files.bat   (Cleanup system)
- restore_from_backup.bat      (Restore system)

DOCUMENTATION (Helpful):
- README.md                    (Main documentation)
- COMPONENT_FINDER_WORKFLOW.txt (Usage workflow)
- ESSENTIAL_FILES_LIST.txt     (This file)

HELP FILES (Required for GUI):
- help_files\about.txt
- help_files\excel_setup.txt
- help_files\learning_system.txt
- help_files\quick_start.txt
- help_files\troubleshooting.txt

EMOJI IMAGES (Required for GUI):
- emoji_images\*.png           (All PNG files in this directory)

RUNTIME DIRECTORIES (Created automatically):
- datasheets\                  (Downloaded PDF files)
- 3d\                         (Downloaded STEP files)
- found-files-log.csv         (Search results log)

VERSION CONTROL:
- version.txt                  (Current backup version number)

========================================
UNNEEDED FILES (Can be deleted):
========================================

TEST FILES:
- automated_test_all_options.py
- check_code.py
- simple_automated_test.py
- simple_step_test.py
- test_*.py (all test files)

DEVELOPMENT FILES:
- debug_output.txt
- digikey_token.py
- download_drawio.py
- fix_drawio_import.py
- open_flowchart*.py
- setup_vscode_mermaid.py

DRAWIO FILES:
- *.drawio (all DrawIO files)
- component_finder_flowchart.mmd
- draw.io-*.exe
- drawio_import_guide.txt
- sample_mermaid_for_drawio.txt

OLD/TEMPORARY FILES:
- rs_components_*.html
- backup_version.bat
- restore_version.bat
- get_step_*.bat
- run_step_downloader.bat
- Teledyne_Flir_master-footprint_list*.xlsx
- actual-web-site-xref.csv
- STEP_DOWNLOADER_README.md

DIRECTORIES TO DELETE:
- __pycache__\ (Python cache)
- 3d-models\ (old location)
- files-download\ (old directory)
- step_downloads\ (old directory)
- python\ (if exists)

========================================
BACKUP SYSTEM USAGE:
========================================

1. CLEANUP: Run cleanup_unneeded_files.bat to remove unnecessary files
2. BACKUP: Run backup.bat to create versioned backup
3. RESTORE: Run restore_from_backup.bat to restore from backup

The backup system automatically:
- Reads current version from version.txt
- Creates backup with _revXX suffix
- Increments version number for next backup
- Only backs up essential files needed to run the program

========================================
