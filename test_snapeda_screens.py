#!/usr/bin/env python3
"""
Test SnapEDA with GUI screens - EXACTLY as requested
"""

from working_screen_gui import get_working_gui
from snapeda_3d_finder import SnapEDA3DFinder

def main():
    print("Starting SnapEDA with GUI screens...")
    
    # Create GUI
    gui = get_working_gui()
    
    # Create SnapEDA finder
    finder = SnapEDA3DFinder()
    finder.gui = gui
    
    # Run it
    result = finder.search_and_download("Texas Instruments", "LM358N", silent=False)
    
    print(f"Result: {result}")

if __name__ == "__main__":
    main()
