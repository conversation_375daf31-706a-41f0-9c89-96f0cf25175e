#!/usr/bin/env python3
"""
Test SnapEDA login options to find alternatives to LinkedIn OAuth
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def test_snapeda_login_options():
    """Test different SnapEDA login approaches"""
    print("🔍 Testing SnapEDA Login Options")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Load homepage
        print("🔸 Loading SnapEDA homepage...")
        driver.get('https://www.snapeda.com/')
        time.sleep(5)
        
        print(f"✅ Page loaded: {driver.title}")
        
        # Step 2: Look for all login/signup options
        print("\n🔸 Looking for login/signup options...")
        
        # Find all links that might be login-related
        all_links = driver.find_elements(By.TAG_NAME, "a")
        login_options = []
        
        for link in all_links:
            try:
                text = link.text.strip().lower()
                href = link.get_attribute('href') or ''
                
                if any(word in text for word in ['login', 'log in', 'sign in', 'signin', 'join', 'register', 'signup', 'sign up']):
                    login_options.append((text, href))
                    
            except:
                continue
        
        print(f"✅ Found {len(login_options)} login-related options:")
        for i, (text, href) in enumerate(login_options):
            print(f"   {i+1}. '{text}' -> {href}")
        
        # Step 3: Try direct login URL
        print("\n🔸 Testing direct login URL...")
        try:
            driver.get('https://www.snapeda.com/login/')
            time.sleep(5)
            print(f"✅ Direct login URL: {driver.current_url}")
            print(f"   Title: {driver.title}")
            
            # Look for login form elements
            email_fields = driver.find_elements(By.CSS_SELECTOR, "input[type='email'], input[name*='email'], input[id*='email']")
            password_fields = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
            
            print(f"   Email fields found: {len(email_fields)}")
            print(f"   Password fields found: {len(password_fields)}")
            
            if email_fields and password_fields:
                print("✅ Direct email/password login form found!")
                return True
            else:
                print("❌ No direct login form found")
                
        except Exception as e:
            print(f"❌ Direct login URL failed: {e}")
        
        # Step 4: Try account/register URL
        print("\n🔸 Testing account/register URL...")
        try:
            driver.get('https://www.snapeda.com/account/register/')
            time.sleep(5)
            print(f"✅ Register URL: {driver.current_url}")
            
            # Look for existing account login option
            login_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'existing account') or contains(text(), 'already have') or contains(text(), 'sign in')]")
            if login_links:
                print(f"✅ Found 'existing account' links: {len(login_links)}")
                for link in login_links:
                    text = link.text.strip()
                    href = link.get_attribute('href')
                    print(f"   '{text}' -> {href}")
            
        except Exception as e:
            print(f"❌ Register URL failed: {e}")
        
        # Step 5: Check current page for alternative login methods
        print("\n🔸 Looking for alternative login methods on current page...")
        
        # Look for email/password fields
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"   Found {len(all_inputs)} input fields:")
        
        for i, inp in enumerate(all_inputs):
            try:
                input_type = inp.get_attribute('type') or 'text'
                name = inp.get_attribute('name') or ''
                placeholder = inp.get_attribute('placeholder') or ''
                visible = inp.is_displayed()
                
                print(f"      {i+1}. Type: {input_type}, Name: '{name}', Placeholder: '{placeholder}', Visible: {visible}")
                
            except:
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        driver.quit()
        print("\n🔒 Browser closed")

if __name__ == "__main__":
    success = test_snapeda_login_options()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Found direct SnapEDA login option!")
    else:
        print("🚫 Only LinkedIn OAuth login available")
