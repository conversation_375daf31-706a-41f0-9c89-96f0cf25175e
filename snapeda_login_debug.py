#!/usr/bin/env python3
"""
Debug SnapEDA login flow step by step
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def debug_snapeda_login():
    print("🔍 Starting SnapEDA login debug...")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Load credentials
        try:
            with open('save/component_site_credentials.json', 'r') as f:
                creds = json.load(f)
                snapeda_creds = creds.get('snapeda', {})
                email = snapeda_creds.get('email', '')
                password = snapeda_creds.get('password', '')
                print(f"✅ Loaded credentials for: {email}")
        except Exception as e:
            print(f"❌ Could not load credentials: {e}")
            return False
        
        # Step 1: Load SnapEDA
        print("\n🔸 STEP 1: Loading SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(3)
        print(f"✅ Loaded: {driver.current_url}")
        
        # Step 2: Find and click first login link
        print("\n🔸 STEP 2: Looking for FIRST login link...")
        login_link = None
        login_selectors = [
            "//a[contains(text(), 'Login')]",
            "//a[contains(text(), 'Log In')]", 
            "//a[contains(text(), 'Sign In')]",
            ".login-link",
            "#login-link"
        ]
        
        for selector in login_selectors:
            try:
                if selector.startswith("//"):
                    login_link = driver.find_element(By.XPATH, selector)
                else:
                    login_link = driver.find_element(By.CSS_SELECTOR, selector)
                
                if login_link.is_displayed():
                    print(f"✅ Found first login link: {selector}")
                    print(f"   Text: '{login_link.text}'")
                    print(f"   Href: '{login_link.get_attribute('href')}'")
                    break
            except:
                continue
        
        if not login_link:
            print("❌ No login link found")
            return False
            
        # Click first login link
        print("🔸 Clicking FIRST login link...")
        login_link.click()
        time.sleep(3)
        print(f"✅ After first click: {driver.current_url}")
        
        # Step 3: Fill email and password
        print("\n🔸 STEP 3: Looking for email and password fields...")
        
        # Find email field
        email_field = None
        email_selectors = ["input[type='email']", "input[name='email']", "#email", "#id_username"]
        for selector in email_selectors:
            try:
                email_field = driver.find_element(By.CSS_SELECTOR, selector)
                if email_field.is_displayed():
                    print(f"✅ Found email field: {selector}")
                    break
            except:
                continue
                
        # Find password field  
        password_field = None
        password_selectors = ["input[type='password']", "input[name='password']", "#password", "#id_password"]
        for selector in password_selectors:
            try:
                password_field = driver.find_element(By.CSS_SELECTOR, selector)
                if password_field.is_displayed():
                    print(f"✅ Found password field: {selector}")
                    break
            except:
                continue
        
        if not email_field or not password_field:
            print("❌ Could not find email/password fields")
            return False
            
        # Fill credentials
        print("🔸 Filling credentials...")
        email_field.clear()
        email_field.send_keys(email)
        password_field.clear() 
        password_field.send_keys(password)
        print("✅ Credentials filled")
        
        # Step 4: Find and click SECOND login button (submit)
        print("\n🔸 STEP 4: Looking for SECOND login button (submit)...")
        
        # Debug: Show all buttons and submit inputs
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        all_submits = driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
        
        print(f"🔍 Found {len(all_buttons)} buttons:")
        for i, btn in enumerate(all_buttons):
            try:
                if btn.is_displayed():
                    text = btn.text.strip()
                    btn_type = btn.get_attribute('type')
                    print(f"  Button {i+1}: '{text}' type='{btn_type}'")
            except:
                pass
                
        print(f"🔍 Found {len(all_submits)} submit inputs:")
        for i, sub in enumerate(all_submits):
            try:
                if sub.is_displayed():
                    value = sub.get_attribute('value')
                    print(f"  Submit {i+1}: value='{value}'")
            except:
                pass
        
        # Try to find submit button
        submit_button = None
        submit_selectors = [
            "button[type='submit']",
            "input[type='submit']", 
            "//button[contains(text(), 'Log In')]",
            "//button[contains(text(), 'Login')]",
            "//input[@value='Log In']",
            "//input[@value='Login']"
        ]
        
        for selector in submit_selectors:
            try:
                if selector.startswith("//"):
                    submit_button = driver.find_element(By.XPATH, selector)
                else:
                    submit_button = driver.find_element(By.CSS_SELECTOR, selector)
                
                if submit_button.is_displayed() and submit_button.is_enabled():
                    btn_text = submit_button.text or submit_button.get_attribute('value')
                    print(f"✅ Found SECOND login button: {selector} - '{btn_text}'")
                    break
            except:
                continue
        
        if not submit_button:
            print("❌ Could not find SECOND login button")
            print("🔍 Manual inspection needed - browser will stay open")
            input("Press Enter after you manually inspect the page...")
            return False
            
        # Click submit button
        print("🔸 Clicking SECOND login button...")
        submit_button.click()
        time.sleep(5)
        print(f"✅ After submit: {driver.current_url}")
        
        # Step 5: Check if logged in
        print("\n🔸 STEP 5: Checking login status...")
        if "login" not in driver.current_url.lower():
            print("✅ Login successful - redirected away from login page")
        else:
            print("❌ Still on login page - login may have failed")
        
        print("\n🎯 Login flow complete!")
        print("🔍 Browser will stay open for inspection...")
        input("Press Enter to close...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to close...")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_snapeda_login()
