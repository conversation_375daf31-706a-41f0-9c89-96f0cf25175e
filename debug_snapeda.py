#!/usr/bin/env python3
"""
SnapEDA Debug Program
====================
Standalone program to test and fix SnapEDA functionality
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

class SnapEDADebugger:
    def __init__(self):
        self.download_dir = os.path.join(os.getcwd(), "3d")
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)

        # Load credentials
        self.credentials = self.load_credentials()
        print(f"🔑 DEBUG: Loaded credentials - Email: {self.credentials.get('email', 'NONE')}, Password: {'***' if self.credentials.get('password') else 'NONE'}")
        
    def load_credentials(self):
        """Load SnapEDA credentials"""
        try:
            with open('component_site_credentials.json', 'r') as f:
                data = json.load(f)
                snapeda_creds = data.get('SnapEDA', {})
                return {
                    'email': snapeda_creds.get('email', ''),
                    'password': snapeda_creds.get('password', '')
                }
        except Exception as e:
            print(f"⚠️ Could not load credentials: {e}")
        return {'email': '', 'password': ''}
    
    def create_driver(self):
        """Create Chrome driver with proper settings"""
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Download settings
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    
    def debug_login(self, driver):
        """Debug SnapEDA login process"""
        print("🔐 DEBUG: Testing SnapEDA login...")

        try:
            # Use the correct working login URL
            login_url = "https://www.snapeda.com/account/login/"
            print(f"🔗 Using working login URL: {login_url}")
            driver.get(login_url)
            time.sleep(3)

            print(f"📍 Current URL: {driver.current_url}")
            print(f"📄 Page title: {driver.title}")

            if "404" in driver.title or "not found" in driver.title.lower():
                print("❌ Login page not found")
                return False
            
            # Look for login elements on any page
            # First check if we need to click a login link
            login_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'login') or contains(@href, 'signin') or contains(text(), 'Login') or contains(text(), 'Sign In')]")

            if login_links:
                for link in login_links:
                    if link.is_displayed():
                        link_text = link.text.strip()
                        link_href = link.get_attribute('href')
                        print(f"🔗 Found login link: '{link_text}' -> {link_href}")

                        if any(word in link_text.lower() for word in ['login', 'sign in']):
                            print(f"🎯 Clicking login link: '{link_text}'")
                            link.click()
                            time.sleep(3)
                            break
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[id*='email']",
                "#id_username"
            ]
            
            email_field = None
            for selector in email_selectors:
                try:
                    email_field = driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"✅ Found email field with: {selector}")
                    break
                except:
                    continue
            
            if not email_field:
                print("❌ No email field found")
                return False
            
            # Find password field
            password_field = None
            try:
                password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
                print("✅ Found password field")
            except:
                print("❌ No password field found")
                return False
            
            # Enter credentials
            if self.credentials['email'] and self.credentials['password']:
                email_field.clear()
                email_field.send_keys(self.credentials['email'])
                password_field.clear()
                password_field.send_keys(self.credentials['password'])
                print("✅ Credentials entered")
                
                # Find and click login button
                login_buttons = driver.find_elements(By.XPATH, "//button | //input[@type='submit'] | //input[@type='button']")
                
                for button in login_buttons:
                    try:
                        if button.is_displayed():
                            button_text = button.text or button.get_attribute('value') or 'no-text'
                            print(f"🔍 Found button: '{button_text}'")
                            
                            # Look for login-related buttons
                            if any(word in button_text.lower() for word in ['login', 'sign in', 'submit']):
                                print(f"🎯 Clicking login button: '{button_text}'")
                                button.click()
                                time.sleep(5)
                                
                                # Check if login was successful
                                if "login" not in driver.current_url.lower():
                                    print("🎉 LOGIN SUCCESSFUL!")
                                    return True
                                else:
                                    print("⚠️ Still on login page")
                    except Exception as e:
                        print(f"⚠️ Button click failed: {e}")
                        continue
            else:
                print("⚠️ No credentials available")
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            
        return False
    
    def debug_search(self, driver, part_number="GCM155R71H104KE02D", manufacturer="Murata"):
        """Debug SnapEDA search process"""
        print(f"🔍 DEBUG: Testing search for {manufacturer} {part_number}...")
        
        try:
            # Try direct URL first
            direct_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
            print(f"🔗 Trying direct URL: {direct_url}")
            driver.get(direct_url)
            time.sleep(3)
            
            print(f"📍 Current URL: {driver.current_url}")
            print(f"📄 Page title: {driver.title}")
            
            # Check if we got a valid page
            if "404" in driver.title or "not found" in driver.page_source.lower():
                print("⚠️ Direct URL failed, trying search...")
                
                # Go to search
                search_url = f"https://www.snapeda.com/search?q={part_number}"
                print(f"🔗 Search URL: {search_url}")
                driver.get(search_url)
                time.sleep(3)
                
                # Look for search results
                search_results = driver.find_elements(By.XPATH, f"//a[contains(@href, '/parts/') and contains(text(), '{part_number}')]")
                print(f"🔍 Found {len(search_results)} search results")
                
                if search_results:
                    result = search_results[0]
                    result_text = result.text
                    result_href = result.get_attribute('href')
                    print(f"🎯 Clicking first result: '{result_text}' -> {result_href}")
                    result.click()
                    time.sleep(3)
                else:
                    print("❌ No search results found")
                    return False
            
            print(f"📍 Final URL: {driver.current_url}")
            
            # Debug page content
            self.debug_page_content(driver)
            
            return True
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return False
    
    def debug_page_content(self, driver):
        """Debug what's actually on the page"""
        print("\n🔍 DEBUG: Analyzing page content...")
        
        try:
            # Check for 3D model indicators
            three_d_keywords = ['3d', 'model', 'download']
            page_text = driver.page_source.lower()
            
            for keyword in three_d_keywords:
                count = page_text.count(keyword)
                print(f"📊 Keyword '{keyword}': {count} occurrences")
            
            # Find all links
            all_links = driver.find_elements(By.TAG_NAME, "a")
            print(f"\n🔗 Found {len(all_links)} links:")
            
            relevant_links = []
            for i, link in enumerate(all_links[:20]):  # First 20 links
                try:
                    if link.is_displayed():
                        text = link.text.strip()
                        href = link.get_attribute('href') or ''
                        
                        # Check if link is relevant to 3D models
                        if any(keyword in text.lower() or keyword in href.lower() 
                               for keyword in ['3d', 'model', 'download']):
                            relevant_links.append((text, href))
                            print(f"  🎯 RELEVANT Link {i+1}: '{text}' -> {href}")
                        else:
                            print(f"     Link {i+1}: '{text[:30]}' -> {href[:50]}")
                except:
                    pass
            
            # Find all buttons
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"\n🔘 Found {len(all_buttons)} buttons:")
            
            for i, button in enumerate(all_buttons[:10]):  # First 10 buttons
                try:
                    if button.is_displayed():
                        text = button.text.strip()
                        class_attr = button.get_attribute('class') or ''
                        
                        if any(keyword in text.lower() or keyword in class_attr.lower() 
                               for keyword in ['3d', 'model', 'download']):
                            print(f"  🎯 RELEVANT Button {i+1}: '{text}' class='{class_attr}'")
                        else:
                            print(f"     Button {i+1}: '{text}' class='{class_attr[:30]}'")
                except:
                    pass
            
            return relevant_links
            
        except Exception as e:
            print(f"❌ Page analysis error: {e}")
            return []
    
    def test_download(self, driver):
        """Test download functionality"""
        print("\n📥 DEBUG: Testing download functionality...")
        
        # Get initial files
        initial_files = set(os.listdir(self.download_dir)) if os.path.exists(self.download_dir) else set()
        print(f"📁 Initial files: {len(initial_files)}")
        
        # Look for download elements
        download_selectors = [
            "//a[contains(text(), 'Download') and contains(text(), '3D')]",
            "//a[contains(text(), 'Download') and contains(text(), 'Model')]",
            "//button[contains(text(), 'Download') and contains(text(), '3D')]",
            "//button[contains(text(), 'Download') and contains(text(), 'Model')]",
            "//a[contains(@href, 'download') and not(contains(text(), 'Datasheet'))]",
            "//button[contains(@class, 'download')]"
        ]
        
        download_element = None
        for selector in download_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element_text = element.text.lower()
                        if 'datasheet' not in element_text:
                            download_element = element
                            print(f"✅ Found download element: '{element.text}' with selector: {selector}")
                            break
                if download_element:
                    break
            except Exception as e:
                print(f"⚠️ Selector failed: {selector} - {e}")
        
        if download_element:
            try:
                print(f"🎯 Attempting to click: '{download_element.text}'")
                driver.execute_script("arguments[0].click();", download_element)
                print("✅ Download clicked")
                
                # Monitor for downloads
                for i in range(30):  # Wait up to 30 seconds
                    time.sleep(1)
                    current_files = set(os.listdir(self.download_dir)) if os.path.exists(self.download_dir) else set()
                    new_files = current_files - initial_files
                    
                    if new_files:
                        print(f"🎉 New files detected: {list(new_files)}")
                        return True
                    
                    if i % 5 == 0:
                        print(f"⏳ Waiting for download... ({i}/30)")
                
                print("❌ No download detected")
                return False
                
            except Exception as e:
                print(f"❌ Download click failed: {e}")
                return False
        else:
            print("❌ No download element found")
            return False

def main():
    """Main debug function"""
    print("🚀 Starting SnapEDA Debug Program...")
    
    debugger = SnapEDADebugger()
    driver = debugger.create_driver()
    
    try:
        # Test login
        if debugger.debug_login(driver):
            print("\n" + "="*50)
            
            # Test search
            if debugger.debug_search(driver):
                print("\n" + "="*50)
                
                # Test download
                debugger.test_download(driver)
        
        print("\n🏁 Debug session complete")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
