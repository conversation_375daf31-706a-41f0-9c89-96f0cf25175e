ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('/var/www/3D/0T/1378702.3.1.stp','2025-01-03T16:13:26',(
    'Author'),(''),'Open CASCADE STEP processor 6.9','FreeCAD','Unknown'
  );
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('ASSEMBLY','ASSEMBLY','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#34),#1020);
#34 = MANIFOLD_SOLID_BREP('',#35);
#35 = CLOSED_SHELL('',(#36,#156,#244,#323,#404,#480,#510,#540,#616,#692,
    #717,#793,#818,#889,#917,#949,#1006,#1013));
#36 = ADVANCED_FACE('',(#37),#51,.F.);
#37 = FACE_BOUND('',#38,.F.);
#38 = EDGE_LOOP('',(#39,#74,#102,#130));
#39 = ORIENTED_EDGE('',*,*,#40,.F.);
#40 = EDGE_CURVE('',#41,#43,#45,.T.);
#41 = VERTEX_POINT('',#42);
#42 = CARTESIAN_POINT('',(-0.3,-0.117,3.3E-02));
#43 = VERTEX_POINT('',#44);
#44 = CARTESIAN_POINT('',(-0.3,-0.117,0.297));
#45 = SURFACE_CURVE('',#46,(#50,#62),.PCURVE_S1.);
#46 = LINE('',#47,#48);
#47 = CARTESIAN_POINT('',(-0.3,-0.117,0.));
#48 = VECTOR('',#49,1.);
#49 = DIRECTION('',(0.,0.,1.));
#50 = PCURVE('',#51,#56);
#51 = PLANE('',#52);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(-0.3,-0.15,0.));
#54 = DIRECTION('',(1.,0.,0.));
#55 = DIRECTION('',(0.,0.,1.));
#56 = DEFINITIONAL_REPRESENTATION('',(#57),#61);
#57 = LINE('',#58,#59);
#58 = CARTESIAN_POINT('',(0.,-3.3E-02));
#59 = VECTOR('',#60,1.);
#60 = DIRECTION('',(1.,0.));
#61 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#62 = PCURVE('',#63,#68);
#63 = CYLINDRICAL_SURFACE('',#64,3.3E-02);
#64 = AXIS2_PLACEMENT_3D('',#65,#66,#67);
#65 = CARTESIAN_POINT('',(-0.267,-0.117,0.));
#66 = DIRECTION('',(0.,0.,1.));
#67 = DIRECTION('',(-1.,-0.,0.));
#68 = DEFINITIONAL_REPRESENTATION('',(#69),#73);
#69 = LINE('',#70,#71);
#70 = CARTESIAN_POINT('',(0.,0.));
#71 = VECTOR('',#72,1.);
#72 = DIRECTION('',(0.,1.));
#73 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#74 = ORIENTED_EDGE('',*,*,#75,.T.);
#75 = EDGE_CURVE('',#41,#76,#78,.T.);
#76 = VERTEX_POINT('',#77);
#77 = CARTESIAN_POINT('',(-0.3,0.117,3.3E-02));
#78 = SURFACE_CURVE('',#79,(#83,#90),.PCURVE_S1.);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(-0.3,-0.15,3.3E-02));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(0.,1.,0.));
#83 = PCURVE('',#51,#84);
#84 = DEFINITIONAL_REPRESENTATION('',(#85),#89);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(3.3E-02,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,-1.));
#89 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#90 = PCURVE('',#91,#96);
#91 = CYLINDRICAL_SURFACE('',#92,3.3E-02);
#92 = AXIS2_PLACEMENT_3D('',#93,#94,#95);
#93 = CARTESIAN_POINT('',(-0.267,-0.15,3.3E-02));
#94 = DIRECTION('',(0.,1.,0.));
#95 = DIRECTION('',(-1.,0.,0.));
#96 = DEFINITIONAL_REPRESENTATION('',(#97),#101);
#97 = LINE('',#98,#99);
#98 = CARTESIAN_POINT('',(-0.,0.));
#99 = VECTOR('',#100,1.);
#100 = DIRECTION('',(-0.,1.));
#101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#102 = ORIENTED_EDGE('',*,*,#103,.T.);
#103 = EDGE_CURVE('',#76,#104,#106,.T.);
#104 = VERTEX_POINT('',#105);
#105 = CARTESIAN_POINT('',(-0.3,0.117,0.297));
#106 = SURFACE_CURVE('',#107,(#111,#118),.PCURVE_S1.);
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(-0.3,0.117,0.));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(0.,0.,1.));
#111 = PCURVE('',#51,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,-0.267));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = CYLINDRICAL_SURFACE('',#120,3.3E-02);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(-0.267,0.117,0.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(-1.,-0.,0.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(-0.,0.));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(-0.,1.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#131,.F.);
#131 = EDGE_CURVE('',#43,#104,#132,.T.);
#132 = SURFACE_CURVE('',#133,(#137,#144),.PCURVE_S1.);
#133 = LINE('',#134,#135);
#134 = CARTESIAN_POINT('',(-0.3,-0.15,0.297));
#135 = VECTOR('',#136,1.);
#136 = DIRECTION('',(0.,1.,0.));
#137 = PCURVE('',#51,#138);
#138 = DEFINITIONAL_REPRESENTATION('',(#139),#143);
#139 = LINE('',#140,#141);
#140 = CARTESIAN_POINT('',(0.297,0.));
#141 = VECTOR('',#142,1.);
#142 = DIRECTION('',(0.,-1.));
#143 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#144 = PCURVE('',#145,#150);
#145 = CYLINDRICAL_SURFACE('',#146,3.3E-02);
#146 = AXIS2_PLACEMENT_3D('',#147,#148,#149);
#147 = CARTESIAN_POINT('',(-0.267,-0.15,0.297));
#148 = DIRECTION('',(0.,1.,0.));
#149 = DIRECTION('',(-1.,0.,0.));
#150 = DEFINITIONAL_REPRESENTATION('',(#151),#155);
#151 = LINE('',#152,#153);
#152 = CARTESIAN_POINT('',(0.,0.));
#153 = VECTOR('',#154,1.);
#154 = DIRECTION('',(0.,1.));
#155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#156 = ADVANCED_FACE('',(#157),#63,.T.);
#157 = FACE_BOUND('',#158,.T.);
#158 = EDGE_LOOP('',(#159,#160,#189,#217));
#159 = ORIENTED_EDGE('',*,*,#40,.F.);
#160 = ORIENTED_EDGE('',*,*,#161,.T.);
#161 = EDGE_CURVE('',#41,#162,#164,.T.);
#162 = VERTEX_POINT('',#163);
#163 = CARTESIAN_POINT('',(-0.267,-0.15,3.3E-02));
#164 = SURFACE_CURVE('',#165,(#170,#177),.PCURVE_S1.);
#165 = CIRCLE('',#166,3.3E-02);
#166 = AXIS2_PLACEMENT_3D('',#167,#168,#169);
#167 = CARTESIAN_POINT('',(-0.267,-0.117,3.3E-02));
#168 = DIRECTION('',(0.,0.,1.));
#169 = DIRECTION('',(-1.,0.,0.));
#170 = PCURVE('',#63,#171);
#171 = DEFINITIONAL_REPRESENTATION('',(#172),#176);
#172 = LINE('',#173,#174);
#173 = CARTESIAN_POINT('',(0.,3.3E-02));
#174 = VECTOR('',#175,1.);
#175 = DIRECTION('',(1.,0.));
#176 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#177 = PCURVE('',#178,#183);
#178 = SPHERICAL_SURFACE('',#179,3.3E-02);
#179 = AXIS2_PLACEMENT_3D('',#180,#181,#182);
#180 = CARTESIAN_POINT('',(-0.267,-0.117,3.3E-02));
#181 = DIRECTION('',(0.,0.,1.));
#182 = DIRECTION('',(-1.,0.,0.));
#183 = DEFINITIONAL_REPRESENTATION('',(#184),#188);
#184 = LINE('',#185,#186);
#185 = CARTESIAN_POINT('',(0.,0.));
#186 = VECTOR('',#187,1.);
#187 = DIRECTION('',(1.,0.));
#188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#189 = ORIENTED_EDGE('',*,*,#190,.T.);
#190 = EDGE_CURVE('',#162,#191,#193,.T.);
#191 = VERTEX_POINT('',#192);
#192 = CARTESIAN_POINT('',(-0.267,-0.15,0.297));
#193 = SURFACE_CURVE('',#194,(#198,#205),.PCURVE_S1.);
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(-0.267,-0.15,0.));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(0.,0.,1.));
#198 = PCURVE('',#63,#199);
#199 = DEFINITIONAL_REPRESENTATION('',(#200),#204);
#200 = LINE('',#201,#202);
#201 = CARTESIAN_POINT('',(1.570796326795,0.));
#202 = VECTOR('',#203,1.);
#203 = DIRECTION('',(0.,1.));
#204 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#205 = PCURVE('',#206,#211);
#206 = PLANE('',#207);
#207 = AXIS2_PLACEMENT_3D('',#208,#209,#210);
#208 = CARTESIAN_POINT('',(-0.3,-0.15,0.));
#209 = DIRECTION('',(0.,1.,0.));
#210 = DIRECTION('',(0.,0.,1.));
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.,3.3E-02));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = ORIENTED_EDGE('',*,*,#218,.F.);
#218 = EDGE_CURVE('',#43,#191,#219,.T.);
#219 = SURFACE_CURVE('',#220,(#225,#232),.PCURVE_S1.);
#220 = CIRCLE('',#221,3.3E-02);
#221 = AXIS2_PLACEMENT_3D('',#222,#223,#224);
#222 = CARTESIAN_POINT('',(-0.267,-0.117,0.297));
#223 = DIRECTION('',(0.,0.,1.));
#224 = DIRECTION('',(-1.,0.,0.));
#225 = PCURVE('',#63,#226);
#226 = DEFINITIONAL_REPRESENTATION('',(#227),#231);
#227 = LINE('',#228,#229);
#228 = CARTESIAN_POINT('',(0.,0.297));
#229 = VECTOR('',#230,1.);
#230 = DIRECTION('',(1.,0.));
#231 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#232 = PCURVE('',#233,#238);
#233 = SPHERICAL_SURFACE('',#234,3.3E-02);
#234 = AXIS2_PLACEMENT_3D('',#235,#236,#237);
#235 = CARTESIAN_POINT('',(-0.267,-0.117,0.297));
#236 = DIRECTION('',(-0.,-0.,-1.));
#237 = DIRECTION('',(-1.,0.,0.));
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(-0.,0.));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(-1.,0.));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = ADVANCED_FACE('',(#245),#91,.T.);
#245 = FACE_BOUND('',#246,.F.);
#246 = EDGE_LOOP('',(#247,#269,#297,#322));
#247 = ORIENTED_EDGE('',*,*,#248,.F.);
#248 = EDGE_CURVE('',#249,#41,#251,.T.);
#249 = VERTEX_POINT('',#250);
#250 = CARTESIAN_POINT('',(-0.267,-0.117,0.));
#251 = SURFACE_CURVE('',#252,(#257,#263),.PCURVE_S1.);
#252 = CIRCLE('',#253,3.3E-02);
#253 = AXIS2_PLACEMENT_3D('',#254,#255,#256);
#254 = CARTESIAN_POINT('',(-0.267,-0.117,3.3E-02));
#255 = DIRECTION('',(0.,1.,-0.));
#256 = DIRECTION('',(-1.,0.,0.));
#257 = PCURVE('',#91,#258);
#258 = DEFINITIONAL_REPRESENTATION('',(#259),#262);
#259 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#260,#261),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#260 = CARTESIAN_POINT('',(-1.570796326795,3.3E-02));
#261 = CARTESIAN_POINT('',(0.,3.3E-02));
#262 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#263 = PCURVE('',#178,#264);
#264 = DEFINITIONAL_REPRESENTATION('',(#265),#268);
#265 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#266,#267),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#266 = CARTESIAN_POINT('',(0.,-1.570796326795));
#267 = CARTESIAN_POINT('',(0.,0.));
#268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#269 = ORIENTED_EDGE('',*,*,#270,.T.);
#270 = EDGE_CURVE('',#249,#271,#273,.T.);
#271 = VERTEX_POINT('',#272);
#272 = CARTESIAN_POINT('',(-0.267,0.117,0.));
#273 = SURFACE_CURVE('',#274,(#278,#285),.PCURVE_S1.);
#274 = LINE('',#275,#276);
#275 = CARTESIAN_POINT('',(-0.267,-0.15,-6.938893903907E-18));
#276 = VECTOR('',#277,1.);
#277 = DIRECTION('',(0.,1.,0.));
#278 = PCURVE('',#91,#279);
#279 = DEFINITIONAL_REPRESENTATION('',(#280),#284);
#280 = LINE('',#281,#282);
#281 = CARTESIAN_POINT('',(-1.570796326795,0.));
#282 = VECTOR('',#283,1.);
#283 = DIRECTION('',(-0.,1.));
#284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#285 = PCURVE('',#286,#291);
#286 = PLANE('',#287);
#287 = AXIS2_PLACEMENT_3D('',#288,#289,#290);
#288 = CARTESIAN_POINT('',(-0.3,-0.15,0.));
#289 = DIRECTION('',(0.,0.,1.));
#290 = DIRECTION('',(1.,0.,0.));
#291 = DEFINITIONAL_REPRESENTATION('',(#292),#296);
#292 = LINE('',#293,#294);
#293 = CARTESIAN_POINT('',(3.3E-02,0.));
#294 = VECTOR('',#295,1.);
#295 = DIRECTION('',(0.,1.));
#296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#297 = ORIENTED_EDGE('',*,*,#298,.T.);
#298 = EDGE_CURVE('',#271,#76,#299,.T.);
#299 = SURFACE_CURVE('',#300,(#305,#311),.PCURVE_S1.);
#300 = CIRCLE('',#301,3.3E-02);
#301 = AXIS2_PLACEMENT_3D('',#302,#303,#304);
#302 = CARTESIAN_POINT('',(-0.267,0.117,3.3E-02));
#303 = DIRECTION('',(-8.410780489585E-16,1.,0.));
#304 = DIRECTION('',(-1.,-8.410780489585E-16,0.));
#305 = PCURVE('',#91,#306);
#306 = DEFINITIONAL_REPRESENTATION('',(#307),#310);
#307 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#308,#309),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#308 = CARTESIAN_POINT('',(-1.570796326795,0.267));
#309 = CARTESIAN_POINT('',(0.,0.267));
#310 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#311 = PCURVE('',#312,#317);
#312 = SPHERICAL_SURFACE('',#313,3.3E-02);
#313 = AXIS2_PLACEMENT_3D('',#314,#315,#316);
#314 = CARTESIAN_POINT('',(-0.267,0.117,3.3E-02));
#315 = DIRECTION('',(0.,0.,1.));
#316 = DIRECTION('',(-1.,-8.410780489585E-16,0.));
#317 = DEFINITIONAL_REPRESENTATION('',(#318),#321);
#318 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#319,#320),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#319 = CARTESIAN_POINT('',(0.,-1.570796326795));
#320 = CARTESIAN_POINT('',(0.,0.));
#321 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#322 = ORIENTED_EDGE('',*,*,#75,.F.);
#323 = ADVANCED_FACE('',(#324),#145,.T.);
#324 = FACE_BOUND('',#325,.T.);
#325 = EDGE_LOOP('',(#326,#348,#376,#403));
#326 = ORIENTED_EDGE('',*,*,#327,.F.);
#327 = EDGE_CURVE('',#328,#43,#330,.T.);
#328 = VERTEX_POINT('',#329);
#329 = CARTESIAN_POINT('',(-0.267,-0.117,0.33));
#330 = SURFACE_CURVE('',#331,(#336,#342),.PCURVE_S1.);
#331 = CIRCLE('',#332,3.3E-02);
#332 = AXIS2_PLACEMENT_3D('',#333,#334,#335);
#333 = CARTESIAN_POINT('',(-0.267,-0.117,0.297));
#334 = DIRECTION('',(0.,-1.,0.));
#335 = DIRECTION('',(-1.,0.,0.));
#336 = PCURVE('',#145,#337);
#337 = DEFINITIONAL_REPRESENTATION('',(#338),#341);
#338 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#339,#340),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#339 = CARTESIAN_POINT('',(1.570796326795,3.3E-02));
#340 = CARTESIAN_POINT('',(0.,3.3E-02));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = PCURVE('',#233,#343);
#343 = DEFINITIONAL_REPRESENTATION('',(#344),#347);
#344 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#345,#346),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#345 = CARTESIAN_POINT('',(0.,-1.570796326795));
#346 = CARTESIAN_POINT('',(0.,0.));
#347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#348 = ORIENTED_EDGE('',*,*,#349,.T.);
#349 = EDGE_CURVE('',#328,#350,#352,.T.);
#350 = VERTEX_POINT('',#351);
#351 = CARTESIAN_POINT('',(-0.267,0.117,0.33));
#352 = SURFACE_CURVE('',#353,(#357,#364),.PCURVE_S1.);
#353 = LINE('',#354,#355);
#354 = CARTESIAN_POINT('',(-0.267,-0.15,0.33));
#355 = VECTOR('',#356,1.);
#356 = DIRECTION('',(0.,1.,0.));
#357 = PCURVE('',#145,#358);
#358 = DEFINITIONAL_REPRESENTATION('',(#359),#363);
#359 = LINE('',#360,#361);
#360 = CARTESIAN_POINT('',(1.570796326795,0.));
#361 = VECTOR('',#362,1.);
#362 = DIRECTION('',(0.,1.));
#363 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#364 = PCURVE('',#365,#370);
#365 = PLANE('',#366);
#366 = AXIS2_PLACEMENT_3D('',#367,#368,#369);
#367 = CARTESIAN_POINT('',(-0.3,-0.15,0.33));
#368 = DIRECTION('',(0.,0.,1.));
#369 = DIRECTION('',(1.,0.,0.));
#370 = DEFINITIONAL_REPRESENTATION('',(#371),#375);
#371 = LINE('',#372,#373);
#372 = CARTESIAN_POINT('',(3.3E-02,0.));
#373 = VECTOR('',#374,1.);
#374 = DIRECTION('',(0.,1.));
#375 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#376 = ORIENTED_EDGE('',*,*,#377,.F.);
#377 = EDGE_CURVE('',#104,#350,#378,.T.);
#378 = SURFACE_CURVE('',#379,(#384,#391),.PCURVE_S1.);
#379 = CIRCLE('',#380,3.3E-02);
#380 = AXIS2_PLACEMENT_3D('',#381,#382,#383);
#381 = CARTESIAN_POINT('',(-0.267,0.117,0.297));
#382 = DIRECTION('',(0.,1.,0.));
#383 = DIRECTION('',(-1.,0.,0.));
#384 = PCURVE('',#145,#385);
#385 = DEFINITIONAL_REPRESENTATION('',(#386),#390);
#386 = LINE('',#387,#388);
#387 = CARTESIAN_POINT('',(0.,0.267));
#388 = VECTOR('',#389,1.);
#389 = DIRECTION('',(1.,0.));
#390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#391 = PCURVE('',#392,#397);
#392 = SPHERICAL_SURFACE('',#393,3.3E-02);
#393 = AXIS2_PLACEMENT_3D('',#394,#395,#396);
#394 = CARTESIAN_POINT('',(-0.267,0.117,0.297));
#395 = DIRECTION('',(-0.,-1.,-0.));
#396 = DIRECTION('',(-1.,0.,0.));
#397 = DEFINITIONAL_REPRESENTATION('',(#398),#402);
#398 = LINE('',#399,#400);
#399 = CARTESIAN_POINT('',(-0.,0.));
#400 = VECTOR('',#401,1.);
#401 = DIRECTION('',(-1.,0.));
#402 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#403 = ORIENTED_EDGE('',*,*,#131,.F.);
#404 = ADVANCED_FACE('',(#405),#119,.T.);
#405 = FACE_BOUND('',#406,.F.);
#406 = EDGE_LOOP('',(#407,#408,#432,#460));
#407 = ORIENTED_EDGE('',*,*,#103,.F.);
#408 = ORIENTED_EDGE('',*,*,#409,.T.);
#409 = EDGE_CURVE('',#76,#410,#412,.T.);
#410 = VERTEX_POINT('',#411);
#411 = CARTESIAN_POINT('',(-0.267,0.15,3.3E-02));
#412 = SURFACE_CURVE('',#413,(#418,#425),.PCURVE_S1.);
#413 = CIRCLE('',#414,3.3E-02);
#414 = AXIS2_PLACEMENT_3D('',#415,#416,#417);
#415 = CARTESIAN_POINT('',(-0.267,0.117,3.3E-02));
#416 = DIRECTION('',(-0.,0.,-1.));
#417 = DIRECTION('',(-1.,-8.410780489585E-16,0.));
#418 = PCURVE('',#119,#419);
#419 = DEFINITIONAL_REPRESENTATION('',(#420),#424);
#420 = LINE('',#421,#422);
#421 = CARTESIAN_POINT('',(-0.,3.3E-02));
#422 = VECTOR('',#423,1.);
#423 = DIRECTION('',(-1.,0.));
#424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#425 = PCURVE('',#312,#426);
#426 = DEFINITIONAL_REPRESENTATION('',(#427),#431);
#427 = LINE('',#428,#429);
#428 = CARTESIAN_POINT('',(-0.,0.));
#429 = VECTOR('',#430,1.);
#430 = DIRECTION('',(-1.,0.));
#431 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#432 = ORIENTED_EDGE('',*,*,#433,.T.);
#433 = EDGE_CURVE('',#410,#434,#436,.T.);
#434 = VERTEX_POINT('',#435);
#435 = CARTESIAN_POINT('',(-0.267,0.15,0.297));
#436 = SURFACE_CURVE('',#437,(#441,#448),.PCURVE_S1.);
#437 = LINE('',#438,#439);
#438 = CARTESIAN_POINT('',(-0.267,0.15,0.));
#439 = VECTOR('',#440,1.);
#440 = DIRECTION('',(0.,0.,1.));
#441 = PCURVE('',#119,#442);
#442 = DEFINITIONAL_REPRESENTATION('',(#443),#447);
#443 = LINE('',#444,#445);
#444 = CARTESIAN_POINT('',(-1.570796326795,0.));
#445 = VECTOR('',#446,1.);
#446 = DIRECTION('',(-0.,1.));
#447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#448 = PCURVE('',#449,#454);
#449 = PLANE('',#450);
#450 = AXIS2_PLACEMENT_3D('',#451,#452,#453);
#451 = CARTESIAN_POINT('',(-0.3,0.15,0.));
#452 = DIRECTION('',(0.,1.,0.));
#453 = DIRECTION('',(0.,0.,1.));
#454 = DEFINITIONAL_REPRESENTATION('',(#455),#459);
#455 = LINE('',#456,#457);
#456 = CARTESIAN_POINT('',(0.,3.3E-02));
#457 = VECTOR('',#458,1.);
#458 = DIRECTION('',(1.,0.));
#459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#460 = ORIENTED_EDGE('',*,*,#461,.T.);
#461 = EDGE_CURVE('',#434,#104,#462,.T.);
#462 = SURFACE_CURVE('',#463,(#468,#474),.PCURVE_S1.);
#463 = CIRCLE('',#464,3.3E-02);
#464 = AXIS2_PLACEMENT_3D('',#465,#466,#467);
#465 = CARTESIAN_POINT('',(-0.267,0.117,0.297));
#466 = DIRECTION('',(0.,0.,1.));
#467 = DIRECTION('',(-1.,0.,0.));
#468 = PCURVE('',#119,#469);
#469 = DEFINITIONAL_REPRESENTATION('',(#470),#473);
#470 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#471,#472),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#471 = CARTESIAN_POINT('',(-1.570796326795,0.297));
#472 = CARTESIAN_POINT('',(0.,0.297));
#473 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#474 = PCURVE('',#392,#475);
#475 = DEFINITIONAL_REPRESENTATION('',(#476),#479);
#476 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#477,#478),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#477 = CARTESIAN_POINT('',(0.,-1.570796326795));
#478 = CARTESIAN_POINT('',(0.,0.));
#479 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#480 = ADVANCED_FACE('',(#481),#178,.T.);
#481 = FACE_BOUND('',#482,.T.);
#482 = EDGE_LOOP('',(#483,#484,#509));
#483 = ORIENTED_EDGE('',*,*,#248,.F.);
#484 = ORIENTED_EDGE('',*,*,#485,.T.);
#485 = EDGE_CURVE('',#249,#162,#486,.T.);
#486 = SURFACE_CURVE('',#487,(#492,#498),.PCURVE_S1.);
#487 = CIRCLE('',#488,3.3E-02);
#488 = AXIS2_PLACEMENT_3D('',#489,#490,#491);
#489 = CARTESIAN_POINT('',(-0.267,-0.117,3.3E-02));
#490 = DIRECTION('',(-1.,6.123233995737E-17,0.));
#491 = DIRECTION('',(-6.123233995737E-17,-1.,0.));
#492 = PCURVE('',#178,#493);
#493 = DEFINITIONAL_REPRESENTATION('',(#494),#497);
#494 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#495,#496),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#495 = CARTESIAN_POINT('',(1.570796326795,-1.570796326795));
#496 = CARTESIAN_POINT('',(1.570796326795,0.));
#497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#498 = PCURVE('',#499,#504);
#499 = CYLINDRICAL_SURFACE('',#500,3.3E-02);
#500 = AXIS2_PLACEMENT_3D('',#501,#502,#503);
#501 = CARTESIAN_POINT('',(-0.3,-0.117,3.3E-02));
#502 = DIRECTION('',(1.,0.,0.));
#503 = DIRECTION('',(0.,-1.,0.));
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#508);
#505 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#506,#507),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#506 = CARTESIAN_POINT('',(1.570796326795,3.3E-02));
#507 = CARTESIAN_POINT('',(0.,3.3E-02));
#508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#509 = ORIENTED_EDGE('',*,*,#161,.F.);
#510 = ADVANCED_FACE('',(#511),#233,.T.);
#511 = FACE_BOUND('',#512,.F.);
#512 = EDGE_LOOP('',(#513,#514,#539));
#513 = ORIENTED_EDGE('',*,*,#327,.F.);
#514 = ORIENTED_EDGE('',*,*,#515,.T.);
#515 = EDGE_CURVE('',#328,#191,#516,.T.);
#516 = SURFACE_CURVE('',#517,(#522,#528),.PCURVE_S1.);
#517 = CIRCLE('',#518,3.3E-02);
#518 = AXIS2_PLACEMENT_3D('',#519,#520,#521);
#519 = CARTESIAN_POINT('',(-0.267,-0.117,0.297));
#520 = DIRECTION('',(1.,-6.123233995737E-17,0.));
#521 = DIRECTION('',(-6.123233995737E-17,-1.,0.));
#522 = PCURVE('',#233,#523);
#523 = DEFINITIONAL_REPRESENTATION('',(#524),#527);
#524 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#525,#526),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#525 = CARTESIAN_POINT('',(-1.570796326795,-1.570796326795));
#526 = CARTESIAN_POINT('',(-1.570796326795,0.));
#527 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#528 = PCURVE('',#529,#534);
#529 = CYLINDRICAL_SURFACE('',#530,3.3E-02);
#530 = AXIS2_PLACEMENT_3D('',#531,#532,#533);
#531 = CARTESIAN_POINT('',(-0.3,-0.117,0.297));
#532 = DIRECTION('',(1.,0.,0.));
#533 = DIRECTION('',(0.,-1.,0.));
#534 = DEFINITIONAL_REPRESENTATION('',(#535),#538);
#535 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#536,#537),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#536 = CARTESIAN_POINT('',(-1.570796326795,3.3E-02));
#537 = CARTESIAN_POINT('',(0.,3.3E-02));
#538 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#539 = ORIENTED_EDGE('',*,*,#218,.F.);
#540 = ADVANCED_FACE('',(#541),#206,.F.);
#541 = FACE_BOUND('',#542,.F.);
#542 = EDGE_LOOP('',(#543,#573,#594,#595));
#543 = ORIENTED_EDGE('',*,*,#544,.F.);
#544 = EDGE_CURVE('',#545,#547,#549,.T.);
#545 = VERTEX_POINT('',#546);
#546 = CARTESIAN_POINT('',(-0.15,-0.15,3.3E-02));
#547 = VERTEX_POINT('',#548);
#548 = CARTESIAN_POINT('',(-0.15,-0.15,0.297));
#549 = SURFACE_CURVE('',#550,(#554,#561),.PCURVE_S1.);
#550 = LINE('',#551,#552);
#551 = CARTESIAN_POINT('',(-0.15,-0.15,0.));
#552 = VECTOR('',#553,1.);
#553 = DIRECTION('',(0.,0.,1.));
#554 = PCURVE('',#206,#555);
#555 = DEFINITIONAL_REPRESENTATION('',(#556),#560);
#556 = LINE('',#557,#558);
#557 = CARTESIAN_POINT('',(0.,0.15));
#558 = VECTOR('',#559,1.);
#559 = DIRECTION('',(1.,0.));
#560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#561 = PCURVE('',#562,#567);
#562 = PLANE('',#563);
#563 = AXIS2_PLACEMENT_3D('',#564,#565,#566);
#564 = CARTESIAN_POINT('',(-0.15,-0.15,0.));
#565 = DIRECTION('',(1.,0.,0.));
#566 = DIRECTION('',(0.,0.,1.));
#567 = DEFINITIONAL_REPRESENTATION('',(#568),#572);
#568 = LINE('',#569,#570);
#569 = CARTESIAN_POINT('',(0.,0.));
#570 = VECTOR('',#571,1.);
#571 = DIRECTION('',(1.,0.));
#572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#573 = ORIENTED_EDGE('',*,*,#574,.F.);
#574 = EDGE_CURVE('',#162,#545,#575,.T.);
#575 = SURFACE_CURVE('',#576,(#580,#587),.PCURVE_S1.);
#576 = LINE('',#577,#578);
#577 = CARTESIAN_POINT('',(-0.3,-0.15,3.3E-02));
#578 = VECTOR('',#579,1.);
#579 = DIRECTION('',(1.,0.,0.));
#580 = PCURVE('',#206,#581);
#581 = DEFINITIONAL_REPRESENTATION('',(#582),#586);
#582 = LINE('',#583,#584);
#583 = CARTESIAN_POINT('',(3.3E-02,0.));
#584 = VECTOR('',#585,1.);
#585 = DIRECTION('',(0.,1.));
#586 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#587 = PCURVE('',#499,#588);
#588 = DEFINITIONAL_REPRESENTATION('',(#589),#593);
#589 = LINE('',#590,#591);
#590 = CARTESIAN_POINT('',(0.,0.));
#591 = VECTOR('',#592,1.);
#592 = DIRECTION('',(0.,1.));
#593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#594 = ORIENTED_EDGE('',*,*,#190,.T.);
#595 = ORIENTED_EDGE('',*,*,#596,.T.);
#596 = EDGE_CURVE('',#191,#547,#597,.T.);
#597 = SURFACE_CURVE('',#598,(#602,#609),.PCURVE_S1.);
#598 = LINE('',#599,#600);
#599 = CARTESIAN_POINT('',(-0.3,-0.15,0.297));
#600 = VECTOR('',#601,1.);
#601 = DIRECTION('',(1.,0.,0.));
#602 = PCURVE('',#206,#603);
#603 = DEFINITIONAL_REPRESENTATION('',(#604),#608);
#604 = LINE('',#605,#606);
#605 = CARTESIAN_POINT('',(0.297,0.));
#606 = VECTOR('',#607,1.);
#607 = DIRECTION('',(0.,1.));
#608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#609 = PCURVE('',#529,#610);
#610 = DEFINITIONAL_REPRESENTATION('',(#611),#615);
#611 = LINE('',#612,#613);
#612 = CARTESIAN_POINT('',(-0.,0.));
#613 = VECTOR('',#614,1.);
#614 = DIRECTION('',(-0.,1.));
#615 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#616 = ADVANCED_FACE('',(#617),#286,.F.);
#617 = FACE_BOUND('',#618,.F.);
#618 = EDGE_LOOP('',(#619,#644,#670,#671));
#619 = ORIENTED_EDGE('',*,*,#620,.T.);
#620 = EDGE_CURVE('',#621,#623,#625,.T.);
#621 = VERTEX_POINT('',#622);
#622 = CARTESIAN_POINT('',(-0.15,-0.117,-6.938893903907E-18));
#623 = VERTEX_POINT('',#624);
#624 = CARTESIAN_POINT('',(-0.15,0.117,-6.938893903907E-18));
#625 = SURFACE_CURVE('',#626,(#630,#637),.PCURVE_S1.);
#626 = LINE('',#627,#628);
#627 = CARTESIAN_POINT('',(-0.15,-0.15,0.));
#628 = VECTOR('',#629,1.);
#629 = DIRECTION('',(0.,1.,0.));
#630 = PCURVE('',#286,#631);
#631 = DEFINITIONAL_REPRESENTATION('',(#632),#636);
#632 = LINE('',#633,#634);
#633 = CARTESIAN_POINT('',(0.15,0.));
#634 = VECTOR('',#635,1.);
#635 = DIRECTION('',(0.,1.));
#636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#637 = PCURVE('',#562,#638);
#638 = DEFINITIONAL_REPRESENTATION('',(#639),#643);
#639 = LINE('',#640,#641);
#640 = CARTESIAN_POINT('',(0.,0.));
#641 = VECTOR('',#642,1.);
#642 = DIRECTION('',(0.,-1.));
#643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#644 = ORIENTED_EDGE('',*,*,#645,.F.);
#645 = EDGE_CURVE('',#271,#623,#646,.T.);
#646 = SURFACE_CURVE('',#647,(#651,#658),.PCURVE_S1.);
#647 = LINE('',#648,#649);
#648 = CARTESIAN_POINT('',(-0.3,0.117,-6.938893903907E-18));
#649 = VECTOR('',#650,1.);
#650 = DIRECTION('',(1.,0.,0.));
#651 = PCURVE('',#286,#652);
#652 = DEFINITIONAL_REPRESENTATION('',(#653),#657);
#653 = LINE('',#654,#655);
#654 = CARTESIAN_POINT('',(0.,0.267));
#655 = VECTOR('',#656,1.);
#656 = DIRECTION('',(1.,0.));
#657 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#658 = PCURVE('',#659,#664);
#659 = CYLINDRICAL_SURFACE('',#660,3.3E-02);
#660 = AXIS2_PLACEMENT_3D('',#661,#662,#663);
#661 = CARTESIAN_POINT('',(-0.3,0.117,3.3E-02));
#662 = DIRECTION('',(1.,0.,0.));
#663 = DIRECTION('',(-0.,1.,0.));
#664 = DEFINITIONAL_REPRESENTATION('',(#665),#669);
#665 = LINE('',#666,#667);
#666 = CARTESIAN_POINT('',(-1.570796326795,0.));
#667 = VECTOR('',#668,1.);
#668 = DIRECTION('',(-0.,1.));
#669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#670 = ORIENTED_EDGE('',*,*,#270,.F.);
#671 = ORIENTED_EDGE('',*,*,#672,.T.);
#672 = EDGE_CURVE('',#249,#621,#673,.T.);
#673 = SURFACE_CURVE('',#674,(#678,#685),.PCURVE_S1.);
#674 = LINE('',#675,#676);
#675 = CARTESIAN_POINT('',(-0.3,-0.117,-6.938893903907E-18));
#676 = VECTOR('',#677,1.);
#677 = DIRECTION('',(1.,0.,0.));
#678 = PCURVE('',#286,#679);
#679 = DEFINITIONAL_REPRESENTATION('',(#680),#684);
#680 = LINE('',#681,#682);
#681 = CARTESIAN_POINT('',(0.,3.3E-02));
#682 = VECTOR('',#683,1.);
#683 = DIRECTION('',(1.,0.));
#684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#685 = PCURVE('',#499,#686);
#686 = DEFINITIONAL_REPRESENTATION('',(#687),#691);
#687 = LINE('',#688,#689);
#688 = CARTESIAN_POINT('',(1.570796326795,0.));
#689 = VECTOR('',#690,1.);
#690 = DIRECTION('',(0.,1.));
#691 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#692 = ADVANCED_FACE('',(#693),#312,.T.);
#693 = FACE_BOUND('',#694,.F.);
#694 = EDGE_LOOP('',(#695,#696,#716));
#695 = ORIENTED_EDGE('',*,*,#298,.F.);
#696 = ORIENTED_EDGE('',*,*,#697,.T.);
#697 = EDGE_CURVE('',#271,#410,#698,.T.);
#698 = SURFACE_CURVE('',#699,(#704,#710),.PCURVE_S1.);
#699 = CIRCLE('',#700,3.3E-02);
#700 = AXIS2_PLACEMENT_3D('',#701,#702,#703);
#701 = CARTESIAN_POINT('',(-0.267,0.117,3.3E-02));
#702 = DIRECTION('',(1.,1.413196921569E-17,-0.));
#703 = DIRECTION('',(-1.413196921569E-17,1.,-0.));
#704 = PCURVE('',#312,#705);
#705 = DEFINITIONAL_REPRESENTATION('',(#706),#709);
#706 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#707,#708),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#707 = CARTESIAN_POINT('',(-1.570796326795,-1.570796326795));
#708 = CARTESIAN_POINT('',(-1.570796326795,0.));
#709 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#710 = PCURVE('',#659,#711);
#711 = DEFINITIONAL_REPRESENTATION('',(#712),#715);
#712 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#713,#714),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#713 = CARTESIAN_POINT('',(-1.570796326795,3.3E-02));
#714 = CARTESIAN_POINT('',(0.,3.3E-02));
#715 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#716 = ORIENTED_EDGE('',*,*,#409,.F.);
#717 = ADVANCED_FACE('',(#718),#365,.T.);
#718 = FACE_BOUND('',#719,.T.);
#719 = EDGE_LOOP('',(#720,#745,#771,#772));
#720 = ORIENTED_EDGE('',*,*,#721,.T.);
#721 = EDGE_CURVE('',#722,#724,#726,.T.);
#722 = VERTEX_POINT('',#723);
#723 = CARTESIAN_POINT('',(-0.15,-0.117,0.33));
#724 = VERTEX_POINT('',#725);
#725 = CARTESIAN_POINT('',(-0.15,0.117,0.33));
#726 = SURFACE_CURVE('',#727,(#731,#738),.PCURVE_S1.);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(-0.15,-0.15,0.33));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(0.,1.,0.));
#731 = PCURVE('',#365,#732);
#732 = DEFINITIONAL_REPRESENTATION('',(#733),#737);
#733 = LINE('',#734,#735);
#734 = CARTESIAN_POINT('',(0.15,0.));
#735 = VECTOR('',#736,1.);
#736 = DIRECTION('',(0.,1.));
#737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#738 = PCURVE('',#562,#739);
#739 = DEFINITIONAL_REPRESENTATION('',(#740),#744);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(0.33,0.));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(0.,-1.));
#744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#745 = ORIENTED_EDGE('',*,*,#746,.F.);
#746 = EDGE_CURVE('',#350,#724,#747,.T.);
#747 = SURFACE_CURVE('',#748,(#752,#759),.PCURVE_S1.);
#748 = LINE('',#749,#750);
#749 = CARTESIAN_POINT('',(-0.3,0.117,0.33));
#750 = VECTOR('',#751,1.);
#751 = DIRECTION('',(1.,0.,0.));
#752 = PCURVE('',#365,#753);
#753 = DEFINITIONAL_REPRESENTATION('',(#754),#758);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(0.,0.267));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(1.,0.));
#758 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#759 = PCURVE('',#760,#765);
#760 = CYLINDRICAL_SURFACE('',#761,3.3E-02);
#761 = AXIS2_PLACEMENT_3D('',#762,#763,#764);
#762 = CARTESIAN_POINT('',(-0.3,0.117,0.297));
#763 = DIRECTION('',(1.,0.,0.));
#764 = DIRECTION('',(-0.,1.,0.));
#765 = DEFINITIONAL_REPRESENTATION('',(#766),#770);
#766 = LINE('',#767,#768);
#767 = CARTESIAN_POINT('',(1.570796326795,0.));
#768 = VECTOR('',#769,1.);
#769 = DIRECTION('',(0.,1.));
#770 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#771 = ORIENTED_EDGE('',*,*,#349,.F.);
#772 = ORIENTED_EDGE('',*,*,#773,.T.);
#773 = EDGE_CURVE('',#328,#722,#774,.T.);
#774 = SURFACE_CURVE('',#775,(#779,#786),.PCURVE_S1.);
#775 = LINE('',#776,#777);
#776 = CARTESIAN_POINT('',(-0.3,-0.117,0.33));
#777 = VECTOR('',#778,1.);
#778 = DIRECTION('',(1.,0.,0.));
#779 = PCURVE('',#365,#780);
#780 = DEFINITIONAL_REPRESENTATION('',(#781),#785);
#781 = LINE('',#782,#783);
#782 = CARTESIAN_POINT('',(0.,3.3E-02));
#783 = VECTOR('',#784,1.);
#784 = DIRECTION('',(1.,0.));
#785 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#786 = PCURVE('',#529,#787);
#787 = DEFINITIONAL_REPRESENTATION('',(#788),#792);
#788 = LINE('',#789,#790);
#789 = CARTESIAN_POINT('',(-1.570796326795,0.));
#790 = VECTOR('',#791,1.);
#791 = DIRECTION('',(-0.,1.));
#792 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#793 = ADVANCED_FACE('',(#794),#392,.T.);
#794 = FACE_BOUND('',#795,.F.);
#795 = EDGE_LOOP('',(#796,#797,#817));
#796 = ORIENTED_EDGE('',*,*,#461,.F.);
#797 = ORIENTED_EDGE('',*,*,#798,.T.);
#798 = EDGE_CURVE('',#434,#350,#799,.T.);
#799 = SURFACE_CURVE('',#800,(#805,#811),.PCURVE_S1.);
#800 = CIRCLE('',#801,3.3E-02);
#801 = AXIS2_PLACEMENT_3D('',#802,#803,#804);
#802 = CARTESIAN_POINT('',(-0.267,0.117,0.297));
#803 = DIRECTION('',(1.,0.,6.123233995737E-17));
#804 = DIRECTION('',(-6.123233995737E-17,0.,1.));
#805 = PCURVE('',#392,#806);
#806 = DEFINITIONAL_REPRESENTATION('',(#807),#810);
#807 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#808,#809),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#808 = CARTESIAN_POINT('',(-1.570796326795,-1.570796326795));
#809 = CARTESIAN_POINT('',(-1.570796326795,0.));
#810 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#811 = PCURVE('',#760,#812);
#812 = DEFINITIONAL_REPRESENTATION('',(#813),#816);
#813 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#814,#815),.UNSPECIFIED.,.F.,.F.,
  (2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#814 = CARTESIAN_POINT('',(0.,3.3E-02));
#815 = CARTESIAN_POINT('',(1.570796326795,3.3E-02));
#816 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#817 = ORIENTED_EDGE('',*,*,#377,.F.);
#818 = ADVANCED_FACE('',(#819),#449,.T.);
#819 = FACE_BOUND('',#820,.T.);
#820 = EDGE_LOOP('',(#821,#846,#867,#868));
#821 = ORIENTED_EDGE('',*,*,#822,.F.);
#822 = EDGE_CURVE('',#823,#825,#827,.T.);
#823 = VERTEX_POINT('',#824);
#824 = CARTESIAN_POINT('',(-0.15,0.15,3.3E-02));
#825 = VERTEX_POINT('',#826);
#826 = CARTESIAN_POINT('',(-0.15,0.15,0.297));
#827 = SURFACE_CURVE('',#828,(#832,#839),.PCURVE_S1.);
#828 = LINE('',#829,#830);
#829 = CARTESIAN_POINT('',(-0.15,0.15,0.));
#830 = VECTOR('',#831,1.);
#831 = DIRECTION('',(0.,0.,1.));
#832 = PCURVE('',#449,#833);
#833 = DEFINITIONAL_REPRESENTATION('',(#834),#838);
#834 = LINE('',#835,#836);
#835 = CARTESIAN_POINT('',(0.,0.15));
#836 = VECTOR('',#837,1.);
#837 = DIRECTION('',(1.,0.));
#838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#839 = PCURVE('',#562,#840);
#840 = DEFINITIONAL_REPRESENTATION('',(#841),#845);
#841 = LINE('',#842,#843);
#842 = CARTESIAN_POINT('',(0.,-0.3));
#843 = VECTOR('',#844,1.);
#844 = DIRECTION('',(1.,0.));
#845 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#846 = ORIENTED_EDGE('',*,*,#847,.F.);
#847 = EDGE_CURVE('',#410,#823,#848,.T.);
#848 = SURFACE_CURVE('',#849,(#853,#860),.PCURVE_S1.);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(-0.3,0.15,3.3E-02));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(1.,0.,0.));
#853 = PCURVE('',#449,#854);
#854 = DEFINITIONAL_REPRESENTATION('',(#855),#859);
#855 = LINE('',#856,#857);
#856 = CARTESIAN_POINT('',(3.3E-02,0.));
#857 = VECTOR('',#858,1.);
#858 = DIRECTION('',(0.,1.));
#859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#860 = PCURVE('',#659,#861);
#861 = DEFINITIONAL_REPRESENTATION('',(#862),#866);
#862 = LINE('',#863,#864);
#863 = CARTESIAN_POINT('',(-0.,0.));
#864 = VECTOR('',#865,1.);
#865 = DIRECTION('',(-0.,1.));
#866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#867 = ORIENTED_EDGE('',*,*,#433,.T.);
#868 = ORIENTED_EDGE('',*,*,#869,.T.);
#869 = EDGE_CURVE('',#434,#825,#870,.T.);
#870 = SURFACE_CURVE('',#871,(#875,#882),.PCURVE_S1.);
#871 = LINE('',#872,#873);
#872 = CARTESIAN_POINT('',(-0.3,0.15,0.297));
#873 = VECTOR('',#874,1.);
#874 = DIRECTION('',(1.,0.,0.));
#875 = PCURVE('',#449,#876);
#876 = DEFINITIONAL_REPRESENTATION('',(#877),#881);
#877 = LINE('',#878,#879);
#878 = CARTESIAN_POINT('',(0.297,0.));
#879 = VECTOR('',#880,1.);
#880 = DIRECTION('',(0.,1.));
#881 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#882 = PCURVE('',#760,#883);
#883 = DEFINITIONAL_REPRESENTATION('',(#884),#888);
#884 = LINE('',#885,#886);
#885 = CARTESIAN_POINT('',(0.,0.));
#886 = VECTOR('',#887,1.);
#887 = DIRECTION('',(0.,1.));
#888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#889 = ADVANCED_FACE('',(#890),#499,.T.);
#890 = FACE_BOUND('',#891,.T.);
#891 = EDGE_LOOP('',(#892,#893,#894,#916));
#892 = ORIENTED_EDGE('',*,*,#485,.F.);
#893 = ORIENTED_EDGE('',*,*,#672,.T.);
#894 = ORIENTED_EDGE('',*,*,#895,.F.);
#895 = EDGE_CURVE('',#545,#621,#896,.T.);
#896 = SURFACE_CURVE('',#897,(#902,#909),.PCURVE_S1.);
#897 = CIRCLE('',#898,3.3E-02);
#898 = AXIS2_PLACEMENT_3D('',#899,#900,#901);
#899 = CARTESIAN_POINT('',(-0.15,-0.117,3.3E-02));
#900 = DIRECTION('',(1.,0.,0.));
#901 = DIRECTION('',(-0.,0.,1.));
#902 = PCURVE('',#499,#903);
#903 = DEFINITIONAL_REPRESENTATION('',(#904),#908);
#904 = LINE('',#905,#906);
#905 = CARTESIAN_POINT('',(-1.570796326795,0.15));
#906 = VECTOR('',#907,1.);
#907 = DIRECTION('',(1.,0.));
#908 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#909 = PCURVE('',#562,#910);
#910 = DEFINITIONAL_REPRESENTATION('',(#911),#915);
#911 = CIRCLE('',#912,3.3E-02);
#912 = AXIS2_PLACEMENT_2D('',#913,#914);
#913 = CARTESIAN_POINT('',(3.3E-02,-3.3E-02));
#914 = DIRECTION('',(1.,0.));
#915 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#916 = ORIENTED_EDGE('',*,*,#574,.F.);
#917 = ADVANCED_FACE('',(#918),#529,.T.);
#918 = FACE_BOUND('',#919,.F.);
#919 = EDGE_LOOP('',(#920,#921,#922,#948));
#920 = ORIENTED_EDGE('',*,*,#515,.F.);
#921 = ORIENTED_EDGE('',*,*,#773,.T.);
#922 = ORIENTED_EDGE('',*,*,#923,.F.);
#923 = EDGE_CURVE('',#547,#722,#924,.T.);
#924 = SURFACE_CURVE('',#925,(#930,#937),.PCURVE_S1.);
#925 = CIRCLE('',#926,3.3E-02);
#926 = AXIS2_PLACEMENT_3D('',#927,#928,#929);
#927 = CARTESIAN_POINT('',(-0.15,-0.117,0.297));
#928 = DIRECTION('',(-1.,0.,-0.));
#929 = DIRECTION('',(-0.,0.,1.));
#930 = PCURVE('',#529,#931);
#931 = DEFINITIONAL_REPRESENTATION('',(#932),#936);
#932 = LINE('',#933,#934);
#933 = CARTESIAN_POINT('',(4.712388980385,0.15));
#934 = VECTOR('',#935,1.);
#935 = DIRECTION('',(-1.,0.));
#936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#937 = PCURVE('',#562,#938);
#938 = DEFINITIONAL_REPRESENTATION('',(#939),#947);
#939 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#940,#941,#942,#943,#944,#945
,#946),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#940 = CARTESIAN_POINT('',(0.33,-3.3E-02));
#941 = CARTESIAN_POINT('',(0.33,-9.015767664977E-02));
#942 = CARTESIAN_POINT('',(0.2805,-6.157883832489E-02));
#943 = CARTESIAN_POINT('',(0.231,-3.3E-02));
#944 = CARTESIAN_POINT('',(0.2805,-4.421161675114E-03));
#945 = CARTESIAN_POINT('',(0.33,2.415767664977E-02));
#946 = CARTESIAN_POINT('',(0.33,-3.3E-02));
#947 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#948 = ORIENTED_EDGE('',*,*,#596,.F.);
#949 = ADVANCED_FACE('',(#950),#562,.T.);
#950 = FACE_BOUND('',#951,.T.);
#951 = EDGE_LOOP('',(#952,#953,#954,#955,#981,#982,#1004,#1005));
#952 = ORIENTED_EDGE('',*,*,#544,.F.);
#953 = ORIENTED_EDGE('',*,*,#895,.T.);
#954 = ORIENTED_EDGE('',*,*,#620,.T.);
#955 = ORIENTED_EDGE('',*,*,#956,.F.);
#956 = EDGE_CURVE('',#823,#623,#957,.T.);
#957 = SURFACE_CURVE('',#958,(#963,#974),.PCURVE_S1.);
#958 = CIRCLE('',#959,3.3E-02);
#959 = AXIS2_PLACEMENT_3D('',#960,#961,#962);
#960 = CARTESIAN_POINT('',(-0.15,0.117,3.3E-02));
#961 = DIRECTION('',(-1.,0.,-0.));
#962 = DIRECTION('',(-0.,0.,1.));
#963 = PCURVE('',#562,#964);
#964 = DEFINITIONAL_REPRESENTATION('',(#965),#973);
#965 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#966,#967,#968,#969,#970,#971
,#972),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#966 = CARTESIAN_POINT('',(6.6E-02,-0.267));
#967 = CARTESIAN_POINT('',(6.6E-02,-0.32415767665));
#968 = CARTESIAN_POINT('',(1.65E-02,-0.295578838325));
#969 = CARTESIAN_POINT('',(-3.3E-02,-0.267));
#970 = CARTESIAN_POINT('',(1.65E-02,-0.238421161675));
#971 = CARTESIAN_POINT('',(6.6E-02,-0.20984232335));
#972 = CARTESIAN_POINT('',(6.6E-02,-0.267));
#973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#974 = PCURVE('',#659,#975);
#975 = DEFINITIONAL_REPRESENTATION('',(#976),#980);
#976 = LINE('',#977,#978);
#977 = CARTESIAN_POINT('',(1.570796326795,0.15));
#978 = VECTOR('',#979,1.);
#979 = DIRECTION('',(-1.,0.));
#980 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#981 = ORIENTED_EDGE('',*,*,#822,.T.);
#982 = ORIENTED_EDGE('',*,*,#983,.T.);
#983 = EDGE_CURVE('',#825,#724,#984,.T.);
#984 = SURFACE_CURVE('',#985,(#990,#997),.PCURVE_S1.);
#985 = CIRCLE('',#986,3.3E-02);
#986 = AXIS2_PLACEMENT_3D('',#987,#988,#989);
#987 = CARTESIAN_POINT('',(-0.15,0.117,0.297));
#988 = DIRECTION('',(1.,0.,0.));
#989 = DIRECTION('',(-0.,0.,1.));
#990 = PCURVE('',#562,#991);
#991 = DEFINITIONAL_REPRESENTATION('',(#992),#996);
#992 = CIRCLE('',#993,3.3E-02);
#993 = AXIS2_PLACEMENT_2D('',#994,#995);
#994 = CARTESIAN_POINT('',(0.297,-0.267));
#995 = DIRECTION('',(1.,0.));
#996 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#997 = PCURVE('',#760,#998);
#998 = DEFINITIONAL_REPRESENTATION('',(#999),#1003);
#999 = LINE('',#1000,#1001);
#1000 = CARTESIAN_POINT('',(-4.712388980385,0.15));
#1001 = VECTOR('',#1002,1.);
#1002 = DIRECTION('',(1.,0.));
#1003 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1004 = ORIENTED_EDGE('',*,*,#721,.F.);
#1005 = ORIENTED_EDGE('',*,*,#923,.F.);
#1006 = ADVANCED_FACE('',(#1007),#659,.T.);
#1007 = FACE_BOUND('',#1008,.F.);
#1008 = EDGE_LOOP('',(#1009,#1010,#1011,#1012));
#1009 = ORIENTED_EDGE('',*,*,#697,.F.);
#1010 = ORIENTED_EDGE('',*,*,#645,.T.);
#1011 = ORIENTED_EDGE('',*,*,#956,.F.);
#1012 = ORIENTED_EDGE('',*,*,#847,.F.);
#1013 = ADVANCED_FACE('',(#1014),#760,.T.);
#1014 = FACE_BOUND('',#1015,.T.);
#1015 = EDGE_LOOP('',(#1016,#1017,#1018,#1019));
#1016 = ORIENTED_EDGE('',*,*,#798,.T.);
#1017 = ORIENTED_EDGE('',*,*,#746,.T.);
#1018 = ORIENTED_EDGE('',*,*,#983,.F.);
#1019 = ORIENTED_EDGE('',*,*,#869,.F.);
#1020 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1024)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1021,#1022,#1023)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1021 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1022 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1023 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1024 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1021,
  'distance_accuracy_value','confusion accuracy');
#1025 = SHAPE_DEFINITION_REPRESENTATION(#1026,#33);
#1026 = PRODUCT_DEFINITION_SHAPE('','',#1027);
#1027 = PRODUCT_DEFINITION('design','',#1028,#1031);
#1028 = PRODUCT_DEFINITION_FORMATION('','',#1029);
#1029 = PRODUCT('Fillet1','Fillet1','',(#1030));
#1030 = PRODUCT_CONTEXT('',#2,'mechanical');
#1031 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1032 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1033,#1035);
#1033 = ( REPRESENTATION_RELATIONSHIP('','',#33,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1034) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1034 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#1035 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1036);
#1036 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','=>[0:1:1:2]','',#5,#1027,$);
#1037 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1029));
#1038 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1039),#2025);
#1039 = MANIFOLD_SOLID_BREP('',#1040);
#1040 = CLOSED_SHELL('',(#1041,#1285,#1361,#1414,#1467,#1521,#1575,#1628
    ,#1681,#1713,#1791,#1816,#1841,#1889,#1939,#1966,#1991,#2018));
#1041 = ADVANCED_FACE('',(#1042),#1056,.F.);
#1042 = FACE_BOUND('',#1043,.F.);
#1043 = EDGE_LOOP('',(#1044,#1079,#1108,#1136,#1169,#1197,#1226,#1254));
#1044 = ORIENTED_EDGE('',*,*,#1045,.F.);
#1045 = EDGE_CURVE('',#1046,#1048,#1050,.T.);
#1046 = VERTEX_POINT('',#1047);
#1047 = CARTESIAN_POINT('',(0.15,-0.15,3.3E-02));
#1048 = VERTEX_POINT('',#1049);
#1049 = CARTESIAN_POINT('',(0.15,-0.15,0.297));
#1050 = SURFACE_CURVE('',#1051,(#1055,#1067),.PCURVE_S1.);
#1051 = LINE('',#1052,#1053);
#1052 = CARTESIAN_POINT('',(0.15,-0.15,0.));
#1053 = VECTOR('',#1054,1.);
#1054 = DIRECTION('',(0.,0.,1.));
#1055 = PCURVE('',#1056,#1061);
#1056 = PLANE('',#1057);
#1057 = AXIS2_PLACEMENT_3D('',#1058,#1059,#1060);
#1058 = CARTESIAN_POINT('',(0.15,-0.15,0.));
#1059 = DIRECTION('',(1.,0.,0.));
#1060 = DIRECTION('',(0.,0.,1.));
#1061 = DEFINITIONAL_REPRESENTATION('',(#1062),#1066);
#1062 = LINE('',#1063,#1064);
#1063 = CARTESIAN_POINT('',(0.,0.));
#1064 = VECTOR('',#1065,1.);
#1065 = DIRECTION('',(1.,0.));
#1066 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1067 = PCURVE('',#1068,#1073);
#1068 = PLANE('',#1069);
#1069 = AXIS2_PLACEMENT_3D('',#1070,#1071,#1072);
#1070 = CARTESIAN_POINT('',(0.15,-0.15,0.));
#1071 = DIRECTION('',(0.,1.,0.));
#1072 = DIRECTION('',(0.,0.,1.));
#1073 = DEFINITIONAL_REPRESENTATION('',(#1074),#1078);
#1074 = LINE('',#1075,#1076);
#1075 = CARTESIAN_POINT('',(0.,0.));
#1076 = VECTOR('',#1077,1.);
#1077 = DIRECTION('',(1.,0.));
#1078 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1079 = ORIENTED_EDGE('',*,*,#1080,.T.);
#1080 = EDGE_CURVE('',#1046,#1081,#1083,.T.);
#1081 = VERTEX_POINT('',#1082);
#1082 = CARTESIAN_POINT('',(0.15,-0.117,-6.938893903907E-18));
#1083 = SURFACE_CURVE('',#1084,(#1089,#1096),.PCURVE_S1.);
#1084 = CIRCLE('',#1085,3.3E-02);
#1085 = AXIS2_PLACEMENT_3D('',#1086,#1087,#1088);
#1086 = CARTESIAN_POINT('',(0.15,-0.117,3.3E-02));
#1087 = DIRECTION('',(1.,0.,0.));
#1088 = DIRECTION('',(-0.,0.,1.));
#1089 = PCURVE('',#1056,#1090);
#1090 = DEFINITIONAL_REPRESENTATION('',(#1091),#1095);
#1091 = CIRCLE('',#1092,3.3E-02);
#1092 = AXIS2_PLACEMENT_2D('',#1093,#1094);
#1093 = CARTESIAN_POINT('',(3.3E-02,-3.3E-02));
#1094 = DIRECTION('',(1.,0.));
#1095 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1096 = PCURVE('',#1097,#1102);
#1097 = CYLINDRICAL_SURFACE('',#1098,3.3E-02);
#1098 = AXIS2_PLACEMENT_3D('',#1099,#1100,#1101);
#1099 = CARTESIAN_POINT('',(0.15,-0.117,3.3E-02));
#1100 = DIRECTION('',(1.,0.,0.));
#1101 = DIRECTION('',(0.,-1.,0.));
#1102 = DEFINITIONAL_REPRESENTATION('',(#1103),#1107);
#1103 = LINE('',#1104,#1105);
#1104 = CARTESIAN_POINT('',(-1.570796326795,0.));
#1105 = VECTOR('',#1106,1.);
#1106 = DIRECTION('',(1.,0.));
#1107 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1108 = ORIENTED_EDGE('',*,*,#1109,.T.);
#1109 = EDGE_CURVE('',#1081,#1110,#1112,.T.);
#1110 = VERTEX_POINT('',#1111);
#1111 = CARTESIAN_POINT('',(0.15,0.117,-6.938893903907E-18));
#1112 = SURFACE_CURVE('',#1113,(#1117,#1124),.PCURVE_S1.);
#1113 = LINE('',#1114,#1115);
#1114 = CARTESIAN_POINT('',(0.15,-0.15,0.));
#1115 = VECTOR('',#1116,1.);
#1116 = DIRECTION('',(0.,1.,0.));
#1117 = PCURVE('',#1056,#1118);
#1118 = DEFINITIONAL_REPRESENTATION('',(#1119),#1123);
#1119 = LINE('',#1120,#1121);
#1120 = CARTESIAN_POINT('',(0.,0.));
#1121 = VECTOR('',#1122,1.);
#1122 = DIRECTION('',(0.,-1.));
#1123 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1124 = PCURVE('',#1125,#1130);
#1125 = PLANE('',#1126);
#1126 = AXIS2_PLACEMENT_3D('',#1127,#1128,#1129);
#1127 = CARTESIAN_POINT('',(0.15,-0.15,0.));
#1128 = DIRECTION('',(0.,0.,1.));
#1129 = DIRECTION('',(1.,0.,0.));
#1130 = DEFINITIONAL_REPRESENTATION('',(#1131),#1135);
#1131 = LINE('',#1132,#1133);
#1132 = CARTESIAN_POINT('',(0.,0.));
#1133 = VECTOR('',#1134,1.);
#1134 = DIRECTION('',(0.,1.));
#1135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1136 = ORIENTED_EDGE('',*,*,#1137,.F.);
#1137 = EDGE_CURVE('',#1138,#1110,#1140,.T.);
#1138 = VERTEX_POINT('',#1139);
#1139 = CARTESIAN_POINT('',(0.15,0.15,3.3E-02));
#1140 = SURFACE_CURVE('',#1141,(#1146,#1157),.PCURVE_S1.);
#1141 = CIRCLE('',#1142,3.3E-02);
#1142 = AXIS2_PLACEMENT_3D('',#1143,#1144,#1145);
#1143 = CARTESIAN_POINT('',(0.15,0.117,3.3E-02));
#1144 = DIRECTION('',(-1.,0.,-0.));
#1145 = DIRECTION('',(-0.,0.,1.));
#1146 = PCURVE('',#1056,#1147);
#1147 = DEFINITIONAL_REPRESENTATION('',(#1148),#1156);
#1148 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1149,#1150,#1151,#1152,
#1153,#1154,#1155),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1149 = CARTESIAN_POINT('',(6.6E-02,-0.267));
#1150 = CARTESIAN_POINT('',(6.6E-02,-0.32415767665));
#1151 = CARTESIAN_POINT('',(1.65E-02,-0.295578838325));
#1152 = CARTESIAN_POINT('',(-3.3E-02,-0.267));
#1153 = CARTESIAN_POINT('',(1.65E-02,-0.238421161675));
#1154 = CARTESIAN_POINT('',(6.6E-02,-0.20984232335));
#1155 = CARTESIAN_POINT('',(6.6E-02,-0.267));
#1156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1157 = PCURVE('',#1158,#1163);
#1158 = CYLINDRICAL_SURFACE('',#1159,3.3E-02);
#1159 = AXIS2_PLACEMENT_3D('',#1160,#1161,#1162);
#1160 = CARTESIAN_POINT('',(0.15,0.117,3.3E-02));
#1161 = DIRECTION('',(1.,0.,0.));
#1162 = DIRECTION('',(-0.,1.,0.));
#1163 = DEFINITIONAL_REPRESENTATION('',(#1164),#1168);
#1164 = LINE('',#1165,#1166);
#1165 = CARTESIAN_POINT('',(1.570796326795,-0.));
#1166 = VECTOR('',#1167,1.);
#1167 = DIRECTION('',(-1.,0.));
#1168 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1169 = ORIENTED_EDGE('',*,*,#1170,.T.);
#1170 = EDGE_CURVE('',#1138,#1171,#1173,.T.);
#1171 = VERTEX_POINT('',#1172);
#1172 = CARTESIAN_POINT('',(0.15,0.15,0.297));
#1173 = SURFACE_CURVE('',#1174,(#1178,#1185),.PCURVE_S1.);
#1174 = LINE('',#1175,#1176);
#1175 = CARTESIAN_POINT('',(0.15,0.15,0.));
#1176 = VECTOR('',#1177,1.);
#1177 = DIRECTION('',(0.,0.,1.));
#1178 = PCURVE('',#1056,#1179);
#1179 = DEFINITIONAL_REPRESENTATION('',(#1180),#1184);
#1180 = LINE('',#1181,#1182);
#1181 = CARTESIAN_POINT('',(0.,-0.3));
#1182 = VECTOR('',#1183,1.);
#1183 = DIRECTION('',(1.,0.));
#1184 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1185 = PCURVE('',#1186,#1191);
#1186 = PLANE('',#1187);
#1187 = AXIS2_PLACEMENT_3D('',#1188,#1189,#1190);
#1188 = CARTESIAN_POINT('',(0.15,0.15,0.));
#1189 = DIRECTION('',(0.,1.,0.));
#1190 = DIRECTION('',(0.,0.,1.));
#1191 = DEFINITIONAL_REPRESENTATION('',(#1192),#1196);
#1192 = LINE('',#1193,#1194);
#1193 = CARTESIAN_POINT('',(0.,0.));
#1194 = VECTOR('',#1195,1.);
#1195 = DIRECTION('',(1.,0.));
#1196 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1197 = ORIENTED_EDGE('',*,*,#1198,.T.);
#1198 = EDGE_CURVE('',#1171,#1199,#1201,.T.);
#1199 = VERTEX_POINT('',#1200);
#1200 = CARTESIAN_POINT('',(0.15,0.117,0.33));
#1201 = SURFACE_CURVE('',#1202,(#1207,#1214),.PCURVE_S1.);
#1202 = CIRCLE('',#1203,3.3E-02);
#1203 = AXIS2_PLACEMENT_3D('',#1204,#1205,#1206);
#1204 = CARTESIAN_POINT('',(0.15,0.117,0.297));
#1205 = DIRECTION('',(1.,0.,0.));
#1206 = DIRECTION('',(-0.,0.,1.));
#1207 = PCURVE('',#1056,#1208);
#1208 = DEFINITIONAL_REPRESENTATION('',(#1209),#1213);
#1209 = CIRCLE('',#1210,3.3E-02);
#1210 = AXIS2_PLACEMENT_2D('',#1211,#1212);
#1211 = CARTESIAN_POINT('',(0.297,-0.267));
#1212 = DIRECTION('',(1.,0.));
#1213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1214 = PCURVE('',#1215,#1220);
#1215 = CYLINDRICAL_SURFACE('',#1216,3.3E-02);
#1216 = AXIS2_PLACEMENT_3D('',#1217,#1218,#1219);
#1217 = CARTESIAN_POINT('',(0.15,0.117,0.297));
#1218 = DIRECTION('',(1.,0.,0.));
#1219 = DIRECTION('',(-0.,1.,0.));
#1220 = DEFINITIONAL_REPRESENTATION('',(#1221),#1225);
#1221 = LINE('',#1222,#1223);
#1222 = CARTESIAN_POINT('',(-4.712388980385,0.));
#1223 = VECTOR('',#1224,1.);
#1224 = DIRECTION('',(1.,0.));
#1225 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1226 = ORIENTED_EDGE('',*,*,#1227,.F.);
#1227 = EDGE_CURVE('',#1228,#1199,#1230,.T.);
#1228 = VERTEX_POINT('',#1229);
#1229 = CARTESIAN_POINT('',(0.15,-0.117,0.33));
#1230 = SURFACE_CURVE('',#1231,(#1235,#1242),.PCURVE_S1.);
#1231 = LINE('',#1232,#1233);
#1232 = CARTESIAN_POINT('',(0.15,-0.15,0.33));
#1233 = VECTOR('',#1234,1.);
#1234 = DIRECTION('',(0.,1.,0.));
#1235 = PCURVE('',#1056,#1236);
#1236 = DEFINITIONAL_REPRESENTATION('',(#1237),#1241);
#1237 = LINE('',#1238,#1239);
#1238 = CARTESIAN_POINT('',(0.33,0.));
#1239 = VECTOR('',#1240,1.);
#1240 = DIRECTION('',(0.,-1.));
#1241 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1242 = PCURVE('',#1243,#1248);
#1243 = PLANE('',#1244);
#1244 = AXIS2_PLACEMENT_3D('',#1245,#1246,#1247);
#1245 = CARTESIAN_POINT('',(0.15,-0.15,0.33));
#1246 = DIRECTION('',(0.,0.,1.));
#1247 = DIRECTION('',(1.,0.,0.));
#1248 = DEFINITIONAL_REPRESENTATION('',(#1249),#1253);
#1249 = LINE('',#1250,#1251);
#1250 = CARTESIAN_POINT('',(0.,0.));
#1251 = VECTOR('',#1252,1.);
#1252 = DIRECTION('',(0.,1.));
#1253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1254 = ORIENTED_EDGE('',*,*,#1255,.F.);
#1255 = EDGE_CURVE('',#1048,#1228,#1256,.T.);
#1256 = SURFACE_CURVE('',#1257,(#1262,#1273),.PCURVE_S1.);
#1257 = CIRCLE('',#1258,3.3E-02);
#1258 = AXIS2_PLACEMENT_3D('',#1259,#1260,#1261);
#1259 = CARTESIAN_POINT('',(0.15,-0.117,0.297));
#1260 = DIRECTION('',(-1.,0.,-0.));
#1261 = DIRECTION('',(-0.,0.,1.));
#1262 = PCURVE('',#1056,#1263);
#1263 = DEFINITIONAL_REPRESENTATION('',(#1264),#1272);
#1264 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1265,#1266,#1267,#1268,
#1269,#1270,#1271),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1265 = CARTESIAN_POINT('',(0.33,-3.3E-02));
#1266 = CARTESIAN_POINT('',(0.33,-9.015767664977E-02));
#1267 = CARTESIAN_POINT('',(0.2805,-6.157883832489E-02));
#1268 = CARTESIAN_POINT('',(0.231,-3.3E-02));
#1269 = CARTESIAN_POINT('',(0.2805,-4.421161675114E-03));
#1270 = CARTESIAN_POINT('',(0.33,2.415767664977E-02));
#1271 = CARTESIAN_POINT('',(0.33,-3.3E-02));
#1272 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1273 = PCURVE('',#1274,#1279);
#1274 = CYLINDRICAL_SURFACE('',#1275,3.3E-02);
#1275 = AXIS2_PLACEMENT_3D('',#1276,#1277,#1278);
#1276 = CARTESIAN_POINT('',(0.15,-0.117,0.297));
#1277 = DIRECTION('',(1.,0.,0.));
#1278 = DIRECTION('',(0.,-1.,0.));
#1279 = DEFINITIONAL_REPRESENTATION('',(#1280),#1284);
#1280 = LINE('',#1281,#1282);
#1281 = CARTESIAN_POINT('',(4.712388980385,-0.));
#1282 = VECTOR('',#1283,1.);
#1283 = DIRECTION('',(-1.,0.));
#1284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1285 = ADVANCED_FACE('',(#1286),#1068,.F.);
#1286 = FACE_BOUND('',#1287,.F.);
#1287 = EDGE_LOOP('',(#1288,#1289,#1312,#1340));
#1288 = ORIENTED_EDGE('',*,*,#1045,.T.);
#1289 = ORIENTED_EDGE('',*,*,#1290,.T.);
#1290 = EDGE_CURVE('',#1048,#1291,#1293,.T.);
#1291 = VERTEX_POINT('',#1292);
#1292 = CARTESIAN_POINT('',(0.267,-0.15,0.297));
#1293 = SURFACE_CURVE('',#1294,(#1298,#1305),.PCURVE_S1.);
#1294 = LINE('',#1295,#1296);
#1295 = CARTESIAN_POINT('',(0.15,-0.15,0.297));
#1296 = VECTOR('',#1297,1.);
#1297 = DIRECTION('',(1.,0.,0.));
#1298 = PCURVE('',#1068,#1299);
#1299 = DEFINITIONAL_REPRESENTATION('',(#1300),#1304);
#1300 = LINE('',#1301,#1302);
#1301 = CARTESIAN_POINT('',(0.297,0.));
#1302 = VECTOR('',#1303,1.);
#1303 = DIRECTION('',(0.,1.));
#1304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1305 = PCURVE('',#1274,#1306);
#1306 = DEFINITIONAL_REPRESENTATION('',(#1307),#1311);
#1307 = LINE('',#1308,#1309);
#1308 = CARTESIAN_POINT('',(-0.,0.));
#1309 = VECTOR('',#1310,1.);
#1310 = DIRECTION('',(-0.,1.));
#1311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1312 = ORIENTED_EDGE('',*,*,#1313,.F.);
#1313 = EDGE_CURVE('',#1314,#1291,#1316,.T.);
#1314 = VERTEX_POINT('',#1315);
#1315 = CARTESIAN_POINT('',(0.267,-0.15,3.3E-02));
#1316 = SURFACE_CURVE('',#1317,(#1321,#1328),.PCURVE_S1.);
#1317 = LINE('',#1318,#1319);
#1318 = CARTESIAN_POINT('',(0.267,-0.15,0.));
#1319 = VECTOR('',#1320,1.);
#1320 = DIRECTION('',(0.,0.,1.));
#1321 = PCURVE('',#1068,#1322);
#1322 = DEFINITIONAL_REPRESENTATION('',(#1323),#1327);
#1323 = LINE('',#1324,#1325);
#1324 = CARTESIAN_POINT('',(0.,0.117));
#1325 = VECTOR('',#1326,1.);
#1326 = DIRECTION('',(1.,0.));
#1327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1328 = PCURVE('',#1329,#1334);
#1329 = CYLINDRICAL_SURFACE('',#1330,3.3E-02);
#1330 = AXIS2_PLACEMENT_3D('',#1331,#1332,#1333);
#1331 = CARTESIAN_POINT('',(0.267,-0.117,0.));
#1332 = DIRECTION('',(0.,0.,1.));
#1333 = DIRECTION('',(1.,0.,-0.));
#1334 = DEFINITIONAL_REPRESENTATION('',(#1335),#1339);
#1335 = LINE('',#1336,#1337);
#1336 = CARTESIAN_POINT('',(-1.570796326795,0.));
#1337 = VECTOR('',#1338,1.);
#1338 = DIRECTION('',(-0.,1.));
#1339 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1340 = ORIENTED_EDGE('',*,*,#1341,.F.);
#1341 = EDGE_CURVE('',#1046,#1314,#1342,.T.);
#1342 = SURFACE_CURVE('',#1343,(#1347,#1354),.PCURVE_S1.);
#1343 = LINE('',#1344,#1345);
#1344 = CARTESIAN_POINT('',(0.15,-0.15,3.3E-02));
#1345 = VECTOR('',#1346,1.);
#1346 = DIRECTION('',(1.,0.,0.));
#1347 = PCURVE('',#1068,#1348);
#1348 = DEFINITIONAL_REPRESENTATION('',(#1349),#1353);
#1349 = LINE('',#1350,#1351);
#1350 = CARTESIAN_POINT('',(3.3E-02,0.));
#1351 = VECTOR('',#1352,1.);
#1352 = DIRECTION('',(0.,1.));
#1353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1354 = PCURVE('',#1097,#1355);
#1355 = DEFINITIONAL_REPRESENTATION('',(#1356),#1360);
#1356 = LINE('',#1357,#1358);
#1357 = CARTESIAN_POINT('',(0.,0.));
#1358 = VECTOR('',#1359,1.);
#1359 = DIRECTION('',(0.,1.));
#1360 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1361 = ADVANCED_FACE('',(#1362),#1097,.T.);
#1362 = FACE_BOUND('',#1363,.T.);
#1363 = EDGE_LOOP('',(#1364,#1365,#1388,#1413));
#1364 = ORIENTED_EDGE('',*,*,#1080,.T.);
#1365 = ORIENTED_EDGE('',*,*,#1366,.T.);
#1366 = EDGE_CURVE('',#1081,#1367,#1369,.T.);
#1367 = VERTEX_POINT('',#1368);
#1368 = CARTESIAN_POINT('',(0.267,-0.117,0.));
#1369 = SURFACE_CURVE('',#1370,(#1374,#1381),.PCURVE_S1.);
#1370 = LINE('',#1371,#1372);
#1371 = CARTESIAN_POINT('',(0.15,-0.117,-6.938893903907E-18));
#1372 = VECTOR('',#1373,1.);
#1373 = DIRECTION('',(1.,0.,0.));
#1374 = PCURVE('',#1097,#1375);
#1375 = DEFINITIONAL_REPRESENTATION('',(#1376),#1380);
#1376 = LINE('',#1377,#1378);
#1377 = CARTESIAN_POINT('',(1.570796326795,0.));
#1378 = VECTOR('',#1379,1.);
#1379 = DIRECTION('',(0.,1.));
#1380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1381 = PCURVE('',#1125,#1382);
#1382 = DEFINITIONAL_REPRESENTATION('',(#1383),#1387);
#1383 = LINE('',#1384,#1385);
#1384 = CARTESIAN_POINT('',(0.,3.3E-02));
#1385 = VECTOR('',#1386,1.);
#1386 = DIRECTION('',(1.,0.));
#1387 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1388 = ORIENTED_EDGE('',*,*,#1389,.T.);
#1389 = EDGE_CURVE('',#1367,#1314,#1390,.T.);
#1390 = SURFACE_CURVE('',#1391,(#1396,#1402),.PCURVE_S1.);
#1391 = CIRCLE('',#1392,3.3E-02);
#1392 = AXIS2_PLACEMENT_3D('',#1393,#1394,#1395);
#1393 = CARTESIAN_POINT('',(0.267,-0.117,3.3E-02));
#1394 = DIRECTION('',(-1.,-1.615544574433E-15,0.));
#1395 = DIRECTION('',(1.615544574433E-15,-1.,-0.));
#1396 = PCURVE('',#1097,#1397);
#1397 = DEFINITIONAL_REPRESENTATION('',(#1398),#1401);
#1398 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1399,#1400),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1399 = CARTESIAN_POINT('',(1.570796326795,0.117));
#1400 = CARTESIAN_POINT('',(0.,0.117));
#1401 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1402 = PCURVE('',#1403,#1408);
#1403 = SPHERICAL_SURFACE('',#1404,3.3E-02);
#1404 = AXIS2_PLACEMENT_3D('',#1405,#1406,#1407);
#1405 = CARTESIAN_POINT('',(0.267,-0.117,3.3E-02));
#1406 = DIRECTION('',(0.,0.,1.));
#1407 = DIRECTION('',(1.,0.,-0.));
#1408 = DEFINITIONAL_REPRESENTATION('',(#1409),#1412);
#1409 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1410,#1411),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1410 = CARTESIAN_POINT('',(-1.570796326795,-1.570796326795));
#1411 = CARTESIAN_POINT('',(-1.570796326795,0.));
#1412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1413 = ORIENTED_EDGE('',*,*,#1341,.F.);
#1414 = ADVANCED_FACE('',(#1415),#1274,.T.);
#1415 = FACE_BOUND('',#1416,.F.);
#1416 = EDGE_LOOP('',(#1417,#1418,#1441,#1466));
#1417 = ORIENTED_EDGE('',*,*,#1255,.T.);
#1418 = ORIENTED_EDGE('',*,*,#1419,.T.);
#1419 = EDGE_CURVE('',#1228,#1420,#1422,.T.);
#1420 = VERTEX_POINT('',#1421);
#1421 = CARTESIAN_POINT('',(0.267,-0.117,0.33));
#1422 = SURFACE_CURVE('',#1423,(#1427,#1434),.PCURVE_S1.);
#1423 = LINE('',#1424,#1425);
#1424 = CARTESIAN_POINT('',(0.15,-0.117,0.33));
#1425 = VECTOR('',#1426,1.);
#1426 = DIRECTION('',(1.,0.,0.));
#1427 = PCURVE('',#1274,#1428);
#1428 = DEFINITIONAL_REPRESENTATION('',(#1429),#1433);
#1429 = LINE('',#1430,#1431);
#1430 = CARTESIAN_POINT('',(-1.570796326795,0.));
#1431 = VECTOR('',#1432,1.);
#1432 = DIRECTION('',(-0.,1.));
#1433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1434 = PCURVE('',#1243,#1435);
#1435 = DEFINITIONAL_REPRESENTATION('',(#1436),#1440);
#1436 = LINE('',#1437,#1438);
#1437 = CARTESIAN_POINT('',(0.,3.3E-02));
#1438 = VECTOR('',#1439,1.);
#1439 = DIRECTION('',(1.,0.));
#1440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1441 = ORIENTED_EDGE('',*,*,#1442,.T.);
#1442 = EDGE_CURVE('',#1420,#1291,#1443,.T.);
#1443 = SURFACE_CURVE('',#1444,(#1449,#1455),.PCURVE_S1.);
#1444 = CIRCLE('',#1445,3.3E-02);
#1445 = AXIS2_PLACEMENT_3D('',#1446,#1447,#1448);
#1446 = CARTESIAN_POINT('',(0.267,-0.117,0.297));
#1447 = DIRECTION('',(1.,1.615544574433E-15,0.));
#1448 = DIRECTION('',(1.615544574433E-15,-1.,0.));
#1449 = PCURVE('',#1274,#1450);
#1450 = DEFINITIONAL_REPRESENTATION('',(#1451),#1454);
#1451 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1452,#1453),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1452 = CARTESIAN_POINT('',(-1.570796326795,0.117));
#1453 = CARTESIAN_POINT('',(0.,0.117));
#1454 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1455 = PCURVE('',#1456,#1461);
#1456 = SPHERICAL_SURFACE('',#1457,3.3E-02);
#1457 = AXIS2_PLACEMENT_3D('',#1458,#1459,#1460);
#1458 = CARTESIAN_POINT('',(0.267,-0.117,0.297));
#1459 = DIRECTION('',(-0.,-0.,-1.));
#1460 = DIRECTION('',(1.,0.,0.));
#1461 = DEFINITIONAL_REPRESENTATION('',(#1462),#1465);
#1462 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1463,#1464),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1463 = CARTESIAN_POINT('',(1.570796326795,-1.570796326795));
#1464 = CARTESIAN_POINT('',(1.570796326795,0.));
#1465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1466 = ORIENTED_EDGE('',*,*,#1290,.F.);
#1467 = ADVANCED_FACE('',(#1468),#1125,.F.);
#1468 = FACE_BOUND('',#1469,.F.);
#1469 = EDGE_LOOP('',(#1470,#1471,#1472,#1500));
#1470 = ORIENTED_EDGE('',*,*,#1109,.F.);
#1471 = ORIENTED_EDGE('',*,*,#1366,.T.);
#1472 = ORIENTED_EDGE('',*,*,#1473,.T.);
#1473 = EDGE_CURVE('',#1367,#1474,#1476,.T.);
#1474 = VERTEX_POINT('',#1475);
#1475 = CARTESIAN_POINT('',(0.267,0.117,0.));
#1476 = SURFACE_CURVE('',#1477,(#1481,#1488),.PCURVE_S1.);
#1477 = LINE('',#1478,#1479);
#1478 = CARTESIAN_POINT('',(0.267,-0.15,-6.938893903907E-18));
#1479 = VECTOR('',#1480,1.);
#1480 = DIRECTION('',(0.,1.,0.));
#1481 = PCURVE('',#1125,#1482);
#1482 = DEFINITIONAL_REPRESENTATION('',(#1483),#1487);
#1483 = LINE('',#1484,#1485);
#1484 = CARTESIAN_POINT('',(0.117,0.));
#1485 = VECTOR('',#1486,1.);
#1486 = DIRECTION('',(0.,1.));
#1487 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1488 = PCURVE('',#1489,#1494);
#1489 = CYLINDRICAL_SURFACE('',#1490,3.3E-02);
#1490 = AXIS2_PLACEMENT_3D('',#1491,#1492,#1493);
#1491 = CARTESIAN_POINT('',(0.267,-0.15,3.3E-02));
#1492 = DIRECTION('',(0.,1.,0.));
#1493 = DIRECTION('',(1.,0.,-0.));
#1494 = DEFINITIONAL_REPRESENTATION('',(#1495),#1499);
#1495 = LINE('',#1496,#1497);
#1496 = CARTESIAN_POINT('',(1.570796326795,0.));
#1497 = VECTOR('',#1498,1.);
#1498 = DIRECTION('',(0.,1.));
#1499 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1500 = ORIENTED_EDGE('',*,*,#1501,.F.);
#1501 = EDGE_CURVE('',#1110,#1474,#1502,.T.);
#1502 = SURFACE_CURVE('',#1503,(#1507,#1514),.PCURVE_S1.);
#1503 = LINE('',#1504,#1505);
#1504 = CARTESIAN_POINT('',(0.15,0.117,-6.938893903907E-18));
#1505 = VECTOR('',#1506,1.);
#1506 = DIRECTION('',(1.,0.,0.));
#1507 = PCURVE('',#1125,#1508);
#1508 = DEFINITIONAL_REPRESENTATION('',(#1509),#1513);
#1509 = LINE('',#1510,#1511);
#1510 = CARTESIAN_POINT('',(0.,0.267));
#1511 = VECTOR('',#1512,1.);
#1512 = DIRECTION('',(1.,0.));
#1513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1514 = PCURVE('',#1158,#1515);
#1515 = DEFINITIONAL_REPRESENTATION('',(#1516),#1520);
#1516 = LINE('',#1517,#1518);
#1517 = CARTESIAN_POINT('',(-1.570796326795,0.));
#1518 = VECTOR('',#1519,1.);
#1519 = DIRECTION('',(-0.,1.));
#1520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1521 = ADVANCED_FACE('',(#1522),#1243,.T.);
#1522 = FACE_BOUND('',#1523,.T.);
#1523 = EDGE_LOOP('',(#1524,#1525,#1526,#1554));
#1524 = ORIENTED_EDGE('',*,*,#1227,.F.);
#1525 = ORIENTED_EDGE('',*,*,#1419,.T.);
#1526 = ORIENTED_EDGE('',*,*,#1527,.T.);
#1527 = EDGE_CURVE('',#1420,#1528,#1530,.T.);
#1528 = VERTEX_POINT('',#1529);
#1529 = CARTESIAN_POINT('',(0.267,0.117,0.33));
#1530 = SURFACE_CURVE('',#1531,(#1535,#1542),.PCURVE_S1.);
#1531 = LINE('',#1532,#1533);
#1532 = CARTESIAN_POINT('',(0.267,-0.15,0.33));
#1533 = VECTOR('',#1534,1.);
#1534 = DIRECTION('',(0.,1.,0.));
#1535 = PCURVE('',#1243,#1536);
#1536 = DEFINITIONAL_REPRESENTATION('',(#1537),#1541);
#1537 = LINE('',#1538,#1539);
#1538 = CARTESIAN_POINT('',(0.117,0.));
#1539 = VECTOR('',#1540,1.);
#1540 = DIRECTION('',(0.,1.));
#1541 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1542 = PCURVE('',#1543,#1548);
#1543 = CYLINDRICAL_SURFACE('',#1544,3.3E-02);
#1544 = AXIS2_PLACEMENT_3D('',#1545,#1546,#1547);
#1545 = CARTESIAN_POINT('',(0.267,-0.15,0.297));
#1546 = DIRECTION('',(0.,1.,0.));
#1547 = DIRECTION('',(1.,0.,-0.));
#1548 = DEFINITIONAL_REPRESENTATION('',(#1549),#1553);
#1549 = LINE('',#1550,#1551);
#1550 = CARTESIAN_POINT('',(-1.570796326795,0.));
#1551 = VECTOR('',#1552,1.);
#1552 = DIRECTION('',(-0.,1.));
#1553 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1554 = ORIENTED_EDGE('',*,*,#1555,.F.);
#1555 = EDGE_CURVE('',#1199,#1528,#1556,.T.);
#1556 = SURFACE_CURVE('',#1557,(#1561,#1568),.PCURVE_S1.);
#1557 = LINE('',#1558,#1559);
#1558 = CARTESIAN_POINT('',(0.15,0.117,0.33));
#1559 = VECTOR('',#1560,1.);
#1560 = DIRECTION('',(1.,0.,0.));
#1561 = PCURVE('',#1243,#1562);
#1562 = DEFINITIONAL_REPRESENTATION('',(#1563),#1567);
#1563 = LINE('',#1564,#1565);
#1564 = CARTESIAN_POINT('',(0.,0.267));
#1565 = VECTOR('',#1566,1.);
#1566 = DIRECTION('',(1.,0.));
#1567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1568 = PCURVE('',#1215,#1569);
#1569 = DEFINITIONAL_REPRESENTATION('',(#1570),#1574);
#1570 = LINE('',#1571,#1572);
#1571 = CARTESIAN_POINT('',(1.570796326795,0.));
#1572 = VECTOR('',#1573,1.);
#1573 = DIRECTION('',(0.,1.));
#1574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1575 = ADVANCED_FACE('',(#1576),#1158,.T.);
#1576 = FACE_BOUND('',#1577,.F.);
#1577 = EDGE_LOOP('',(#1578,#1579,#1580,#1607));
#1578 = ORIENTED_EDGE('',*,*,#1137,.T.);
#1579 = ORIENTED_EDGE('',*,*,#1501,.T.);
#1580 = ORIENTED_EDGE('',*,*,#1581,.T.);
#1581 = EDGE_CURVE('',#1474,#1582,#1584,.T.);
#1582 = VERTEX_POINT('',#1583);
#1583 = CARTESIAN_POINT('',(0.267,0.15,3.3E-02));
#1584 = SURFACE_CURVE('',#1585,(#1590,#1596),.PCURVE_S1.);
#1585 = CIRCLE('',#1586,3.3E-02);
#1586 = AXIS2_PLACEMENT_3D('',#1587,#1588,#1589);
#1587 = CARTESIAN_POINT('',(0.267,0.117,3.3E-02));
#1588 = DIRECTION('',(1.,-1.790488808616E-15,0.));
#1589 = DIRECTION('',(1.790488808616E-15,1.,0.));
#1590 = PCURVE('',#1158,#1591);
#1591 = DEFINITIONAL_REPRESENTATION('',(#1592),#1595);
#1592 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1593,#1594),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1593 = CARTESIAN_POINT('',(-1.570796326795,0.117));
#1594 = CARTESIAN_POINT('',(0.,0.117));
#1595 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1596 = PCURVE('',#1597,#1602);
#1597 = SPHERICAL_SURFACE('',#1598,3.3E-02);
#1598 = AXIS2_PLACEMENT_3D('',#1599,#1600,#1601);
#1599 = CARTESIAN_POINT('',(0.267,0.117,3.3E-02));
#1600 = DIRECTION('',(0.,0.,1.));
#1601 = DIRECTION('',(1.,-8.410780489585E-16,0.));
#1602 = DEFINITIONAL_REPRESENTATION('',(#1603),#1606);
#1603 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1604,#1605),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1604 = CARTESIAN_POINT('',(1.570796326795,-1.570796326795));
#1605 = CARTESIAN_POINT('',(1.570796326795,0.));
#1606 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1607 = ORIENTED_EDGE('',*,*,#1608,.F.);
#1608 = EDGE_CURVE('',#1138,#1582,#1609,.T.);
#1609 = SURFACE_CURVE('',#1610,(#1614,#1621),.PCURVE_S1.);
#1610 = LINE('',#1611,#1612);
#1611 = CARTESIAN_POINT('',(0.15,0.15,3.3E-02));
#1612 = VECTOR('',#1613,1.);
#1613 = DIRECTION('',(1.,0.,0.));
#1614 = PCURVE('',#1158,#1615);
#1615 = DEFINITIONAL_REPRESENTATION('',(#1616),#1620);
#1616 = LINE('',#1617,#1618);
#1617 = CARTESIAN_POINT('',(-0.,0.));
#1618 = VECTOR('',#1619,1.);
#1619 = DIRECTION('',(-0.,1.));
#1620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1621 = PCURVE('',#1186,#1622);
#1622 = DEFINITIONAL_REPRESENTATION('',(#1623),#1627);
#1623 = LINE('',#1624,#1625);
#1624 = CARTESIAN_POINT('',(3.3E-02,0.));
#1625 = VECTOR('',#1626,1.);
#1626 = DIRECTION('',(0.,1.));
#1627 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1628 = ADVANCED_FACE('',(#1629),#1215,.T.);
#1629 = FACE_BOUND('',#1630,.T.);
#1630 = EDGE_LOOP('',(#1631,#1632,#1633,#1660));
#1631 = ORIENTED_EDGE('',*,*,#1198,.T.);
#1632 = ORIENTED_EDGE('',*,*,#1555,.T.);
#1633 = ORIENTED_EDGE('',*,*,#1634,.F.);
#1634 = EDGE_CURVE('',#1635,#1528,#1637,.T.);
#1635 = VERTEX_POINT('',#1636);
#1636 = CARTESIAN_POINT('',(0.267,0.15,0.297));
#1637 = SURFACE_CURVE('',#1638,(#1643,#1649),.PCURVE_S1.);
#1638 = CIRCLE('',#1639,3.3E-02);
#1639 = AXIS2_PLACEMENT_3D('',#1640,#1641,#1642);
#1640 = CARTESIAN_POINT('',(0.267,0.117,0.297));
#1641 = DIRECTION('',(1.,0.,-1.615544574433E-15));
#1642 = DIRECTION('',(1.615544574433E-15,0.,1.));
#1643 = PCURVE('',#1215,#1644);
#1644 = DEFINITIONAL_REPRESENTATION('',(#1645),#1648);
#1645 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1646,#1647),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1646 = CARTESIAN_POINT('',(0.,0.117));
#1647 = CARTESIAN_POINT('',(1.570796326795,0.117));
#1648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1649 = PCURVE('',#1650,#1655);
#1650 = SPHERICAL_SURFACE('',#1651,3.3E-02);
#1651 = AXIS2_PLACEMENT_3D('',#1652,#1653,#1654);
#1652 = CARTESIAN_POINT('',(0.267,0.117,0.297));
#1653 = DIRECTION('',(-0.,-1.,-0.));
#1654 = DIRECTION('',(1.,-0.,0.));
#1655 = DEFINITIONAL_REPRESENTATION('',(#1656),#1659);
#1656 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1657,#1658),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1657 = CARTESIAN_POINT('',(1.570796326795,-1.570796326795));
#1658 = CARTESIAN_POINT('',(1.570796326795,0.));
#1659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1660 = ORIENTED_EDGE('',*,*,#1661,.F.);
#1661 = EDGE_CURVE('',#1171,#1635,#1662,.T.);
#1662 = SURFACE_CURVE('',#1663,(#1667,#1674),.PCURVE_S1.);
#1663 = LINE('',#1664,#1665);
#1664 = CARTESIAN_POINT('',(0.15,0.15,0.297));
#1665 = VECTOR('',#1666,1.);
#1666 = DIRECTION('',(1.,0.,0.));
#1667 = PCURVE('',#1215,#1668);
#1668 = DEFINITIONAL_REPRESENTATION('',(#1669),#1673);
#1669 = LINE('',#1670,#1671);
#1670 = CARTESIAN_POINT('',(0.,0.));
#1671 = VECTOR('',#1672,1.);
#1672 = DIRECTION('',(0.,1.));
#1673 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1674 = PCURVE('',#1186,#1675);
#1675 = DEFINITIONAL_REPRESENTATION('',(#1676),#1680);
#1676 = LINE('',#1677,#1678);
#1677 = CARTESIAN_POINT('',(0.297,0.));
#1678 = VECTOR('',#1679,1.);
#1679 = DIRECTION('',(0.,1.));
#1680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1681 = ADVANCED_FACE('',(#1682),#1186,.T.);
#1682 = FACE_BOUND('',#1683,.T.);
#1683 = EDGE_LOOP('',(#1684,#1685,#1686,#1712));
#1684 = ORIENTED_EDGE('',*,*,#1170,.T.);
#1685 = ORIENTED_EDGE('',*,*,#1661,.T.);
#1686 = ORIENTED_EDGE('',*,*,#1687,.F.);
#1687 = EDGE_CURVE('',#1582,#1635,#1688,.T.);
#1688 = SURFACE_CURVE('',#1689,(#1693,#1700),.PCURVE_S1.);
#1689 = LINE('',#1690,#1691);
#1690 = CARTESIAN_POINT('',(0.267,0.15,0.));
#1691 = VECTOR('',#1692,1.);
#1692 = DIRECTION('',(0.,0.,1.));
#1693 = PCURVE('',#1186,#1694);
#1694 = DEFINITIONAL_REPRESENTATION('',(#1695),#1699);
#1695 = LINE('',#1696,#1697);
#1696 = CARTESIAN_POINT('',(0.,0.117));
#1697 = VECTOR('',#1698,1.);
#1698 = DIRECTION('',(1.,0.));
#1699 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1700 = PCURVE('',#1701,#1706);
#1701 = CYLINDRICAL_SURFACE('',#1702,3.3E-02);
#1702 = AXIS2_PLACEMENT_3D('',#1703,#1704,#1705);
#1703 = CARTESIAN_POINT('',(0.267,0.117,0.));
#1704 = DIRECTION('',(0.,0.,1.));
#1705 = DIRECTION('',(1.,0.,-0.));
#1706 = DEFINITIONAL_REPRESENTATION('',(#1707),#1711);
#1707 = LINE('',#1708,#1709);
#1708 = CARTESIAN_POINT('',(1.570796326795,0.));
#1709 = VECTOR('',#1710,1.);
#1710 = DIRECTION('',(0.,1.));
#1711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1712 = ORIENTED_EDGE('',*,*,#1608,.F.);
#1713 = ADVANCED_FACE('',(#1714),#1329,.T.);
#1714 = FACE_BOUND('',#1715,.F.);
#1715 = EDGE_LOOP('',(#1716,#1746,#1768,#1769));
#1716 = ORIENTED_EDGE('',*,*,#1717,.F.);
#1717 = EDGE_CURVE('',#1718,#1720,#1722,.T.);
#1718 = VERTEX_POINT('',#1719);
#1719 = CARTESIAN_POINT('',(0.3,-0.117,3.3E-02));
#1720 = VERTEX_POINT('',#1721);
#1721 = CARTESIAN_POINT('',(0.3,-0.117,0.297));
#1722 = SURFACE_CURVE('',#1723,(#1727,#1734),.PCURVE_S1.);
#1723 = LINE('',#1724,#1725);
#1724 = CARTESIAN_POINT('',(0.3,-0.117,0.));
#1725 = VECTOR('',#1726,1.);
#1726 = DIRECTION('',(0.,0.,1.));
#1727 = PCURVE('',#1329,#1728);
#1728 = DEFINITIONAL_REPRESENTATION('',(#1729),#1733);
#1729 = LINE('',#1730,#1731);
#1730 = CARTESIAN_POINT('',(-0.,0.));
#1731 = VECTOR('',#1732,1.);
#1732 = DIRECTION('',(-0.,1.));
#1733 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1734 = PCURVE('',#1735,#1740);
#1735 = PLANE('',#1736);
#1736 = AXIS2_PLACEMENT_3D('',#1737,#1738,#1739);
#1737 = CARTESIAN_POINT('',(0.3,-0.15,0.));
#1738 = DIRECTION('',(1.,0.,0.));
#1739 = DIRECTION('',(0.,0.,1.));
#1740 = DEFINITIONAL_REPRESENTATION('',(#1741),#1745);
#1741 = LINE('',#1742,#1743);
#1742 = CARTESIAN_POINT('',(0.,-3.3E-02));
#1743 = VECTOR('',#1744,1.);
#1744 = DIRECTION('',(1.,0.));
#1745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1746 = ORIENTED_EDGE('',*,*,#1747,.T.);
#1747 = EDGE_CURVE('',#1718,#1314,#1748,.T.);
#1748 = SURFACE_CURVE('',#1749,(#1754,#1761),.PCURVE_S1.);
#1749 = CIRCLE('',#1750,3.3E-02);
#1750 = AXIS2_PLACEMENT_3D('',#1751,#1752,#1753);
#1751 = CARTESIAN_POINT('',(0.267,-0.117,3.3E-02));
#1752 = DIRECTION('',(0.,-0.,-1.));
#1753 = DIRECTION('',(1.,0.,-0.));
#1754 = PCURVE('',#1329,#1755);
#1755 = DEFINITIONAL_REPRESENTATION('',(#1756),#1760);
#1756 = LINE('',#1757,#1758);
#1757 = CARTESIAN_POINT('',(-0.,3.3E-02));
#1758 = VECTOR('',#1759,1.);
#1759 = DIRECTION('',(-1.,0.));
#1760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1761 = PCURVE('',#1403,#1762);
#1762 = DEFINITIONAL_REPRESENTATION('',(#1763),#1767);
#1763 = LINE('',#1764,#1765);
#1764 = CARTESIAN_POINT('',(-0.,0.));
#1765 = VECTOR('',#1766,1.);
#1766 = DIRECTION('',(-1.,0.));
#1767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1768 = ORIENTED_EDGE('',*,*,#1313,.T.);
#1769 = ORIENTED_EDGE('',*,*,#1770,.F.);
#1770 = EDGE_CURVE('',#1720,#1291,#1771,.T.);
#1771 = SURFACE_CURVE('',#1772,(#1777,#1784),.PCURVE_S1.);
#1772 = CIRCLE('',#1773,3.3E-02);
#1773 = AXIS2_PLACEMENT_3D('',#1774,#1775,#1776);
#1774 = CARTESIAN_POINT('',(0.267,-0.117,0.297));
#1775 = DIRECTION('',(0.,0.,-1.));
#1776 = DIRECTION('',(1.,0.,0.));
#1777 = PCURVE('',#1329,#1778);
#1778 = DEFINITIONAL_REPRESENTATION('',(#1779),#1783);
#1779 = LINE('',#1780,#1781);
#1780 = CARTESIAN_POINT('',(-0.,0.297));
#1781 = VECTOR('',#1782,1.);
#1782 = DIRECTION('',(-1.,0.));
#1783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1784 = PCURVE('',#1456,#1785);
#1785 = DEFINITIONAL_REPRESENTATION('',(#1786),#1790);
#1786 = LINE('',#1787,#1788);
#1787 = CARTESIAN_POINT('',(0.,0.));
#1788 = VECTOR('',#1789,1.);
#1789 = DIRECTION('',(1.,0.));
#1790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1791 = ADVANCED_FACE('',(#1792),#1403,.T.);
#1792 = FACE_BOUND('',#1793,.F.);
#1793 = EDGE_LOOP('',(#1794,#1814,#1815));
#1794 = ORIENTED_EDGE('',*,*,#1795,.F.);
#1795 = EDGE_CURVE('',#1367,#1718,#1796,.T.);
#1796 = SURFACE_CURVE('',#1797,(#1802,#1808),.PCURVE_S1.);
#1797 = CIRCLE('',#1798,3.3E-02);
#1798 = AXIS2_PLACEMENT_3D('',#1799,#1800,#1801);
#1799 = CARTESIAN_POINT('',(0.267,-0.117,3.3E-02));
#1800 = DIRECTION('',(0.,-1.,0.));
#1801 = DIRECTION('',(1.,0.,0.));
#1802 = PCURVE('',#1403,#1803);
#1803 = DEFINITIONAL_REPRESENTATION('',(#1804),#1807);
#1804 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1805,#1806),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1805 = CARTESIAN_POINT('',(0.,-1.570796326795));
#1806 = CARTESIAN_POINT('',(0.,0.));
#1807 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1808 = PCURVE('',#1489,#1809);
#1809 = DEFINITIONAL_REPRESENTATION('',(#1810),#1813);
#1810 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1811,#1812),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1811 = CARTESIAN_POINT('',(1.570796326795,3.3E-02));
#1812 = CARTESIAN_POINT('',(0.,3.3E-02));
#1813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1814 = ORIENTED_EDGE('',*,*,#1389,.T.);
#1815 = ORIENTED_EDGE('',*,*,#1747,.F.);
#1816 = ADVANCED_FACE('',(#1817),#1456,.T.);
#1817 = FACE_BOUND('',#1818,.T.);
#1818 = EDGE_LOOP('',(#1819,#1839,#1840));
#1819 = ORIENTED_EDGE('',*,*,#1820,.F.);
#1820 = EDGE_CURVE('',#1420,#1720,#1821,.T.);
#1821 = SURFACE_CURVE('',#1822,(#1827,#1833),.PCURVE_S1.);
#1822 = CIRCLE('',#1823,3.3E-02);
#1823 = AXIS2_PLACEMENT_3D('',#1824,#1825,#1826);
#1824 = CARTESIAN_POINT('',(0.267,-0.117,0.297));
#1825 = DIRECTION('',(-0.,1.,0.));
#1826 = DIRECTION('',(1.,0.,0.));
#1827 = PCURVE('',#1456,#1828);
#1828 = DEFINITIONAL_REPRESENTATION('',(#1829),#1832);
#1829 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1830,#1831),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1830 = CARTESIAN_POINT('',(0.,-1.570796326795));
#1831 = CARTESIAN_POINT('',(0.,0.));
#1832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1833 = PCURVE('',#1543,#1834);
#1834 = DEFINITIONAL_REPRESENTATION('',(#1835),#1838);
#1835 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1836,#1837),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1836 = CARTESIAN_POINT('',(-1.570796326795,3.3E-02));
#1837 = CARTESIAN_POINT('',(0.,3.3E-02));
#1838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1839 = ORIENTED_EDGE('',*,*,#1442,.T.);
#1840 = ORIENTED_EDGE('',*,*,#1770,.F.);
#1841 = ADVANCED_FACE('',(#1842),#1489,.T.);
#1842 = FACE_BOUND('',#1843,.T.);
#1843 = EDGE_LOOP('',(#1844,#1845,#1846,#1868));
#1844 = ORIENTED_EDGE('',*,*,#1795,.F.);
#1845 = ORIENTED_EDGE('',*,*,#1473,.T.);
#1846 = ORIENTED_EDGE('',*,*,#1847,.T.);
#1847 = EDGE_CURVE('',#1474,#1848,#1850,.T.);
#1848 = VERTEX_POINT('',#1849);
#1849 = CARTESIAN_POINT('',(0.3,0.117,3.3E-02));
#1850 = SURFACE_CURVE('',#1851,(#1856,#1862),.PCURVE_S1.);
#1851 = CIRCLE('',#1852,3.3E-02);
#1852 = AXIS2_PLACEMENT_3D('',#1853,#1854,#1855);
#1853 = CARTESIAN_POINT('',(0.267,0.117,3.3E-02));
#1854 = DIRECTION('',(-8.410780489585E-16,-1.,0.));
#1855 = DIRECTION('',(1.,-8.410780489585E-16,0.));
#1856 = PCURVE('',#1489,#1857);
#1857 = DEFINITIONAL_REPRESENTATION('',(#1858),#1861);
#1858 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1859,#1860),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1859 = CARTESIAN_POINT('',(1.570796326795,0.267));
#1860 = CARTESIAN_POINT('',(0.,0.267));
#1861 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1862 = PCURVE('',#1597,#1863);
#1863 = DEFINITIONAL_REPRESENTATION('',(#1864),#1867);
#1864 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1865,#1866),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1865 = CARTESIAN_POINT('',(0.,-1.570796326795));
#1866 = CARTESIAN_POINT('',(0.,0.));
#1867 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1868 = ORIENTED_EDGE('',*,*,#1869,.F.);
#1869 = EDGE_CURVE('',#1718,#1848,#1870,.T.);
#1870 = SURFACE_CURVE('',#1871,(#1875,#1882),.PCURVE_S1.);
#1871 = LINE('',#1872,#1873);
#1872 = CARTESIAN_POINT('',(0.3,-0.15,3.3E-02));
#1873 = VECTOR('',#1874,1.);
#1874 = DIRECTION('',(0.,1.,0.));
#1875 = PCURVE('',#1489,#1876);
#1876 = DEFINITIONAL_REPRESENTATION('',(#1877),#1881);
#1877 = LINE('',#1878,#1879);
#1878 = CARTESIAN_POINT('',(0.,0.));
#1879 = VECTOR('',#1880,1.);
#1880 = DIRECTION('',(0.,1.));
#1881 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1882 = PCURVE('',#1735,#1883);
#1883 = DEFINITIONAL_REPRESENTATION('',(#1884),#1888);
#1884 = LINE('',#1885,#1886);
#1885 = CARTESIAN_POINT('',(3.3E-02,0.));
#1886 = VECTOR('',#1887,1.);
#1887 = DIRECTION('',(0.,-1.));
#1888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1889 = ADVANCED_FACE('',(#1890),#1543,.T.);
#1890 = FACE_BOUND('',#1891,.F.);
#1891 = EDGE_LOOP('',(#1892,#1893,#1894,#1918));
#1892 = ORIENTED_EDGE('',*,*,#1820,.F.);
#1893 = ORIENTED_EDGE('',*,*,#1527,.T.);
#1894 = ORIENTED_EDGE('',*,*,#1895,.F.);
#1895 = EDGE_CURVE('',#1896,#1528,#1898,.T.);
#1896 = VERTEX_POINT('',#1897);
#1897 = CARTESIAN_POINT('',(0.3,0.117,0.297));
#1898 = SURFACE_CURVE('',#1899,(#1904,#1911),.PCURVE_S1.);
#1899 = CIRCLE('',#1900,3.3E-02);
#1900 = AXIS2_PLACEMENT_3D('',#1901,#1902,#1903);
#1901 = CARTESIAN_POINT('',(0.267,0.117,0.297));
#1902 = DIRECTION('',(-0.,-1.,0.));
#1903 = DIRECTION('',(1.,-0.,0.));
#1904 = PCURVE('',#1543,#1905);
#1905 = DEFINITIONAL_REPRESENTATION('',(#1906),#1910);
#1906 = LINE('',#1907,#1908);
#1907 = CARTESIAN_POINT('',(-0.,0.267));
#1908 = VECTOR('',#1909,1.);
#1909 = DIRECTION('',(-1.,0.));
#1910 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1911 = PCURVE('',#1650,#1912);
#1912 = DEFINITIONAL_REPRESENTATION('',(#1913),#1917);
#1913 = LINE('',#1914,#1915);
#1914 = CARTESIAN_POINT('',(0.,0.));
#1915 = VECTOR('',#1916,1.);
#1916 = DIRECTION('',(1.,0.));
#1917 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1918 = ORIENTED_EDGE('',*,*,#1919,.F.);
#1919 = EDGE_CURVE('',#1720,#1896,#1920,.T.);
#1920 = SURFACE_CURVE('',#1921,(#1925,#1932),.PCURVE_S1.);
#1921 = LINE('',#1922,#1923);
#1922 = CARTESIAN_POINT('',(0.3,-0.15,0.297));
#1923 = VECTOR('',#1924,1.);
#1924 = DIRECTION('',(0.,1.,0.));
#1925 = PCURVE('',#1543,#1926);
#1926 = DEFINITIONAL_REPRESENTATION('',(#1927),#1931);
#1927 = LINE('',#1928,#1929);
#1928 = CARTESIAN_POINT('',(-0.,0.));
#1929 = VECTOR('',#1930,1.);
#1930 = DIRECTION('',(-0.,1.));
#1931 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1932 = PCURVE('',#1735,#1933);
#1933 = DEFINITIONAL_REPRESENTATION('',(#1934),#1938);
#1934 = LINE('',#1935,#1936);
#1935 = CARTESIAN_POINT('',(0.297,0.));
#1936 = VECTOR('',#1937,1.);
#1937 = DIRECTION('',(0.,-1.));
#1938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1939 = ADVANCED_FACE('',(#1940),#1597,.T.);
#1940 = FACE_BOUND('',#1941,.T.);
#1941 = EDGE_LOOP('',(#1942,#1943,#1944));
#1942 = ORIENTED_EDGE('',*,*,#1847,.F.);
#1943 = ORIENTED_EDGE('',*,*,#1581,.T.);
#1944 = ORIENTED_EDGE('',*,*,#1945,.F.);
#1945 = EDGE_CURVE('',#1848,#1582,#1946,.T.);
#1946 = SURFACE_CURVE('',#1947,(#1952,#1959),.PCURVE_S1.);
#1947 = CIRCLE('',#1948,3.3E-02);
#1948 = AXIS2_PLACEMENT_3D('',#1949,#1950,#1951);
#1949 = CARTESIAN_POINT('',(0.267,0.117,3.3E-02));
#1950 = DIRECTION('',(0.,0.,1.));
#1951 = DIRECTION('',(1.,-8.410780489585E-16,0.));
#1952 = PCURVE('',#1597,#1953);
#1953 = DEFINITIONAL_REPRESENTATION('',(#1954),#1958);
#1954 = LINE('',#1955,#1956);
#1955 = CARTESIAN_POINT('',(0.,0.));
#1956 = VECTOR('',#1957,1.);
#1957 = DIRECTION('',(1.,0.));
#1958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1959 = PCURVE('',#1701,#1960);
#1960 = DEFINITIONAL_REPRESENTATION('',(#1961),#1965);
#1961 = LINE('',#1962,#1963);
#1962 = CARTESIAN_POINT('',(0.,3.3E-02));
#1963 = VECTOR('',#1964,1.);
#1964 = DIRECTION('',(1.,0.));
#1965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1966 = ADVANCED_FACE('',(#1967),#1650,.T.);
#1967 = FACE_BOUND('',#1968,.T.);
#1968 = EDGE_LOOP('',(#1969,#1989,#1990));
#1969 = ORIENTED_EDGE('',*,*,#1970,.F.);
#1970 = EDGE_CURVE('',#1635,#1896,#1971,.T.);
#1971 = SURFACE_CURVE('',#1972,(#1977,#1983),.PCURVE_S1.);
#1972 = CIRCLE('',#1973,3.3E-02);
#1973 = AXIS2_PLACEMENT_3D('',#1974,#1975,#1976);
#1974 = CARTESIAN_POINT('',(0.267,0.117,0.297));
#1975 = DIRECTION('',(0.,0.,-1.));
#1976 = DIRECTION('',(1.,0.,0.));
#1977 = PCURVE('',#1650,#1978);
#1978 = DEFINITIONAL_REPRESENTATION('',(#1979),#1982);
#1979 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1980,#1981),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1980 = CARTESIAN_POINT('',(0.,-1.570796326795));
#1981 = CARTESIAN_POINT('',(0.,0.));
#1982 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1983 = PCURVE('',#1701,#1984);
#1984 = DEFINITIONAL_REPRESENTATION('',(#1985),#1988);
#1985 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1986,#1987),.UNSPECIFIED.,.F.,
  .F.,(2,2),(4.712388980385,6.28318530718),.PIECEWISE_BEZIER_KNOTS.);
#1986 = CARTESIAN_POINT('',(1.570796326795,0.297));
#1987 = CARTESIAN_POINT('',(0.,0.297));
#1988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1989 = ORIENTED_EDGE('',*,*,#1634,.T.);
#1990 = ORIENTED_EDGE('',*,*,#1895,.F.);
#1991 = ADVANCED_FACE('',(#1992),#1701,.T.);
#1992 = FACE_BOUND('',#1993,.T.);
#1993 = EDGE_LOOP('',(#1994,#2015,#2016,#2017));
#1994 = ORIENTED_EDGE('',*,*,#1995,.F.);
#1995 = EDGE_CURVE('',#1848,#1896,#1996,.T.);
#1996 = SURFACE_CURVE('',#1997,(#2001,#2008),.PCURVE_S1.);
#1997 = LINE('',#1998,#1999);
#1998 = CARTESIAN_POINT('',(0.3,0.117,0.));
#1999 = VECTOR('',#2000,1.);
#2000 = DIRECTION('',(0.,0.,1.));
#2001 = PCURVE('',#1701,#2002);
#2002 = DEFINITIONAL_REPRESENTATION('',(#2003),#2007);
#2003 = LINE('',#2004,#2005);
#2004 = CARTESIAN_POINT('',(0.,0.));
#2005 = VECTOR('',#2006,1.);
#2006 = DIRECTION('',(0.,1.));
#2007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2008 = PCURVE('',#1735,#2009);
#2009 = DEFINITIONAL_REPRESENTATION('',(#2010),#2014);
#2010 = LINE('',#2011,#2012);
#2011 = CARTESIAN_POINT('',(0.,-0.267));
#2012 = VECTOR('',#2013,1.);
#2013 = DIRECTION('',(1.,0.));
#2014 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2015 = ORIENTED_EDGE('',*,*,#1945,.T.);
#2016 = ORIENTED_EDGE('',*,*,#1687,.T.);
#2017 = ORIENTED_EDGE('',*,*,#1970,.T.);
#2018 = ADVANCED_FACE('',(#2019),#1735,.T.);
#2019 = FACE_BOUND('',#2020,.T.);
#2020 = EDGE_LOOP('',(#2021,#2022,#2023,#2024));
#2021 = ORIENTED_EDGE('',*,*,#1717,.F.);
#2022 = ORIENTED_EDGE('',*,*,#1869,.T.);
#2023 = ORIENTED_EDGE('',*,*,#1995,.T.);
#2024 = ORIENTED_EDGE('',*,*,#1919,.F.);
#2025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2029)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2026,#2027,#2028)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2026 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2027 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2028 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2029 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2026,
  'distance_accuracy_value','confusion accuracy');
#2030 = SHAPE_DEFINITION_REPRESENTATION(#2031,#1038);
#2031 = PRODUCT_DEFINITION_SHAPE('','',#2032);
#2032 = PRODUCT_DEFINITION('design','',#2033,#2036);
#2033 = PRODUCT_DEFINITION_FORMATION('','',#2034);
#2034 = PRODUCT('Fillet2','Fillet2','',(#2035));
#2035 = PRODUCT_CONTEXT('',#2,'mechanical');
#2036 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2037 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2038,#2040);
#2038 = ( REPRESENTATION_RELATIONSHIP('','',#1038,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2039) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2039 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#2040 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2041);
#2041 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','=>[0:1:1:3]','',#5,#2032,$);
#2042 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2034));
#2043 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#2044),#2710);
#2044 = MANIFOLD_SOLID_BREP('',#2045);
#2045 = CLOSED_SHELL('',(#2046,#2290,#2366,#2416,#2470,#2519,#2568,#2622
    ,#2672,#2699));
#2046 = ADVANCED_FACE('',(#2047),#2061,.F.);
#2047 = FACE_BOUND('',#2048,.F.);
#2048 = EDGE_LOOP('',(#2049,#2084,#2113,#2141,#2174,#2202,#2231,#2259));
#2049 = ORIENTED_EDGE('',*,*,#2050,.F.);
#2050 = EDGE_CURVE('',#2051,#2053,#2055,.T.);
#2051 = VERTEX_POINT('',#2052);
#2052 = CARTESIAN_POINT('',(-0.15,-0.147,3.63E-02));
#2053 = VERTEX_POINT('',#2054);
#2054 = CARTESIAN_POINT('',(-0.15,-0.147,0.2937));
#2055 = SURFACE_CURVE('',#2056,(#2060,#2072),.PCURVE_S1.);
#2056 = LINE('',#2057,#2058);
#2057 = CARTESIAN_POINT('',(-0.15,-0.147,3.3E-03));
#2058 = VECTOR('',#2059,1.);
#2059 = DIRECTION('',(0.,0.,1.));
#2060 = PCURVE('',#2061,#2066);
#2061 = PLANE('',#2062);
#2062 = AXIS2_PLACEMENT_3D('',#2063,#2064,#2065);
#2063 = CARTESIAN_POINT('',(-0.15,-0.147,3.3E-03));
#2064 = DIRECTION('',(1.,0.,0.));
#2065 = DIRECTION('',(0.,0.,1.));
#2066 = DEFINITIONAL_REPRESENTATION('',(#2067),#2071);
#2067 = LINE('',#2068,#2069);
#2068 = CARTESIAN_POINT('',(0.,0.));
#2069 = VECTOR('',#2070,1.);
#2070 = DIRECTION('',(1.,0.));
#2071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2072 = PCURVE('',#2073,#2078);
#2073 = PLANE('',#2074);
#2074 = AXIS2_PLACEMENT_3D('',#2075,#2076,#2077);
#2075 = CARTESIAN_POINT('',(-0.15,-0.147,3.3E-03));
#2076 = DIRECTION('',(0.,1.,0.));
#2077 = DIRECTION('',(0.,0.,1.));
#2078 = DEFINITIONAL_REPRESENTATION('',(#2079),#2083);
#2079 = LINE('',#2080,#2081);
#2080 = CARTESIAN_POINT('',(0.,0.));
#2081 = VECTOR('',#2082,1.);
#2082 = DIRECTION('',(1.,0.));
#2083 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2084 = ORIENTED_EDGE('',*,*,#2085,.T.);
#2085 = EDGE_CURVE('',#2051,#2086,#2088,.T.);
#2086 = VERTEX_POINT('',#2087);
#2087 = CARTESIAN_POINT('',(-0.15,-0.114,3.3E-03));
#2088 = SURFACE_CURVE('',#2089,(#2094,#2101),.PCURVE_S1.);
#2089 = CIRCLE('',#2090,3.3E-02);
#2090 = AXIS2_PLACEMENT_3D('',#2091,#2092,#2093);
#2091 = CARTESIAN_POINT('',(-0.15,-0.114,3.63E-02));
#2092 = DIRECTION('',(1.,0.,0.));
#2093 = DIRECTION('',(-0.,0.,1.));
#2094 = PCURVE('',#2061,#2095);
#2095 = DEFINITIONAL_REPRESENTATION('',(#2096),#2100);
#2096 = CIRCLE('',#2097,3.3E-02);
#2097 = AXIS2_PLACEMENT_2D('',#2098,#2099);
#2098 = CARTESIAN_POINT('',(3.3E-02,-3.3E-02));
#2099 = DIRECTION('',(1.,0.));
#2100 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2101 = PCURVE('',#2102,#2107);
#2102 = CYLINDRICAL_SURFACE('',#2103,3.3E-02);
#2103 = AXIS2_PLACEMENT_3D('',#2104,#2105,#2106);
#2104 = CARTESIAN_POINT('',(-0.15,-0.114,3.63E-02));
#2105 = DIRECTION('',(1.,0.,0.));
#2106 = DIRECTION('',(0.,-1.,0.));
#2107 = DEFINITIONAL_REPRESENTATION('',(#2108),#2112);
#2108 = LINE('',#2109,#2110);
#2109 = CARTESIAN_POINT('',(-1.570796326795,0.));
#2110 = VECTOR('',#2111,1.);
#2111 = DIRECTION('',(1.,0.));
#2112 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2113 = ORIENTED_EDGE('',*,*,#2114,.T.);
#2114 = EDGE_CURVE('',#2086,#2115,#2117,.T.);
#2115 = VERTEX_POINT('',#2116);
#2116 = CARTESIAN_POINT('',(-0.15,0.114,3.3E-03));
#2117 = SURFACE_CURVE('',#2118,(#2122,#2129),.PCURVE_S1.);
#2118 = LINE('',#2119,#2120);
#2119 = CARTESIAN_POINT('',(-0.15,-0.147,3.3E-03));
#2120 = VECTOR('',#2121,1.);
#2121 = DIRECTION('',(0.,1.,0.));
#2122 = PCURVE('',#2061,#2123);
#2123 = DEFINITIONAL_REPRESENTATION('',(#2124),#2128);
#2124 = LINE('',#2125,#2126);
#2125 = CARTESIAN_POINT('',(0.,0.));
#2126 = VECTOR('',#2127,1.);
#2127 = DIRECTION('',(0.,-1.));
#2128 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2129 = PCURVE('',#2130,#2135);
#2130 = PLANE('',#2131);
#2131 = AXIS2_PLACEMENT_3D('',#2132,#2133,#2134);
#2132 = CARTESIAN_POINT('',(-0.15,-0.147,3.3E-03));
#2133 = DIRECTION('',(0.,0.,1.));
#2134 = DIRECTION('',(1.,0.,0.));
#2135 = DEFINITIONAL_REPRESENTATION('',(#2136),#2140);
#2136 = LINE('',#2137,#2138);
#2137 = CARTESIAN_POINT('',(0.,0.));
#2138 = VECTOR('',#2139,1.);
#2139 = DIRECTION('',(0.,1.));
#2140 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2141 = ORIENTED_EDGE('',*,*,#2142,.F.);
#2142 = EDGE_CURVE('',#2143,#2115,#2145,.T.);
#2143 = VERTEX_POINT('',#2144);
#2144 = CARTESIAN_POINT('',(-0.15,0.147,3.63E-02));
#2145 = SURFACE_CURVE('',#2146,(#2151,#2162),.PCURVE_S1.);
#2146 = CIRCLE('',#2147,3.3E-02);
#2147 = AXIS2_PLACEMENT_3D('',#2148,#2149,#2150);
#2148 = CARTESIAN_POINT('',(-0.15,0.114,3.63E-02));
#2149 = DIRECTION('',(-1.,0.,-0.));
#2150 = DIRECTION('',(-0.,0.,1.));
#2151 = PCURVE('',#2061,#2152);
#2152 = DEFINITIONAL_REPRESENTATION('',(#2153),#2161);
#2153 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2154,#2155,#2156,#2157,
#2158,#2159,#2160),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2154 = CARTESIAN_POINT('',(6.6E-02,-0.261));
#2155 = CARTESIAN_POINT('',(6.6E-02,-0.31815767665));
#2156 = CARTESIAN_POINT('',(1.65E-02,-0.289578838325));
#2157 = CARTESIAN_POINT('',(-3.3E-02,-0.261));
#2158 = CARTESIAN_POINT('',(1.65E-02,-0.232421161675));
#2159 = CARTESIAN_POINT('',(6.6E-02,-0.20384232335));
#2160 = CARTESIAN_POINT('',(6.6E-02,-0.261));
#2161 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2162 = PCURVE('',#2163,#2168);
#2163 = CYLINDRICAL_SURFACE('',#2164,3.3E-02);
#2164 = AXIS2_PLACEMENT_3D('',#2165,#2166,#2167);
#2165 = CARTESIAN_POINT('',(-0.15,0.114,3.63E-02));
#2166 = DIRECTION('',(1.,0.,0.));
#2167 = DIRECTION('',(-0.,1.,0.));
#2168 = DEFINITIONAL_REPRESENTATION('',(#2169),#2173);
#2169 = LINE('',#2170,#2171);
#2170 = CARTESIAN_POINT('',(1.570796326795,-0.));
#2171 = VECTOR('',#2172,1.);
#2172 = DIRECTION('',(-1.,0.));
#2173 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2174 = ORIENTED_EDGE('',*,*,#2175,.T.);
#2175 = EDGE_CURVE('',#2143,#2176,#2178,.T.);
#2176 = VERTEX_POINT('',#2177);
#2177 = CARTESIAN_POINT('',(-0.15,0.147,0.2937));
#2178 = SURFACE_CURVE('',#2179,(#2183,#2190),.PCURVE_S1.);
#2179 = LINE('',#2180,#2181);
#2180 = CARTESIAN_POINT('',(-0.15,0.147,3.3E-03));
#2181 = VECTOR('',#2182,1.);
#2182 = DIRECTION('',(0.,0.,1.));
#2183 = PCURVE('',#2061,#2184);
#2184 = DEFINITIONAL_REPRESENTATION('',(#2185),#2189);
#2185 = LINE('',#2186,#2187);
#2186 = CARTESIAN_POINT('',(0.,-0.294));
#2187 = VECTOR('',#2188,1.);
#2188 = DIRECTION('',(1.,0.));
#2189 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2190 = PCURVE('',#2191,#2196);
#2191 = PLANE('',#2192);
#2192 = AXIS2_PLACEMENT_3D('',#2193,#2194,#2195);
#2193 = CARTESIAN_POINT('',(-0.15,0.147,3.3E-03));
#2194 = DIRECTION('',(0.,1.,0.));
#2195 = DIRECTION('',(0.,0.,1.));
#2196 = DEFINITIONAL_REPRESENTATION('',(#2197),#2201);
#2197 = LINE('',#2198,#2199);
#2198 = CARTESIAN_POINT('',(0.,0.));
#2199 = VECTOR('',#2200,1.);
#2200 = DIRECTION('',(1.,0.));
#2201 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2202 = ORIENTED_EDGE('',*,*,#2203,.T.);
#2203 = EDGE_CURVE('',#2176,#2204,#2206,.T.);
#2204 = VERTEX_POINT('',#2205);
#2205 = CARTESIAN_POINT('',(-0.15,0.114,0.3267));
#2206 = SURFACE_CURVE('',#2207,(#2212,#2219),.PCURVE_S1.);
#2207 = CIRCLE('',#2208,3.3E-02);
#2208 = AXIS2_PLACEMENT_3D('',#2209,#2210,#2211);
#2209 = CARTESIAN_POINT('',(-0.15,0.114,0.2937));
#2210 = DIRECTION('',(1.,0.,0.));
#2211 = DIRECTION('',(-0.,0.,1.));
#2212 = PCURVE('',#2061,#2213);
#2213 = DEFINITIONAL_REPRESENTATION('',(#2214),#2218);
#2214 = CIRCLE('',#2215,3.3E-02);
#2215 = AXIS2_PLACEMENT_2D('',#2216,#2217);
#2216 = CARTESIAN_POINT('',(0.2904,-0.261));
#2217 = DIRECTION('',(1.,0.));
#2218 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2219 = PCURVE('',#2220,#2225);
#2220 = CYLINDRICAL_SURFACE('',#2221,3.3E-02);
#2221 = AXIS2_PLACEMENT_3D('',#2222,#2223,#2224);
#2222 = CARTESIAN_POINT('',(-0.15,0.114,0.2937));
#2223 = DIRECTION('',(1.,0.,0.));
#2224 = DIRECTION('',(-0.,1.,0.));
#2225 = DEFINITIONAL_REPRESENTATION('',(#2226),#2230);
#2226 = LINE('',#2227,#2228);
#2227 = CARTESIAN_POINT('',(-4.712388980385,0.));
#2228 = VECTOR('',#2229,1.);
#2229 = DIRECTION('',(1.,0.));
#2230 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2231 = ORIENTED_EDGE('',*,*,#2232,.F.);
#2232 = EDGE_CURVE('',#2233,#2204,#2235,.T.);
#2233 = VERTEX_POINT('',#2234);
#2234 = CARTESIAN_POINT('',(-0.15,-0.114,0.3267));
#2235 = SURFACE_CURVE('',#2236,(#2240,#2247),.PCURVE_S1.);
#2236 = LINE('',#2237,#2238);
#2237 = CARTESIAN_POINT('',(-0.15,-0.147,0.3267));
#2238 = VECTOR('',#2239,1.);
#2239 = DIRECTION('',(0.,1.,0.));
#2240 = PCURVE('',#2061,#2241);
#2241 = DEFINITIONAL_REPRESENTATION('',(#2242),#2246);
#2242 = LINE('',#2243,#2244);
#2243 = CARTESIAN_POINT('',(0.3234,0.));
#2244 = VECTOR('',#2245,1.);
#2245 = DIRECTION('',(0.,-1.));
#2246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2247 = PCURVE('',#2248,#2253);
#2248 = PLANE('',#2249);
#2249 = AXIS2_PLACEMENT_3D('',#2250,#2251,#2252);
#2250 = CARTESIAN_POINT('',(-0.15,-0.147,0.3267));
#2251 = DIRECTION('',(0.,0.,1.));
#2252 = DIRECTION('',(1.,0.,0.));
#2253 = DEFINITIONAL_REPRESENTATION('',(#2254),#2258);
#2254 = LINE('',#2255,#2256);
#2255 = CARTESIAN_POINT('',(0.,0.));
#2256 = VECTOR('',#2257,1.);
#2257 = DIRECTION('',(0.,1.));
#2258 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2259 = ORIENTED_EDGE('',*,*,#2260,.F.);
#2260 = EDGE_CURVE('',#2053,#2233,#2261,.T.);
#2261 = SURFACE_CURVE('',#2262,(#2267,#2278),.PCURVE_S1.);
#2262 = CIRCLE('',#2263,3.3E-02);
#2263 = AXIS2_PLACEMENT_3D('',#2264,#2265,#2266);
#2264 = CARTESIAN_POINT('',(-0.15,-0.114,0.2937));
#2265 = DIRECTION('',(-1.,0.,-0.));
#2266 = DIRECTION('',(-0.,0.,1.));
#2267 = PCURVE('',#2061,#2268);
#2268 = DEFINITIONAL_REPRESENTATION('',(#2269),#2277);
#2269 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2270,#2271,#2272,#2273,
#2274,#2275,#2276),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2270 = CARTESIAN_POINT('',(0.3234,-3.3E-02));
#2271 = CARTESIAN_POINT('',(0.3234,-9.015767664977E-02));
#2272 = CARTESIAN_POINT('',(0.2739,-6.157883832489E-02));
#2273 = CARTESIAN_POINT('',(0.2244,-3.3E-02));
#2274 = CARTESIAN_POINT('',(0.2739,-4.421161675114E-03));
#2275 = CARTESIAN_POINT('',(0.3234,2.415767664977E-02));
#2276 = CARTESIAN_POINT('',(0.3234,-3.3E-02));
#2277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2278 = PCURVE('',#2279,#2284);
#2279 = CYLINDRICAL_SURFACE('',#2280,3.3E-02);
#2280 = AXIS2_PLACEMENT_3D('',#2281,#2282,#2283);
#2281 = CARTESIAN_POINT('',(-0.15,-0.114,0.2937));
#2282 = DIRECTION('',(1.,0.,0.));
#2283 = DIRECTION('',(0.,-1.,0.));
#2284 = DEFINITIONAL_REPRESENTATION('',(#2285),#2289);
#2285 = LINE('',#2286,#2287);
#2286 = CARTESIAN_POINT('',(4.712388980385,-0.));
#2287 = VECTOR('',#2288,1.);
#2288 = DIRECTION('',(-1.,0.));
#2289 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2290 = ADVANCED_FACE('',(#2291),#2073,.F.);
#2291 = FACE_BOUND('',#2292,.F.);
#2292 = EDGE_LOOP('',(#2293,#2323,#2344,#2345));
#2293 = ORIENTED_EDGE('',*,*,#2294,.F.);
#2294 = EDGE_CURVE('',#2295,#2297,#2299,.T.);
#2295 = VERTEX_POINT('',#2296);
#2296 = CARTESIAN_POINT('',(0.15,-0.147,3.63E-02));
#2297 = VERTEX_POINT('',#2298);
#2298 = CARTESIAN_POINT('',(0.15,-0.147,0.2937));
#2299 = SURFACE_CURVE('',#2300,(#2304,#2311),.PCURVE_S1.);
#2300 = LINE('',#2301,#2302);
#2301 = CARTESIAN_POINT('',(0.15,-0.147,3.3E-03));
#2302 = VECTOR('',#2303,1.);
#2303 = DIRECTION('',(0.,0.,1.));
#2304 = PCURVE('',#2073,#2305);
#2305 = DEFINITIONAL_REPRESENTATION('',(#2306),#2310);
#2306 = LINE('',#2307,#2308);
#2307 = CARTESIAN_POINT('',(0.,0.3));
#2308 = VECTOR('',#2309,1.);
#2309 = DIRECTION('',(1.,0.));
#2310 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2311 = PCURVE('',#2312,#2317);
#2312 = PLANE('',#2313);
#2313 = AXIS2_PLACEMENT_3D('',#2314,#2315,#2316);
#2314 = CARTESIAN_POINT('',(0.15,-0.147,3.3E-03));
#2315 = DIRECTION('',(1.,0.,0.));
#2316 = DIRECTION('',(0.,0.,1.));
#2317 = DEFINITIONAL_REPRESENTATION('',(#2318),#2322);
#2318 = LINE('',#2319,#2320);
#2319 = CARTESIAN_POINT('',(0.,0.));
#2320 = VECTOR('',#2321,1.);
#2321 = DIRECTION('',(1.,0.));
#2322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2323 = ORIENTED_EDGE('',*,*,#2324,.F.);
#2324 = EDGE_CURVE('',#2051,#2295,#2325,.T.);
#2325 = SURFACE_CURVE('',#2326,(#2330,#2337),.PCURVE_S1.);
#2326 = LINE('',#2327,#2328);
#2327 = CARTESIAN_POINT('',(-0.15,-0.147,3.63E-02));
#2328 = VECTOR('',#2329,1.);
#2329 = DIRECTION('',(1.,0.,0.));
#2330 = PCURVE('',#2073,#2331);
#2331 = DEFINITIONAL_REPRESENTATION('',(#2332),#2336);
#2332 = LINE('',#2333,#2334);
#2333 = CARTESIAN_POINT('',(3.3E-02,0.));
#2334 = VECTOR('',#2335,1.);
#2335 = DIRECTION('',(0.,1.));
#2336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2337 = PCURVE('',#2102,#2338);
#2338 = DEFINITIONAL_REPRESENTATION('',(#2339),#2343);
#2339 = LINE('',#2340,#2341);
#2340 = CARTESIAN_POINT('',(0.,0.));
#2341 = VECTOR('',#2342,1.);
#2342 = DIRECTION('',(0.,1.));
#2343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2344 = ORIENTED_EDGE('',*,*,#2050,.T.);
#2345 = ORIENTED_EDGE('',*,*,#2346,.T.);
#2346 = EDGE_CURVE('',#2053,#2297,#2347,.T.);
#2347 = SURFACE_CURVE('',#2348,(#2352,#2359),.PCURVE_S1.);
#2348 = LINE('',#2349,#2350);
#2349 = CARTESIAN_POINT('',(-0.15,-0.147,0.2937));
#2350 = VECTOR('',#2351,1.);
#2351 = DIRECTION('',(1.,0.,0.));
#2352 = PCURVE('',#2073,#2353);
#2353 = DEFINITIONAL_REPRESENTATION('',(#2354),#2358);
#2354 = LINE('',#2355,#2356);
#2355 = CARTESIAN_POINT('',(0.2904,0.));
#2356 = VECTOR('',#2357,1.);
#2357 = DIRECTION('',(0.,1.));
#2358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2359 = PCURVE('',#2279,#2360);
#2360 = DEFINITIONAL_REPRESENTATION('',(#2361),#2365);
#2361 = LINE('',#2362,#2363);
#2362 = CARTESIAN_POINT('',(-0.,0.));
#2363 = VECTOR('',#2364,1.);
#2364 = DIRECTION('',(-0.,1.));
#2365 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2366 = ADVANCED_FACE('',(#2367),#2102,.T.);
#2367 = FACE_BOUND('',#2368,.T.);
#2368 = EDGE_LOOP('',(#2369,#2370,#2393,#2415));
#2369 = ORIENTED_EDGE('',*,*,#2085,.T.);
#2370 = ORIENTED_EDGE('',*,*,#2371,.T.);
#2371 = EDGE_CURVE('',#2086,#2372,#2374,.T.);
#2372 = VERTEX_POINT('',#2373);
#2373 = CARTESIAN_POINT('',(0.15,-0.114,3.3E-03));
#2374 = SURFACE_CURVE('',#2375,(#2379,#2386),.PCURVE_S1.);
#2375 = LINE('',#2376,#2377);
#2376 = CARTESIAN_POINT('',(-0.15,-0.114,3.3E-03));
#2377 = VECTOR('',#2378,1.);
#2378 = DIRECTION('',(1.,0.,0.));
#2379 = PCURVE('',#2102,#2380);
#2380 = DEFINITIONAL_REPRESENTATION('',(#2381),#2385);
#2381 = LINE('',#2382,#2383);
#2382 = CARTESIAN_POINT('',(1.570796326795,0.));
#2383 = VECTOR('',#2384,1.);
#2384 = DIRECTION('',(0.,1.));
#2385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2386 = PCURVE('',#2130,#2387);
#2387 = DEFINITIONAL_REPRESENTATION('',(#2388),#2392);
#2388 = LINE('',#2389,#2390);
#2389 = CARTESIAN_POINT('',(0.,3.3E-02));
#2390 = VECTOR('',#2391,1.);
#2391 = DIRECTION('',(1.,0.));
#2392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2393 = ORIENTED_EDGE('',*,*,#2394,.F.);
#2394 = EDGE_CURVE('',#2295,#2372,#2395,.T.);
#2395 = SURFACE_CURVE('',#2396,(#2401,#2408),.PCURVE_S1.);
#2396 = CIRCLE('',#2397,3.3E-02);
#2397 = AXIS2_PLACEMENT_3D('',#2398,#2399,#2400);
#2398 = CARTESIAN_POINT('',(0.15,-0.114,3.63E-02));
#2399 = DIRECTION('',(1.,0.,0.));
#2400 = DIRECTION('',(-0.,0.,1.));
#2401 = PCURVE('',#2102,#2402);
#2402 = DEFINITIONAL_REPRESENTATION('',(#2403),#2407);
#2403 = LINE('',#2404,#2405);
#2404 = CARTESIAN_POINT('',(-1.570796326795,0.3));
#2405 = VECTOR('',#2406,1.);
#2406 = DIRECTION('',(1.,0.));
#2407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2408 = PCURVE('',#2312,#2409);
#2409 = DEFINITIONAL_REPRESENTATION('',(#2410),#2414);
#2410 = CIRCLE('',#2411,3.3E-02);
#2411 = AXIS2_PLACEMENT_2D('',#2412,#2413);
#2412 = CARTESIAN_POINT('',(3.3E-02,-3.3E-02));
#2413 = DIRECTION('',(1.,0.));
#2414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2415 = ORIENTED_EDGE('',*,*,#2324,.F.);
#2416 = ADVANCED_FACE('',(#2417),#2279,.T.);
#2417 = FACE_BOUND('',#2418,.F.);
#2418 = EDGE_LOOP('',(#2419,#2420,#2443,#2469));
#2419 = ORIENTED_EDGE('',*,*,#2260,.T.);
#2420 = ORIENTED_EDGE('',*,*,#2421,.T.);
#2421 = EDGE_CURVE('',#2233,#2422,#2424,.T.);
#2422 = VERTEX_POINT('',#2423);
#2423 = CARTESIAN_POINT('',(0.15,-0.114,0.3267));
#2424 = SURFACE_CURVE('',#2425,(#2429,#2436),.PCURVE_S1.);
#2425 = LINE('',#2426,#2427);
#2426 = CARTESIAN_POINT('',(-0.15,-0.114,0.3267));
#2427 = VECTOR('',#2428,1.);
#2428 = DIRECTION('',(1.,0.,0.));
#2429 = PCURVE('',#2279,#2430);
#2430 = DEFINITIONAL_REPRESENTATION('',(#2431),#2435);
#2431 = LINE('',#2432,#2433);
#2432 = CARTESIAN_POINT('',(-1.570796326795,0.));
#2433 = VECTOR('',#2434,1.);
#2434 = DIRECTION('',(-0.,1.));
#2435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2436 = PCURVE('',#2248,#2437);
#2437 = DEFINITIONAL_REPRESENTATION('',(#2438),#2442);
#2438 = LINE('',#2439,#2440);
#2439 = CARTESIAN_POINT('',(0.,3.3E-02));
#2440 = VECTOR('',#2441,1.);
#2441 = DIRECTION('',(1.,0.));
#2442 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2443 = ORIENTED_EDGE('',*,*,#2444,.F.);
#2444 = EDGE_CURVE('',#2297,#2422,#2445,.T.);
#2445 = SURFACE_CURVE('',#2446,(#2451,#2458),.PCURVE_S1.);
#2446 = CIRCLE('',#2447,3.3E-02);
#2447 = AXIS2_PLACEMENT_3D('',#2448,#2449,#2450);
#2448 = CARTESIAN_POINT('',(0.15,-0.114,0.2937));
#2449 = DIRECTION('',(-1.,0.,-0.));
#2450 = DIRECTION('',(-0.,0.,1.));
#2451 = PCURVE('',#2279,#2452);
#2452 = DEFINITIONAL_REPRESENTATION('',(#2453),#2457);
#2453 = LINE('',#2454,#2455);
#2454 = CARTESIAN_POINT('',(4.712388980385,0.3));
#2455 = VECTOR('',#2456,1.);
#2456 = DIRECTION('',(-1.,0.));
#2457 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2458 = PCURVE('',#2312,#2459);
#2459 = DEFINITIONAL_REPRESENTATION('',(#2460),#2468);
#2460 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2461,#2462,#2463,#2464,
#2465,#2466,#2467),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2461 = CARTESIAN_POINT('',(0.3234,-3.3E-02));
#2462 = CARTESIAN_POINT('',(0.3234,-9.015767664977E-02));
#2463 = CARTESIAN_POINT('',(0.2739,-6.157883832489E-02));
#2464 = CARTESIAN_POINT('',(0.2244,-3.3E-02));
#2465 = CARTESIAN_POINT('',(0.2739,-4.421161675114E-03));
#2466 = CARTESIAN_POINT('',(0.3234,2.415767664977E-02));
#2467 = CARTESIAN_POINT('',(0.3234,-3.3E-02));
#2468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2469 = ORIENTED_EDGE('',*,*,#2346,.F.);
#2470 = ADVANCED_FACE('',(#2471),#2130,.F.);
#2471 = FACE_BOUND('',#2472,.F.);
#2472 = EDGE_LOOP('',(#2473,#2474,#2475,#2498));
#2473 = ORIENTED_EDGE('',*,*,#2114,.F.);
#2474 = ORIENTED_EDGE('',*,*,#2371,.T.);
#2475 = ORIENTED_EDGE('',*,*,#2476,.T.);
#2476 = EDGE_CURVE('',#2372,#2477,#2479,.T.);
#2477 = VERTEX_POINT('',#2478);
#2478 = CARTESIAN_POINT('',(0.15,0.114,3.3E-03));
#2479 = SURFACE_CURVE('',#2480,(#2484,#2491),.PCURVE_S1.);
#2480 = LINE('',#2481,#2482);
#2481 = CARTESIAN_POINT('',(0.15,-0.147,3.3E-03));
#2482 = VECTOR('',#2483,1.);
#2483 = DIRECTION('',(0.,1.,0.));
#2484 = PCURVE('',#2130,#2485);
#2485 = DEFINITIONAL_REPRESENTATION('',(#2486),#2490);
#2486 = LINE('',#2487,#2488);
#2487 = CARTESIAN_POINT('',(0.3,0.));
#2488 = VECTOR('',#2489,1.);
#2489 = DIRECTION('',(0.,1.));
#2490 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2491 = PCURVE('',#2312,#2492);
#2492 = DEFINITIONAL_REPRESENTATION('',(#2493),#2497);
#2493 = LINE('',#2494,#2495);
#2494 = CARTESIAN_POINT('',(0.,0.));
#2495 = VECTOR('',#2496,1.);
#2496 = DIRECTION('',(0.,-1.));
#2497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2498 = ORIENTED_EDGE('',*,*,#2499,.F.);
#2499 = EDGE_CURVE('',#2115,#2477,#2500,.T.);
#2500 = SURFACE_CURVE('',#2501,(#2505,#2512),.PCURVE_S1.);
#2501 = LINE('',#2502,#2503);
#2502 = CARTESIAN_POINT('',(-0.15,0.114,3.3E-03));
#2503 = VECTOR('',#2504,1.);
#2504 = DIRECTION('',(1.,0.,0.));
#2505 = PCURVE('',#2130,#2506);
#2506 = DEFINITIONAL_REPRESENTATION('',(#2507),#2511);
#2507 = LINE('',#2508,#2509);
#2508 = CARTESIAN_POINT('',(0.,0.261));
#2509 = VECTOR('',#2510,1.);
#2510 = DIRECTION('',(1.,0.));
#2511 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2512 = PCURVE('',#2163,#2513);
#2513 = DEFINITIONAL_REPRESENTATION('',(#2514),#2518);
#2514 = LINE('',#2515,#2516);
#2515 = CARTESIAN_POINT('',(-1.570796326795,0.));
#2516 = VECTOR('',#2517,1.);
#2517 = DIRECTION('',(-0.,1.));
#2518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2519 = ADVANCED_FACE('',(#2520),#2248,.T.);
#2520 = FACE_BOUND('',#2521,.T.);
#2521 = EDGE_LOOP('',(#2522,#2523,#2524,#2547));
#2522 = ORIENTED_EDGE('',*,*,#2232,.F.);
#2523 = ORIENTED_EDGE('',*,*,#2421,.T.);
#2524 = ORIENTED_EDGE('',*,*,#2525,.T.);
#2525 = EDGE_CURVE('',#2422,#2526,#2528,.T.);
#2526 = VERTEX_POINT('',#2527);
#2527 = CARTESIAN_POINT('',(0.15,0.114,0.3267));
#2528 = SURFACE_CURVE('',#2529,(#2533,#2540),.PCURVE_S1.);
#2529 = LINE('',#2530,#2531);
#2530 = CARTESIAN_POINT('',(0.15,-0.147,0.3267));
#2531 = VECTOR('',#2532,1.);
#2532 = DIRECTION('',(0.,1.,0.));
#2533 = PCURVE('',#2248,#2534);
#2534 = DEFINITIONAL_REPRESENTATION('',(#2535),#2539);
#2535 = LINE('',#2536,#2537);
#2536 = CARTESIAN_POINT('',(0.3,0.));
#2537 = VECTOR('',#2538,1.);
#2538 = DIRECTION('',(0.,1.));
#2539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2540 = PCURVE('',#2312,#2541);
#2541 = DEFINITIONAL_REPRESENTATION('',(#2542),#2546);
#2542 = LINE('',#2543,#2544);
#2543 = CARTESIAN_POINT('',(0.3234,0.));
#2544 = VECTOR('',#2545,1.);
#2545 = DIRECTION('',(0.,-1.));
#2546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2547 = ORIENTED_EDGE('',*,*,#2548,.F.);
#2548 = EDGE_CURVE('',#2204,#2526,#2549,.T.);
#2549 = SURFACE_CURVE('',#2550,(#2554,#2561),.PCURVE_S1.);
#2550 = LINE('',#2551,#2552);
#2551 = CARTESIAN_POINT('',(-0.15,0.114,0.3267));
#2552 = VECTOR('',#2553,1.);
#2553 = DIRECTION('',(1.,0.,0.));
#2554 = PCURVE('',#2248,#2555);
#2555 = DEFINITIONAL_REPRESENTATION('',(#2556),#2560);
#2556 = LINE('',#2557,#2558);
#2557 = CARTESIAN_POINT('',(0.,0.261));
#2558 = VECTOR('',#2559,1.);
#2559 = DIRECTION('',(1.,0.));
#2560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2561 = PCURVE('',#2220,#2562);
#2562 = DEFINITIONAL_REPRESENTATION('',(#2563),#2567);
#2563 = LINE('',#2564,#2565);
#2564 = CARTESIAN_POINT('',(1.570796326795,0.));
#2565 = VECTOR('',#2566,1.);
#2566 = DIRECTION('',(0.,1.));
#2567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2568 = ADVANCED_FACE('',(#2569),#2163,.T.);
#2569 = FACE_BOUND('',#2570,.F.);
#2570 = EDGE_LOOP('',(#2571,#2572,#2573,#2601));
#2571 = ORIENTED_EDGE('',*,*,#2142,.T.);
#2572 = ORIENTED_EDGE('',*,*,#2499,.T.);
#2573 = ORIENTED_EDGE('',*,*,#2574,.F.);
#2574 = EDGE_CURVE('',#2575,#2477,#2577,.T.);
#2575 = VERTEX_POINT('',#2576);
#2576 = CARTESIAN_POINT('',(0.15,0.147,3.63E-02));
#2577 = SURFACE_CURVE('',#2578,(#2583,#2590),.PCURVE_S1.);
#2578 = CIRCLE('',#2579,3.3E-02);
#2579 = AXIS2_PLACEMENT_3D('',#2580,#2581,#2582);
#2580 = CARTESIAN_POINT('',(0.15,0.114,3.63E-02));
#2581 = DIRECTION('',(-1.,0.,-0.));
#2582 = DIRECTION('',(-0.,0.,1.));
#2583 = PCURVE('',#2163,#2584);
#2584 = DEFINITIONAL_REPRESENTATION('',(#2585),#2589);
#2585 = LINE('',#2586,#2587);
#2586 = CARTESIAN_POINT('',(1.570796326795,0.3));
#2587 = VECTOR('',#2588,1.);
#2588 = DIRECTION('',(-1.,0.));
#2589 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2590 = PCURVE('',#2312,#2591);
#2591 = DEFINITIONAL_REPRESENTATION('',(#2592),#2600);
#2592 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2593,#2594,#2595,#2596,
#2597,#2598,#2599),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2593 = CARTESIAN_POINT('',(6.6E-02,-0.261));
#2594 = CARTESIAN_POINT('',(6.6E-02,-0.31815767665));
#2595 = CARTESIAN_POINT('',(1.65E-02,-0.289578838325));
#2596 = CARTESIAN_POINT('',(-3.3E-02,-0.261));
#2597 = CARTESIAN_POINT('',(1.65E-02,-0.232421161675));
#2598 = CARTESIAN_POINT('',(6.6E-02,-0.20384232335));
#2599 = CARTESIAN_POINT('',(6.6E-02,-0.261));
#2600 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2601 = ORIENTED_EDGE('',*,*,#2602,.F.);
#2602 = EDGE_CURVE('',#2143,#2575,#2603,.T.);
#2603 = SURFACE_CURVE('',#2604,(#2608,#2615),.PCURVE_S1.);
#2604 = LINE('',#2605,#2606);
#2605 = CARTESIAN_POINT('',(-0.15,0.147,3.63E-02));
#2606 = VECTOR('',#2607,1.);
#2607 = DIRECTION('',(1.,0.,0.));
#2608 = PCURVE('',#2163,#2609);
#2609 = DEFINITIONAL_REPRESENTATION('',(#2610),#2614);
#2610 = LINE('',#2611,#2612);
#2611 = CARTESIAN_POINT('',(-0.,0.));
#2612 = VECTOR('',#2613,1.);
#2613 = DIRECTION('',(-0.,1.));
#2614 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2615 = PCURVE('',#2191,#2616);
#2616 = DEFINITIONAL_REPRESENTATION('',(#2617),#2621);
#2617 = LINE('',#2618,#2619);
#2618 = CARTESIAN_POINT('',(3.3E-02,0.));
#2619 = VECTOR('',#2620,1.);
#2620 = DIRECTION('',(0.,1.));
#2621 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2622 = ADVANCED_FACE('',(#2623),#2220,.T.);
#2623 = FACE_BOUND('',#2624,.T.);
#2624 = EDGE_LOOP('',(#2625,#2626,#2627,#2651));
#2625 = ORIENTED_EDGE('',*,*,#2203,.T.);
#2626 = ORIENTED_EDGE('',*,*,#2548,.T.);
#2627 = ORIENTED_EDGE('',*,*,#2628,.F.);
#2628 = EDGE_CURVE('',#2629,#2526,#2631,.T.);
#2629 = VERTEX_POINT('',#2630);
#2630 = CARTESIAN_POINT('',(0.15,0.147,0.2937));
#2631 = SURFACE_CURVE('',#2632,(#2637,#2644),.PCURVE_S1.);
#2632 = CIRCLE('',#2633,3.3E-02);
#2633 = AXIS2_PLACEMENT_3D('',#2634,#2635,#2636);
#2634 = CARTESIAN_POINT('',(0.15,0.114,0.2937));
#2635 = DIRECTION('',(1.,0.,0.));
#2636 = DIRECTION('',(-0.,0.,1.));
#2637 = PCURVE('',#2220,#2638);
#2638 = DEFINITIONAL_REPRESENTATION('',(#2639),#2643);
#2639 = LINE('',#2640,#2641);
#2640 = CARTESIAN_POINT('',(-4.712388980385,0.3));
#2641 = VECTOR('',#2642,1.);
#2642 = DIRECTION('',(1.,0.));
#2643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2644 = PCURVE('',#2312,#2645);
#2645 = DEFINITIONAL_REPRESENTATION('',(#2646),#2650);
#2646 = CIRCLE('',#2647,3.3E-02);
#2647 = AXIS2_PLACEMENT_2D('',#2648,#2649);
#2648 = CARTESIAN_POINT('',(0.2904,-0.261));
#2649 = DIRECTION('',(1.,0.));
#2650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2651 = ORIENTED_EDGE('',*,*,#2652,.F.);
#2652 = EDGE_CURVE('',#2176,#2629,#2653,.T.);
#2653 = SURFACE_CURVE('',#2654,(#2658,#2665),.PCURVE_S1.);
#2654 = LINE('',#2655,#2656);
#2655 = CARTESIAN_POINT('',(-0.15,0.147,0.2937));
#2656 = VECTOR('',#2657,1.);
#2657 = DIRECTION('',(1.,0.,0.));
#2658 = PCURVE('',#2220,#2659);
#2659 = DEFINITIONAL_REPRESENTATION('',(#2660),#2664);
#2660 = LINE('',#2661,#2662);
#2661 = CARTESIAN_POINT('',(0.,0.));
#2662 = VECTOR('',#2663,1.);
#2663 = DIRECTION('',(0.,1.));
#2664 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2665 = PCURVE('',#2191,#2666);
#2666 = DEFINITIONAL_REPRESENTATION('',(#2667),#2671);
#2667 = LINE('',#2668,#2669);
#2668 = CARTESIAN_POINT('',(0.2904,0.));
#2669 = VECTOR('',#2670,1.);
#2670 = DIRECTION('',(0.,1.));
#2671 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2672 = ADVANCED_FACE('',(#2673),#2191,.T.);
#2673 = FACE_BOUND('',#2674,.T.);
#2674 = EDGE_LOOP('',(#2675,#2696,#2697,#2698));
#2675 = ORIENTED_EDGE('',*,*,#2676,.F.);
#2676 = EDGE_CURVE('',#2575,#2629,#2677,.T.);
#2677 = SURFACE_CURVE('',#2678,(#2682,#2689),.PCURVE_S1.);
#2678 = LINE('',#2679,#2680);
#2679 = CARTESIAN_POINT('',(0.15,0.147,3.3E-03));
#2680 = VECTOR('',#2681,1.);
#2681 = DIRECTION('',(0.,0.,1.));
#2682 = PCURVE('',#2191,#2683);
#2683 = DEFINITIONAL_REPRESENTATION('',(#2684),#2688);
#2684 = LINE('',#2685,#2686);
#2685 = CARTESIAN_POINT('',(0.,0.3));
#2686 = VECTOR('',#2687,1.);
#2687 = DIRECTION('',(1.,0.));
#2688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2689 = PCURVE('',#2312,#2690);
#2690 = DEFINITIONAL_REPRESENTATION('',(#2691),#2695);
#2691 = LINE('',#2692,#2693);
#2692 = CARTESIAN_POINT('',(0.,-0.294));
#2693 = VECTOR('',#2694,1.);
#2694 = DIRECTION('',(1.,0.));
#2695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2696 = ORIENTED_EDGE('',*,*,#2602,.F.);
#2697 = ORIENTED_EDGE('',*,*,#2175,.T.);
#2698 = ORIENTED_EDGE('',*,*,#2652,.T.);
#2699 = ADVANCED_FACE('',(#2700),#2312,.T.);
#2700 = FACE_BOUND('',#2701,.T.);
#2701 = EDGE_LOOP('',(#2702,#2703,#2704,#2705,#2706,#2707,#2708,#2709));
#2702 = ORIENTED_EDGE('',*,*,#2294,.F.);
#2703 = ORIENTED_EDGE('',*,*,#2394,.T.);
#2704 = ORIENTED_EDGE('',*,*,#2476,.T.);
#2705 = ORIENTED_EDGE('',*,*,#2574,.F.);
#2706 = ORIENTED_EDGE('',*,*,#2676,.T.);
#2707 = ORIENTED_EDGE('',*,*,#2628,.T.);
#2708 = ORIENTED_EDGE('',*,*,#2525,.F.);
#2709 = ORIENTED_EDGE('',*,*,#2444,.F.);
#2710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2714)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2711,#2712,#2713)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2711 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2712 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2713 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2714 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2711,
  'distance_accuracy_value','confusion accuracy');
#2715 = SHAPE_DEFINITION_REPRESENTATION(#2716,#2043);
#2716 = PRODUCT_DEFINITION_SHAPE('','',#2717);
#2717 = PRODUCT_DEFINITION('design','',#2718,#2721);
#2718 = PRODUCT_DEFINITION_FORMATION('','',#2719);
#2719 = PRODUCT('body2','body2','',(#2720));
#2720 = PRODUCT_CONTEXT('',#2,'mechanical');
#2721 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2722 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2723,#2725);
#2723 = ( REPRESENTATION_RELATIONSHIP('','',#2043,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2724) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2724 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#2725 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2726);
#2726 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','=>[0:1:1:4]','',#5,#2717,$);
#2727 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2719));
#2728 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #2729),#2025);
#2729 = STYLED_ITEM('color',(#2730),#1039);
#2730 = PRESENTATION_STYLE_ASSIGNMENT((#2731,#2737));
#2731 = SURFACE_STYLE_USAGE(.BOTH.,#2732);
#2732 = SURFACE_SIDE_STYLE('',(#2733));
#2733 = SURFACE_STYLE_FILL_AREA(#2734);
#2734 = FILL_AREA_STYLE('',(#2735));
#2735 = FILL_AREA_STYLE_COLOUR('',#2736);
#2736 = COLOUR_RGB('',0.73400002718,0.773000001907,0.79699999094);
#2737 = CURVE_STYLE('',#2738,POSITIVE_LENGTH_MEASURE(0.1),#2736);
#2738 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#2739 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #2740),#2710);
#2740 = STYLED_ITEM('color',(#2741),#2044);
#2741 = PRESENTATION_STYLE_ASSIGNMENT((#2742,#2748));
#2742 = SURFACE_STYLE_USAGE(.BOTH.,#2743);
#2743 = SURFACE_SIDE_STYLE('',(#2744));
#2744 = SURFACE_STYLE_FILL_AREA(#2745);
#2745 = FILL_AREA_STYLE('',(#2746));
#2746 = FILL_AREA_STYLE_COLOUR('',#2747);
#2747 = COLOUR_RGB('',0.430000007153,0.330000013113,1.999999955297E-02);
#2748 = CURVE_STYLE('',#2749,POSITIVE_LENGTH_MEASURE(0.1),#2747);
#2749 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#2750 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #2751),#1020);
#2751 = STYLED_ITEM('color',(#2752),#34);
#2752 = PRESENTATION_STYLE_ASSIGNMENT((#2753,#2758));
#2753 = SURFACE_STYLE_USAGE(.BOTH.,#2754);
#2754 = SURFACE_SIDE_STYLE('',(#2755));
#2755 = SURFACE_STYLE_FILL_AREA(#2756);
#2756 = FILL_AREA_STYLE('',(#2757));
#2757 = FILL_AREA_STYLE_COLOUR('',#2736);
#2758 = CURVE_STYLE('',#2759,POSITIVE_LENGTH_MEASURE(0.1),#2736);
#2759 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
