ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('STEP AP214'),'1');
FILE_NAME('IS25WJ032F-JTLE-TR_ISI','2025-10-03T08:23:01',(''),(''),'','','');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN'));
ENDSEC;
DATA;
#1=SHAPE_DEFINITION_REPRESENTATION(#2,#3);
#2=PRODUCT_DEFINITION_SHAPE('',$,#4);
#3=SHAPE_REPRESENTATION('',(#188,#244,#540,#836,#1133,#1995,#2857,#3719,#4581,#5443,#6305,#7167,#19),#11);
#4=PRODUCT_DEFINITION('design','example product_definition',#6,#5);
#5=PRODUCT_DEFINITION_CONTEXT('3D Mechanical Parts',#10,'design');
#6=PRODUCT_DEFINITION_FORMATION('1.0','first version',#8);
#7=APPLICATION_PROTOCOL_DEFINITION('international standard','automotive_design',2003,#10);
#8=PRODUCT('product','part','',(#9));
#9=PRODUCT_CONTEXT('3D Mechanical Parts',#10,'mechanical');
#10=APPLICATION_CONTEXT('Core Data for Automotive Mechanical Design Process');
#11=(GEOMETRIC_REPRESENTATION_CONTEXT(3) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#12)) GLOBAL_UNIT_ASSIGNED_CONTEXT((#13,#14,#18)) REPRESENTATION_CONTEXT('ID1','3D'));
#12=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-005),#13,'DISTANCE_ACCURACY_VALUE','Maximum model space distance between geometric entities at asserted connectivities');
#13=(LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.));
#14=(CONVERSION_BASED_UNIT('degree',#16) NAMED_UNIT(#15) PLANE_ANGLE_UNIT());
#15=DIMENSIONAL_EXPONENTS(0.,0.,0.,0.,0.,0.,0.);
#16=MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(0.01745329252),#17);
#17=(NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.));
#18=(NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT());
#19=AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20=CARTESIAN_POINT('',(0.0,0.0,0.0));
#21=DIRECTION('',(0.0,0.0,1.0));
#22=DIRECTION('',(1.0,0.0,0.0));
#25=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION($,(#240,#241,#242,#246,#295,#344,#393,#442,#491,#542,#591,#640,#689,#738,#787,#838,#887,#936,#985,#1034,#1083,#1135,#1184,#1233,#1302,#1371,#1423,#1475,#1528,#1997,#2046,#2095,#2164,#2233,#2285,#2337,#2390,#2859,#2908,#2957,#3026,#3095,#3147,#3199,#3252,#3721,#3770,#3819,#3888,#3957,#4009,#4061,#4114,#4583,#4632,#4681,#4750,#4819,#4871,#4923,#4976,#5445,#5494,#5543,#5612,#5681,#5733,#5785,#5838,#6307,#6356,#6405,#6474,#6543,#6595,#6647,#6700,#7169,#7218,#7267,#7336,#7405,#7457,#7509,#7562),#11);
#26=PRODUCT_CATEGORY_RELATIONSHIP('','',#27,#28);
#27=PRODUCT_CATEGORY('part','');
#28=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#8));
#34=PRESENTATION_STYLE_ASSIGNMENT((#35,#40));
#35=SURFACE_STYLE_USAGE(.BOTH.,#36);
#36=SURFACE_SIDE_STYLE('',(#37));
#37=SURFACE_STYLE_FILL_AREA(#38);
#38=FILL_AREA_STYLE('',(#39));
#39=FILL_AREA_STYLE_COLOUR('',#41);
#40=CURVE_STYLE('',#42,POSITIVE_LENGTH_MEASURE(0.1),#41);
#41=COLOUR_RGB('Aluminum',0.725,0.725,0.725);
#42=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#43=PRESENTATION_STYLE_ASSIGNMENT((#44,#49));
#44=SURFACE_STYLE_USAGE(.BOTH.,#45);
#45=SURFACE_SIDE_STYLE('',(#46));
#46=SURFACE_STYLE_FILL_AREA(#47);
#47=FILL_AREA_STYLE('',(#48));
#48=FILL_AREA_STYLE_COLOUR('',#50);
#49=CURVE_STYLE('',#51,POSITIVE_LENGTH_MEASURE(0.1),#50);
#50=COLOUR_RGB('Black',0.196,0.196,0.196);
#51=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#52=PRESENTATION_STYLE_ASSIGNMENT((#53,#58));
#53=SURFACE_STYLE_USAGE(.BOTH.,#54);
#54=SURFACE_SIDE_STYLE('',(#55));
#55=SURFACE_STYLE_FILL_AREA(#56);
#56=FILL_AREA_STYLE('',(#57));
#57=FILL_AREA_STYLE_COLOUR('',#59);
#58=CURVE_STYLE('',#60,POSITIVE_LENGTH_MEASURE(0.1),#59);
#59=COLOUR_RGB('Pin1',0.588,0.588,0.588);
#60=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#61=PRESENTATION_STYLE_ASSIGNMENT((#62,#67));
#62=SURFACE_STYLE_USAGE(.BOTH.,#63);
#63=SURFACE_SIDE_STYLE('',(#64));
#64=SURFACE_STYLE_FILL_AREA(#65);
#65=FILL_AREA_STYLE('',(#66));
#66=FILL_AREA_STYLE_COLOUR('',#68);
#67=CURVE_STYLE('',#69,POSITIVE_LENGTH_MEASURE(0.1),#68);
#68=COLOUR_RGB('HeatTab',0.588,0.588,0.588);
#69=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#70=PRESENTATION_STYLE_ASSIGNMENT((#71,#76));
#71=SURFACE_STYLE_USAGE(.BOTH.,#72);
#72=SURFACE_SIDE_STYLE('',(#73));
#73=SURFACE_STYLE_FILL_AREA(#74);
#74=FILL_AREA_STYLE('',(#75));
#75=FILL_AREA_STYLE_COLOUR('',#77);
#76=CURVE_STYLE('',#78,POSITIVE_LENGTH_MEASURE(0.1),#77);
#77=COLOUR_RGB('Gold',0.843,0.686,0.0);
#78=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#79=PRESENTATION_STYLE_ASSIGNMENT((#80,#85));
#80=SURFACE_STYLE_USAGE(.BOTH.,#81);
#81=SURFACE_SIDE_STYLE('',(#82));
#82=SURFACE_STYLE_FILL_AREA(#83);
#83=FILL_AREA_STYLE('',(#84));
#84=FILL_AREA_STYLE_COLOUR('',#86);
#85=CURVE_STYLE('',#87,POSITIVE_LENGTH_MEASURE(0.1),#86);
#86=COLOUR_RGB('Brown',0.459,0.345,0.176);
#87=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#88=PRESENTATION_STYLE_ASSIGNMENT((#89,#94));
#89=SURFACE_STYLE_USAGE(.BOTH.,#90);
#90=SURFACE_SIDE_STYLE('',(#91));
#91=SURFACE_STYLE_FILL_AREA(#92);
#92=FILL_AREA_STYLE('',(#93));
#93=FILL_AREA_STYLE_COLOUR('',#95);
#94=CURVE_STYLE('',#96,POSITIVE_LENGTH_MEASURE(0.1),#95);
#95=COLOUR_RGB('Tan',0.784,0.686,0.51);
#96=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#97=PRESENTATION_STYLE_ASSIGNMENT((#98,#103));
#98=SURFACE_STYLE_USAGE(.BOTH.,#99);
#99=SURFACE_SIDE_STYLE('',(#100));
#100=SURFACE_STYLE_FILL_AREA(#101);
#101=FILL_AREA_STYLE('',(#102));
#102=FILL_AREA_STYLE_COLOUR('',#104);
#103=CURVE_STYLE('',#105,POSITIVE_LENGTH_MEASURE(0.1),#104);
#104=COLOUR_RGB('Gray',0.431,0.431,0.431);
#105=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#106=PRESENTATION_STYLE_ASSIGNMENT((#107,#112));
#107=SURFACE_STYLE_USAGE(.BOTH.,#108);
#108=SURFACE_SIDE_STYLE('',(#109));
#109=SURFACE_STYLE_FILL_AREA(#110);
#110=FILL_AREA_STYLE('',(#111));
#111=FILL_AREA_STYLE_COLOUR('',#113);
#112=CURVE_STYLE('',#114,POSITIVE_LENGTH_MEASURE(0.1),#113);
#113=COLOUR_RGB('Red',0.6,0.0,0.0);
#114=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#115=PRESENTATION_STYLE_ASSIGNMENT((#116,#121));
#116=SURFACE_STYLE_USAGE(.BOTH.,#117);
#117=SURFACE_SIDE_STYLE('',(#118));
#118=SURFACE_STYLE_FILL_AREA(#119);
#119=FILL_AREA_STYLE('',(#120));
#120=FILL_AREA_STYLE_COLOUR('',#122);
#121=CURVE_STYLE('',#123,POSITIVE_LENGTH_MEASURE(0.1),#122);
#122=COLOUR_RGB('Blue',0.157,0.157,0.588);
#123=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#124=PRESENTATION_STYLE_ASSIGNMENT((#125,#130));
#125=SURFACE_STYLE_USAGE(.BOTH.,#126);
#126=SURFACE_SIDE_STYLE('',(#127));
#127=SURFACE_STYLE_FILL_AREA(#128);
#128=FILL_AREA_STYLE('',(#129));
#129=FILL_AREA_STYLE_COLOUR('',#131);
#130=CURVE_STYLE('',#132,POSITIVE_LENGTH_MEASURE(0.1),#131);
#131=COLOUR_RGB('Maroon',0.294,0.0,0.0);
#132=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#133=PRESENTATION_STYLE_ASSIGNMENT((#134,#139));
#134=SURFACE_STYLE_USAGE(.BOTH.,#135);
#135=SURFACE_SIDE_STYLE('',(#136));
#136=SURFACE_STYLE_FILL_AREA(#137);
#137=FILL_AREA_STYLE('',(#138));
#138=FILL_AREA_STYLE_COLOUR('',#140);
#139=CURVE_STYLE('',#141,POSITIVE_LENGTH_MEASURE(0.1),#140);
#140=COLOUR_RGB('Green',0.0,0.294,0.0);
#141=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#142=PRESENTATION_STYLE_ASSIGNMENT((#143,#148));
#143=SURFACE_STYLE_USAGE(.BOTH.,#144);
#144=SURFACE_SIDE_STYLE('',(#145));
#145=SURFACE_STYLE_FILL_AREA(#146);
#146=FILL_AREA_STYLE('',(#147));
#147=FILL_AREA_STYLE_COLOUR('',#149);
#148=CURVE_STYLE('',#150,POSITIVE_LENGTH_MEASURE(0.1),#149);
#149=COLOUR_RGB('Pin1Wrap',0.98,0.706,0.176);
#150=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#151=PRESENTATION_STYLE_ASSIGNMENT((#152,#157));
#152=SURFACE_STYLE_USAGE(.BOTH.,#153);
#153=SURFACE_SIDE_STYLE('',(#154));
#154=SURFACE_STYLE_FILL_AREA(#155);
#155=FILL_AREA_STYLE('',(#156));
#156=FILL_AREA_STYLE_COLOUR('',#158);
#157=CURVE_STYLE('',#159,POSITIVE_LENGTH_MEASURE(0.1),#158);
#158=COLOUR_RGB('Pin1Rad',0.588,0.588,0.588);
#159=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#160=PRESENTATION_STYLE_ASSIGNMENT((#161,#166));
#161=SURFACE_STYLE_USAGE(.BOTH.,#162);
#162=SURFACE_SIDE_STYLE('',(#163));
#163=SURFACE_STYLE_FILL_AREA(#164);
#164=FILL_AREA_STYLE('',(#165));
#165=FILL_AREA_STYLE_COLOUR('',#167);
#166=CURVE_STYLE('',#168,POSITIVE_LENGTH_MEASURE(0.1),#167);
#167=COLOUR_RGB('Pin1Axial',0.98,0.706,0.176);
#168=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#169=PRESENTATION_STYLE_ASSIGNMENT((#170,#175));
#170=SURFACE_STYLE_USAGE(.BOTH.,#171);
#171=SURFACE_SIDE_STYLE('',(#172));
#172=SURFACE_STYLE_FILL_AREA(#173);
#173=FILL_AREA_STYLE('',(#174));
#174=FILL_AREA_STYLE_COLOUR('',#176);
#175=CURVE_STYLE('',#177,POSITIVE_LENGTH_MEASURE(0.1),#176);
#176=COLOUR_RGB('Pin1Tant',0.459,0.345,0.176);
#177=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#178=PRESENTATION_STYLE_ASSIGNMENT((#179,#184));
#179=SURFACE_STYLE_USAGE(.BOTH.,#180);
#180=SURFACE_SIDE_STYLE('',(#181));
#181=SURFACE_STYLE_FILL_AREA(#182);
#182=FILL_AREA_STYLE('',(#183));
#183=FILL_AREA_STYLE_COLOUR('',#185);
#184=CURVE_STYLE('',#186,POSITIVE_LENGTH_MEASURE(0.1),#185);
#185=COLOUR_RGB('Shroud',0.235,0.235,0.235);
#186=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#188=MANIFOLD_SOLID_BREP($,#189);
#189=CLOSED_SHELL('',(#190,#191,#192));
#190=ADVANCED_FACE($,(#196,#195),#213,.T.);
#191=ADVANCED_FACE($,(#197),#193,.F.);
#192=ADVANCED_FACE($,(#198),#194,.T.);
#193=PLANE($,#218);
#194=PLANE($,#219);
#195=FACE_BOUND($,#200,.T.);
#196=FACE_OUTER_BOUND($,#199,.T.);
#197=FACE_OUTER_BOUND($,#201,.T.);
#198=FACE_OUTER_BOUND($,#202,.T.);
#199=EDGE_LOOP($,(#209));
#200=EDGE_LOOP($,(#210));
#201=EDGE_LOOP($,(#211));
#202=EDGE_LOOP($,(#212));
#203=CIRCLE($,#216,0.07493);
#204=CIRCLE($,#217,0.07493);
#205=VERTEX_POINT('',#234);
#206=VERTEX_POINT('',#236);
#207=EDGE_CURVE($,#205,#205,#203,.T.);
#208=EDGE_CURVE($,#206,#206,#204,.T.);
#209=ORIENTED_EDGE($,*,*,#207,.F.);
#210=ORIENTED_EDGE($,*,*,#208,.F.);
#211=ORIENTED_EDGE($,*,*,#208,.T.);
#212=ORIENTED_EDGE($,*,*,#207,.T.);
#213=CYLINDRICAL_SURFACE($,#215,0.07493);
#214=AXIS2_PLACEMENT_3D('placement',#232,#220,#221);
#215=AXIS2_PLACEMENT_3D($,#233,#222,#223);
#216=AXIS2_PLACEMENT_3D($,#235,#224,#225);
#217=AXIS2_PLACEMENT_3D($,#237,#226,#227);
#218=AXIS2_PLACEMENT_3D($,#238,#228,#229);
#219=AXIS2_PLACEMENT_3D($,#239,#230,#231);
#220=DIRECTION('axis',(0.,1.,0.));
#221=DIRECTION('refdir',(1.,0.,0.));
#222=DIRECTION('',(0.,0.,1.));
#223=DIRECTION('',(1.,0.,0.));
#224=DIRECTION('',(0.,0.,1.));
#225=DIRECTION('',(1.,0.,0.));
#226=DIRECTION('',(0.,0.,-1.));
#227=DIRECTION('',(1.,0.,0.));
#228=DIRECTION('',(0.,0.,1.));
#229=DIRECTION('',(0.,1.,0.));
#230=DIRECTION('',(0.,0.,1.));
#231=DIRECTION('',(0.,1.,0.));
#232=CARTESIAN_POINT('',(0.,0.,0.));
#233=CARTESIAN_POINT('',(-1.199,1.2,0.61));
#234=CARTESIAN_POINT('',(-1.274,1.2,0.612));
#235=CARTESIAN_POINT('',(-1.199,1.2,0.612));
#236=CARTESIAN_POINT('',(-1.274,1.2,0.61));
#237=CARTESIAN_POINT('',(-1.199,1.2,0.61));
#238=CARTESIAN_POINT('',(-1.199,1.2,0.61));
#239=CARTESIAN_POINT('',(-1.199,1.2,0.612));
#240=STYLED_ITEM('color',(#52),#190);
#241=STYLED_ITEM('color',(#52),#191);
#242=STYLED_ITEM('color',(#52),#192);
#244=MANIFOLD_SOLID_BREP($,#245);
#245=CLOSED_SHELL('',(#247,#296,#345,#394,#443,#492));
#246=STYLED_ITEM('',(#43),#247);
#247=ADVANCED_FACE('',(#253),#248,.T.);
#248=PLANE('',#249);
#249=AXIS2_PLACEMENT_3D('',#250,#251,#252);
#250=CARTESIAN_POINT('',(-1.499,-2.045,0.051));
#251=DIRECTION('',(0.0,-1.0,0.0));
#252=DIRECTION('',(0.,0.,1.));
#253=FACE_OUTER_BOUND('',#254,.T.);
#254=EDGE_LOOP('',(#255,#265,#275,#285));
#258=CARTESIAN_POINT('',(1.499,-2.045,0.051));
#257=VERTEX_POINT('',#258);
#260=CARTESIAN_POINT('',(-1.499,-2.045,0.051));
#259=VERTEX_POINT('',#260);
#256=EDGE_CURVE('',#257,#259,#261,.T.);
#261=LINE('',#258,#263);
#263=VECTOR('',#264,2.9972);
#264=DIRECTION('',(-1.0,0.0,0.0));
#255=ORIENTED_EDGE('',*,*,#256,.F.);
#268=CARTESIAN_POINT('',(1.499,-2.045,0.61));
#267=VERTEX_POINT('',#268);
#266=EDGE_CURVE('',#267,#257,#271,.T.);
#271=LINE('',#268,#273);
#273=VECTOR('',#274,0.5587746);
#274=DIRECTION('',(0.0,0.0,-1.0));
#265=ORIENTED_EDGE('',*,*,#266,.F.);
#278=CARTESIAN_POINT('',(-1.499,-2.045,0.61));
#277=VERTEX_POINT('',#278);
#276=EDGE_CURVE('',#277,#267,#281,.T.);
#281=LINE('',#278,#283);
#283=VECTOR('',#284,2.9972);
#284=DIRECTION('',(1.0,0.0,0.0));
#275=ORIENTED_EDGE('',*,*,#276,.F.);
#286=EDGE_CURVE('',#259,#277,#291,.T.);
#291=LINE('',#260,#293);
#293=VECTOR('',#294,0.5587746);
#294=DIRECTION('',(0.0,0.0,1.0));
#285=ORIENTED_EDGE('',*,*,#286,.F.);
#295=STYLED_ITEM('',(#43),#296);
#296=ADVANCED_FACE('',(#302),#297,.T.);
#297=PLANE('',#298);
#298=AXIS2_PLACEMENT_3D('',#299,#300,#301);
#299=CARTESIAN_POINT('',(1.499,-2.045,0.051));
#300=DIRECTION('',(1.0,0.0,0.0));
#301=DIRECTION('',(0.,0.,1.));
#302=FACE_OUTER_BOUND('',#303,.T.);
#303=EDGE_LOOP('',(#304,#314,#324,#334));
#307=CARTESIAN_POINT('',(1.499,2.045,0.051));
#306=VERTEX_POINT('',#307);
#305=EDGE_CURVE('',#306,#257,#310,.T.);
#310=LINE('',#307,#312);
#312=VECTOR('',#313,4.0894);
#313=DIRECTION('',(0.0,-1.0,0.0));
#304=ORIENTED_EDGE('',*,*,#305,.F.);
#317=CARTESIAN_POINT('',(1.499,2.045,0.61));
#316=VERTEX_POINT('',#317);
#315=EDGE_CURVE('',#316,#306,#320,.T.);
#320=LINE('',#317,#322);
#322=VECTOR('',#323,0.5587746);
#323=DIRECTION('',(0.0,0.0,-1.0));
#314=ORIENTED_EDGE('',*,*,#315,.F.);
#325=EDGE_CURVE('',#267,#316,#330,.T.);
#330=LINE('',#268,#332);
#332=VECTOR('',#333,4.0894);
#333=DIRECTION('',(0.0,1.0,0.0));
#324=ORIENTED_EDGE('',*,*,#325,.F.);
#334=ORIENTED_EDGE('',*,*,#266,.T.);
#344=STYLED_ITEM('',(#43),#345);
#345=ADVANCED_FACE('',(#351),#346,.T.);
#346=PLANE('',#347);
#347=AXIS2_PLACEMENT_3D('',#348,#349,#350);
#348=CARTESIAN_POINT('',(1.499,2.045,0.051));
#349=DIRECTION('',(0.0,1.0,0.0));
#350=DIRECTION('',(0.,0.,1.));
#351=FACE_OUTER_BOUND('',#352,.T.);
#352=EDGE_LOOP('',(#353,#363,#373,#383));
#356=CARTESIAN_POINT('',(-1.499,2.045,0.051));
#355=VERTEX_POINT('',#356);
#354=EDGE_CURVE('',#355,#306,#359,.T.);
#359=LINE('',#356,#361);
#361=VECTOR('',#362,2.9972);
#362=DIRECTION('',(1.0,0.0,0.0));
#353=ORIENTED_EDGE('',*,*,#354,.F.);
#366=CARTESIAN_POINT('',(-1.499,2.045,0.61));
#365=VERTEX_POINT('',#366);
#364=EDGE_CURVE('',#365,#355,#369,.T.);
#369=LINE('',#366,#371);
#371=VECTOR('',#372,0.5587746);
#372=DIRECTION('',(0.0,0.0,-1.0));
#363=ORIENTED_EDGE('',*,*,#364,.F.);
#374=EDGE_CURVE('',#316,#365,#379,.T.);
#379=LINE('',#317,#381);
#381=VECTOR('',#382,2.9972);
#382=DIRECTION('',(-1.0,0.0,0.0));
#373=ORIENTED_EDGE('',*,*,#374,.F.);
#383=ORIENTED_EDGE('',*,*,#315,.T.);
#393=STYLED_ITEM('',(#43),#394);
#394=ADVANCED_FACE('',(#400),#395,.T.);
#395=PLANE('',#396);
#396=AXIS2_PLACEMENT_3D('',#397,#398,#399);
#397=CARTESIAN_POINT('',(-1.499,2.045,0.051));
#398=DIRECTION('',(-1.0,0.0,0.0));
#399=DIRECTION('',(0.,0.,1.));
#400=FACE_OUTER_BOUND('',#401,.T.);
#401=EDGE_LOOP('',(#402,#412,#422,#432));
#403=EDGE_CURVE('',#259,#355,#408,.T.);
#408=LINE('',#260,#410);
#410=VECTOR('',#411,4.0894);
#411=DIRECTION('',(0.0,1.0,0.0));
#402=ORIENTED_EDGE('',*,*,#403,.F.);
#412=ORIENTED_EDGE('',*,*,#286,.T.);
#423=EDGE_CURVE('',#365,#277,#428,.T.);
#428=LINE('',#366,#430);
#430=VECTOR('',#431,4.0894);
#431=DIRECTION('',(0.0,-1.0,0.0));
#422=ORIENTED_EDGE('',*,*,#423,.F.);
#432=ORIENTED_EDGE('',*,*,#364,.T.);
#442=STYLED_ITEM('',(#43),#443);
#443=ADVANCED_FACE('',(#449),#444,.T.);
#444=PLANE('',#445);
#445=AXIS2_PLACEMENT_3D('',#446,#447,#448);
#446=CARTESIAN_POINT('',(-1.499,2.045,0.051));
#447=DIRECTION('',(0.0,0.0,-1.0));
#448=DIRECTION('',(0.,1.,0.));
#449=FACE_OUTER_BOUND('',#450,.T.);
#450=EDGE_LOOP('',(#451,#461,#471,#481));
#451=ORIENTED_EDGE('',*,*,#354,.T.);
#461=ORIENTED_EDGE('',*,*,#305,.T.);
#471=ORIENTED_EDGE('',*,*,#256,.T.);
#481=ORIENTED_EDGE('',*,*,#403,.T.);
#491=STYLED_ITEM('',(#43),#492);
#492=ADVANCED_FACE('',(#498),#493,.T.);
#493=PLANE('',#494);
#494=AXIS2_PLACEMENT_3D('',#495,#496,#497);
#495=CARTESIAN_POINT('',(-1.499,-2.045,0.61));
#496=DIRECTION('',(0.0,0.0,1.0));
#497=DIRECTION('',(0.,1.,0.));
#498=FACE_OUTER_BOUND('',#499,.T.);
#499=EDGE_LOOP('',(#500,#510,#520,#530));
#500=ORIENTED_EDGE('',*,*,#276,.T.);
#510=ORIENTED_EDGE('',*,*,#325,.T.);
#520=ORIENTED_EDGE('',*,*,#374,.T.);
#530=ORIENTED_EDGE('',*,*,#423,.T.);
#540=MANIFOLD_SOLID_BREP($,#541);
#541=CLOSED_SHELL('',(#543,#592,#641,#690,#739,#788));
#542=STYLED_ITEM('',(#61),#543);
#543=ADVANCED_FACE('',(#549),#544,.T.);
#544=PLANE('',#545);
#545=AXIS2_PLACEMENT_3D('',#546,#547,#548);
#546=CARTESIAN_POINT('',(-0.152,0.368,0.0));
#547=DIRECTION('',(0.0,-1.0,0.0));
#548=DIRECTION('',(0.,0.,1.));
#549=FACE_OUTER_BOUND('',#550,.T.);
#550=EDGE_LOOP('',(#551,#561,#571,#581));
#554=CARTESIAN_POINT('',(0.152,0.368,0.0));
#553=VERTEX_POINT('',#554);
#556=CARTESIAN_POINT('',(-0.152,0.368,0.0));
#555=VERTEX_POINT('',#556);
#552=EDGE_CURVE('',#553,#555,#557,.T.);
#557=LINE('',#554,#559);
#559=VECTOR('',#560,0.3048);
#560=DIRECTION('',(-1.0,0.0,0.0));
#551=ORIENTED_EDGE('',*,*,#552,.F.);
#564=CARTESIAN_POINT('',(0.152,0.368,0.051));
#563=VERTEX_POINT('',#564);
#562=EDGE_CURVE('',#563,#553,#567,.T.);
#567=LINE('',#564,#569);
#569=VECTOR('',#570,0.0508);
#570=DIRECTION('',(0.0,0.0,-1.0));
#561=ORIENTED_EDGE('',*,*,#562,.F.);
#574=CARTESIAN_POINT('',(-0.152,0.368,0.051));
#573=VERTEX_POINT('',#574);
#572=EDGE_CURVE('',#573,#563,#577,.T.);
#577=LINE('',#574,#579);
#579=VECTOR('',#580,0.3048);
#580=DIRECTION('',(1.0,0.0,0.0));
#571=ORIENTED_EDGE('',*,*,#572,.F.);
#582=EDGE_CURVE('',#555,#573,#587,.T.);
#587=LINE('',#556,#589);
#589=VECTOR('',#590,0.0508);
#590=DIRECTION('',(0.0,0.0,1.0));
#581=ORIENTED_EDGE('',*,*,#582,.F.);
#591=STYLED_ITEM('',(#61),#592);
#592=ADVANCED_FACE('',(#598),#593,.T.);
#593=PLANE('',#594);
#594=AXIS2_PLACEMENT_3D('',#595,#596,#597);
#595=CARTESIAN_POINT('',(0.152,0.368,0.0));
#596=DIRECTION('',(1.0,0.0,0.0));
#597=DIRECTION('',(0.,0.,1.));
#598=FACE_OUTER_BOUND('',#599,.T.);
#599=EDGE_LOOP('',(#600,#610,#620,#630));
#603=CARTESIAN_POINT('',(0.152,1.257,0.0));
#602=VERTEX_POINT('',#603);
#601=EDGE_CURVE('',#602,#553,#606,.T.);
#606=LINE('',#603,#608);
#608=VECTOR('',#609,0.889);
#609=DIRECTION('',(0.0,-1.0,0.0));
#600=ORIENTED_EDGE('',*,*,#601,.F.);
#613=CARTESIAN_POINT('',(0.152,1.257,0.051));
#612=VERTEX_POINT('',#613);
#611=EDGE_CURVE('',#612,#602,#616,.T.);
#616=LINE('',#613,#618);
#618=VECTOR('',#619,0.0508);
#619=DIRECTION('',(0.0,0.0,-1.0));
#610=ORIENTED_EDGE('',*,*,#611,.F.);
#621=EDGE_CURVE('',#563,#612,#626,.T.);
#626=LINE('',#564,#628);
#628=VECTOR('',#629,0.889);
#629=DIRECTION('',(0.0,1.0,0.0));
#620=ORIENTED_EDGE('',*,*,#621,.F.);
#630=ORIENTED_EDGE('',*,*,#562,.T.);
#640=STYLED_ITEM('',(#61),#641);
#641=ADVANCED_FACE('',(#647),#642,.T.);
#642=PLANE('',#643);
#643=AXIS2_PLACEMENT_3D('',#644,#645,#646);
#644=CARTESIAN_POINT('',(0.152,1.257,0.0));
#645=DIRECTION('',(0.0,1.0,0.0));
#646=DIRECTION('',(0.,0.,1.));
#647=FACE_OUTER_BOUND('',#648,.T.);
#648=EDGE_LOOP('',(#649,#659,#669,#679));
#652=CARTESIAN_POINT('',(-0.152,1.257,0.0));
#651=VERTEX_POINT('',#652);
#650=EDGE_CURVE('',#651,#602,#655,.T.);
#655=LINE('',#652,#657);
#657=VECTOR('',#658,0.3048);
#658=DIRECTION('',(1.0,0.0,0.0));
#649=ORIENTED_EDGE('',*,*,#650,.F.);
#662=CARTESIAN_POINT('',(-0.152,1.257,0.051));
#661=VERTEX_POINT('',#662);
#660=EDGE_CURVE('',#661,#651,#665,.T.);
#665=LINE('',#662,#667);
#667=VECTOR('',#668,0.0508);
#668=DIRECTION('',(0.0,0.0,-1.0));
#659=ORIENTED_EDGE('',*,*,#660,.F.);
#670=EDGE_CURVE('',#612,#661,#675,.T.);
#675=LINE('',#613,#677);
#677=VECTOR('',#678,0.3048);
#678=DIRECTION('',(-1.0,0.0,0.0));
#669=ORIENTED_EDGE('',*,*,#670,.F.);
#679=ORIENTED_EDGE('',*,*,#611,.T.);
#689=STYLED_ITEM('',(#61),#690);
#690=ADVANCED_FACE('',(#696),#691,.T.);
#691=PLANE('',#692);
#692=AXIS2_PLACEMENT_3D('',#693,#694,#695);
#693=CARTESIAN_POINT('',(-0.152,1.257,0.0));
#694=DIRECTION('',(-1.0,0.0,0.0));
#695=DIRECTION('',(0.,0.,1.));
#696=FACE_OUTER_BOUND('',#697,.T.);
#697=EDGE_LOOP('',(#698,#708,#718,#728));
#699=EDGE_CURVE('',#555,#651,#704,.T.);
#704=LINE('',#556,#706);
#706=VECTOR('',#707,0.889);
#707=DIRECTION('',(0.0,1.0,0.0));
#698=ORIENTED_EDGE('',*,*,#699,.F.);
#708=ORIENTED_EDGE('',*,*,#582,.T.);
#719=EDGE_CURVE('',#661,#573,#724,.T.);
#724=LINE('',#662,#726);
#726=VECTOR('',#727,0.889);
#727=DIRECTION('',(0.0,-1.0,0.0));
#718=ORIENTED_EDGE('',*,*,#719,.F.);
#728=ORIENTED_EDGE('',*,*,#660,.T.);
#738=STYLED_ITEM('',(#61),#739);
#739=ADVANCED_FACE('',(#745),#740,.T.);
#740=PLANE('',#741);
#741=AXIS2_PLACEMENT_3D('',#742,#743,#744);
#742=CARTESIAN_POINT('',(-0.152,1.257,0.0));
#743=DIRECTION('',(0.0,0.0,-1.0));
#744=DIRECTION('',(0.,1.,0.));
#745=FACE_OUTER_BOUND('',#746,.T.);
#746=EDGE_LOOP('',(#747,#757,#767,#777));
#747=ORIENTED_EDGE('',*,*,#650,.T.);
#757=ORIENTED_EDGE('',*,*,#601,.T.);
#767=ORIENTED_EDGE('',*,*,#552,.T.);
#777=ORIENTED_EDGE('',*,*,#699,.T.);
#787=STYLED_ITEM('',(#61),#788);
#788=ADVANCED_FACE('',(#794),#789,.T.);
#789=PLANE('',#790);
#790=AXIS2_PLACEMENT_3D('',#791,#792,#793);
#791=CARTESIAN_POINT('',(-0.152,0.368,0.051));
#792=DIRECTION('',(0.0,0.0,1.0));
#793=DIRECTION('',(0.,1.,0.));
#794=FACE_OUTER_BOUND('',#795,.T.);
#795=EDGE_LOOP('',(#796,#806,#816,#826));
#796=ORIENTED_EDGE('',*,*,#572,.T.);
#806=ORIENTED_EDGE('',*,*,#621,.T.);
#816=ORIENTED_EDGE('',*,*,#670,.T.);
#826=ORIENTED_EDGE('',*,*,#719,.T.);
#836=MANIFOLD_SOLID_BREP($,#837);
#837=CLOSED_SHELL('',(#839,#888,#937,#986,#1035,#1084));
#838=STYLED_ITEM('',(#34),#839);
#839=ADVANCED_FACE('',(#845),#840,.T.);
#840=PLANE('',#841);
#841=AXIS2_PLACEMENT_3D('',#842,#843,#844);
#842=CARTESIAN_POINT('',(-0.152,-1.257,0.0));
#843=DIRECTION('',(0.0,-1.0,0.0));
#844=DIRECTION('',(0.,0.,1.));
#845=FACE_OUTER_BOUND('',#846,.T.);
#846=EDGE_LOOP('',(#847,#857,#867,#877));
#850=CARTESIAN_POINT('',(0.152,-1.257,0.0));
#849=VERTEX_POINT('',#850);
#852=CARTESIAN_POINT('',(-0.152,-1.257,0.0));
#851=VERTEX_POINT('',#852);
#848=EDGE_CURVE('',#849,#851,#853,.T.);
#853=LINE('',#850,#855);
#855=VECTOR('',#856,0.3048);
#856=DIRECTION('',(-1.0,0.0,0.0));
#847=ORIENTED_EDGE('',*,*,#848,.F.);
#860=CARTESIAN_POINT('',(0.152,-1.257,0.051));
#859=VERTEX_POINT('',#860);
#858=EDGE_CURVE('',#859,#849,#863,.T.);
#863=LINE('',#860,#865);
#865=VECTOR('',#866,0.0508);
#866=DIRECTION('',(0.0,0.0,-1.0));
#857=ORIENTED_EDGE('',*,*,#858,.F.);
#870=CARTESIAN_POINT('',(-0.152,-1.257,0.051));
#869=VERTEX_POINT('',#870);
#868=EDGE_CURVE('',#869,#859,#873,.T.);
#873=LINE('',#870,#875);
#875=VECTOR('',#876,0.3048);
#876=DIRECTION('',(1.0,0.0,0.0));
#867=ORIENTED_EDGE('',*,*,#868,.F.);
#878=EDGE_CURVE('',#851,#869,#883,.T.);
#883=LINE('',#852,#885);
#885=VECTOR('',#886,0.0508);
#886=DIRECTION('',(0.0,0.0,1.0));
#877=ORIENTED_EDGE('',*,*,#878,.F.);
#887=STYLED_ITEM('',(#34),#888);
#888=ADVANCED_FACE('',(#894),#889,.T.);
#889=PLANE('',#890);
#890=AXIS2_PLACEMENT_3D('',#891,#892,#893);
#891=CARTESIAN_POINT('',(0.152,-1.257,0.0));
#892=DIRECTION('',(1.0,0.0,0.0));
#893=DIRECTION('',(0.,0.,1.));
#894=FACE_OUTER_BOUND('',#895,.T.);
#895=EDGE_LOOP('',(#896,#906,#916,#926));
#899=CARTESIAN_POINT('',(0.152,-0.368,0.0));
#898=VERTEX_POINT('',#899);
#897=EDGE_CURVE('',#898,#849,#902,.T.);
#902=LINE('',#899,#904);
#904=VECTOR('',#905,0.889);
#905=DIRECTION('',(0.0,-1.0,0.0));
#896=ORIENTED_EDGE('',*,*,#897,.F.);
#909=CARTESIAN_POINT('',(0.152,-0.368,0.051));
#908=VERTEX_POINT('',#909);
#907=EDGE_CURVE('',#908,#898,#912,.T.);
#912=LINE('',#909,#914);
#914=VECTOR('',#915,0.0508);
#915=DIRECTION('',(0.0,0.0,-1.0));
#906=ORIENTED_EDGE('',*,*,#907,.F.);
#917=EDGE_CURVE('',#859,#908,#922,.T.);
#922=LINE('',#860,#924);
#924=VECTOR('',#925,0.889);
#925=DIRECTION('',(0.0,1.0,0.0));
#916=ORIENTED_EDGE('',*,*,#917,.F.);
#926=ORIENTED_EDGE('',*,*,#858,.T.);
#936=STYLED_ITEM('',(#34),#937);
#937=ADVANCED_FACE('',(#943),#938,.T.);
#938=PLANE('',#939);
#939=AXIS2_PLACEMENT_3D('',#940,#941,#942);
#940=CARTESIAN_POINT('',(0.152,-0.368,0.0));
#941=DIRECTION('',(0.0,1.0,0.0));
#942=DIRECTION('',(0.,0.,1.));
#943=FACE_OUTER_BOUND('',#944,.T.);
#944=EDGE_LOOP('',(#945,#955,#965,#975));
#948=CARTESIAN_POINT('',(-0.152,-0.368,0.0));
#947=VERTEX_POINT('',#948);
#946=EDGE_CURVE('',#947,#898,#951,.T.);
#951=LINE('',#948,#953);
#953=VECTOR('',#954,0.3048);
#954=DIRECTION('',(1.0,0.0,0.0));
#945=ORIENTED_EDGE('',*,*,#946,.F.);
#958=CARTESIAN_POINT('',(-0.152,-0.368,0.051));
#957=VERTEX_POINT('',#958);
#956=EDGE_CURVE('',#957,#947,#961,.T.);
#961=LINE('',#958,#963);
#963=VECTOR('',#964,0.0508);
#964=DIRECTION('',(0.0,0.0,-1.0));
#955=ORIENTED_EDGE('',*,*,#956,.F.);
#966=EDGE_CURVE('',#908,#957,#971,.T.);
#971=LINE('',#909,#973);
#973=VECTOR('',#974,0.3048);
#974=DIRECTION('',(-1.0,0.0,0.0));
#965=ORIENTED_EDGE('',*,*,#966,.F.);
#975=ORIENTED_EDGE('',*,*,#907,.T.);
#985=STYLED_ITEM('',(#34),#986);
#986=ADVANCED_FACE('',(#992),#987,.T.);
#987=PLANE('',#988);
#988=AXIS2_PLACEMENT_3D('',#989,#990,#991);
#989=CARTESIAN_POINT('',(-0.152,-0.368,0.0));
#990=DIRECTION('',(-1.0,0.0,0.0));
#991=DIRECTION('',(0.,0.,1.));
#992=FACE_OUTER_BOUND('',#993,.T.);
#993=EDGE_LOOP('',(#994,#1004,#1014,#1024));
#995=EDGE_CURVE('',#851,#947,#1000,.T.);
#1000=LINE('',#852,#1002);
#1002=VECTOR('',#1003,0.889);
#1003=DIRECTION('',(0.0,1.0,0.0));
#994=ORIENTED_EDGE('',*,*,#995,.F.);
#1004=ORIENTED_EDGE('',*,*,#878,.T.);
#1015=EDGE_CURVE('',#957,#869,#1020,.T.);
#1020=LINE('',#958,#1022);
#1022=VECTOR('',#1023,0.889);
#1023=DIRECTION('',(0.0,-1.0,0.0));
#1014=ORIENTED_EDGE('',*,*,#1015,.F.);
#1024=ORIENTED_EDGE('',*,*,#956,.T.);
#1034=STYLED_ITEM('',(#34),#1035);
#1035=ADVANCED_FACE('',(#1041),#1036,.T.);
#1036=PLANE('',#1037);
#1037=AXIS2_PLACEMENT_3D('',#1038,#1039,#1040);
#1038=CARTESIAN_POINT('',(-0.152,-0.368,0.0));
#1039=DIRECTION('',(0.0,0.0,-1.0));
#1040=DIRECTION('',(0.,1.,0.));
#1041=FACE_OUTER_BOUND('',#1042,.T.);
#1042=EDGE_LOOP('',(#1043,#1053,#1063,#1073));
#1043=ORIENTED_EDGE('',*,*,#946,.T.);
#1053=ORIENTED_EDGE('',*,*,#897,.T.);
#1063=ORIENTED_EDGE('',*,*,#848,.T.);
#1073=ORIENTED_EDGE('',*,*,#995,.T.);
#1083=STYLED_ITEM('',(#34),#1084);
#1084=ADVANCED_FACE('',(#1090),#1085,.T.);
#1085=PLANE('',#1086);
#1086=AXIS2_PLACEMENT_3D('',#1087,#1088,#1089);
#1087=CARTESIAN_POINT('',(-0.152,-1.257,0.051));
#1088=DIRECTION('',(0.0,0.0,1.0));
#1089=DIRECTION('',(0.,1.,0.));
#1090=FACE_OUTER_BOUND('',#1091,.T.);
#1091=EDGE_LOOP('',(#1092,#1102,#1112,#1122));
#1092=ORIENTED_EDGE('',*,*,#868,.T.);
#1102=ORIENTED_EDGE('',*,*,#917,.T.);
#1112=ORIENTED_EDGE('',*,*,#966,.T.);
#1122=ORIENTED_EDGE('',*,*,#1015,.T.);
#1133=MANIFOLD_SOLID_BREP($,#1134);
#1134=CLOSED_SHELL('',(#1136,#1185,#1234,#1303,#1372,#1424,#1476,#1529));
#1135=STYLED_ITEM('',(#34),#1136);
#1136=ADVANCED_FACE('',(#1142),#1137,.T.);
#1137=PLANE('',#1138);
#1138=AXIS2_PLACEMENT_3D('',#1139,#1140,#1141);
#1139=CARTESIAN_POINT('',(1.549,1.022,0.305));
#1140=DIRECTION('',(0.0,0.0,1.0));
#1141=DIRECTION('',(0.,1.,0.));
#1142=FACE_OUTER_BOUND('',#1143,.T.);
#1143=EDGE_LOOP('',(#1144,#1154,#1164,#1174));
#1147=CARTESIAN_POINT('',(1.549,1.378,0.305));
#1146=VERTEX_POINT('',#1147);
#1149=CARTESIAN_POINT('',(1.549,1.022,0.305));
#1148=VERTEX_POINT('',#1149);
#1145=EDGE_CURVE('',#1146,#1148,#1150,.T.);
#1150=LINE('',#1147,#1152);
#1152=VECTOR('',#1153,0.3556);
#1153=DIRECTION('',(0.0,-1.0,0.0));
#1144=ORIENTED_EDGE('',*,*,#1145,.F.);
#1157=CARTESIAN_POINT('',(1.499,1.378,0.305));
#1156=VERTEX_POINT('',#1157);
#1155=EDGE_CURVE('',#1156,#1146,#1160,.T.);
#1160=LINE('',#1157,#1162);
#1162=VECTOR('',#1163,0.0507999991522912);
#1163=DIRECTION('',(1.0,0.0,0.0));
#1154=ORIENTED_EDGE('',*,*,#1155,.F.);
#1167=CARTESIAN_POINT('',(1.499,1.022,0.305));
#1166=VERTEX_POINT('',#1167);
#1165=EDGE_CURVE('',#1166,#1156,#1170,.T.);
#1170=LINE('',#1167,#1172);
#1172=VECTOR('',#1173,0.355599997762043);
#1173=DIRECTION('',(0.0,1.0,0.0));
#1164=ORIENTED_EDGE('',*,*,#1165,.F.);
#1175=EDGE_CURVE('',#1148,#1166,#1180,.T.);
#1180=LINE('',#1149,#1182);
#1182=VECTOR('',#1183,0.0507999991522912);
#1183=DIRECTION('',(-1.0,0.0,0.0));
#1174=ORIENTED_EDGE('',*,*,#1175,.F.);
#1184=STYLED_ITEM('',(#34),#1185);
#1185=ADVANCED_FACE('',(#1191),#1186,.T.);
#1186=PLANE('',#1187);
#1187=AXIS2_PLACEMENT_3D('',#1188,#1189,#1190);
#1188=CARTESIAN_POINT('',(1.549,1.022,0.0));
#1189=DIRECTION('',(1.0,0.0,0.0));
#1190=DIRECTION('',(0.,0.,1.));
#1191=FACE_OUTER_BOUND('',#1192,.T.);
#1192=EDGE_LOOP('',(#1193,#1203,#1213,#1223));
#1196=CARTESIAN_POINT('',(1.549,1.378,0.0));
#1195=VERTEX_POINT('',#1196);
#1198=CARTESIAN_POINT('',(1.549,1.022,0.0));
#1197=VERTEX_POINT('',#1198);
#1194=EDGE_CURVE('',#1195,#1197,#1199,.T.);
#1199=LINE('',#1196,#1201);
#1201=VECTOR('',#1202,0.3556);
#1202=DIRECTION('',(0.0,-1.0,0.0));
#1193=ORIENTED_EDGE('',*,*,#1194,.F.);
#1204=EDGE_CURVE('',#1146,#1195,#1209,.T.);
#1209=LINE('',#1147,#1211);
#1211=VECTOR('',#1212,0.3047873);
#1212=DIRECTION('',(0.0,0.0,-1.0));
#1203=ORIENTED_EDGE('',*,*,#1204,.F.);
#1213=ORIENTED_EDGE('',*,*,#1145,.T.);
#1224=EDGE_CURVE('',#1197,#1148,#1229,.T.);
#1229=LINE('',#1198,#1231);
#1231=VECTOR('',#1232,0.3047873);
#1232=DIRECTION('',(0.0,0.0,1.0));
#1223=ORIENTED_EDGE('',*,*,#1224,.F.);
#1233=STYLED_ITEM('',(#34),#1234);
#1234=ADVANCED_FACE('',(#1240),#1235,.T.);
#1235=PLANE('',#1236);
#1236=AXIS2_PLACEMENT_3D('',#1237,#1238,#1239);
#1237=CARTESIAN_POINT('',(1.549,1.022,0.0));
#1238=DIRECTION('',(0.0,-1.0,0.0));
#1239=DIRECTION('',(0.,0.,1.));
#1240=FACE_OUTER_BOUND('',#1241,.T.);
#1241=EDGE_LOOP('',(#1242,#1252,#1262,#1272,#1282,#1292));
#1242=ORIENTED_EDGE('',*,*,#1224,.T.);
#1252=ORIENTED_EDGE('',*,*,#1175,.T.);
#1265=CARTESIAN_POINT('',(1.499,1.022,0.051));
#1264=VERTEX_POINT('',#1265);
#1263=EDGE_CURVE('',#1264,#1166,#1268,.T.);
#1268=LINE('',#1265,#1270);
#1270=VECTOR('',#1271,0.2539873);
#1271=DIRECTION('',(0.0,0.0,1.0));
#1262=ORIENTED_EDGE('',*,*,#1263,.F.);
#1275=CARTESIAN_POINT('',(1.067,1.022,0.051));
#1274=VERTEX_POINT('',#1275);
#1273=EDGE_CURVE('',#1274,#1264,#1278,.T.);
#1278=LINE('',#1275,#1280);
#1280=VECTOR('',#1281,0.43179999715318);
#1281=DIRECTION('',(1.0,0.0,0.0));
#1272=ORIENTED_EDGE('',*,*,#1273,.F.);
#1285=CARTESIAN_POINT('',(1.067,1.022,0.0));
#1284=VERTEX_POINT('',#1285);
#1283=EDGE_CURVE('',#1284,#1274,#1288,.T.);
#1288=LINE('',#1285,#1290);
#1290=VECTOR('',#1291,0.0508);
#1291=DIRECTION('',(0.0,0.0,1.0));
#1282=ORIENTED_EDGE('',*,*,#1283,.F.);
#1293=EDGE_CURVE('',#1197,#1284,#1298,.T.);
#1298=LINE('',#1198,#1300);
#1300=VECTOR('',#1301,0.482599996305472);
#1301=DIRECTION('',(-1.0,0.0,0.0));
#1292=ORIENTED_EDGE('',*,*,#1293,.F.);
#1302=STYLED_ITEM('',(#34),#1303);
#1303=ADVANCED_FACE('',(#1309),#1304,.T.);
#1304=PLANE('',#1305);
#1305=AXIS2_PLACEMENT_3D('',#1306,#1307,#1308);
#1306=CARTESIAN_POINT('',(1.067,1.378,0.0));
#1307=DIRECTION('',(0.0,1.0,0.0));
#1308=DIRECTION('',(0.,0.,1.));
#1309=FACE_OUTER_BOUND('',#1310,.T.);
#1310=EDGE_LOOP('',(#1311,#1321,#1331,#1341,#1351,#1361));
#1314=CARTESIAN_POINT('',(1.067,1.378,0.051));
#1313=VERTEX_POINT('',#1314);
#1316=CARTESIAN_POINT('',(1.067,1.378,0.0));
#1315=VERTEX_POINT('',#1316);
#1312=EDGE_CURVE('',#1313,#1315,#1317,.T.);
#1317=LINE('',#1314,#1319);
#1319=VECTOR('',#1320,0.0508);
#1320=DIRECTION('',(0.0,0.0,-1.0));
#1311=ORIENTED_EDGE('',*,*,#1312,.F.);
#1324=CARTESIAN_POINT('',(1.499,1.378,0.051));
#1323=VERTEX_POINT('',#1324);
#1322=EDGE_CURVE('',#1323,#1313,#1327,.T.);
#1327=LINE('',#1324,#1329);
#1329=VECTOR('',#1330,0.43179999715318);
#1330=DIRECTION('',(-1.0,0.0,0.0));
#1321=ORIENTED_EDGE('',*,*,#1322,.F.);
#1332=EDGE_CURVE('',#1156,#1323,#1337,.T.);
#1337=LINE('',#1157,#1339);
#1339=VECTOR('',#1340,0.2539873);
#1340=DIRECTION('',(0.0,0.0,-1.0));
#1331=ORIENTED_EDGE('',*,*,#1332,.F.);
#1341=ORIENTED_EDGE('',*,*,#1155,.T.);
#1351=ORIENTED_EDGE('',*,*,#1204,.T.);
#1362=EDGE_CURVE('',#1315,#1195,#1367,.T.);
#1367=LINE('',#1316,#1369);
#1369=VECTOR('',#1370,0.482599996305472);
#1370=DIRECTION('',(1.0,0.0,0.0));
#1361=ORIENTED_EDGE('',*,*,#1362,.F.);
#1371=STYLED_ITEM('color',(#34),#1372);
#1372=ADVANCED_FACE($,(#1378),#1373,.T.);
#1373=PLANE($,#1374);
#1374=AXIS2_PLACEMENT_3D($,#1375,#1376,#1377);
#1375=CARTESIAN_POINT('',(1.499,1.022,0.051));
#1376=DIRECTION('',(0.0,0.0,1.0));
#1377=DIRECTION('',(1.,0.,0.));
#1378=FACE_OUTER_BOUND($,#1379,.T.);
#1379=EDGE_LOOP($,(#1382, #1392, #1402, #1412));
#1382=ORIENTED_EDGE('',*,*,#1273,.T.);
#1393=EDGE_CURVE('',#1323,#1264,#1398,.T.);
#1398=LINE('',#1324,#1400);
#1400=VECTOR('',#1401,0.355599997762043);
#1401=DIRECTION('',(0.0,-1.0,0.0));
#1392=ORIENTED_EDGE('',*,*,#1393,.F.);
#1402=ORIENTED_EDGE('',*,*,#1322,.T.);
#1413=EDGE_CURVE($,#1274,#1313,#1418,.T.);
#1418=CIRCLE($,#1419,0.1778);
#1420=CARTESIAN_POINT('',(1.067,1.2,0.051));
#1419=AXIS2_PLACEMENT_3D($,#1420,#1421,#1422);
#1421=DIRECTION('',(0.0,0.0,-1.0));
#1422=DIRECTION('',(0.0,1.0,0.0));
#1412=ORIENTED_EDGE('',*,*,#1413,.F.);
#1423=STYLED_ITEM('color',(#34),#1424);
#1424=ADVANCED_FACE($,(#1430),#1425,.T.);
#1425=PLANE($,#1426);
#1426=AXIS2_PLACEMENT_3D($,#1427,#1428,#1429);
#1427=CARTESIAN_POINT('',(1.067,1.378,0.0));
#1428=DIRECTION('',(0.0,0.0,-1.0));
#1429=DIRECTION('',(1.,0.,0.));
#1430=FACE_OUTER_BOUND($,#1431,.T.);
#1431=EDGE_LOOP($,(#1434, #1444, #1454, #1464));
#1434=ORIENTED_EDGE('',*,*,#1362,.T.);
#1444=ORIENTED_EDGE('',*,*,#1194,.T.);
#1454=ORIENTED_EDGE('',*,*,#1293,.T.);
#1465=EDGE_CURVE($,#1315,#1284,#1470,.T.);
#1470=CIRCLE($,#1471,0.1778);
#1472=CARTESIAN_POINT('',(1.067,1.2,0.0));
#1471=AXIS2_PLACEMENT_3D($,#1472,#1473,#1474);
#1473=DIRECTION('',(0.0,0.0,1.0));
#1474=DIRECTION('',(0.0,-1.0,0.0));
#1464=ORIENTED_EDGE('',*,*,#1465,.F.);
#1475=STYLED_ITEM('color',(#34),#1476);
#1476=ADVANCED_FACE($,(#1482),#1477,.T.);
#1477=CYLINDRICAL_SURFACE($,#1478,0.1778);
#1478=AXIS2_PLACEMENT_3D($,#1479,#1480,#1481);
#1479=CARTESIAN_POINT('',(1.067,1.2,0.0));
#1480=DIRECTION('',(0.,0.,1.));
#1481=DIRECTION('',(0.,1.,0.));
#1482=FACE_OUTER_BOUND($,#1483,.T.);
#1483=EDGE_LOOP($,(#1506, #1484, #1516, #1495));
#1506=ORIENTED_EDGE('',*,*,#1312,.T.);
#1484=ORIENTED_EDGE('',*,*,#1465,.T.);
#1516=ORIENTED_EDGE('',*,*,#1283,.T.);
#1495=ORIENTED_EDGE('',*,*,#1413,.T.);
#1528=STYLED_ITEM('',(#34),#1529);
#1529=ADVANCED_FACE('',(#1535),#1530,.T.);
#1530=PLANE('',#1531);
#1531=AXIS2_PLACEMENT_3D('',#1532,#1533,#1534);
#1532=CARTESIAN_POINT('',(1.499,1.022,0.305));
#1533=DIRECTION('',(-1.0,0.0,0.0));
#1534=DIRECTION('',(0.,0.,1.));
#1535=FACE_OUTER_BOUND('',#1536,.T.);
#1536=EDGE_LOOP('',(#1537,#1547,#1557,#1567));
#1537=ORIENTED_EDGE('',*,*,#1165,.T.);
#1547=ORIENTED_EDGE('',*,*,#1332,.T.);
#1557=ORIENTED_EDGE('',*,*,#1393,.T.);
#1567=ORIENTED_EDGE('',*,*,#1263,.T.);
#1995=MANIFOLD_SOLID_BREP($,#1996);
#1996=CLOSED_SHELL('',(#1998,#2047,#2096,#2165,#2234,#2286,#2338,#2391));
#1997=STYLED_ITEM('',(#34),#1998);
#1998=ADVANCED_FACE('',(#2004),#1999,.T.);
#1999=PLANE('',#2000);
#2000=AXIS2_PLACEMENT_3D('',#2001,#2002,#2003);
#2001=CARTESIAN_POINT('',(1.549,0.222,0.305));
#2002=DIRECTION('',(0.0,0.0,1.0));
#2003=DIRECTION('',(0.,1.,0.));
#2004=FACE_OUTER_BOUND('',#2005,.T.);
#2005=EDGE_LOOP('',(#2006,#2016,#2026,#2036));
#2009=CARTESIAN_POINT('',(1.549,0.578,0.305));
#2008=VERTEX_POINT('',#2009);
#2011=CARTESIAN_POINT('',(1.549,0.222,0.305));
#2010=VERTEX_POINT('',#2011);
#2007=EDGE_CURVE('',#2008,#2010,#2012,.T.);
#2012=LINE('',#2009,#2014);
#2014=VECTOR('',#2015,0.3556);
#2015=DIRECTION('',(0.0,-1.0,0.0));
#2006=ORIENTED_EDGE('',*,*,#2007,.F.);
#2019=CARTESIAN_POINT('',(1.499,0.578,0.305));
#2018=VERTEX_POINT('',#2019);
#2017=EDGE_CURVE('',#2018,#2008,#2022,.T.);
#2022=LINE('',#2019,#2024);
#2024=VECTOR('',#2025,0.0507999991522912);
#2025=DIRECTION('',(1.0,0.0,0.0));
#2016=ORIENTED_EDGE('',*,*,#2017,.F.);
#2029=CARTESIAN_POINT('',(1.499,0.222,0.305));
#2028=VERTEX_POINT('',#2029);
#2027=EDGE_CURVE('',#2028,#2018,#2032,.T.);
#2032=LINE('',#2029,#2034);
#2034=VECTOR('',#2035,0.355599997762043);
#2035=DIRECTION('',(0.0,1.0,0.0));
#2026=ORIENTED_EDGE('',*,*,#2027,.F.);
#2037=EDGE_CURVE('',#2010,#2028,#2042,.T.);
#2042=LINE('',#2011,#2044);
#2044=VECTOR('',#2045,0.0507999991522912);
#2045=DIRECTION('',(-1.0,0.0,0.0));
#2036=ORIENTED_EDGE('',*,*,#2037,.F.);
#2046=STYLED_ITEM('',(#34),#2047);
#2047=ADVANCED_FACE('',(#2053),#2048,.T.);
#2048=PLANE('',#2049);
#2049=AXIS2_PLACEMENT_3D('',#2050,#2051,#2052);
#2050=CARTESIAN_POINT('',(1.549,0.222,0.0));
#2051=DIRECTION('',(1.0,0.0,0.0));
#2052=DIRECTION('',(0.,0.,1.));
#2053=FACE_OUTER_BOUND('',#2054,.T.);
#2054=EDGE_LOOP('',(#2055,#2065,#2075,#2085));
#2058=CARTESIAN_POINT('',(1.549,0.578,0.0));
#2057=VERTEX_POINT('',#2058);
#2060=CARTESIAN_POINT('',(1.549,0.222,0.0));
#2059=VERTEX_POINT('',#2060);
#2056=EDGE_CURVE('',#2057,#2059,#2061,.T.);
#2061=LINE('',#2058,#2063);
#2063=VECTOR('',#2064,0.3556);
#2064=DIRECTION('',(0.0,-1.0,0.0));
#2055=ORIENTED_EDGE('',*,*,#2056,.F.);
#2066=EDGE_CURVE('',#2008,#2057,#2071,.T.);
#2071=LINE('',#2009,#2073);
#2073=VECTOR('',#2074,0.3047873);
#2074=DIRECTION('',(0.0,0.0,-1.0));
#2065=ORIENTED_EDGE('',*,*,#2066,.F.);
#2075=ORIENTED_EDGE('',*,*,#2007,.T.);
#2086=EDGE_CURVE('',#2059,#2010,#2091,.T.);
#2091=LINE('',#2060,#2093);
#2093=VECTOR('',#2094,0.3047873);
#2094=DIRECTION('',(0.0,0.0,1.0));
#2085=ORIENTED_EDGE('',*,*,#2086,.F.);
#2095=STYLED_ITEM('',(#34),#2096);
#2096=ADVANCED_FACE('',(#2102),#2097,.T.);
#2097=PLANE('',#2098);
#2098=AXIS2_PLACEMENT_3D('',#2099,#2100,#2101);
#2099=CARTESIAN_POINT('',(1.549,0.222,0.0));
#2100=DIRECTION('',(0.0,-1.0,0.0));
#2101=DIRECTION('',(0.,0.,1.));
#2102=FACE_OUTER_BOUND('',#2103,.T.);
#2103=EDGE_LOOP('',(#2104,#2114,#2124,#2134,#2144,#2154));
#2104=ORIENTED_EDGE('',*,*,#2086,.T.);
#2114=ORIENTED_EDGE('',*,*,#2037,.T.);
#2127=CARTESIAN_POINT('',(1.499,0.222,0.051));
#2126=VERTEX_POINT('',#2127);
#2125=EDGE_CURVE('',#2126,#2028,#2130,.T.);
#2130=LINE('',#2127,#2132);
#2132=VECTOR('',#2133,0.2539873);
#2133=DIRECTION('',(0.0,0.0,1.0));
#2124=ORIENTED_EDGE('',*,*,#2125,.F.);
#2137=CARTESIAN_POINT('',(1.067,0.222,0.051));
#2136=VERTEX_POINT('',#2137);
#2135=EDGE_CURVE('',#2136,#2126,#2140,.T.);
#2140=LINE('',#2137,#2142);
#2142=VECTOR('',#2143,0.43179999715318);
#2143=DIRECTION('',(1.0,0.0,0.0));
#2134=ORIENTED_EDGE('',*,*,#2135,.F.);
#2147=CARTESIAN_POINT('',(1.067,0.222,0.0));
#2146=VERTEX_POINT('',#2147);
#2145=EDGE_CURVE('',#2146,#2136,#2150,.T.);
#2150=LINE('',#2147,#2152);
#2152=VECTOR('',#2153,0.0508);
#2153=DIRECTION('',(0.0,0.0,1.0));
#2144=ORIENTED_EDGE('',*,*,#2145,.F.);
#2155=EDGE_CURVE('',#2059,#2146,#2160,.T.);
#2160=LINE('',#2060,#2162);
#2162=VECTOR('',#2163,0.482599996305472);
#2163=DIRECTION('',(-1.0,0.0,0.0));
#2154=ORIENTED_EDGE('',*,*,#2155,.F.);
#2164=STYLED_ITEM('',(#34),#2165);
#2165=ADVANCED_FACE('',(#2171),#2166,.T.);
#2166=PLANE('',#2167);
#2167=AXIS2_PLACEMENT_3D('',#2168,#2169,#2170);
#2168=CARTESIAN_POINT('',(1.067,0.578,0.0));
#2169=DIRECTION('',(0.0,1.0,0.0));
#2170=DIRECTION('',(0.,0.,1.));
#2171=FACE_OUTER_BOUND('',#2172,.T.);
#2172=EDGE_LOOP('',(#2173,#2183,#2193,#2203,#2213,#2223));
#2176=CARTESIAN_POINT('',(1.067,0.578,0.051));
#2175=VERTEX_POINT('',#2176);
#2178=CARTESIAN_POINT('',(1.067,0.578,0.0));
#2177=VERTEX_POINT('',#2178);
#2174=EDGE_CURVE('',#2175,#2177,#2179,.T.);
#2179=LINE('',#2176,#2181);
#2181=VECTOR('',#2182,0.0508);
#2182=DIRECTION('',(0.0,0.0,-1.0));
#2173=ORIENTED_EDGE('',*,*,#2174,.F.);
#2186=CARTESIAN_POINT('',(1.499,0.578,0.051));
#2185=VERTEX_POINT('',#2186);
#2184=EDGE_CURVE('',#2185,#2175,#2189,.T.);
#2189=LINE('',#2186,#2191);
#2191=VECTOR('',#2192,0.43179999715318);
#2192=DIRECTION('',(-1.0,0.0,0.0));
#2183=ORIENTED_EDGE('',*,*,#2184,.F.);
#2194=EDGE_CURVE('',#2018,#2185,#2199,.T.);
#2199=LINE('',#2019,#2201);
#2201=VECTOR('',#2202,0.2539873);
#2202=DIRECTION('',(0.0,0.0,-1.0));
#2193=ORIENTED_EDGE('',*,*,#2194,.F.);
#2203=ORIENTED_EDGE('',*,*,#2017,.T.);
#2213=ORIENTED_EDGE('',*,*,#2066,.T.);
#2224=EDGE_CURVE('',#2177,#2057,#2229,.T.);
#2229=LINE('',#2178,#2231);
#2231=VECTOR('',#2232,0.482599996305472);
#2232=DIRECTION('',(1.0,0.0,0.0));
#2223=ORIENTED_EDGE('',*,*,#2224,.F.);
#2233=STYLED_ITEM('color',(#34),#2234);
#2234=ADVANCED_FACE($,(#2240),#2235,.T.);
#2235=PLANE($,#2236);
#2236=AXIS2_PLACEMENT_3D($,#2237,#2238,#2239);
#2237=CARTESIAN_POINT('',(1.499,0.222,0.051));
#2238=DIRECTION('',(0.0,0.0,1.0));
#2239=DIRECTION('',(1.,0.,0.));
#2240=FACE_OUTER_BOUND($,#2241,.T.);
#2241=EDGE_LOOP($,(#2244, #2254, #2264, #2274));
#2244=ORIENTED_EDGE('',*,*,#2135,.T.);
#2255=EDGE_CURVE('',#2185,#2126,#2260,.T.);
#2260=LINE('',#2186,#2262);
#2262=VECTOR('',#2263,0.355599997762043);
#2263=DIRECTION('',(0.0,-1.0,0.0));
#2254=ORIENTED_EDGE('',*,*,#2255,.F.);
#2264=ORIENTED_EDGE('',*,*,#2184,.T.);
#2275=EDGE_CURVE($,#2136,#2175,#2280,.T.);
#2280=CIRCLE($,#2281,0.1778);
#2282=CARTESIAN_POINT('',(1.067,0.4,0.051));
#2281=AXIS2_PLACEMENT_3D($,#2282,#2283,#2284);
#2283=DIRECTION('',(0.0,0.0,-1.0));
#2284=DIRECTION('',(0.0,1.0,0.0));
#2274=ORIENTED_EDGE('',*,*,#2275,.F.);
#2285=STYLED_ITEM('color',(#34),#2286);
#2286=ADVANCED_FACE($,(#2292),#2287,.T.);
#2287=PLANE($,#2288);
#2288=AXIS2_PLACEMENT_3D($,#2289,#2290,#2291);
#2289=CARTESIAN_POINT('',(1.067,0.578,0.0));
#2290=DIRECTION('',(0.0,0.0,-1.0));
#2291=DIRECTION('',(1.,0.,0.));
#2292=FACE_OUTER_BOUND($,#2293,.T.);
#2293=EDGE_LOOP($,(#2296, #2306, #2316, #2326));
#2296=ORIENTED_EDGE('',*,*,#2224,.T.);
#2306=ORIENTED_EDGE('',*,*,#2056,.T.);
#2316=ORIENTED_EDGE('',*,*,#2155,.T.);
#2327=EDGE_CURVE($,#2177,#2146,#2332,.T.);
#2332=CIRCLE($,#2333,0.1778);
#2334=CARTESIAN_POINT('',(1.067,0.4,0.0));
#2333=AXIS2_PLACEMENT_3D($,#2334,#2335,#2336);
#2335=DIRECTION('',(0.0,0.0,1.0));
#2336=DIRECTION('',(0.0,-1.0,0.0));
#2326=ORIENTED_EDGE('',*,*,#2327,.F.);
#2337=STYLED_ITEM('color',(#34),#2338);
#2338=ADVANCED_FACE($,(#2344),#2339,.T.);
#2339=CYLINDRICAL_SURFACE($,#2340,0.1778);
#2340=AXIS2_PLACEMENT_3D($,#2341,#2342,#2343);
#2341=CARTESIAN_POINT('',(1.067,0.4,0.0));
#2342=DIRECTION('',(0.,0.,1.));
#2343=DIRECTION('',(0.,1.,0.));
#2344=FACE_OUTER_BOUND($,#2345,.T.);
#2345=EDGE_LOOP($,(#2368, #2346, #2378, #2357));
#2368=ORIENTED_EDGE('',*,*,#2174,.T.);
#2346=ORIENTED_EDGE('',*,*,#2327,.T.);
#2378=ORIENTED_EDGE('',*,*,#2145,.T.);
#2357=ORIENTED_EDGE('',*,*,#2275,.T.);
#2390=STYLED_ITEM('',(#34),#2391);
#2391=ADVANCED_FACE('',(#2397),#2392,.T.);
#2392=PLANE('',#2393);
#2393=AXIS2_PLACEMENT_3D('',#2394,#2395,#2396);
#2394=CARTESIAN_POINT('',(1.499,0.222,0.305));
#2395=DIRECTION('',(-1.0,0.0,0.0));
#2396=DIRECTION('',(0.,0.,1.));
#2397=FACE_OUTER_BOUND('',#2398,.T.);
#2398=EDGE_LOOP('',(#2399,#2409,#2419,#2429));
#2399=ORIENTED_EDGE('',*,*,#2027,.T.);
#2409=ORIENTED_EDGE('',*,*,#2194,.T.);
#2419=ORIENTED_EDGE('',*,*,#2255,.T.);
#2429=ORIENTED_EDGE('',*,*,#2125,.T.);
#2857=MANIFOLD_SOLID_BREP($,#2858);
#2858=CLOSED_SHELL('',(#2860,#2909,#2958,#3027,#3096,#3148,#3200,#3253));
#2859=STYLED_ITEM('',(#34),#2860);
#2860=ADVANCED_FACE('',(#2866),#2861,.T.);
#2861=PLANE('',#2862);
#2862=AXIS2_PLACEMENT_3D('',#2863,#2864,#2865);
#2863=CARTESIAN_POINT('',(1.549,-0.578,0.305));
#2864=DIRECTION('',(0.0,0.0,1.0));
#2865=DIRECTION('',(0.,1.,0.));
#2866=FACE_OUTER_BOUND('',#2867,.T.);
#2867=EDGE_LOOP('',(#2868,#2878,#2888,#2898));
#2871=CARTESIAN_POINT('',(1.549,-0.222,0.305));
#2870=VERTEX_POINT('',#2871);
#2873=CARTESIAN_POINT('',(1.549,-0.578,0.305));
#2872=VERTEX_POINT('',#2873);
#2869=EDGE_CURVE('',#2870,#2872,#2874,.T.);
#2874=LINE('',#2871,#2876);
#2876=VECTOR('',#2877,0.3556);
#2877=DIRECTION('',(0.0,-1.0,0.0));
#2868=ORIENTED_EDGE('',*,*,#2869,.F.);
#2881=CARTESIAN_POINT('',(1.499,-0.222,0.305));
#2880=VERTEX_POINT('',#2881);
#2879=EDGE_CURVE('',#2880,#2870,#2884,.T.);
#2884=LINE('',#2881,#2886);
#2886=VECTOR('',#2887,0.0507999991522912);
#2887=DIRECTION('',(1.0,0.0,0.0));
#2878=ORIENTED_EDGE('',*,*,#2879,.F.);
#2891=CARTESIAN_POINT('',(1.499,-0.578,0.305));
#2890=VERTEX_POINT('',#2891);
#2889=EDGE_CURVE('',#2890,#2880,#2894,.T.);
#2894=LINE('',#2891,#2896);
#2896=VECTOR('',#2897,0.355599997762043);
#2897=DIRECTION('',(0.0,1.0,0.0));
#2888=ORIENTED_EDGE('',*,*,#2889,.F.);
#2899=EDGE_CURVE('',#2872,#2890,#2904,.T.);
#2904=LINE('',#2873,#2906);
#2906=VECTOR('',#2907,0.0507999991522912);
#2907=DIRECTION('',(-1.0,0.0,0.0));
#2898=ORIENTED_EDGE('',*,*,#2899,.F.);
#2908=STYLED_ITEM('',(#34),#2909);
#2909=ADVANCED_FACE('',(#2915),#2910,.T.);
#2910=PLANE('',#2911);
#2911=AXIS2_PLACEMENT_3D('',#2912,#2913,#2914);
#2912=CARTESIAN_POINT('',(1.549,-0.578,0.0));
#2913=DIRECTION('',(1.0,0.0,0.0));
#2914=DIRECTION('',(0.,0.,1.));
#2915=FACE_OUTER_BOUND('',#2916,.T.);
#2916=EDGE_LOOP('',(#2917,#2927,#2937,#2947));
#2920=CARTESIAN_POINT('',(1.549,-0.222,0.0));
#2919=VERTEX_POINT('',#2920);
#2922=CARTESIAN_POINT('',(1.549,-0.578,0.0));
#2921=VERTEX_POINT('',#2922);
#2918=EDGE_CURVE('',#2919,#2921,#2923,.T.);
#2923=LINE('',#2920,#2925);
#2925=VECTOR('',#2926,0.3556);
#2926=DIRECTION('',(0.0,-1.0,0.0));
#2917=ORIENTED_EDGE('',*,*,#2918,.F.);
#2928=EDGE_CURVE('',#2870,#2919,#2933,.T.);
#2933=LINE('',#2871,#2935);
#2935=VECTOR('',#2936,0.3047873);
#2936=DIRECTION('',(0.0,0.0,-1.0));
#2927=ORIENTED_EDGE('',*,*,#2928,.F.);
#2937=ORIENTED_EDGE('',*,*,#2869,.T.);
#2948=EDGE_CURVE('',#2921,#2872,#2953,.T.);
#2953=LINE('',#2922,#2955);
#2955=VECTOR('',#2956,0.3047873);
#2956=DIRECTION('',(0.0,0.0,1.0));
#2947=ORIENTED_EDGE('',*,*,#2948,.F.);
#2957=STYLED_ITEM('',(#34),#2958);
#2958=ADVANCED_FACE('',(#2964),#2959,.T.);
#2959=PLANE('',#2960);
#2960=AXIS2_PLACEMENT_3D('',#2961,#2962,#2963);
#2961=CARTESIAN_POINT('',(1.549,-0.578,0.0));
#2962=DIRECTION('',(0.0,-1.0,0.0));
#2963=DIRECTION('',(0.,0.,1.));
#2964=FACE_OUTER_BOUND('',#2965,.T.);
#2965=EDGE_LOOP('',(#2966,#2976,#2986,#2996,#3006,#3016));
#2966=ORIENTED_EDGE('',*,*,#2948,.T.);
#2976=ORIENTED_EDGE('',*,*,#2899,.T.);
#2989=CARTESIAN_POINT('',(1.499,-0.578,0.051));
#2988=VERTEX_POINT('',#2989);
#2987=EDGE_CURVE('',#2988,#2890,#2992,.T.);
#2992=LINE('',#2989,#2994);
#2994=VECTOR('',#2995,0.2539873);
#2995=DIRECTION('',(0.0,0.0,1.0));
#2986=ORIENTED_EDGE('',*,*,#2987,.F.);
#2999=CARTESIAN_POINT('',(1.067,-0.578,0.051));
#2998=VERTEX_POINT('',#2999);
#2997=EDGE_CURVE('',#2998,#2988,#3002,.T.);
#3002=LINE('',#2999,#3004);
#3004=VECTOR('',#3005,0.43179999715318);
#3005=DIRECTION('',(1.0,0.0,0.0));
#2996=ORIENTED_EDGE('',*,*,#2997,.F.);
#3009=CARTESIAN_POINT('',(1.067,-0.578,0.0));
#3008=VERTEX_POINT('',#3009);
#3007=EDGE_CURVE('',#3008,#2998,#3012,.T.);
#3012=LINE('',#3009,#3014);
#3014=VECTOR('',#3015,0.0508);
#3015=DIRECTION('',(0.0,0.0,1.0));
#3006=ORIENTED_EDGE('',*,*,#3007,.F.);
#3017=EDGE_CURVE('',#2921,#3008,#3022,.T.);
#3022=LINE('',#2922,#3024);
#3024=VECTOR('',#3025,0.482599996305472);
#3025=DIRECTION('',(-1.0,0.0,0.0));
#3016=ORIENTED_EDGE('',*,*,#3017,.F.);
#3026=STYLED_ITEM('',(#34),#3027);
#3027=ADVANCED_FACE('',(#3033),#3028,.T.);
#3028=PLANE('',#3029);
#3029=AXIS2_PLACEMENT_3D('',#3030,#3031,#3032);
#3030=CARTESIAN_POINT('',(1.067,-0.222,0.0));
#3031=DIRECTION('',(0.0,1.0,0.0));
#3032=DIRECTION('',(0.,0.,1.));
#3033=FACE_OUTER_BOUND('',#3034,.T.);
#3034=EDGE_LOOP('',(#3035,#3045,#3055,#3065,#3075,#3085));
#3038=CARTESIAN_POINT('',(1.067,-0.222,0.051));
#3037=VERTEX_POINT('',#3038);
#3040=CARTESIAN_POINT('',(1.067,-0.222,0.0));
#3039=VERTEX_POINT('',#3040);
#3036=EDGE_CURVE('',#3037,#3039,#3041,.T.);
#3041=LINE('',#3038,#3043);
#3043=VECTOR('',#3044,0.0508);
#3044=DIRECTION('',(0.0,0.0,-1.0));
#3035=ORIENTED_EDGE('',*,*,#3036,.F.);
#3048=CARTESIAN_POINT('',(1.499,-0.222,0.051));
#3047=VERTEX_POINT('',#3048);
#3046=EDGE_CURVE('',#3047,#3037,#3051,.T.);
#3051=LINE('',#3048,#3053);
#3053=VECTOR('',#3054,0.43179999715318);
#3054=DIRECTION('',(-1.0,0.0,0.0));
#3045=ORIENTED_EDGE('',*,*,#3046,.F.);
#3056=EDGE_CURVE('',#2880,#3047,#3061,.T.);
#3061=LINE('',#2881,#3063);
#3063=VECTOR('',#3064,0.2539873);
#3064=DIRECTION('',(0.0,0.0,-1.0));
#3055=ORIENTED_EDGE('',*,*,#3056,.F.);
#3065=ORIENTED_EDGE('',*,*,#2879,.T.);
#3075=ORIENTED_EDGE('',*,*,#2928,.T.);
#3086=EDGE_CURVE('',#3039,#2919,#3091,.T.);
#3091=LINE('',#3040,#3093);
#3093=VECTOR('',#3094,0.482599996305472);
#3094=DIRECTION('',(1.0,0.0,0.0));
#3085=ORIENTED_EDGE('',*,*,#3086,.F.);
#3095=STYLED_ITEM('color',(#34),#3096);
#3096=ADVANCED_FACE($,(#3102),#3097,.T.);
#3097=PLANE($,#3098);
#3098=AXIS2_PLACEMENT_3D($,#3099,#3100,#3101);
#3099=CARTESIAN_POINT('',(1.499,-0.578,0.051));
#3100=DIRECTION('',(0.0,0.0,1.0));
#3101=DIRECTION('',(1.,0.,0.));
#3102=FACE_OUTER_BOUND($,#3103,.T.);
#3103=EDGE_LOOP($,(#3106, #3116, #3126, #3136));
#3106=ORIENTED_EDGE('',*,*,#2997,.T.);
#3117=EDGE_CURVE('',#3047,#2988,#3122,.T.);
#3122=LINE('',#3048,#3124);
#3124=VECTOR('',#3125,0.355599997762043);
#3125=DIRECTION('',(0.0,-1.0,0.0));
#3116=ORIENTED_EDGE('',*,*,#3117,.F.);
#3126=ORIENTED_EDGE('',*,*,#3046,.T.);
#3137=EDGE_CURVE($,#2998,#3037,#3142,.T.);
#3142=CIRCLE($,#3143,0.1778);
#3144=CARTESIAN_POINT('',(1.067,-0.4,0.051));
#3143=AXIS2_PLACEMENT_3D($,#3144,#3145,#3146);
#3145=DIRECTION('',(0.0,0.0,-1.0));
#3146=DIRECTION('',(0.0,1.0,0.0));
#3136=ORIENTED_EDGE('',*,*,#3137,.F.);
#3147=STYLED_ITEM('color',(#34),#3148);
#3148=ADVANCED_FACE($,(#3154),#3149,.T.);
#3149=PLANE($,#3150);
#3150=AXIS2_PLACEMENT_3D($,#3151,#3152,#3153);
#3151=CARTESIAN_POINT('',(1.067,-0.222,0.0));
#3152=DIRECTION('',(0.0,0.0,-1.0));
#3153=DIRECTION('',(1.,0.,0.));
#3154=FACE_OUTER_BOUND($,#3155,.T.);
#3155=EDGE_LOOP($,(#3158, #3168, #3178, #3188));
#3158=ORIENTED_EDGE('',*,*,#3086,.T.);
#3168=ORIENTED_EDGE('',*,*,#2918,.T.);
#3178=ORIENTED_EDGE('',*,*,#3017,.T.);
#3189=EDGE_CURVE($,#3039,#3008,#3194,.T.);
#3194=CIRCLE($,#3195,0.1778);
#3196=CARTESIAN_POINT('',(1.067,-0.4,0.0));
#3195=AXIS2_PLACEMENT_3D($,#3196,#3197,#3198);
#3197=DIRECTION('',(0.0,0.0,1.0));
#3198=DIRECTION('',(0.0,-1.0,0.0));
#3188=ORIENTED_EDGE('',*,*,#3189,.F.);
#3199=STYLED_ITEM('color',(#34),#3200);
#3200=ADVANCED_FACE($,(#3206),#3201,.T.);
#3201=CYLINDRICAL_SURFACE($,#3202,0.1778);
#3202=AXIS2_PLACEMENT_3D($,#3203,#3204,#3205);
#3203=CARTESIAN_POINT('',(1.067,-0.4,0.0));
#3204=DIRECTION('',(0.,0.,1.));
#3205=DIRECTION('',(0.,1.,0.));
#3206=FACE_OUTER_BOUND($,#3207,.T.);
#3207=EDGE_LOOP($,(#3230, #3208, #3240, #3219));
#3230=ORIENTED_EDGE('',*,*,#3036,.T.);
#3208=ORIENTED_EDGE('',*,*,#3189,.T.);
#3240=ORIENTED_EDGE('',*,*,#3007,.T.);
#3219=ORIENTED_EDGE('',*,*,#3137,.T.);
#3252=STYLED_ITEM('',(#34),#3253);
#3253=ADVANCED_FACE('',(#3259),#3254,.T.);
#3254=PLANE('',#3255);
#3255=AXIS2_PLACEMENT_3D('',#3256,#3257,#3258);
#3256=CARTESIAN_POINT('',(1.499,-0.578,0.305));
#3257=DIRECTION('',(-1.0,0.0,0.0));
#3258=DIRECTION('',(0.,0.,1.));
#3259=FACE_OUTER_BOUND('',#3260,.T.);
#3260=EDGE_LOOP('',(#3261,#3271,#3281,#3291));
#3261=ORIENTED_EDGE('',*,*,#2889,.T.);
#3271=ORIENTED_EDGE('',*,*,#3056,.T.);
#3281=ORIENTED_EDGE('',*,*,#3117,.T.);
#3291=ORIENTED_EDGE('',*,*,#2987,.T.);
#3719=MANIFOLD_SOLID_BREP($,#3720);
#3720=CLOSED_SHELL('',(#3722,#3771,#3820,#3889,#3958,#4010,#4062,#4115));
#3721=STYLED_ITEM('',(#34),#3722);
#3722=ADVANCED_FACE('',(#3728),#3723,.T.);
#3723=PLANE('',#3724);
#3724=AXIS2_PLACEMENT_3D('',#3725,#3726,#3727);
#3725=CARTESIAN_POINT('',(1.549,-1.378,0.305));
#3726=DIRECTION('',(0.0,0.0,1.0));
#3727=DIRECTION('',(0.,1.,0.));
#3728=FACE_OUTER_BOUND('',#3729,.T.);
#3729=EDGE_LOOP('',(#3730,#3740,#3750,#3760));
#3733=CARTESIAN_POINT('',(1.549,-1.022,0.305));
#3732=VERTEX_POINT('',#3733);
#3735=CARTESIAN_POINT('',(1.549,-1.378,0.305));
#3734=VERTEX_POINT('',#3735);
#3731=EDGE_CURVE('',#3732,#3734,#3736,.T.);
#3736=LINE('',#3733,#3738);
#3738=VECTOR('',#3739,0.3556);
#3739=DIRECTION('',(0.0,-1.0,0.0));
#3730=ORIENTED_EDGE('',*,*,#3731,.F.);
#3743=CARTESIAN_POINT('',(1.499,-1.022,0.305));
#3742=VERTEX_POINT('',#3743);
#3741=EDGE_CURVE('',#3742,#3732,#3746,.T.);
#3746=LINE('',#3743,#3748);
#3748=VECTOR('',#3749,0.0507999991522912);
#3749=DIRECTION('',(1.0,0.0,0.0));
#3740=ORIENTED_EDGE('',*,*,#3741,.F.);
#3753=CARTESIAN_POINT('',(1.499,-1.378,0.305));
#3752=VERTEX_POINT('',#3753);
#3751=EDGE_CURVE('',#3752,#3742,#3756,.T.);
#3756=LINE('',#3753,#3758);
#3758=VECTOR('',#3759,0.355599997762043);
#3759=DIRECTION('',(0.0,1.0,0.0));
#3750=ORIENTED_EDGE('',*,*,#3751,.F.);
#3761=EDGE_CURVE('',#3734,#3752,#3766,.T.);
#3766=LINE('',#3735,#3768);
#3768=VECTOR('',#3769,0.0507999991522912);
#3769=DIRECTION('',(-1.0,0.0,0.0));
#3760=ORIENTED_EDGE('',*,*,#3761,.F.);
#3770=STYLED_ITEM('',(#34),#3771);
#3771=ADVANCED_FACE('',(#3777),#3772,.T.);
#3772=PLANE('',#3773);
#3773=AXIS2_PLACEMENT_3D('',#3774,#3775,#3776);
#3774=CARTESIAN_POINT('',(1.549,-1.378,0.0));
#3775=DIRECTION('',(1.0,0.0,0.0));
#3776=DIRECTION('',(0.,0.,1.));
#3777=FACE_OUTER_BOUND('',#3778,.T.);
#3778=EDGE_LOOP('',(#3779,#3789,#3799,#3809));
#3782=CARTESIAN_POINT('',(1.549,-1.022,0.0));
#3781=VERTEX_POINT('',#3782);
#3784=CARTESIAN_POINT('',(1.549,-1.378,0.0));
#3783=VERTEX_POINT('',#3784);
#3780=EDGE_CURVE('',#3781,#3783,#3785,.T.);
#3785=LINE('',#3782,#3787);
#3787=VECTOR('',#3788,0.3556);
#3788=DIRECTION('',(0.0,-1.0,0.0));
#3779=ORIENTED_EDGE('',*,*,#3780,.F.);
#3790=EDGE_CURVE('',#3732,#3781,#3795,.T.);
#3795=LINE('',#3733,#3797);
#3797=VECTOR('',#3798,0.3047873);
#3798=DIRECTION('',(0.0,0.0,-1.0));
#3789=ORIENTED_EDGE('',*,*,#3790,.F.);
#3799=ORIENTED_EDGE('',*,*,#3731,.T.);
#3810=EDGE_CURVE('',#3783,#3734,#3815,.T.);
#3815=LINE('',#3784,#3817);
#3817=VECTOR('',#3818,0.3047873);
#3818=DIRECTION('',(0.0,0.0,1.0));
#3809=ORIENTED_EDGE('',*,*,#3810,.F.);
#3819=STYLED_ITEM('',(#34),#3820);
#3820=ADVANCED_FACE('',(#3826),#3821,.T.);
#3821=PLANE('',#3822);
#3822=AXIS2_PLACEMENT_3D('',#3823,#3824,#3825);
#3823=CARTESIAN_POINT('',(1.549,-1.378,0.0));
#3824=DIRECTION('',(0.0,-1.0,0.0));
#3825=DIRECTION('',(0.,0.,1.));
#3826=FACE_OUTER_BOUND('',#3827,.T.);
#3827=EDGE_LOOP('',(#3828,#3838,#3848,#3858,#3868,#3878));
#3828=ORIENTED_EDGE('',*,*,#3810,.T.);
#3838=ORIENTED_EDGE('',*,*,#3761,.T.);
#3851=CARTESIAN_POINT('',(1.499,-1.378,0.051));
#3850=VERTEX_POINT('',#3851);
#3849=EDGE_CURVE('',#3850,#3752,#3854,.T.);
#3854=LINE('',#3851,#3856);
#3856=VECTOR('',#3857,0.2539873);
#3857=DIRECTION('',(0.0,0.0,1.0));
#3848=ORIENTED_EDGE('',*,*,#3849,.F.);
#3861=CARTESIAN_POINT('',(1.067,-1.378,0.051));
#3860=VERTEX_POINT('',#3861);
#3859=EDGE_CURVE('',#3860,#3850,#3864,.T.);
#3864=LINE('',#3861,#3866);
#3866=VECTOR('',#3867,0.43179999715318);
#3867=DIRECTION('',(1.0,0.0,0.0));
#3858=ORIENTED_EDGE('',*,*,#3859,.F.);
#3871=CARTESIAN_POINT('',(1.067,-1.378,0.0));
#3870=VERTEX_POINT('',#3871);
#3869=EDGE_CURVE('',#3870,#3860,#3874,.T.);
#3874=LINE('',#3871,#3876);
#3876=VECTOR('',#3877,0.0508);
#3877=DIRECTION('',(0.0,0.0,1.0));
#3868=ORIENTED_EDGE('',*,*,#3869,.F.);
#3879=EDGE_CURVE('',#3783,#3870,#3884,.T.);
#3884=LINE('',#3784,#3886);
#3886=VECTOR('',#3887,0.482599996305472);
#3887=DIRECTION('',(-1.0,0.0,0.0));
#3878=ORIENTED_EDGE('',*,*,#3879,.F.);
#3888=STYLED_ITEM('',(#34),#3889);
#3889=ADVANCED_FACE('',(#3895),#3890,.T.);
#3890=PLANE('',#3891);
#3891=AXIS2_PLACEMENT_3D('',#3892,#3893,#3894);
#3892=CARTESIAN_POINT('',(1.067,-1.022,0.0));
#3893=DIRECTION('',(0.0,1.0,0.0));
#3894=DIRECTION('',(0.,0.,1.));
#3895=FACE_OUTER_BOUND('',#3896,.T.);
#3896=EDGE_LOOP('',(#3897,#3907,#3917,#3927,#3937,#3947));
#3900=CARTESIAN_POINT('',(1.067,-1.022,0.051));
#3899=VERTEX_POINT('',#3900);
#3902=CARTESIAN_POINT('',(1.067,-1.022,0.0));
#3901=VERTEX_POINT('',#3902);
#3898=EDGE_CURVE('',#3899,#3901,#3903,.T.);
#3903=LINE('',#3900,#3905);
#3905=VECTOR('',#3906,0.0508);
#3906=DIRECTION('',(0.0,0.0,-1.0));
#3897=ORIENTED_EDGE('',*,*,#3898,.F.);
#3910=CARTESIAN_POINT('',(1.499,-1.022,0.051));
#3909=VERTEX_POINT('',#3910);
#3908=EDGE_CURVE('',#3909,#3899,#3913,.T.);
#3913=LINE('',#3910,#3915);
#3915=VECTOR('',#3916,0.43179999715318);
#3916=DIRECTION('',(-1.0,0.0,0.0));
#3907=ORIENTED_EDGE('',*,*,#3908,.F.);
#3918=EDGE_CURVE('',#3742,#3909,#3923,.T.);
#3923=LINE('',#3743,#3925);
#3925=VECTOR('',#3926,0.2539873);
#3926=DIRECTION('',(0.0,0.0,-1.0));
#3917=ORIENTED_EDGE('',*,*,#3918,.F.);
#3927=ORIENTED_EDGE('',*,*,#3741,.T.);
#3937=ORIENTED_EDGE('',*,*,#3790,.T.);
#3948=EDGE_CURVE('',#3901,#3781,#3953,.T.);
#3953=LINE('',#3902,#3955);
#3955=VECTOR('',#3956,0.482599996305472);
#3956=DIRECTION('',(1.0,0.0,0.0));
#3947=ORIENTED_EDGE('',*,*,#3948,.F.);
#3957=STYLED_ITEM('color',(#34),#3958);
#3958=ADVANCED_FACE($,(#3964),#3959,.T.);
#3959=PLANE($,#3960);
#3960=AXIS2_PLACEMENT_3D($,#3961,#3962,#3963);
#3961=CARTESIAN_POINT('',(1.499,-1.378,0.051));
#3962=DIRECTION('',(0.0,0.0,1.0));
#3963=DIRECTION('',(1.,0.,0.));
#3964=FACE_OUTER_BOUND($,#3965,.T.);
#3965=EDGE_LOOP($,(#3968, #3978, #3988, #3998));
#3968=ORIENTED_EDGE('',*,*,#3859,.T.);
#3979=EDGE_CURVE('',#3909,#3850,#3984,.T.);
#3984=LINE('',#3910,#3986);
#3986=VECTOR('',#3987,0.355599997762043);
#3987=DIRECTION('',(0.0,-1.0,0.0));
#3978=ORIENTED_EDGE('',*,*,#3979,.F.);
#3988=ORIENTED_EDGE('',*,*,#3908,.T.);
#3999=EDGE_CURVE($,#3860,#3899,#4004,.T.);
#4004=CIRCLE($,#4005,0.1778);
#4006=CARTESIAN_POINT('',(1.067,-1.2,0.051));
#4005=AXIS2_PLACEMENT_3D($,#4006,#4007,#4008);
#4007=DIRECTION('',(0.0,0.0,-1.0));
#4008=DIRECTION('',(0.0,1.0,0.0));
#3998=ORIENTED_EDGE('',*,*,#3999,.F.);
#4009=STYLED_ITEM('color',(#34),#4010);
#4010=ADVANCED_FACE($,(#4016),#4011,.T.);
#4011=PLANE($,#4012);
#4012=AXIS2_PLACEMENT_3D($,#4013,#4014,#4015);
#4013=CARTESIAN_POINT('',(1.067,-1.022,0.0));
#4014=DIRECTION('',(0.0,0.0,-1.0));
#4015=DIRECTION('',(1.,0.,0.));
#4016=FACE_OUTER_BOUND($,#4017,.T.);
#4017=EDGE_LOOP($,(#4020, #4030, #4040, #4050));
#4020=ORIENTED_EDGE('',*,*,#3948,.T.);
#4030=ORIENTED_EDGE('',*,*,#3780,.T.);
#4040=ORIENTED_EDGE('',*,*,#3879,.T.);
#4051=EDGE_CURVE($,#3901,#3870,#4056,.T.);
#4056=CIRCLE($,#4057,0.1778);
#4058=CARTESIAN_POINT('',(1.067,-1.2,0.0));
#4057=AXIS2_PLACEMENT_3D($,#4058,#4059,#4060);
#4059=DIRECTION('',(0.0,0.0,1.0));
#4060=DIRECTION('',(0.0,-1.0,0.0));
#4050=ORIENTED_EDGE('',*,*,#4051,.F.);
#4061=STYLED_ITEM('color',(#34),#4062);
#4062=ADVANCED_FACE($,(#4068),#4063,.T.);
#4063=CYLINDRICAL_SURFACE($,#4064,0.1778);
#4064=AXIS2_PLACEMENT_3D($,#4065,#4066,#4067);
#4065=CARTESIAN_POINT('',(1.067,-1.2,0.0));
#4066=DIRECTION('',(0.,0.,1.));
#4067=DIRECTION('',(0.,1.,0.));
#4068=FACE_OUTER_BOUND($,#4069,.T.);
#4069=EDGE_LOOP($,(#4092, #4070, #4102, #4081));
#4092=ORIENTED_EDGE('',*,*,#3898,.T.);
#4070=ORIENTED_EDGE('',*,*,#4051,.T.);
#4102=ORIENTED_EDGE('',*,*,#3869,.T.);
#4081=ORIENTED_EDGE('',*,*,#3999,.T.);
#4114=STYLED_ITEM('',(#34),#4115);
#4115=ADVANCED_FACE('',(#4121),#4116,.T.);
#4116=PLANE('',#4117);
#4117=AXIS2_PLACEMENT_3D('',#4118,#4119,#4120);
#4118=CARTESIAN_POINT('',(1.499,-1.378,0.305));
#4119=DIRECTION('',(-1.0,0.0,0.0));
#4120=DIRECTION('',(0.,0.,1.));
#4121=FACE_OUTER_BOUND('',#4122,.T.);
#4122=EDGE_LOOP('',(#4123,#4133,#4143,#4153));
#4123=ORIENTED_EDGE('',*,*,#3751,.T.);
#4133=ORIENTED_EDGE('',*,*,#3918,.T.);
#4143=ORIENTED_EDGE('',*,*,#3979,.T.);
#4153=ORIENTED_EDGE('',*,*,#3849,.T.);
#4581=MANIFOLD_SOLID_BREP($,#4582);
#4582=CLOSED_SHELL('',(#4584,#4633,#4682,#4751,#4820,#4872,#4924,#4977));
#4583=STYLED_ITEM('',(#34),#4584);
#4584=ADVANCED_FACE('',(#4590),#4585,.T.);
#4585=PLANE('',#4586);
#4586=AXIS2_PLACEMENT_3D('',#4587,#4588,#4589);
#4587=CARTESIAN_POINT('',(-1.549,-1.022,0.305));
#4588=DIRECTION('',(0.0,0.0,1.0));
#4589=DIRECTION('',(0.,1.,0.));
#4590=FACE_OUTER_BOUND('',#4591,.T.);
#4591=EDGE_LOOP('',(#4592,#4602,#4612,#4622));
#4595=CARTESIAN_POINT('',(-1.549,-1.378,0.305));
#4594=VERTEX_POINT('',#4595);
#4597=CARTESIAN_POINT('',(-1.549,-1.022,0.305));
#4596=VERTEX_POINT('',#4597);
#4593=EDGE_CURVE('',#4594,#4596,#4598,.T.);
#4598=LINE('',#4595,#4600);
#4600=VECTOR('',#4601,0.3556);
#4601=DIRECTION('',(0.0,1.0,0.0));
#4592=ORIENTED_EDGE('',*,*,#4593,.F.);
#4605=CARTESIAN_POINT('',(-1.499,-1.378,0.305));
#4604=VERTEX_POINT('',#4605);
#4603=EDGE_CURVE('',#4604,#4594,#4608,.T.);
#4608=LINE('',#4605,#4610);
#4610=VECTOR('',#4611,0.0507999991522912);
#4611=DIRECTION('',(-1.0,0.0,0.0));
#4602=ORIENTED_EDGE('',*,*,#4603,.F.);
#4615=CARTESIAN_POINT('',(-1.499,-1.022,0.305));
#4614=VERTEX_POINT('',#4615);
#4613=EDGE_CURVE('',#4614,#4604,#4618,.T.);
#4618=LINE('',#4615,#4620);
#4620=VECTOR('',#4621,0.355599997762043);
#4621=DIRECTION('',(0.0,-1.0,0.0));
#4612=ORIENTED_EDGE('',*,*,#4613,.F.);
#4623=EDGE_CURVE('',#4596,#4614,#4628,.T.);
#4628=LINE('',#4597,#4630);
#4630=VECTOR('',#4631,0.0507999991522911);
#4631=DIRECTION('',(1.0,0.0,0.0));
#4622=ORIENTED_EDGE('',*,*,#4623,.F.);
#4632=STYLED_ITEM('',(#34),#4633);
#4633=ADVANCED_FACE('',(#4639),#4634,.T.);
#4634=PLANE('',#4635);
#4635=AXIS2_PLACEMENT_3D('',#4636,#4637,#4638);
#4636=CARTESIAN_POINT('',(-1.549,-1.022,0.0));
#4637=DIRECTION('',(-1.0,0.0,0.0));
#4638=DIRECTION('',(0.,0.,1.));
#4639=FACE_OUTER_BOUND('',#4640,.T.);
#4640=EDGE_LOOP('',(#4641,#4651,#4661,#4671));
#4644=CARTESIAN_POINT('',(-1.549,-1.378,0.0));
#4643=VERTEX_POINT('',#4644);
#4646=CARTESIAN_POINT('',(-1.549,-1.022,0.0));
#4645=VERTEX_POINT('',#4646);
#4642=EDGE_CURVE('',#4643,#4645,#4647,.T.);
#4647=LINE('',#4644,#4649);
#4649=VECTOR('',#4650,0.3556);
#4650=DIRECTION('',(0.0,1.0,0.0));
#4641=ORIENTED_EDGE('',*,*,#4642,.F.);
#4652=EDGE_CURVE('',#4594,#4643,#4657,.T.);
#4657=LINE('',#4595,#4659);
#4659=VECTOR('',#4660,0.3047873);
#4660=DIRECTION('',(0.0,0.0,-1.0));
#4651=ORIENTED_EDGE('',*,*,#4652,.F.);
#4661=ORIENTED_EDGE('',*,*,#4593,.T.);
#4672=EDGE_CURVE('',#4645,#4596,#4677,.T.);
#4677=LINE('',#4646,#4679);
#4679=VECTOR('',#4680,0.3047873);
#4680=DIRECTION('',(0.0,0.0,1.0));
#4671=ORIENTED_EDGE('',*,*,#4672,.F.);
#4681=STYLED_ITEM('',(#34),#4682);
#4682=ADVANCED_FACE('',(#4688),#4683,.T.);
#4683=PLANE('',#4684);
#4684=AXIS2_PLACEMENT_3D('',#4685,#4686,#4687);
#4685=CARTESIAN_POINT('',(-1.549,-1.022,0.0));
#4686=DIRECTION('',(0.0,1.0,0.0));
#4687=DIRECTION('',(0.,0.,1.));
#4688=FACE_OUTER_BOUND('',#4689,.T.);
#4689=EDGE_LOOP('',(#4690,#4700,#4710,#4720,#4730,#4740));
#4690=ORIENTED_EDGE('',*,*,#4672,.T.);
#4700=ORIENTED_EDGE('',*,*,#4623,.T.);
#4713=CARTESIAN_POINT('',(-1.499,-1.022,0.051));
#4712=VERTEX_POINT('',#4713);
#4711=EDGE_CURVE('',#4712,#4614,#4716,.T.);
#4716=LINE('',#4713,#4718);
#4718=VECTOR('',#4719,0.2539873);
#4719=DIRECTION('',(0.0,0.0,1.0));
#4710=ORIENTED_EDGE('',*,*,#4711,.F.);
#4723=CARTESIAN_POINT('',(-1.067,-1.022,0.051));
#4722=VERTEX_POINT('',#4723);
#4721=EDGE_CURVE('',#4722,#4712,#4726,.T.);
#4726=LINE('',#4723,#4728);
#4728=VECTOR('',#4729,0.43179999715318);
#4729=DIRECTION('',(-1.0,0.0,0.0));
#4720=ORIENTED_EDGE('',*,*,#4721,.F.);
#4733=CARTESIAN_POINT('',(-1.067,-1.022,0.0));
#4732=VERTEX_POINT('',#4733);
#4731=EDGE_CURVE('',#4732,#4722,#4736,.T.);
#4736=LINE('',#4733,#4738);
#4738=VECTOR('',#4739,0.0508);
#4739=DIRECTION('',(0.0,0.0,1.0));
#4730=ORIENTED_EDGE('',*,*,#4731,.F.);
#4741=EDGE_CURVE('',#4645,#4732,#4746,.T.);
#4746=LINE('',#4646,#4748);
#4748=VECTOR('',#4749,0.482599996305472);
#4749=DIRECTION('',(1.0,0.0,0.0));
#4740=ORIENTED_EDGE('',*,*,#4741,.F.);
#4750=STYLED_ITEM('',(#34),#4751);
#4751=ADVANCED_FACE('',(#4757),#4752,.T.);
#4752=PLANE('',#4753);
#4753=AXIS2_PLACEMENT_3D('',#4754,#4755,#4756);
#4754=CARTESIAN_POINT('',(-1.067,-1.378,0.0));
#4755=DIRECTION('',(0.0,-1.0,0.0));
#4756=DIRECTION('',(0.,0.,1.));
#4757=FACE_OUTER_BOUND('',#4758,.T.);
#4758=EDGE_LOOP('',(#4759,#4769,#4779,#4789,#4799,#4809));
#4762=CARTESIAN_POINT('',(-1.067,-1.378,0.051));
#4761=VERTEX_POINT('',#4762);
#4764=CARTESIAN_POINT('',(-1.067,-1.378,0.0));
#4763=VERTEX_POINT('',#4764);
#4760=EDGE_CURVE('',#4761,#4763,#4765,.T.);
#4765=LINE('',#4762,#4767);
#4767=VECTOR('',#4768,0.0508);
#4768=DIRECTION('',(0.0,0.0,-1.0));
#4759=ORIENTED_EDGE('',*,*,#4760,.F.);
#4772=CARTESIAN_POINT('',(-1.499,-1.378,0.051));
#4771=VERTEX_POINT('',#4772);
#4770=EDGE_CURVE('',#4771,#4761,#4775,.T.);
#4775=LINE('',#4772,#4777);
#4777=VECTOR('',#4778,0.43179999715318);
#4778=DIRECTION('',(1.0,0.0,0.0));
#4769=ORIENTED_EDGE('',*,*,#4770,.F.);
#4780=EDGE_CURVE('',#4604,#4771,#4785,.T.);
#4785=LINE('',#4605,#4787);
#4787=VECTOR('',#4788,0.2539873);
#4788=DIRECTION('',(0.0,0.0,-1.0));
#4779=ORIENTED_EDGE('',*,*,#4780,.F.);
#4789=ORIENTED_EDGE('',*,*,#4603,.T.);
#4799=ORIENTED_EDGE('',*,*,#4652,.T.);
#4810=EDGE_CURVE('',#4763,#4643,#4815,.T.);
#4815=LINE('',#4764,#4817);
#4817=VECTOR('',#4818,0.482599996305472);
#4818=DIRECTION('',(-1.0,0.0,0.0));
#4809=ORIENTED_EDGE('',*,*,#4810,.F.);
#4819=STYLED_ITEM('color',(#34),#4820);
#4820=ADVANCED_FACE($,(#4826),#4821,.T.);
#4821=PLANE($,#4822);
#4822=AXIS2_PLACEMENT_3D($,#4823,#4824,#4825);
#4823=CARTESIAN_POINT('',(-1.499,-1.022,0.051));
#4824=DIRECTION('',(0.0,0.0,1.0));
#4825=DIRECTION('',(1.,0.,0.));
#4826=FACE_OUTER_BOUND($,#4827,.T.);
#4827=EDGE_LOOP($,(#4830, #4840, #4850, #4860));
#4830=ORIENTED_EDGE('',*,*,#4721,.T.);
#4841=EDGE_CURVE('',#4771,#4712,#4846,.T.);
#4846=LINE('',#4772,#4848);
#4848=VECTOR('',#4849,0.355599997762043);
#4849=DIRECTION('',(0.0,1.0,0.0));
#4840=ORIENTED_EDGE('',*,*,#4841,.F.);
#4850=ORIENTED_EDGE('',*,*,#4770,.T.);
#4861=EDGE_CURVE($,#4722,#4761,#4866,.T.);
#4866=CIRCLE($,#4867,0.1778);
#4868=CARTESIAN_POINT('',(-1.067,-1.2,0.051));
#4867=AXIS2_PLACEMENT_3D($,#4868,#4869,#4870);
#4869=DIRECTION('',(0.0,0.0,-1.0));
#4870=DIRECTION('',(0.0,-1.0,0.0));
#4860=ORIENTED_EDGE('',*,*,#4861,.F.);
#4871=STYLED_ITEM('color',(#34),#4872);
#4872=ADVANCED_FACE($,(#4878),#4873,.T.);
#4873=PLANE($,#4874);
#4874=AXIS2_PLACEMENT_3D($,#4875,#4876,#4877);
#4875=CARTESIAN_POINT('',(-1.067,-1.378,0.0));
#4876=DIRECTION('',(0.0,0.0,-1.0));
#4877=DIRECTION('',(1.,0.,0.));
#4878=FACE_OUTER_BOUND($,#4879,.T.);
#4879=EDGE_LOOP($,(#4882, #4892, #4902, #4912));
#4882=ORIENTED_EDGE('',*,*,#4810,.T.);
#4892=ORIENTED_EDGE('',*,*,#4642,.T.);
#4902=ORIENTED_EDGE('',*,*,#4741,.T.);
#4913=EDGE_CURVE($,#4763,#4732,#4918,.T.);
#4918=CIRCLE($,#4919,0.1778);
#4920=CARTESIAN_POINT('',(-1.067,-1.2,0.0));
#4919=AXIS2_PLACEMENT_3D($,#4920,#4921,#4922);
#4921=DIRECTION('',(0.0,0.0,1.0));
#4922=DIRECTION('',(0.0,1.0,0.0));
#4912=ORIENTED_EDGE('',*,*,#4913,.F.);
#4923=STYLED_ITEM('color',(#34),#4924);
#4924=ADVANCED_FACE($,(#4930),#4925,.T.);
#4925=CYLINDRICAL_SURFACE($,#4926,0.1778);
#4926=AXIS2_PLACEMENT_3D($,#4927,#4928,#4929);
#4927=CARTESIAN_POINT('',(-1.067,-1.2,0.0));
#4928=DIRECTION('',(0.,0.,1.));
#4929=DIRECTION('',(0.,1.,0.));
#4930=FACE_OUTER_BOUND($,#4931,.T.);
#4931=EDGE_LOOP($,(#4954, #4932, #4964, #4943));
#4954=ORIENTED_EDGE('',*,*,#4760,.T.);
#4932=ORIENTED_EDGE('',*,*,#4913,.T.);
#4964=ORIENTED_EDGE('',*,*,#4731,.T.);
#4943=ORIENTED_EDGE('',*,*,#4861,.T.);
#4976=STYLED_ITEM('',(#34),#4977);
#4977=ADVANCED_FACE('',(#4983),#4978,.T.);
#4978=PLANE('',#4979);
#4979=AXIS2_PLACEMENT_3D('',#4980,#4981,#4982);
#4980=CARTESIAN_POINT('',(-1.499,-1.022,0.305));
#4981=DIRECTION('',(1.0,0.0,0.0));
#4982=DIRECTION('',(0.,0.,1.));
#4983=FACE_OUTER_BOUND('',#4984,.T.);
#4984=EDGE_LOOP('',(#4985,#4995,#5005,#5015));
#4985=ORIENTED_EDGE('',*,*,#4613,.T.);
#4995=ORIENTED_EDGE('',*,*,#4780,.T.);
#5005=ORIENTED_EDGE('',*,*,#4841,.T.);
#5015=ORIENTED_EDGE('',*,*,#4711,.T.);
#5443=MANIFOLD_SOLID_BREP($,#5444);
#5444=CLOSED_SHELL('',(#5446,#5495,#5544,#5613,#5682,#5734,#5786,#5839));
#5445=STYLED_ITEM('',(#34),#5446);
#5446=ADVANCED_FACE('',(#5452),#5447,.T.);
#5447=PLANE('',#5448);
#5448=AXIS2_PLACEMENT_3D('',#5449,#5450,#5451);
#5449=CARTESIAN_POINT('',(-1.549,-0.222,0.305));
#5450=DIRECTION('',(0.0,0.0,1.0));
#5451=DIRECTION('',(0.,1.,0.));
#5452=FACE_OUTER_BOUND('',#5453,.T.);
#5453=EDGE_LOOP('',(#5454,#5464,#5474,#5484));
#5457=CARTESIAN_POINT('',(-1.549,-0.578,0.305));
#5456=VERTEX_POINT('',#5457);
#5459=CARTESIAN_POINT('',(-1.549,-0.222,0.305));
#5458=VERTEX_POINT('',#5459);
#5455=EDGE_CURVE('',#5456,#5458,#5460,.T.);
#5460=LINE('',#5457,#5462);
#5462=VECTOR('',#5463,0.3556);
#5463=DIRECTION('',(0.0,1.0,0.0));
#5454=ORIENTED_EDGE('',*,*,#5455,.F.);
#5467=CARTESIAN_POINT('',(-1.499,-0.578,0.305));
#5466=VERTEX_POINT('',#5467);
#5465=EDGE_CURVE('',#5466,#5456,#5470,.T.);
#5470=LINE('',#5467,#5472);
#5472=VECTOR('',#5473,0.0507999991522912);
#5473=DIRECTION('',(-1.0,0.0,0.0));
#5464=ORIENTED_EDGE('',*,*,#5465,.F.);
#5477=CARTESIAN_POINT('',(-1.499,-0.222,0.305));
#5476=VERTEX_POINT('',#5477);
#5475=EDGE_CURVE('',#5476,#5466,#5480,.T.);
#5480=LINE('',#5477,#5482);
#5482=VECTOR('',#5483,0.355599997762043);
#5483=DIRECTION('',(0.0,-1.0,0.0));
#5474=ORIENTED_EDGE('',*,*,#5475,.F.);
#5485=EDGE_CURVE('',#5458,#5476,#5490,.T.);
#5490=LINE('',#5459,#5492);
#5492=VECTOR('',#5493,0.0507999991522911);
#5493=DIRECTION('',(1.0,0.0,0.0));
#5484=ORIENTED_EDGE('',*,*,#5485,.F.);
#5494=STYLED_ITEM('',(#34),#5495);
#5495=ADVANCED_FACE('',(#5501),#5496,.T.);
#5496=PLANE('',#5497);
#5497=AXIS2_PLACEMENT_3D('',#5498,#5499,#5500);
#5498=CARTESIAN_POINT('',(-1.549,-0.222,0.0));
#5499=DIRECTION('',(-1.0,0.0,0.0));
#5500=DIRECTION('',(0.,0.,1.));
#5501=FACE_OUTER_BOUND('',#5502,.T.);
#5502=EDGE_LOOP('',(#5503,#5513,#5523,#5533));
#5506=CARTESIAN_POINT('',(-1.549,-0.578,0.0));
#5505=VERTEX_POINT('',#5506);
#5508=CARTESIAN_POINT('',(-1.549,-0.222,0.0));
#5507=VERTEX_POINT('',#5508);
#5504=EDGE_CURVE('',#5505,#5507,#5509,.T.);
#5509=LINE('',#5506,#5511);
#5511=VECTOR('',#5512,0.3556);
#5512=DIRECTION('',(0.0,1.0,0.0));
#5503=ORIENTED_EDGE('',*,*,#5504,.F.);
#5514=EDGE_CURVE('',#5456,#5505,#5519,.T.);
#5519=LINE('',#5457,#5521);
#5521=VECTOR('',#5522,0.3047873);
#5522=DIRECTION('',(0.0,0.0,-1.0));
#5513=ORIENTED_EDGE('',*,*,#5514,.F.);
#5523=ORIENTED_EDGE('',*,*,#5455,.T.);
#5534=EDGE_CURVE('',#5507,#5458,#5539,.T.);
#5539=LINE('',#5508,#5541);
#5541=VECTOR('',#5542,0.3047873);
#5542=DIRECTION('',(0.0,0.0,1.0));
#5533=ORIENTED_EDGE('',*,*,#5534,.F.);
#5543=STYLED_ITEM('',(#34),#5544);
#5544=ADVANCED_FACE('',(#5550),#5545,.T.);
#5545=PLANE('',#5546);
#5546=AXIS2_PLACEMENT_3D('',#5547,#5548,#5549);
#5547=CARTESIAN_POINT('',(-1.549,-0.222,0.0));
#5548=DIRECTION('',(0.0,1.0,0.0));
#5549=DIRECTION('',(0.,0.,1.));
#5550=FACE_OUTER_BOUND('',#5551,.T.);
#5551=EDGE_LOOP('',(#5552,#5562,#5572,#5582,#5592,#5602));
#5552=ORIENTED_EDGE('',*,*,#5534,.T.);
#5562=ORIENTED_EDGE('',*,*,#5485,.T.);
#5575=CARTESIAN_POINT('',(-1.499,-0.222,0.051));
#5574=VERTEX_POINT('',#5575);
#5573=EDGE_CURVE('',#5574,#5476,#5578,.T.);
#5578=LINE('',#5575,#5580);
#5580=VECTOR('',#5581,0.2539873);
#5581=DIRECTION('',(0.0,0.0,1.0));
#5572=ORIENTED_EDGE('',*,*,#5573,.F.);
#5585=CARTESIAN_POINT('',(-1.067,-0.222,0.051));
#5584=VERTEX_POINT('',#5585);
#5583=EDGE_CURVE('',#5584,#5574,#5588,.T.);
#5588=LINE('',#5585,#5590);
#5590=VECTOR('',#5591,0.43179999715318);
#5591=DIRECTION('',(-1.0,0.0,0.0));
#5582=ORIENTED_EDGE('',*,*,#5583,.F.);
#5595=CARTESIAN_POINT('',(-1.067,-0.222,0.0));
#5594=VERTEX_POINT('',#5595);
#5593=EDGE_CURVE('',#5594,#5584,#5598,.T.);
#5598=LINE('',#5595,#5600);
#5600=VECTOR('',#5601,0.0508);
#5601=DIRECTION('',(0.0,0.0,1.0));
#5592=ORIENTED_EDGE('',*,*,#5593,.F.);
#5603=EDGE_CURVE('',#5507,#5594,#5608,.T.);
#5608=LINE('',#5508,#5610);
#5610=VECTOR('',#5611,0.482599996305472);
#5611=DIRECTION('',(1.0,0.0,0.0));
#5602=ORIENTED_EDGE('',*,*,#5603,.F.);
#5612=STYLED_ITEM('',(#34),#5613);
#5613=ADVANCED_FACE('',(#5619),#5614,.T.);
#5614=PLANE('',#5615);
#5615=AXIS2_PLACEMENT_3D('',#5616,#5617,#5618);
#5616=CARTESIAN_POINT('',(-1.067,-0.578,0.0));
#5617=DIRECTION('',(0.0,-1.0,0.0));
#5618=DIRECTION('',(0.,0.,1.));
#5619=FACE_OUTER_BOUND('',#5620,.T.);
#5620=EDGE_LOOP('',(#5621,#5631,#5641,#5651,#5661,#5671));
#5624=CARTESIAN_POINT('',(-1.067,-0.578,0.051));
#5623=VERTEX_POINT('',#5624);
#5626=CARTESIAN_POINT('',(-1.067,-0.578,0.0));
#5625=VERTEX_POINT('',#5626);
#5622=EDGE_CURVE('',#5623,#5625,#5627,.T.);
#5627=LINE('',#5624,#5629);
#5629=VECTOR('',#5630,0.0508);
#5630=DIRECTION('',(0.0,0.0,-1.0));
#5621=ORIENTED_EDGE('',*,*,#5622,.F.);
#5634=CARTESIAN_POINT('',(-1.499,-0.578,0.051));
#5633=VERTEX_POINT('',#5634);
#5632=EDGE_CURVE('',#5633,#5623,#5637,.T.);
#5637=LINE('',#5634,#5639);
#5639=VECTOR('',#5640,0.43179999715318);
#5640=DIRECTION('',(1.0,0.0,0.0));
#5631=ORIENTED_EDGE('',*,*,#5632,.F.);
#5642=EDGE_CURVE('',#5466,#5633,#5647,.T.);
#5647=LINE('',#5467,#5649);
#5649=VECTOR('',#5650,0.2539873);
#5650=DIRECTION('',(0.0,0.0,-1.0));
#5641=ORIENTED_EDGE('',*,*,#5642,.F.);
#5651=ORIENTED_EDGE('',*,*,#5465,.T.);
#5661=ORIENTED_EDGE('',*,*,#5514,.T.);
#5672=EDGE_CURVE('',#5625,#5505,#5677,.T.);
#5677=LINE('',#5626,#5679);
#5679=VECTOR('',#5680,0.482599996305472);
#5680=DIRECTION('',(-1.0,0.0,0.0));
#5671=ORIENTED_EDGE('',*,*,#5672,.F.);
#5681=STYLED_ITEM('color',(#34),#5682);
#5682=ADVANCED_FACE($,(#5688),#5683,.T.);
#5683=PLANE($,#5684);
#5684=AXIS2_PLACEMENT_3D($,#5685,#5686,#5687);
#5685=CARTESIAN_POINT('',(-1.499,-0.222,0.051));
#5686=DIRECTION('',(0.0,0.0,1.0));
#5687=DIRECTION('',(1.,0.,0.));
#5688=FACE_OUTER_BOUND($,#5689,.T.);
#5689=EDGE_LOOP($,(#5692, #5702, #5712, #5722));
#5692=ORIENTED_EDGE('',*,*,#5583,.T.);
#5703=EDGE_CURVE('',#5633,#5574,#5708,.T.);
#5708=LINE('',#5634,#5710);
#5710=VECTOR('',#5711,0.355599997762043);
#5711=DIRECTION('',(0.0,1.0,0.0));
#5702=ORIENTED_EDGE('',*,*,#5703,.F.);
#5712=ORIENTED_EDGE('',*,*,#5632,.T.);
#5723=EDGE_CURVE($,#5584,#5623,#5728,.T.);
#5728=CIRCLE($,#5729,0.1778);
#5730=CARTESIAN_POINT('',(-1.067,-0.4,0.051));
#5729=AXIS2_PLACEMENT_3D($,#5730,#5731,#5732);
#5731=DIRECTION('',(0.0,0.0,-1.0));
#5732=DIRECTION('',(0.0,-1.0,0.0));
#5722=ORIENTED_EDGE('',*,*,#5723,.F.);
#5733=STYLED_ITEM('color',(#34),#5734);
#5734=ADVANCED_FACE($,(#5740),#5735,.T.);
#5735=PLANE($,#5736);
#5736=AXIS2_PLACEMENT_3D($,#5737,#5738,#5739);
#5737=CARTESIAN_POINT('',(-1.067,-0.578,0.0));
#5738=DIRECTION('',(0.0,0.0,-1.0));
#5739=DIRECTION('',(1.,0.,0.));
#5740=FACE_OUTER_BOUND($,#5741,.T.);
#5741=EDGE_LOOP($,(#5744, #5754, #5764, #5774));
#5744=ORIENTED_EDGE('',*,*,#5672,.T.);
#5754=ORIENTED_EDGE('',*,*,#5504,.T.);
#5764=ORIENTED_EDGE('',*,*,#5603,.T.);
#5775=EDGE_CURVE($,#5625,#5594,#5780,.T.);
#5780=CIRCLE($,#5781,0.1778);
#5782=CARTESIAN_POINT('',(-1.067,-0.4,0.0));
#5781=AXIS2_PLACEMENT_3D($,#5782,#5783,#5784);
#5783=DIRECTION('',(0.0,0.0,1.0));
#5784=DIRECTION('',(0.0,1.0,0.0));
#5774=ORIENTED_EDGE('',*,*,#5775,.F.);
#5785=STYLED_ITEM('color',(#34),#5786);
#5786=ADVANCED_FACE($,(#5792),#5787,.T.);
#5787=CYLINDRICAL_SURFACE($,#5788,0.1778);
#5788=AXIS2_PLACEMENT_3D($,#5789,#5790,#5791);
#5789=CARTESIAN_POINT('',(-1.067,-0.4,0.0));
#5790=DIRECTION('',(0.,0.,1.));
#5791=DIRECTION('',(0.,1.,0.));
#5792=FACE_OUTER_BOUND($,#5793,.T.);
#5793=EDGE_LOOP($,(#5816, #5794, #5826, #5805));
#5816=ORIENTED_EDGE('',*,*,#5622,.T.);
#5794=ORIENTED_EDGE('',*,*,#5775,.T.);
#5826=ORIENTED_EDGE('',*,*,#5593,.T.);
#5805=ORIENTED_EDGE('',*,*,#5723,.T.);
#5838=STYLED_ITEM('',(#34),#5839);
#5839=ADVANCED_FACE('',(#5845),#5840,.T.);
#5840=PLANE('',#5841);
#5841=AXIS2_PLACEMENT_3D('',#5842,#5843,#5844);
#5842=CARTESIAN_POINT('',(-1.499,-0.222,0.305));
#5843=DIRECTION('',(1.0,0.0,0.0));
#5844=DIRECTION('',(0.,0.,1.));
#5845=FACE_OUTER_BOUND('',#5846,.T.);
#5846=EDGE_LOOP('',(#5847,#5857,#5867,#5877));
#5847=ORIENTED_EDGE('',*,*,#5475,.T.);
#5857=ORIENTED_EDGE('',*,*,#5642,.T.);
#5867=ORIENTED_EDGE('',*,*,#5703,.T.);
#5877=ORIENTED_EDGE('',*,*,#5573,.T.);
#6305=MANIFOLD_SOLID_BREP($,#6306);
#6306=CLOSED_SHELL('',(#6308,#6357,#6406,#6475,#6544,#6596,#6648,#6701));
#6307=STYLED_ITEM('',(#34),#6308);
#6308=ADVANCED_FACE('',(#6314),#6309,.T.);
#6309=PLANE('',#6310);
#6310=AXIS2_PLACEMENT_3D('',#6311,#6312,#6313);
#6311=CARTESIAN_POINT('',(-1.549,0.578,0.305));
#6312=DIRECTION('',(0.0,0.0,1.0));
#6313=DIRECTION('',(0.,1.,0.));
#6314=FACE_OUTER_BOUND('',#6315,.T.);
#6315=EDGE_LOOP('',(#6316,#6326,#6336,#6346));
#6319=CARTESIAN_POINT('',(-1.549,0.222,0.305));
#6318=VERTEX_POINT('',#6319);
#6321=CARTESIAN_POINT('',(-1.549,0.578,0.305));
#6320=VERTEX_POINT('',#6321);
#6317=EDGE_CURVE('',#6318,#6320,#6322,.T.);
#6322=LINE('',#6319,#6324);
#6324=VECTOR('',#6325,0.3556);
#6325=DIRECTION('',(0.0,1.0,0.0));
#6316=ORIENTED_EDGE('',*,*,#6317,.F.);
#6329=CARTESIAN_POINT('',(-1.499,0.222,0.305));
#6328=VERTEX_POINT('',#6329);
#6327=EDGE_CURVE('',#6328,#6318,#6332,.T.);
#6332=LINE('',#6329,#6334);
#6334=VECTOR('',#6335,0.0507999991522912);
#6335=DIRECTION('',(-1.0,0.0,0.0));
#6326=ORIENTED_EDGE('',*,*,#6327,.F.);
#6339=CARTESIAN_POINT('',(-1.499,0.578,0.305));
#6338=VERTEX_POINT('',#6339);
#6337=EDGE_CURVE('',#6338,#6328,#6342,.T.);
#6342=LINE('',#6339,#6344);
#6344=VECTOR('',#6345,0.355599997762043);
#6345=DIRECTION('',(0.0,-1.0,0.0));
#6336=ORIENTED_EDGE('',*,*,#6337,.F.);
#6347=EDGE_CURVE('',#6320,#6338,#6352,.T.);
#6352=LINE('',#6321,#6354);
#6354=VECTOR('',#6355,0.0507999991522911);
#6355=DIRECTION('',(1.0,0.0,0.0));
#6346=ORIENTED_EDGE('',*,*,#6347,.F.);
#6356=STYLED_ITEM('',(#34),#6357);
#6357=ADVANCED_FACE('',(#6363),#6358,.T.);
#6358=PLANE('',#6359);
#6359=AXIS2_PLACEMENT_3D('',#6360,#6361,#6362);
#6360=CARTESIAN_POINT('',(-1.549,0.578,0.0));
#6361=DIRECTION('',(-1.0,0.0,0.0));
#6362=DIRECTION('',(0.,0.,1.));
#6363=FACE_OUTER_BOUND('',#6364,.T.);
#6364=EDGE_LOOP('',(#6365,#6375,#6385,#6395));
#6368=CARTESIAN_POINT('',(-1.549,0.222,0.0));
#6367=VERTEX_POINT('',#6368);
#6370=CARTESIAN_POINT('',(-1.549,0.578,0.0));
#6369=VERTEX_POINT('',#6370);
#6366=EDGE_CURVE('',#6367,#6369,#6371,.T.);
#6371=LINE('',#6368,#6373);
#6373=VECTOR('',#6374,0.3556);
#6374=DIRECTION('',(0.0,1.0,0.0));
#6365=ORIENTED_EDGE('',*,*,#6366,.F.);
#6376=EDGE_CURVE('',#6318,#6367,#6381,.T.);
#6381=LINE('',#6319,#6383);
#6383=VECTOR('',#6384,0.3047873);
#6384=DIRECTION('',(0.0,0.0,-1.0));
#6375=ORIENTED_EDGE('',*,*,#6376,.F.);
#6385=ORIENTED_EDGE('',*,*,#6317,.T.);
#6396=EDGE_CURVE('',#6369,#6320,#6401,.T.);
#6401=LINE('',#6370,#6403);
#6403=VECTOR('',#6404,0.3047873);
#6404=DIRECTION('',(0.0,0.0,1.0));
#6395=ORIENTED_EDGE('',*,*,#6396,.F.);
#6405=STYLED_ITEM('',(#34),#6406);
#6406=ADVANCED_FACE('',(#6412),#6407,.T.);
#6407=PLANE('',#6408);
#6408=AXIS2_PLACEMENT_3D('',#6409,#6410,#6411);
#6409=CARTESIAN_POINT('',(-1.549,0.578,0.0));
#6410=DIRECTION('',(0.0,1.0,0.0));
#6411=DIRECTION('',(0.,0.,1.));
#6412=FACE_OUTER_BOUND('',#6413,.T.);
#6413=EDGE_LOOP('',(#6414,#6424,#6434,#6444,#6454,#6464));
#6414=ORIENTED_EDGE('',*,*,#6396,.T.);
#6424=ORIENTED_EDGE('',*,*,#6347,.T.);
#6437=CARTESIAN_POINT('',(-1.499,0.578,0.051));
#6436=VERTEX_POINT('',#6437);
#6435=EDGE_CURVE('',#6436,#6338,#6440,.T.);
#6440=LINE('',#6437,#6442);
#6442=VECTOR('',#6443,0.2539873);
#6443=DIRECTION('',(0.0,0.0,1.0));
#6434=ORIENTED_EDGE('',*,*,#6435,.F.);
#6447=CARTESIAN_POINT('',(-1.067,0.578,0.051));
#6446=VERTEX_POINT('',#6447);
#6445=EDGE_CURVE('',#6446,#6436,#6450,.T.);
#6450=LINE('',#6447,#6452);
#6452=VECTOR('',#6453,0.43179999715318);
#6453=DIRECTION('',(-1.0,0.0,0.0));
#6444=ORIENTED_EDGE('',*,*,#6445,.F.);
#6457=CARTESIAN_POINT('',(-1.067,0.578,0.0));
#6456=VERTEX_POINT('',#6457);
#6455=EDGE_CURVE('',#6456,#6446,#6460,.T.);
#6460=LINE('',#6457,#6462);
#6462=VECTOR('',#6463,0.0508);
#6463=DIRECTION('',(0.0,0.0,1.0));
#6454=ORIENTED_EDGE('',*,*,#6455,.F.);
#6465=EDGE_CURVE('',#6369,#6456,#6470,.T.);
#6470=LINE('',#6370,#6472);
#6472=VECTOR('',#6473,0.482599996305472);
#6473=DIRECTION('',(1.0,0.0,0.0));
#6464=ORIENTED_EDGE('',*,*,#6465,.F.);
#6474=STYLED_ITEM('',(#34),#6475);
#6475=ADVANCED_FACE('',(#6481),#6476,.T.);
#6476=PLANE('',#6477);
#6477=AXIS2_PLACEMENT_3D('',#6478,#6479,#6480);
#6478=CARTESIAN_POINT('',(-1.067,0.222,0.0));
#6479=DIRECTION('',(0.0,-1.0,0.0));
#6480=DIRECTION('',(0.,0.,1.));
#6481=FACE_OUTER_BOUND('',#6482,.T.);
#6482=EDGE_LOOP('',(#6483,#6493,#6503,#6513,#6523,#6533));
#6486=CARTESIAN_POINT('',(-1.067,0.222,0.051));
#6485=VERTEX_POINT('',#6486);
#6488=CARTESIAN_POINT('',(-1.067,0.222,0.0));
#6487=VERTEX_POINT('',#6488);
#6484=EDGE_CURVE('',#6485,#6487,#6489,.T.);
#6489=LINE('',#6486,#6491);
#6491=VECTOR('',#6492,0.0508);
#6492=DIRECTION('',(0.0,0.0,-1.0));
#6483=ORIENTED_EDGE('',*,*,#6484,.F.);
#6496=CARTESIAN_POINT('',(-1.499,0.222,0.051));
#6495=VERTEX_POINT('',#6496);
#6494=EDGE_CURVE('',#6495,#6485,#6499,.T.);
#6499=LINE('',#6496,#6501);
#6501=VECTOR('',#6502,0.43179999715318);
#6502=DIRECTION('',(1.0,0.0,0.0));
#6493=ORIENTED_EDGE('',*,*,#6494,.F.);
#6504=EDGE_CURVE('',#6328,#6495,#6509,.T.);
#6509=LINE('',#6329,#6511);
#6511=VECTOR('',#6512,0.2539873);
#6512=DIRECTION('',(0.0,0.0,-1.0));
#6503=ORIENTED_EDGE('',*,*,#6504,.F.);
#6513=ORIENTED_EDGE('',*,*,#6327,.T.);
#6523=ORIENTED_EDGE('',*,*,#6376,.T.);
#6534=EDGE_CURVE('',#6487,#6367,#6539,.T.);
#6539=LINE('',#6488,#6541);
#6541=VECTOR('',#6542,0.482599996305472);
#6542=DIRECTION('',(-1.0,0.0,0.0));
#6533=ORIENTED_EDGE('',*,*,#6534,.F.);
#6543=STYLED_ITEM('color',(#34),#6544);
#6544=ADVANCED_FACE($,(#6550),#6545,.T.);
#6545=PLANE($,#6546);
#6546=AXIS2_PLACEMENT_3D($,#6547,#6548,#6549);
#6547=CARTESIAN_POINT('',(-1.499,0.578,0.051));
#6548=DIRECTION('',(0.0,0.0,1.0));
#6549=DIRECTION('',(1.,0.,0.));
#6550=FACE_OUTER_BOUND($,#6551,.T.);
#6551=EDGE_LOOP($,(#6554, #6564, #6574, #6584));
#6554=ORIENTED_EDGE('',*,*,#6445,.T.);
#6565=EDGE_CURVE('',#6495,#6436,#6570,.T.);
#6570=LINE('',#6496,#6572);
#6572=VECTOR('',#6573,0.355599997762043);
#6573=DIRECTION('',(0.0,1.0,0.0));
#6564=ORIENTED_EDGE('',*,*,#6565,.F.);
#6574=ORIENTED_EDGE('',*,*,#6494,.T.);
#6585=EDGE_CURVE($,#6446,#6485,#6590,.T.);
#6590=CIRCLE($,#6591,0.1778);
#6592=CARTESIAN_POINT('',(-1.067,0.4,0.051));
#6591=AXIS2_PLACEMENT_3D($,#6592,#6593,#6594);
#6593=DIRECTION('',(0.0,0.0,-1.0));
#6594=DIRECTION('',(0.0,-1.0,0.0));
#6584=ORIENTED_EDGE('',*,*,#6585,.F.);
#6595=STYLED_ITEM('color',(#34),#6596);
#6596=ADVANCED_FACE($,(#6602),#6597,.T.);
#6597=PLANE($,#6598);
#6598=AXIS2_PLACEMENT_3D($,#6599,#6600,#6601);
#6599=CARTESIAN_POINT('',(-1.067,0.222,0.0));
#6600=DIRECTION('',(0.0,0.0,-1.0));
#6601=DIRECTION('',(1.,0.,0.));
#6602=FACE_OUTER_BOUND($,#6603,.T.);
#6603=EDGE_LOOP($,(#6606, #6616, #6626, #6636));
#6606=ORIENTED_EDGE('',*,*,#6534,.T.);
#6616=ORIENTED_EDGE('',*,*,#6366,.T.);
#6626=ORIENTED_EDGE('',*,*,#6465,.T.);
#6637=EDGE_CURVE($,#6487,#6456,#6642,.T.);
#6642=CIRCLE($,#6643,0.1778);
#6644=CARTESIAN_POINT('',(-1.067,0.4,0.0));
#6643=AXIS2_PLACEMENT_3D($,#6644,#6645,#6646);
#6645=DIRECTION('',(0.0,0.0,1.0));
#6646=DIRECTION('',(0.0,1.0,0.0));
#6636=ORIENTED_EDGE('',*,*,#6637,.F.);
#6647=STYLED_ITEM('color',(#34),#6648);
#6648=ADVANCED_FACE($,(#6654),#6649,.T.);
#6649=CYLINDRICAL_SURFACE($,#6650,0.1778);
#6650=AXIS2_PLACEMENT_3D($,#6651,#6652,#6653);
#6651=CARTESIAN_POINT('',(-1.067,0.4,0.0));
#6652=DIRECTION('',(0.,0.,1.));
#6653=DIRECTION('',(0.,1.,0.));
#6654=FACE_OUTER_BOUND($,#6655,.T.);
#6655=EDGE_LOOP($,(#6678, #6656, #6688, #6667));
#6678=ORIENTED_EDGE('',*,*,#6484,.T.);
#6656=ORIENTED_EDGE('',*,*,#6637,.T.);
#6688=ORIENTED_EDGE('',*,*,#6455,.T.);
#6667=ORIENTED_EDGE('',*,*,#6585,.T.);
#6700=STYLED_ITEM('',(#34),#6701);
#6701=ADVANCED_FACE('',(#6707),#6702,.T.);
#6702=PLANE('',#6703);
#6703=AXIS2_PLACEMENT_3D('',#6704,#6705,#6706);
#6704=CARTESIAN_POINT('',(-1.499,0.578,0.305));
#6705=DIRECTION('',(1.0,0.0,0.0));
#6706=DIRECTION('',(0.,0.,1.));
#6707=FACE_OUTER_BOUND('',#6708,.T.);
#6708=EDGE_LOOP('',(#6709,#6719,#6729,#6739));
#6709=ORIENTED_EDGE('',*,*,#6337,.T.);
#6719=ORIENTED_EDGE('',*,*,#6504,.T.);
#6729=ORIENTED_EDGE('',*,*,#6565,.T.);
#6739=ORIENTED_EDGE('',*,*,#6435,.T.);
#7167=MANIFOLD_SOLID_BREP($,#7168);
#7168=CLOSED_SHELL('',(#7170,#7219,#7268,#7337,#7406,#7458,#7510,#7563));
#7169=STYLED_ITEM('',(#34),#7170);
#7170=ADVANCED_FACE('',(#7176),#7171,.T.);
#7171=PLANE('',#7172);
#7172=AXIS2_PLACEMENT_3D('',#7173,#7174,#7175);
#7173=CARTESIAN_POINT('',(-1.549,1.378,0.305));
#7174=DIRECTION('',(0.0,0.0,1.0));
#7175=DIRECTION('',(0.,1.,0.));
#7176=FACE_OUTER_BOUND('',#7177,.T.);
#7177=EDGE_LOOP('',(#7178,#7188,#7198,#7208));
#7181=CARTESIAN_POINT('',(-1.549,1.022,0.305));
#7180=VERTEX_POINT('',#7181);
#7183=CARTESIAN_POINT('',(-1.549,1.378,0.305));
#7182=VERTEX_POINT('',#7183);
#7179=EDGE_CURVE('',#7180,#7182,#7184,.T.);
#7184=LINE('',#7181,#7186);
#7186=VECTOR('',#7187,0.3556);
#7187=DIRECTION('',(0.0,1.0,0.0));
#7178=ORIENTED_EDGE('',*,*,#7179,.F.);
#7191=CARTESIAN_POINT('',(-1.499,1.022,0.305));
#7190=VERTEX_POINT('',#7191);
#7189=EDGE_CURVE('',#7190,#7180,#7194,.T.);
#7194=LINE('',#7191,#7196);
#7196=VECTOR('',#7197,0.0507999991522912);
#7197=DIRECTION('',(-1.0,0.0,0.0));
#7188=ORIENTED_EDGE('',*,*,#7189,.F.);
#7201=CARTESIAN_POINT('',(-1.499,1.378,0.305));
#7200=VERTEX_POINT('',#7201);
#7199=EDGE_CURVE('',#7200,#7190,#7204,.T.);
#7204=LINE('',#7201,#7206);
#7206=VECTOR('',#7207,0.355599997762043);
#7207=DIRECTION('',(0.0,-1.0,0.0));
#7198=ORIENTED_EDGE('',*,*,#7199,.F.);
#7209=EDGE_CURVE('',#7182,#7200,#7214,.T.);
#7214=LINE('',#7183,#7216);
#7216=VECTOR('',#7217,0.0507999991522911);
#7217=DIRECTION('',(1.0,0.0,0.0));
#7208=ORIENTED_EDGE('',*,*,#7209,.F.);
#7218=STYLED_ITEM('',(#34),#7219);
#7219=ADVANCED_FACE('',(#7225),#7220,.T.);
#7220=PLANE('',#7221);
#7221=AXIS2_PLACEMENT_3D('',#7222,#7223,#7224);
#7222=CARTESIAN_POINT('',(-1.549,1.378,0.0));
#7223=DIRECTION('',(-1.0,0.0,0.0));
#7224=DIRECTION('',(0.,0.,1.));
#7225=FACE_OUTER_BOUND('',#7226,.T.);
#7226=EDGE_LOOP('',(#7227,#7237,#7247,#7257));
#7230=CARTESIAN_POINT('',(-1.549,1.022,0.0));
#7229=VERTEX_POINT('',#7230);
#7232=CARTESIAN_POINT('',(-1.549,1.378,0.0));
#7231=VERTEX_POINT('',#7232);
#7228=EDGE_CURVE('',#7229,#7231,#7233,.T.);
#7233=LINE('',#7230,#7235);
#7235=VECTOR('',#7236,0.3556);
#7236=DIRECTION('',(0.0,1.0,0.0));
#7227=ORIENTED_EDGE('',*,*,#7228,.F.);
#7238=EDGE_CURVE('',#7180,#7229,#7243,.T.);
#7243=LINE('',#7181,#7245);
#7245=VECTOR('',#7246,0.3047873);
#7246=DIRECTION('',(0.0,0.0,-1.0));
#7237=ORIENTED_EDGE('',*,*,#7238,.F.);
#7247=ORIENTED_EDGE('',*,*,#7179,.T.);
#7258=EDGE_CURVE('',#7231,#7182,#7263,.T.);
#7263=LINE('',#7232,#7265);
#7265=VECTOR('',#7266,0.3047873);
#7266=DIRECTION('',(0.0,0.0,1.0));
#7257=ORIENTED_EDGE('',*,*,#7258,.F.);
#7267=STYLED_ITEM('',(#34),#7268);
#7268=ADVANCED_FACE('',(#7274),#7269,.T.);
#7269=PLANE('',#7270);
#7270=AXIS2_PLACEMENT_3D('',#7271,#7272,#7273);
#7271=CARTESIAN_POINT('',(-1.549,1.378,0.0));
#7272=DIRECTION('',(0.0,1.0,0.0));
#7273=DIRECTION('',(0.,0.,1.));
#7274=FACE_OUTER_BOUND('',#7275,.T.);
#7275=EDGE_LOOP('',(#7276,#7286,#7296,#7306,#7316,#7326));
#7276=ORIENTED_EDGE('',*,*,#7258,.T.);
#7286=ORIENTED_EDGE('',*,*,#7209,.T.);
#7299=CARTESIAN_POINT('',(-1.499,1.378,0.051));
#7298=VERTEX_POINT('',#7299);
#7297=EDGE_CURVE('',#7298,#7200,#7302,.T.);
#7302=LINE('',#7299,#7304);
#7304=VECTOR('',#7305,0.2539873);
#7305=DIRECTION('',(0.0,0.0,1.0));
#7296=ORIENTED_EDGE('',*,*,#7297,.F.);
#7309=CARTESIAN_POINT('',(-1.067,1.378,0.051));
#7308=VERTEX_POINT('',#7309);
#7307=EDGE_CURVE('',#7308,#7298,#7312,.T.);
#7312=LINE('',#7309,#7314);
#7314=VECTOR('',#7315,0.43179999715318);
#7315=DIRECTION('',(-1.0,0.0,0.0));
#7306=ORIENTED_EDGE('',*,*,#7307,.F.);
#7319=CARTESIAN_POINT('',(-1.067,1.378,0.0));
#7318=VERTEX_POINT('',#7319);
#7317=EDGE_CURVE('',#7318,#7308,#7322,.T.);
#7322=LINE('',#7319,#7324);
#7324=VECTOR('',#7325,0.0508);
#7325=DIRECTION('',(0.0,0.0,1.0));
#7316=ORIENTED_EDGE('',*,*,#7317,.F.);
#7327=EDGE_CURVE('',#7231,#7318,#7332,.T.);
#7332=LINE('',#7232,#7334);
#7334=VECTOR('',#7335,0.482599996305472);
#7335=DIRECTION('',(1.0,0.0,0.0));
#7326=ORIENTED_EDGE('',*,*,#7327,.F.);
#7336=STYLED_ITEM('',(#34),#7337);
#7337=ADVANCED_FACE('',(#7343),#7338,.T.);
#7338=PLANE('',#7339);
#7339=AXIS2_PLACEMENT_3D('',#7340,#7341,#7342);
#7340=CARTESIAN_POINT('',(-1.067,1.022,0.0));
#7341=DIRECTION('',(0.0,-1.0,0.0));
#7342=DIRECTION('',(0.,0.,1.));
#7343=FACE_OUTER_BOUND('',#7344,.T.);
#7344=EDGE_LOOP('',(#7345,#7355,#7365,#7375,#7385,#7395));
#7348=CARTESIAN_POINT('',(-1.067,1.022,0.051));
#7347=VERTEX_POINT('',#7348);
#7350=CARTESIAN_POINT('',(-1.067,1.022,0.0));
#7349=VERTEX_POINT('',#7350);
#7346=EDGE_CURVE('',#7347,#7349,#7351,.T.);
#7351=LINE('',#7348,#7353);
#7353=VECTOR('',#7354,0.0508);
#7354=DIRECTION('',(0.0,0.0,-1.0));
#7345=ORIENTED_EDGE('',*,*,#7346,.F.);
#7358=CARTESIAN_POINT('',(-1.499,1.022,0.051));
#7357=VERTEX_POINT('',#7358);
#7356=EDGE_CURVE('',#7357,#7347,#7361,.T.);
#7361=LINE('',#7358,#7363);
#7363=VECTOR('',#7364,0.43179999715318);
#7364=DIRECTION('',(1.0,0.0,0.0));
#7355=ORIENTED_EDGE('',*,*,#7356,.F.);
#7366=EDGE_CURVE('',#7190,#7357,#7371,.T.);
#7371=LINE('',#7191,#7373);
#7373=VECTOR('',#7374,0.2539873);
#7374=DIRECTION('',(0.0,0.0,-1.0));
#7365=ORIENTED_EDGE('',*,*,#7366,.F.);
#7375=ORIENTED_EDGE('',*,*,#7189,.T.);
#7385=ORIENTED_EDGE('',*,*,#7238,.T.);
#7396=EDGE_CURVE('',#7349,#7229,#7401,.T.);
#7401=LINE('',#7350,#7403);
#7403=VECTOR('',#7404,0.482599996305472);
#7404=DIRECTION('',(-1.0,0.0,0.0));
#7395=ORIENTED_EDGE('',*,*,#7396,.F.);
#7405=STYLED_ITEM('color',(#34),#7406);
#7406=ADVANCED_FACE($,(#7412),#7407,.T.);
#7407=PLANE($,#7408);
#7408=AXIS2_PLACEMENT_3D($,#7409,#7410,#7411);
#7409=CARTESIAN_POINT('',(-1.499,1.378,0.051));
#7410=DIRECTION('',(0.0,0.0,1.0));
#7411=DIRECTION('',(1.,0.,0.));
#7412=FACE_OUTER_BOUND($,#7413,.T.);
#7413=EDGE_LOOP($,(#7416, #7426, #7436, #7446));
#7416=ORIENTED_EDGE('',*,*,#7307,.T.);
#7427=EDGE_CURVE('',#7357,#7298,#7432,.T.);
#7432=LINE('',#7358,#7434);
#7434=VECTOR('',#7435,0.355599997762043);
#7435=DIRECTION('',(0.0,1.0,0.0));
#7426=ORIENTED_EDGE('',*,*,#7427,.F.);
#7436=ORIENTED_EDGE('',*,*,#7356,.T.);
#7447=EDGE_CURVE($,#7308,#7347,#7452,.T.);
#7452=CIRCLE($,#7453,0.1778);
#7454=CARTESIAN_POINT('',(-1.067,1.2,0.051));
#7453=AXIS2_PLACEMENT_3D($,#7454,#7455,#7456);
#7455=DIRECTION('',(0.0,0.0,-1.0));
#7456=DIRECTION('',(0.0,-1.0,0.0));
#7446=ORIENTED_EDGE('',*,*,#7447,.F.);
#7457=STYLED_ITEM('color',(#34),#7458);
#7458=ADVANCED_FACE($,(#7464),#7459,.T.);
#7459=PLANE($,#7460);
#7460=AXIS2_PLACEMENT_3D($,#7461,#7462,#7463);
#7461=CARTESIAN_POINT('',(-1.067,1.022,0.0));
#7462=DIRECTION('',(0.0,0.0,-1.0));
#7463=DIRECTION('',(1.,0.,0.));
#7464=FACE_OUTER_BOUND($,#7465,.T.);
#7465=EDGE_LOOP($,(#7468, #7478, #7488, #7498));
#7468=ORIENTED_EDGE('',*,*,#7396,.T.);
#7478=ORIENTED_EDGE('',*,*,#7228,.T.);
#7488=ORIENTED_EDGE('',*,*,#7327,.T.);
#7499=EDGE_CURVE($,#7349,#7318,#7504,.T.);
#7504=CIRCLE($,#7505,0.1778);
#7506=CARTESIAN_POINT('',(-1.067,1.2,0.0));
#7505=AXIS2_PLACEMENT_3D($,#7506,#7507,#7508);
#7507=DIRECTION('',(0.0,0.0,1.0));
#7508=DIRECTION('',(0.0,1.0,0.0));
#7498=ORIENTED_EDGE('',*,*,#7499,.F.);
#7509=STYLED_ITEM('color',(#34),#7510);
#7510=ADVANCED_FACE($,(#7516),#7511,.T.);
#7511=CYLINDRICAL_SURFACE($,#7512,0.1778);
#7512=AXIS2_PLACEMENT_3D($,#7513,#7514,#7515);
#7513=CARTESIAN_POINT('',(-1.067,1.2,0.0));
#7514=DIRECTION('',(0.,0.,1.));
#7515=DIRECTION('',(0.,1.,0.));
#7516=FACE_OUTER_BOUND($,#7517,.T.);
#7517=EDGE_LOOP($,(#7540, #7518, #7550, #7529));
#7540=ORIENTED_EDGE('',*,*,#7346,.T.);
#7518=ORIENTED_EDGE('',*,*,#7499,.T.);
#7550=ORIENTED_EDGE('',*,*,#7317,.T.);
#7529=ORIENTED_EDGE('',*,*,#7447,.T.);
#7562=STYLED_ITEM('',(#34),#7563);
#7563=ADVANCED_FACE('',(#7569),#7564,.T.);
#7564=PLANE('',#7565);
#7565=AXIS2_PLACEMENT_3D('',#7566,#7567,#7568);
#7566=CARTESIAN_POINT('',(-1.499,1.378,0.305));
#7567=DIRECTION('',(1.0,0.0,0.0));
#7568=DIRECTION('',(0.,0.,1.));
#7569=FACE_OUTER_BOUND('',#7570,.T.);
#7570=EDGE_LOOP('',(#7571,#7581,#7591,#7601));
#7571=ORIENTED_EDGE('',*,*,#7199,.T.);
#7581=ORIENTED_EDGE('',*,*,#7366,.T.);
#7591=ORIENTED_EDGE('',*,*,#7427,.T.);
#7601=ORIENTED_EDGE('',*,*,#7297,.T.);
ENDSEC;
END-ISO-10303-21;
