#!/usr/bin/env python3
"""
SnapEDA 3D Model Finder with Working GUI
This version uses the working GUI to show each screen step by step
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from working_screen_gui import get_working_gui

class SnapEDAWithGUI:
    def __init__(self):
        self.download_dir = os.path.abspath('3d')
        os.makedirs(self.download_dir, exist_ok=True)
        self.credentials = self.load_credentials()
        self.gui = None
        
    def load_credentials(self):
        """Load SnapEDA credentials"""
        try:
            with open('component_site_credentials.json', 'r') as f:
                data = json.load(f)
                return data.get('SnapEDA', {})
        except:
            return {
                'email': 'lennyale<PERSON><PERSON>@gmail.com',
                'password': 'Lennyai123#'
            }
    
    def setup_driver(self):
        """Setup Chrome driver with download preferences"""
        chrome_options = Options()
        chrome_options.add_experimental_option("prefs", {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        })
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    
    def find_3d_model(self, manufacturer, part_number):
        """Find and download 3D model with GUI tracking"""
        print(f"🎯 Starting SnapEDA 3D model search with GUI")
        print(f"Manufacturer: {manufacturer}")
        print(f"Part Number: {part_number}")
        
        # Start GUI first
        self.gui = get_working_gui()
        
        # Setup driver and go to SnapEDA
        driver = self.setup_driver()
        
        try:
            # Screen 1: Go to login page
            print("🌐 Opening SnapEDA login page...")
            driver.get("https://www.snapeda.com/login")
            time.sleep(2)
            
            current_url = driver.current_url
            self.gui.update_screen("SnapEDA", 1, "Login Page", current_url,
                                 "LOGIN PAGE:\n" +
                                 "- Email input field (empty)\n" +
                                 "- Password input field (empty)\n" +
                                 "- Login button\n" +
                                 "- SnapEDA logo at top\n" +
                                 "- Ready to enter credentials")
            self.gui.wait_for_continue()
            
            # Screen 2: Fill in credentials
            print("📝 Filling in credentials...")
            email_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, "email"))
            )
            password_field = driver.find_element(By.NAME, "password")
            
            email_field.clear()
            email_field.send_keys(self.credentials['email'])
            password_field.clear()
            password_field.send_keys(self.credentials['password'])
            
            current_url = driver.current_url
            self.gui.update_screen("SnapEDA", 2, "Credentials Entered", current_url,
                                 "LOGIN PAGE WITH FILLED FIELDS:\n" +
                                 f"- Email: {self.credentials['email']}\n" +
                                 "- Password: ********\n" +
                                 "- Login button ready to click\n" +
                                 "- Fields are now filled with your credentials")
            self.gui.wait_for_continue()
            
            # Screen 3: Click login button
            print("🔐 Clicking login button...")
            login_button = driver.find_element(By.XPATH, "//button[@type='submit' or contains(text(), 'Log in') or contains(text(), 'Login')]")
            login_button.click()
            
            current_url = driver.current_url
            self.gui.update_screen("SnapEDA", 3, "Login Processing", current_url,
                                 "LOGIN BUTTON CLICKED:\n" +
                                 "- Login button was clicked\n" +
                                 "- Page is processing login\n" +
                                 "- URL may be changing\n" +
                                 "- Waiting for redirect to dashboard")
            self.gui.wait_for_continue()
            
            # Screen 4: Wait for login success
            print("⏳ Waiting for login to complete...")
            WebDriverWait(driver, 15).until(
                lambda d: "login" not in d.current_url.lower()
            )
            
            current_url = driver.current_url
            page_title = driver.title
            self.gui.update_screen("SnapEDA", 4, "Login Success", current_url,
                                 f"LOGIN SUCCESSFUL:\n" +
                                 f"- URL changed to: {current_url}\n" +
                                 f"- Page title: {page_title}\n" +
                                 "- No longer on login page\n" +
                                 "- Successfully logged into SnapEDA")
            self.gui.wait_for_continue()
            
            # Screen 5: Handle any popup screens
            print("🔍 Checking for popup screens...")
            time.sleep(2)
            
            # Look for welcome/survey popups
            popup_found = False
            try:
                skip_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Skip') or contains(text(), 'Continue') or contains(text(), 'Close')]")
                if skip_buttons and skip_buttons[0].is_displayed():
                    popup_found = True
                    current_url = driver.current_url
                    self.gui.update_screen("SnapEDA", 5, "Welcome Popup", current_url,
                                         "WELCOME/ONBOARDING POPUP:\n" +
                                         "- Welcome message popup appeared\n" +
                                         "- Skip/Continue/Close button visible\n" +
                                         "- Will click to dismiss popup\n" +
                                         "- This is an extra screen after login")
                    self.gui.wait_for_continue()
                    skip_buttons[0].click()
                    time.sleep(1)
            except:
                pass
            
            if not popup_found:
                current_url = driver.current_url
                self.gui.update_screen("SnapEDA", 5, "No Popups", current_url,
                                     "NO EXTRA POPUPS:\n" +
                                     "- No welcome popups appeared\n" +
                                     "- Ready to search for part\n" +
                                     "- Main dashboard/home page loaded\n" +
                                     "- Proceeding to part search")
                self.gui.wait_for_continue()
            
            # Screen 6: Navigate to part page
            print(f"🔍 Searching for part: {manufacturer} {part_number}")
            part_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
            driver.get(part_url)
            time.sleep(3)
            
            current_url = driver.current_url
            self.gui.update_screen("SnapEDA", 6, "Part Page Loading", current_url,
                                 f"LOADING PART PAGE:\n" +
                                 f"- Manufacturer: {manufacturer}\n" +
                                 f"- Part Number: {part_number}\n" +
                                 f"- URL: {part_url}\n" +
                                 "- Loading component details page")
            self.gui.wait_for_continue()
            
            # Screen 7: Check if part page loaded successfully
            if "404" in driver.title or "not found" in driver.page_source.lower():
                current_url = driver.current_url
                self.gui.update_screen("SnapEDA", 7, "Part Not Found", current_url,
                                     "PART NOT FOUND:\n" +
                                     "- 404 error or part not found\n" +
                                     "- Direct URL failed\n" +
                                     "- Need to try search instead\n" +
                                     "- Will attempt search method")
                self.gui.wait_for_continue()
                return None
            else:
                current_url = driver.current_url
                page_title = driver.title
                self.gui.update_screen("SnapEDA", 7, "Part Page Loaded", current_url,
                                     f"PART PAGE LOADED:\n" +
                                     f"- Page title: {page_title}\n" +
                                     "- Component information visible\n" +
                                     "- Tabs should be visible (Overview, 3D Model, etc.)\n" +
                                     "- Ready to find 3D model section")
                self.gui.wait_for_continue()
            
            # Screen 8: Find and click 3D Model tab
            print("🎯 Looking for 3D Model tab...")
            try:
                model_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), '3D Model') or contains(@href, '3d-model')]"))
                )
                model_tab.click()
                time.sleep(2)
                
                current_url = driver.current_url
                self.gui.update_screen("SnapEDA", 8, "3D Model Tab Clicked", current_url,
                                     "3D MODEL TAB CLICKED:\n" +
                                     "- 3D Model tab was found and clicked\n" +
                                     "- 3D model section should now be active\n" +
                                     "- Looking for download button\n" +
                                     "- 3D preview may be visible")
                self.gui.wait_for_continue()
                
            except Exception as e:
                current_url = driver.current_url
                self.gui.update_screen("SnapEDA", 8, "3D Tab Not Found", current_url,
                                     "3D MODEL TAB NOT FOUND:\n" +
                                     "- Could not find 3D Model tab\n" +
                                     "- Tab may not exist for this part\n" +
                                     "- No 3D model available\n" +
                                     f"- Error: {str(e)}")
                self.gui.wait_for_continue()
                return None
            
            # Continue with download process...
            print("✅ GUI demonstration completed successfully!")
            return "demo_completed"
            
        except Exception as e:
            if self.gui:
                self.gui.show_error(f"Error occurred: {str(e)}")
            print(f"❌ Error: {e}")
            return None
        finally:
            if 'driver' in locals():
                driver.quit()

def test_snapeda_gui():
    """Test the SnapEDA finder with GUI"""
    finder = SnapEDAWithGUI()
    result = finder.find_3d_model("Texas Instruments", "LM358N")
    print(f"Result: {result}")

if __name__ == "__main__":
    test_snapeda_gui()
