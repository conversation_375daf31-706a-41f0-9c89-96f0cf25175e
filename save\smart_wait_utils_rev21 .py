#!/usr/bin/env python3
"""
Smart Wait Utilities for Web Scraping
Replaces time.sleep() with intelligent WebDriverWait conditions
"""

import os
import time
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, WebDriverException

class SmartWait:
    """Utility class for intelligent waiting in web scraping"""
    
    def __init__(self, driver, default_timeout=10):
        self.driver = driver
        self.default_timeout = default_timeout
        self.wait = WebDriverWait(driver, default_timeout)
    
    def for_element_clickable(self, locator, timeout=None):
        """Wait for element to be clickable"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(EC.element_to_be_clickable(locator))
        except TimeoutException:
            return None
    
    def for_element_visible(self, locator, timeout=None):
        """Wait for element to be visible"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(EC.visibility_of_element_located(locator))
        except TimeoutException:
            return None
    
    def for_element_present(self, locator, timeout=None):
        """Wait for element to be present in DOM"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(EC.presence_of_element_located(locator))
        except TimeoutException:
            return None
    
    def for_url_contains(self, text, timeout=None):
        """Wait for URL to contain specific text"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(EC.url_contains(text))
        except TimeoutException:
            return False
    
    def for_url_changes(self, current_url, timeout=None):
        """Wait for URL to change from current URL"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(lambda driver: driver.current_url != current_url)
        except TimeoutException:
            return False
    
    def for_page_load(self, timeout=None):
        """Wait for page to fully load"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            # Wait for document ready state
            return wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
        except TimeoutException:
            return False
    
    def for_text_in_element(self, locator, text, timeout=None):
        """Wait for specific text to appear in element"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(EC.text_to_be_present_in_element(locator, text))
        except TimeoutException:
            return False
    
    def for_element_to_disappear(self, locator, timeout=None):
        """Wait for element to disappear"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(EC.invisibility_of_element_located(locator))
        except TimeoutException:
            return False
    
    def for_new_window(self, current_handles, timeout=None):
        """Wait for new window to open"""
        timeout = timeout or self.default_timeout
        wait = WebDriverWait(self.driver, timeout)
        try:
            return wait.until(lambda driver: len(driver.window_handles) > len(current_handles))
        except TimeoutException:
            return False
    
    def for_download_start(self, download_dir, initial_files, timeout=30):
        """Wait for download to start (new file appears)"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                current_files = set(os.listdir(download_dir))
                new_files = current_files - initial_files
                if new_files:
                    return list(new_files)
                time.sleep(0.5)  # Short poll interval
            except Exception:
                time.sleep(0.5)
        return []
    
    def for_download_complete(self, download_dir, filename, timeout=60):
        """Wait for specific download to complete"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                file_path = os.path.join(download_dir, filename)
                if os.path.exists(file_path):
                    # Check if file is still being written (size changes)
                    initial_size = os.path.getsize(file_path)
                    time.sleep(1)
                    if os.path.exists(file_path):
                        final_size = os.path.getsize(file_path)
                        if initial_size == final_size and final_size > 0:
                            return True
                time.sleep(0.5)
            except Exception:
                time.sleep(0.5)
        return False
    
    def smart_click(self, locator, timeout=None):
        """Smart click with multiple strategies"""
        timeout = timeout or self.default_timeout
        
        # Strategy 1: Wait for clickable and click
        element = self.for_element_clickable(locator, timeout)
        if element:
            try:
                element.click()
                return True
            except Exception:
                pass
        
        # Strategy 2: Find element and scroll into view, then click
        element = self.for_element_present(locator, timeout)
        if element:
            try:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(0.5)  # Brief pause for scroll
                element.click()
                return True
            except Exception:
                pass
        
        # Strategy 3: JavaScript click
        if element:
            try:
                self.driver.execute_script("arguments[0].click();", element)
                return True
            except Exception:
                pass
        
        return False
    
    def smart_send_keys(self, locator, text, clear_first=True, timeout=None):
        """Smart send keys with wait"""
        element = self.for_element_visible(locator, timeout)
        if element:
            try:
                if clear_first:
                    element.clear()
                element.send_keys(text)
                return True
            except Exception:
                pass
        return False
    
    def wait_for_any_element(self, locators, timeout=None):
        """Wait for any of multiple elements to appear"""
        timeout = timeout or self.default_timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            for locator in locators:
                try:
                    element = self.driver.find_element(*locator)
                    if element.is_displayed():
                        return element, locator
                except Exception:
                    continue
            time.sleep(0.5)
        
        return None, None
    
    def wait_with_retry(self, action_func, max_retries=3, delay=1):
        """Execute action with retries"""
        for attempt in range(max_retries):
            try:
                result = action_func()
                if result:
                    return result
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                time.sleep(delay)
        return None

def create_smart_wait(driver, timeout=10):
    """Factory function to create SmartWait instance"""
    return SmartWait(driver, timeout)

# Common locator patterns
class CommonLocators:
    """Common locator patterns for web elements"""
    
    LOGIN_LINKS = [
        (By.XPATH, "//a[contains(@href, 'login')]"),
        (By.XPATH, "//a[contains(text(), 'Login')]"),
        (By.XPATH, "//a[contains(text(), 'Sign In')]"),
        (By.CSS_SELECTOR, "a[href*='login']"),
        (By.CSS_SELECTOR, ".login-link"),
        (By.ID, "login-link")
    ]
    
    DOWNLOAD_BUTTONS = [
        (By.XPATH, "//a[contains(@href, 'download')]"),
        (By.XPATH, "//button[contains(text(), 'Download')]"),
        (By.XPATH, "//a[contains(text(), 'Download')]"),
        (By.CSS_SELECTOR, ".download-btn"),
        (By.CSS_SELECTOR, ".download-button"),
        (By.CSS_SELECTOR, "a[href*='download']")
    ]
    
    SEARCH_BOXES = [
        (By.CSS_SELECTOR, "input[type='search']"),
        (By.CSS_SELECTOR, "input[placeholder*='search']"),
        (By.CSS_SELECTOR, "input[placeholder*='Search']"),
        (By.CSS_SELECTOR, ".search-input"),
        (By.ID, "search"),
        (By.NAME, "q"),
        (By.NAME, "search")
    ]

# Usage example:
# smart_wait = create_smart_wait(driver, timeout=15)
# element = smart_wait.for_element_clickable((By.ID, "login-button"))
# if element:
#     element.click()
