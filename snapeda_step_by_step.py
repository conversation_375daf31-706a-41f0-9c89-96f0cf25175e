#!/usr/bin/env python3
"""
SnapEDA Step by Step - One screen at a time with GUI dialogs
"""

import time
import tkinter as tk
from tkinter import messagebox
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def ask_user_gui(question):
    """Show GUI dialog asking user a question - ALWAYS ON TOP"""
    root = tk.Tk()
    root.withdraw()  # Hide main window
    root.attributes('-topmost', True)  # Force on top
    root.lift()  # Bring to front
    root.focus_force()  # Force focus

    result = messagebox.askyesno("SnapEDA Test", question)
    root.destroy()
    return result

def step_by_step():
    """Show one screen at a time"""
    print("🚀 SnapEDA Step by Step...")
    
    # Create VERY visible Chrome driver with download setup
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    # Force window to be visible and on top
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--force-device-scale-factor=1")

    # Set up download directory
    import os
    download_dir = os.path.join(os.getcwd(), "3d")
    os.makedirs(download_dir, exist_ok=True)

    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    print(f"📁 Download directory set to: {download_dir}")

    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()

    # Force window to front
    driver.execute_script("window.focus();")
    print("🖥️ Browser window should now be MAXIMIZED and VISIBLE!")
    
    try:
        # Step 1: Show login page and STOP
        print("STEP 1: Going to SnapEDA login page...")
        driver.get("https://www.snapeda.com/account/login/?next=/home/")
        time.sleep(2)
        
        print("✅ Login page loaded")

        # Ask about the screen with GUI popup
        if not ask_user_gui("Do you see the SnapEDA login screen?"):
            print("❌ Login screen not visible - but continuing...")
        else:
            print("✅ User confirmed login screen is visible")

        # Step 2: Login
        print("STEP 2: Logging in...")
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")

        email_field.send_keys("<EMAIL>")
        password_field.send_keys("Lennyai123#")

        # Find the login button BELOW the password field (the bottom one)
        login_button = driver.find_element(By.XPATH, "//input[@type='password']/following::*[contains(text(), 'Log in') or @value='Log in' or @type='submit'][1]")
        login_button.click()
        time.sleep(3)
        print("✅ Login completed")

        # Ask about the screen with GUI popup
        if not ask_user_gui("Do you see the logged-in screen (are you now logged in)?"):
            print("❌ Login failed - but continuing...")
        else:
            print("✅ User confirmed login worked")

        # Step 3: Go directly to part page (like working version)
        print("STEP 3: Going to part page...")
        part_url = "https://www.snapeda.com/parts/GCM155R71H104KE02D/Murata/view-part/"
        driver.get(part_url)
        time.sleep(2)
        print("✅ Part page loaded")

        # Ask about the screen with GUI popup
        if not ask_user_gui("Do you see the part page with component information?"):
            print("❌ Part page not visible - but continuing...")
        else:
            print("✅ User confirmed part page is visible")

        # Step 4: Click 3D Model tab and ask about it
        print("STEP 4: Clicking 3D Model tab...")
        try:
            three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
            three_d_tab.click()
            time.sleep(2)
            print("✅ 3D Model tab clicked")

            # Ask about 3D model screen
            if not ask_user_gui("Do you see the 3D Model content after clicking the tab?"):
                print("❌ 3D Model content not visible")
            else:
                print("✅ User confirmed 3D Model content is visible")

                # Step 5: Press the download button
                print("STEP 5: Looking for and pressing download button...")
                download_selectors = [
                    "//a[contains(@class, '3D-model-download')]",
                    "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
                    "//a[text()='Download 3D Model' and contains(@class, 'orange')]",
                    "//a[contains(text(), 'Download') and contains(text(), '3D')]"
                ]

                download_button = None
                for selector in download_selectors:
                    try:
                        button = driver.find_element(By.XPATH, selector)
                        if button.is_displayed():
                            download_button = button
                            print(f"✅ Found download button: '{button.text}'")
                            break
                    except:
                        continue

                if download_button:
                    # Check files before download
                    files_before = set(os.listdir(download_dir)) if os.path.exists(download_dir) else set()

                    print("🔽 Clicking download button...")
                    download_button.click()
                    time.sleep(5)  # Wait longer for download
                    print("✅ Download button clicked!")

                    # Check for new files
                    files_after = set(os.listdir(download_dir)) if os.path.exists(download_dir) else set()
                    new_files = files_after - files_before

                    if new_files:
                        print(f"✅ Downloaded files: {list(new_files)}")
                        ask_user_gui(f"SUCCESS! Downloaded: {list(new_files)}")
                    else:
                        print("❌ No new files found in download directory")
                        # Ask if download worked
                        if ask_user_gui("Did the download start? (Do you see a download or modal?)"):
                            print("✅ User confirmed download started")
                        else:
                            print("❌ User says download did not start")
                else:
                    print("❌ Could not find download button")

        except Exception as e:
            print(f"❌ Could not click 3D Model tab: {e}")

        # Keep browser open for final inspection
        ask_user_gui("Click OK to close the browser and finish the test")

        print("✅ All steps completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()
        print("🏁 Done")

if __name__ == "__main__":
    step_by_step()
