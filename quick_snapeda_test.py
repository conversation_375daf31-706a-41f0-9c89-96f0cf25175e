#!/usr/bin/env python3
"""
Quick test of SnapEDA login with the fixes
"""

print("🔍 Quick SnapEDA test starting...")

try:
    import sys
    sys.path.append('save')
    
    from snapeda_3d_finder_final import SnapEDA3DFinder
    
    print("✅ Imported SnapEDA finder")
    
    finder = SnapEDA3DFinder()
    print("✅ Created finder instance")
    
    # Test login only
    driver = finder.setup_driver(silent=False)
    if driver:
        print("✅ Driver setup successful")
        
        login_result = finder.login_to_snapeda(driver)
        print(f"✅ Login result: {login_result}")
        
        driver.quit()
        print("✅ Driver closed")
    else:
        print("❌ Driver setup failed")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("🎯 Quick test complete")
