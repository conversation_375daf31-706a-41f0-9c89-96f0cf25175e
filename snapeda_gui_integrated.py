#!/usr/bin/env python3
"""
SnapEDA 3D Model Finder with Integrated Working GUI
This version properly integrates the working GUI with the existing SnapEDA finder
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from working_screen_gui import get_working_gui

def find_3d_model_with_gui(manufacturer, part_number):
    """Find 3D model with GUI tracking - main function"""
    print(f"🎯 SnapEDA 3D Model Finder with GUI")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 50)
    
    # Create GUI first
    gui = get_working_gui()
    
    # Load credentials
    credentials = load_credentials()
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    # Setup Chrome driver
    driver = setup_chrome_driver(download_dir)
    
    try:
        # SCREEN 1: Open SnapEDA login page
        print("🌐 Opening SnapEDA login page...")
        driver.get("https://www.snapeda.com/account/login/?next=/home/")
        time.sleep(2)
        
        current_url = driver.current_url
        gui.update_screen("SnapEDA", 1, "Login Page", current_url,
                         "LOGIN PAGE LOADED:\n" +
                         "✅ SnapEDA login page is now open\n" +
                         f"✅ URL: {current_url}\n" +
                         "👀 You should see:\n" +
                         "  - Email input field (empty)\n" +
                         "  - Password input field (empty)\n" +
                         "  - Login button\n" +
                         "  - SnapEDA logo\n\n" +
                         "🔄 Next: Will fill in email and password")
        gui.wait_for_continue()
        
        # SCREEN 2: Fill credentials
        print("📝 Filling in credentials...")
        email_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.NAME, "email"))
        )
        password_field = driver.find_element(By.NAME, "password")
        
        email_field.clear()
        email_field.send_keys(credentials['email'])
        password_field.clear()
        password_field.send_keys(credentials['password'])
        
        current_url = driver.current_url
        gui.update_screen("SnapEDA", 2, "Credentials Filled", current_url,
                         "CREDENTIALS ENTERED:\n" +
                         "✅ Email and password have been typed\n" +
                         f"✅ URL: {current_url}\n" +
                         "👀 You should see:\n" +
                         f"  - Email field: {credentials['email']}\n" +
                         "  - Password field: ********\n" +
                         "  - Login button ready to click\n\n" +
                         "🔄 Next: Will click the login button")
        gui.wait_for_continue()
        
        # SCREEN 3: Click login
        print("🔐 Clicking login button...")
        login_button = driver.find_element(By.XPATH, "//button[@type='submit' or contains(text(), 'Log in') or contains(text(), 'Login')]")
        login_button.click()
        
        current_url = driver.current_url
        gui.update_screen("SnapEDA", 3, "Login Clicked", current_url,
                         "LOGIN BUTTON CLICKED:\n" +
                         "✅ Login button has been clicked\n" +
                         "👀 You should see:\n" +
                         "  - Page processing/loading\n" +
                         "  - URL may be changing\n" +
                         "  - Loading spinner or redirect\n\n" +
                         "🔄 Next: Waiting for login to complete")
        gui.wait_for_continue()
        
        # SCREEN 4: Wait for login success
        print("⏳ Waiting for login to complete...")
        WebDriverWait(driver, 15).until(
            lambda d: "login" not in d.current_url.lower()
        )
        
        current_url = driver.current_url
        page_title = driver.title
        gui.update_screen("SnapEDA", 4, "Login Successful", current_url,
                         "LOGIN SUCCESSFUL:\n" +
                         "✅ Successfully logged into SnapEDA\n" +
                         "👀 You should see:\n" +
                         f"  - New URL: {current_url}\n" +
                         f"  - Page title: {page_title}\n" +
                         "  - Dashboard or home page\n" +
                         "  - No longer on login page\n\n" +
                         "🔄 Next: Checking for popup screens")
        gui.wait_for_continue()
        
        # SCREEN 5: Handle popups (SnapEDA's extra screens)
        print("🔍 Checking for popup screens...")
        time.sleep(2)
        
        popup_handled = False
        try:
            # Look for welcome/onboarding popups
            skip_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Skip') or contains(text(), 'Continue') or contains(text(), 'Close') or contains(text(), 'Get Started')]")
            if skip_buttons and skip_buttons[0].is_displayed():
                current_url = driver.current_url
                gui.update_screen("SnapEDA", 5, "Welcome Popup Found", current_url,
                                 "WELCOME POPUP DETECTED:\n" +
                                 "✅ Found welcome/onboarding popup\n" +
                                 "👀 You should see:\n" +
                                 "  - Welcome message overlay\n" +
                                 "  - Skip/Continue/Close button\n" +
                                 "  - Popup covering main page\n\n" +
                                 "🔄 Next: Will click to dismiss popup")
                gui.wait_for_continue()
                
                skip_buttons[0].click()
                time.sleep(1)
                popup_handled = True
                
                # Check for second popup (survey/feedback)
                dismiss_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'No thanks') or contains(text(), 'Dismiss') or contains(text(), 'Later') or contains(text(), 'Maybe later')]")
                if dismiss_buttons and dismiss_buttons[0].is_displayed():
                    current_url = driver.current_url
                    gui.update_screen("SnapEDA", 6, "Survey Popup Found", current_url,
                                     "SURVEY POPUP DETECTED:\n" +
                                     "✅ Found survey/feedback popup\n" +
                                     "👀 You should see:\n" +
                                     "  - Survey or feedback request\n" +
                                     "  - No thanks/Dismiss button\n" +
                                     "  - Second popup overlay\n\n" +
                                     "🔄 Next: Will dismiss this popup too")
                    gui.wait_for_continue()
                    dismiss_buttons[0].click()
                    time.sleep(1)
        except:
            pass
        
        if not popup_handled:
            current_url = driver.current_url
            gui.update_screen("SnapEDA", 5, "No Popups", current_url,
                             "NO EXTRA POPUPS:\n" +
                             "✅ No welcome popups appeared\n" +
                             "👀 You should see:\n" +
                             "  - Clean dashboard/home page\n" +
                             "  - No overlay popups\n" +
                             "  - Ready to search for parts\n\n" +
                             "🔄 Next: Will navigate to part page")
            gui.wait_for_continue()
        
        # SCREEN 6: Navigate to part
        print(f"🔍 Navigating to part: {manufacturer} {part_number}")
        part_url = f"https://www.snapeda.com/parts/{part_number}/{manufacturer.replace(' ', '%20')}/view-part/"
        driver.get(part_url)
        time.sleep(3)
        
        current_url = driver.current_url
        gui.update_screen("SnapEDA", 7, "Part Page Loading", current_url,
                         "LOADING PART PAGE:\n" +
                         f"✅ Navigating to {manufacturer} {part_number}\n" +
                         "👀 You should see:\n" +
                         "  - Component page loading\n" +
                         f"  - URL: {part_url}\n" +
                         "  - Part information appearing\n\n" +
                         "🔄 Next: Will check if part page loaded successfully")
        gui.wait_for_continue()
        
        # SCREEN 7: Check part page
        if "404" in driver.title or "not found" in driver.page_source.lower():
            current_url = driver.current_url
            gui.update_screen("SnapEDA", 8, "Part Not Found", current_url,
                             "PART NOT FOUND:\n" +
                             "❌ Component not found on SnapEDA\n" +
                             "👀 You should see:\n" +
                             "  - 404 error page\n" +
                             "  - 'Not found' message\n" +
                             "  - No component information\n\n" +
                             "❌ Cannot proceed - part doesn't exist")
            gui.wait_for_continue()
            return None
        else:
            current_url = driver.current_url
            page_title = driver.title
            gui.update_screen("SnapEDA", 8, "Part Page Loaded", current_url,
                             "PART PAGE SUCCESS:\n" +
                             f"✅ {manufacturer} {part_number} page loaded\n" +
                             "👀 You should see:\n" +
                             f"  - Page title: {page_title}\n" +
                             "  - Component information\n" +
                             "  - Tabs (Overview, 3D Model, etc.)\n" +
                             "  - Component details and specs\n\n" +
                             "🔄 Next: Will look for 3D Model tab")
            gui.wait_for_continue()
        
        # SCREEN 8: Find 3D Model tab
        print("🎯 Looking for 3D Model tab...")
        try:
            model_tab = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), '3D Model') or contains(@href, '3d-model')]"))
            )
            model_tab.click()
            time.sleep(2)
            
            current_url = driver.current_url
            gui.update_screen("SnapEDA", 9, "3D Model Tab Active", current_url,
                             "3D MODEL TAB CLICKED:\n" +
                             "✅ 3D Model tab found and clicked\n" +
                             "👀 You should see:\n" +
                             "  - 3D Model tab is now active\n" +
                             "  - 3D model section visible\n" +
                             "  - Download button should appear\n" +
                             "  - 3D preview (if available)\n\n" +
                             "🔄 Next: Will look for download button")
            gui.wait_for_continue()
            
        except Exception as e:
            current_url = driver.current_url
            gui.update_screen("SnapEDA", 9, "No 3D Model", current_url,
                             "NO 3D MODEL AVAILABLE:\n" +
                             "❌ 3D Model tab not found\n" +
                             "👀 You should see:\n" +
                             "  - No 3D Model tab in navigation\n" +
                             "  - Only other tabs available\n" +
                             "  - This part has no 3D model\n\n" +
                             f"❌ Error: {str(e)}")
            gui.wait_for_continue()
            return None
        
        # SCREEN 9: Success message
        current_url = driver.current_url
        gui.update_screen("SnapEDA", 10, "Process Complete", current_url,
                         "GUI DEMONSTRATION COMPLETE:\n" +
                         "✅ Successfully demonstrated all screens\n" +
                         "👀 Summary of what was shown:\n" +
                         "  1. Login page\n" +
                         "  2. Credentials filled\n" +
                         "  3. Login clicked\n" +
                         "  4. Login successful\n" +
                         "  5. Popup handling\n" +
                         "  6. Part page loading\n" +
                         "  7. Part page loaded\n" +
                         "  8. 3D Model tab clicked\n\n" +
                         "🎉 GUI is working perfectly!")
        gui.wait_for_continue()
        
        print("✅ GUI demonstration completed successfully!")
        return "gui_demo_complete"
        
    except Exception as e:
        if gui:
            gui.show_error(f"Error occurred: {str(e)}")
        print(f"❌ Error: {e}")
        return None
    finally:
        if 'driver' in locals():
            driver.quit()

def load_credentials():
    """Load SnapEDA credentials"""
    try:
        with open('component_site_credentials.json', 'r') as f:
            data = json.load(f)
            return data.get('SnapEDA', {})
    except:
        return {
            'email': '<EMAIL>',
            'password': 'Lennyai123#'
        }

def setup_chrome_driver(download_dir):
    """Setup Chrome driver with download preferences"""
    chrome_options = Options()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

if __name__ == "__main__":
    # Test with a known component
    result = find_3d_model_with_gui("Texas Instruments", "LM358N")
    print(f"Final result: {result}")
