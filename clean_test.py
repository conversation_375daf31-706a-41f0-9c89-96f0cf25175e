#!/usr/bin/env python3
"""
Clean test - Start fresh
"""

print("Starting clean test...")

try:
    from working_screen_gui import get_working_gui
    print("GUI import successful")
    
    gui = get_working_gui()
    print("GUI created")
    
    gui.update_screen("SnapEDA", 1, "Clean Test", "http://test.com", 
                     "CLEAN TEST SCREEN:\n\nThis is a fresh start.\nClick CONTINUE to proceed.")
    print("GUI screen updated - you should see the window now")
    
    gui.wait_for_continue()
    print("Continue button clicked!")
    
    # Now test SnapEDA import
    from snapeda_3d_finder import SnapEDA3DFinder
    print("SnapEDA import successful")
    
    finder = SnapEDA3DFinder()
    finder.gui = gui
    print("SnapEDA finder created with GUI")
    
    gui.update_screen("SnapEDA", 2, "Ready to Start", "about:blank",
                     "READY TO START SNAPEDA:\n\nBrowser will open next.\nClick CONTINUE to begin.")
    gui.wait_for_continue()
    
    print("Starting SnapEDA process...")
    result = finder.search_and_download("Texas Instruments", "LM358N", silent=False)
    
    if result:
        print(f"SUCCESS: {result}")
    else:
        print("FAILED")
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
