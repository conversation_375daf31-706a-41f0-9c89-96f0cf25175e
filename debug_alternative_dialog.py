#!/usr/bin/env python3
"""
Debug script to trace the alternative dialog issue step by step
"""

import sys
import os
sys.path.append('.')

def debug_alternative_flow():
    """Debug the complete alternative acceptance flow"""
    print("🐛 DEBUGGING ALTERNATIVE DIALOG ISSUE")
    print("=" * 60)
    
    from component_finder import ComponentFinderGUI
    import tkinter as tk
    
    # Create a test instance
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    app = ComponentFinderGUI(root)
    
    # Enable debug mode
    app.silent_mode = False
    
    print("🔍 Step 1: Testing API function that should trigger alternative dialog")
    print("-" * 50)
    
    # Test with a part that might trigger alternatives
    manufacturer = 'Texas Instruments'
    original_part = 'LP590722QDQNRQ1WRONG'  # Wrong part that might find similar parts
    
    print(f"🔍 Testing: {manufacturer} {original_part}")
    
    try:
        # Test Digi-Key API directly
        print("\n📡 Testing Digi-Key API...")
        digikey_result = app.try_digikey_api_fallback(manufacturer, original_part)
        
        if digikey_result:
            print(f"✅ Digi-Key result: {digikey_result}")
            api_part = digikey_result.get('part_number', '')
            updated_part = digikey_result.get('updated_part_number', original_part)
            
            print(f"📋 API found part: {api_part}")
            print(f"📋 Updated part number: {updated_part}")
            
            # Check if this would trigger alternative dialog
            if api_part and original_part.upper() not in api_part.upper():
                print(f"🚨 THIS SHOULD TRIGGER ALTERNATIVE DIALOG!")
                print(f"   Looking for: {original_part}")
                print(f"   API found: {api_part}")
                print(f"   Condition: '{original_part.upper()}' not in '{api_part.upper()}' = {original_part.upper() not in api_part.upper()}")
            else:
                print("ℹ️ This would NOT trigger alternative dialog")
        else:
            print("❌ No Digi-Key result")
            
    except Exception as e:
        print(f"❌ Error testing Digi-Key: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 Step 2: Testing with a part that WILL trigger alternative")
    print("-" * 50)
    
    # Manually test the alternative dialog logic
    try:
        # Mock a scenario where API finds a different part
        mock_api_part = "LP590722QDQNRQ1"  # What API found
        search_part = "LP590722QDQNRQ1WRONG"  # What we're looking for (wrong part)
        
        print(f"🔍 Mock scenario:")
        print(f"   Searching for: {search_part}")
        print(f"   API found: {mock_api_part}")
        
        # Check the condition
        condition_result = search_part.upper() not in mock_api_part.upper()
        print(f"   Condition check: '{search_part.upper()}' not in '{mock_api_part.upper()}' = {condition_result}")
        
        if condition_result:
            print("🚨 THIS WOULD TRIGGER ALTERNATIVE DIALOG!")
            
            # Test the alternative dialog function
            print("\n🧪 Testing alternative dialog function...")
            
            # Mock the dialog (since we can't show real dialog in test)
            print(f"   Would show dialog with alternative: {mock_api_part}")
            print(f"   Simulating user accepting alternative...")
            
            # This is what should happen when user accepts
            accepted_alternative = mock_api_part
            print(f"✅ User accepted: {accepted_alternative}")
            
            # Test the return value handling
            mock_result = {
                'manufacturer': manufacturer,
                'website': 'https://www.ti.com',
                'datasheet_url': 'https://example.com/datasheet.pdf',
                'success': True,
                'updated_part_number': accepted_alternative  # This is the key!
            }
            
            print(f"📋 Mock API result with accepted alternative: {mock_result}")
            
            # Test if main search would handle this correctly
            updated_part_from_result = mock_result.get('updated_part_number', search_part)
            if updated_part_from_result != search_part:
                print(f"✅ Main search SHOULD update part number: {search_part} → {updated_part_from_result}")
            else:
                print(f"❌ Main search would NOT update part number")
                
        else:
            print("ℹ️ This would NOT trigger alternative dialog")
            
    except Exception as e:
        print(f"❌ Error in mock test: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 Step 3: Testing actual search flow")
    print("-" * 50)
    
    try:
        # Test the distributor search function
        print("📡 Testing distributor search function...")
        distributor_result = app.search_distributors_for_part_info(manufacturer, "LP590722QDQNRQ1WRONG")
        
        if distributor_result:
            print(f"✅ Distributor search result: {distributor_result}")
            updated_part = distributor_result.get('updated_part_number', 'LP5907')
            print(f"📋 Updated part number from distributor search: {updated_part}")
        else:
            print("❌ No distributor result")
            
    except Exception as e:
        print(f"❌ Error in distributor search: {e}")
        import traceback
        traceback.print_exc()
    
    root.destroy()
    print("\n🐛 DEBUG COMPLETED")

if __name__ == "__main__":
    debug_alternative_flow()
