#!/usr/bin/env python3
"""
Test the real alternative scenario by mocking the API to return a different part
"""

import sys
import os
sys.path.append('.')

def test_real_alternative_scenario():
    """Test by mocking the API to return a different part number"""
    print("🐛 TESTING REAL ALTERNATIVE SCENARIO")
    print("=" * 60)
    
    from component_finder import ComponentFinderGUI
    import tkinter as tk
    
    # Create a test instance
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    app = ComponentFinderGUI(root)
    
    # Mock the API function to return a different part number
    original_digikey_api = app.try_digikey_api_fallback
    
    def mock_digikey_api(manufacturer, part_number):
        """Mock API that returns a different part number to trigger alternative dialog"""
        print(f"🎭 MOCK API called with: {manufacturer} {part_number}")

        if part_number == "LP5907TEST":
            # Return a different but similar part to trigger alternative dialog
            print("🎭 MOCK API returning different part to trigger alternative dialog")

            # Simulate the actual API behavior - it would find a similar part
            # and the try_digikey_api_fallback function would detect the mismatch
            # and trigger the alternative dialog

            # Mock what the API module would return
            mock_api_response = {
                'manufacturer': 'Texas Instruments',
                'part_number': 'LP590722QDQNRQ1',  # What the API actually found
                'datasheet_url': 'https://www.ti.com/lit/ds/symlink/lp590722qdqnrq1.pdf',
                'website': 'https://www.ti.com'
            }

            # Now simulate the try_digikey_api_fallback logic
            api_part_number = mock_api_response['part_number']

            # Check if this would trigger alternative dialog
            if api_part_number and part_number.upper() not in api_part_number.upper():
                print(f"🎭 MOCK: Would trigger alternative dialog - '{part_number}' not in '{api_part_number}'")

                # Simulate the alternative dialog being accepted
                accepted_alternate = mock_alternative_dialog(manufacturer, part_number, [api_part_number])

                if accepted_alternate:
                    print(f"🎭 MOCK: Alternative accepted, updating part_number to {accepted_alternate}")
                    part_number = accepted_alternate  # This is what the real code does

            return {
                'manufacturer': mock_api_response['manufacturer'],
                'website': mock_api_response['website'],
                'datasheet_url': mock_api_response['datasheet_url'],
                'success': True,
                'updated_part_number': part_number  # This should now be the accepted alternative
            }
        else:
            # Call the real API for other parts
            return original_digikey_api(manufacturer, part_number)
    
    # Replace the API function with our mock
    app.try_digikey_api_fallback = mock_digikey_api
    
    # Mock the alternative dialog to automatically accept the alternative
    original_dialog = app.show_alternate_part_acceptance_dialog
    
    def mock_alternative_dialog(manufacturer, original_part, alternatives):
        """Mock dialog that automatically accepts the first alternative"""
        print(f"🎭 MOCK DIALOG called:")
        print(f"   Manufacturer: {manufacturer}")
        print(f"   Original part: {original_part}")
        print(f"   Alternatives: {alternatives}")
        
        if alternatives:
            accepted = alternatives[0]
            print(f"🎭 MOCK DIALOG automatically accepting: {accepted}")
            return accepted
        return None
    
    # Replace the dialog with our mock
    app.show_alternate_part_acceptance_dialog = mock_alternative_dialog
    
    print("🔍 Testing with part that will trigger alternative dialog...")
    manufacturer = 'Texas Instruments'
    original_part = 'LP5907TEST'
    
    try:
        # Test the API function directly
        print("\n📡 Step 1: Testing mocked API function...")
        api_result = app.try_digikey_api_fallback(manufacturer, original_part)
        print(f"✅ API result: {api_result}")
        
        if api_result:
            api_part = api_result.get('part_number', '')
            print(f"📋 API found part: {api_part}")
            print(f"📋 Original search: {original_part}")
            
            # Check if this triggers alternative dialog condition
            if api_part and original_part.upper() not in api_part.upper():
                print(f"🚨 THIS SHOULD TRIGGER ALTERNATIVE DIALOG!")
                print(f"   Condition: '{original_part.upper()}' not in '{api_part.upper()}' = True")
                
                # Test the alternative dialog
                print("\n🎭 Step 2: Testing alternative dialog...")
                accepted = app.show_alternate_part_acceptance_dialog(manufacturer, original_part, [api_part])
                print(f"✅ Dialog returned: {accepted}")
                
                if accepted:
                    # Update the API result as the real code would do
                    api_result['updated_part_number'] = accepted
                    print(f"📋 Updated API result: {api_result}")
                    
        # Test the distributor search function
        print("\n📡 Step 3: Testing distributor search function...")
        distributor_result = app.search_distributors_for_part_info(manufacturer, original_part)
        
        if distributor_result:
            print(f"✅ Distributor result: {distributor_result}")
            updated_part = distributor_result.get('updated_part_number', original_part)
            print(f"📋 Final updated part number: {updated_part}")
            
            if updated_part != original_part:
                print(f"🎉 SUCCESS: Part number was updated from '{original_part}' to '{updated_part}'")
                
                # Now test what happens in the main search
                print("\n🔍 Step 4: Testing main search integration...")
                
                # Mock the main search to see if it handles the updated part number
                print("📋 Simulating main search receiving distributor result...")
                
                # This is what the main search should do:
                if isinstance(distributor_result, dict):
                    main_search_updated_part = distributor_result.get('updated_part_number', original_part)
                    if main_search_updated_part != original_part:
                        print(f"✅ Main search SHOULD update part number: {original_part} → {main_search_updated_part}")
                        print(f"✅ All subsequent searches should use: {main_search_updated_part}")
                    else:
                        print(f"❌ Main search would NOT update part number")
                        
            else:
                print(f"❌ Part number was NOT updated")
        else:
            print("❌ No distributor result")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    # Restore original functions
    app.try_digikey_api_fallback = original_digikey_api
    app.show_alternate_part_acceptance_dialog = original_dialog
    
    root.destroy()
    print("\n🐛 TEST COMPLETED")

if __name__ == "__main__":
    test_real_alternative_scenario()
