#!/usr/bin/env python3
"""
Stripped Component Finder - GUI Dialogs Only for Testing
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog, font
import pandas as pd
import os
from pathlib import Path

class ComponentFinderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Component Finder - GUI Test")
        self.root.geometry("1400x900")

        print(f"\n🏠 DEBUG: ComponentFinderGUI initialized")
        print(f"🏠 DEBUG: Initial window geometry: 1400x900")

        # Add window position tracking
        self.root.bind('<Configure>', self._on_window_configure)

        # Visual Studio Forms colors
        self.vs_colors = {
            'form_bg': '#f0f0f0',           # Light gray form background
            'control_bg': '#ffffff',        # White control background  
            'button_bg': '#e1e1e1',         # Light gray button background
            'text_color': '#000000',        # Black text
            'group_bg': '#f0f0f0',          # Group box background
            'accent_blue': '#007acc'        # Blue accent color for selections
        }

        # Create test Excel data
        self.excel_df = pd.DataFrame({
            'Manufacturer': ['Texas Instruments', 'Murata', 'Analog Devices'],
            'Part Number': ['LM358P', 'GCM155R71H104KE02D', 'AD8065'],
            'Description': ['Op Amp', 'Capacitor', 'Op Amp']
        })

        # Initialize variables
        self.manufacturer_var = tk.StringVar()
        self.part_number_var = tk.StringVar()
        self.show_alternative_dialog = tk.BooleanVar(value=True)

        self.setup_gui()

    def _on_window_configure(self, event):
        """Track main window position changes"""
        if event.widget == self.root:
            try:
                x = self.root.winfo_rootx()
                y = self.root.winfo_rooty()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                print(f"🏠 DEBUG: Main window moved/resized to ({x}, {y}) size {width}x{height}")
            except:
                pass

    def setup_gui(self):
        """Setup the GUI"""
        self.root.configure(bg=self.vs_colors['form_bg'])
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.vs_colors['form_bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = tk.Label(main_frame, text="GUI Dialog Tester", 
                              font=('Arial', 16, 'bold'), 
                              bg=self.vs_colors['form_bg'], 
                              fg=self.vs_colors['text_color'])
        title_label.pack(pady=10)

        # Test buttons frame
        button_frame = tk.Frame(main_frame, bg=self.vs_colors['form_bg'])
        button_frame.pack(pady=20)

        # Test buttons
        tests = [
            ("Test Excel Column Mapping", self.show_column_mapping_dialog),
            ("Test Part Number Correction", self.test_part_correction),
            ("Test Alternative Parts", self.test_alternative_parts),
            ("Test Manual Entry", self.show_manual_entry_dialog),
            ("Test Error Dialog", self.test_error),
            ("Test Info Dialog", self.test_info),
            ("Test File Dialog", self.test_file_dialog),
        ]

        for i, (text, command) in enumerate(tests):
            btn = tk.Button(button_frame, text=text, command=command,
                           font=('Arial', 10), width=25, height=2,
                           bg=self.vs_colors['button_bg'])
            btn.pack(pady=5)

        # Input fields for testing
        input_frame = tk.Frame(main_frame, bg=self.vs_colors['form_bg'])
        input_frame.pack(pady=20)

        tk.Label(input_frame, text="Manufacturer:", bg=self.vs_colors['form_bg']).pack()
        tk.Entry(input_frame, textvariable=self.manufacturer_var, width=30).pack(pady=5)

        tk.Label(input_frame, text="Part Number:", bg=self.vs_colors['form_bg']).pack()
        tk.Entry(input_frame, textvariable=self.part_number_var, width=30).pack(pady=5)

    def _position_dialog_on_main_window(self, dialog, width, height):
        """Position dialog centered on main window for multi-monitor support"""
        print(f"\n🔧 DEBUG: _position_dialog_on_main_window called for dialog: {dialog.title()}")
        print(f"🔧 DEBUG: Requested dialog size: {width}x{height}")
        
        try:
            # Simple approach: use the main window as reference
            dialog.transient(self.root)
            dialog.grab_set()
            print(f"🔧 DEBUG: Set transient and grab_set")
            
            # Wait for dialog to be mapped
            dialog.update_idletasks()
            self.root.update_idletasks()
            print(f"🔧 DEBUG: Updated idle tasks")
            
            # Get main window position
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()
            
            print(f"🔧 DEBUG: Main window position: ({main_x}, {main_y})")
            print(f"🔧 DEBUG: Main window size: {main_width}x{main_height}")
            
            # Calculate center position
            x = main_x + (main_width - width) // 2
            y = main_y + (main_height - height) // 2
            
            print(f"🔧 DEBUG: Calculated dialog position: ({x}, {y})")
            
            # Set geometry with explicit position
            geometry_str = f"{width}x{height}+{x}+{y}"
            dialog.geometry(geometry_str)
            print(f"🔧 DEBUG: Set dialog geometry: {geometry_str}")
            
            # Bring to front
            dialog.lift()
            dialog.focus_force()
            print(f"🔧 DEBUG: Dialog lifted and focused")
            
            # Verify final position
            dialog.update_idletasks()
            final_x = dialog.winfo_rootx()
            final_y = dialog.winfo_rooty()
            print(f"🔧 DEBUG: Final dialog position: ({final_x}, {final_y})")
            
        except Exception as e:
            print(f"❌ DEBUG: Dialog positioning failed: {e}")
            # Fallback: center on screen
            dialog.geometry(f"{width}x{height}")
            print(f"🔧 DEBUG: Used fallback geometry: {width}x{height}")

    def _show_messagebox_on_main(self, msg_type, title, message, **kwargs):
        """Show messagebox positioned over main window using parent parameter"""
        print(f"\n📢 DEBUG: _show_messagebox_on_main called")
        print(f"📢 DEBUG: Message type: {msg_type}")
        print(f"📢 DEBUG: Title: {title}")
        print(f"📢 DEBUG: Message: {message[:100]}...")
        
        # Get main window position for debugging
        try:
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()
            print(f"📢 DEBUG: Main window at ({main_x}, {main_y}) size {main_width}x{main_height}")
        except Exception as e:
            print(f"📢 DEBUG: Could not get main window position: {e}")
        
        print(f"📢 DEBUG: Using parent=self.root for messagebox positioning")
        
        # Use the main window as parent - this should position correctly
        if msg_type == 'info':
            result = messagebox.showinfo(title, message, parent=self.root, **kwargs)
        elif msg_type == 'error':
            result = messagebox.showerror(title, message, parent=self.root, **kwargs)
        elif msg_type == 'warning':
            result = messagebox.showwarning(title, message, parent=self.root, **kwargs)
        elif msg_type == 'yesno':
            result = messagebox.askyesno(title, message, parent=self.root, **kwargs)
        elif msg_type == 'yesnocancel':
            result = messagebox.askyesnocancel(title, message, parent=self.root, **kwargs)
        else:
            result = messagebox.showinfo(title, message, parent=self.root, **kwargs)
            
        print(f"📢 DEBUG: Messagebox result: {result}")
        return result

    def _show_askstring_on_main(self, title, prompt, **kwargs):
        """Show askstring dialog positioned over main window"""
        print(f"\n💬 DEBUG: _show_askstring_on_main called")
        print(f"💬 DEBUG: Title: {title}")
        print(f"💬 DEBUG: Prompt: {prompt}")
        
        # Get main window position for debugging
        try:
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()
            print(f"💬 DEBUG: Main window at ({main_x}, {main_y}) size {main_width}x{main_height}")
        except Exception as e:
            print(f"💬 DEBUG: Could not get main window position: {e}")
        
        print(f"💬 DEBUG: Using parent=self.root for askstring positioning")
        
        result = simpledialog.askstring(title, prompt, parent=self.root, **kwargs)
        print(f"💬 DEBUG: Askstring result: {result}")
        return result

    def add_comment(self, text):
        """Dummy add_comment method"""
        print(f"COMMENT: {text}")

    # Test methods from the actual Component Finder
    def show_column_mapping_dialog(self):
        """Show dialog to map Excel columns to data types"""
        print(f"\n📊 DEBUG: show_column_mapping_dialog called")

        try:
            # Create dialog window
            dialog = tk.Toplevel(self.root)
            dialog.title("📊 Column Mapping")
            dialog.geometry("600x400")
            dialog.transient(self.root)
            dialog.grab_set()
            print(f"📊 DEBUG: Created column mapping dialog")

            # Position dialog on main window
            print(f"📊 DEBUG: Calling _position_dialog_on_main_window for column mapping")
            self._position_dialog_on_main_window(dialog, 600, 400)

            # Main frame
            main_frame = ttk.Frame(dialog, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title
            title_label = ttk.Label(main_frame, text="📊 Map Excel Columns to Data Types",
                                   font=('TkDefaultFont', 12, 'bold'))
            title_label.pack(pady=(0, 10))

            # Instructions
            instr_text = ("Select which columns in your Excel file contain each type of data.\n"
                         "Only Manufacturer and Part Number are required.")
            instr_label = ttk.Label(main_frame, text=instr_text, justify=tk.CENTER)
            instr_label.pack(pady=(0, 15))

            # Get column names
            columns = ['(None)'] + list(self.excel_df.columns)

            # Mapping frame
            mapping_frame = ttk.LabelFrame(main_frame, text="Column Mapping", padding="10")
            mapping_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # Variables to store selections
            mapping_vars = {}

            # Required fields
            required_fields = [
                ('manufacturer', 'Manufacturer (Required)', 'Company that makes the part'),
                ('part_number', 'Part Number (Required)', 'The specific part number to search for'),
            ]

            # Optional fields
            optional_fields = [
                ('description', 'Description (Optional)', 'Part description or notes'),
                ('package', 'Package (Optional)', 'Physical package type (e.g., SOIC, QFN)'),
                ('value', 'Value (Optional)', 'Component value (e.g., 10uF, 1kΩ)'),
            ]

            all_fields = required_fields + optional_fields

            for field_key, field_label, field_desc in all_fields:
                field_frame = ttk.Frame(mapping_frame)
                field_frame.pack(fill=tk.X, pady=2)

                # Label
                label = ttk.Label(field_frame, text=field_label, width=25, anchor='w')
                label.pack(side=tk.LEFT, padx=(0, 10))

                # Combobox
                var = tk.StringVar(value='(None)')
                mapping_vars[field_key] = var
                combo = ttk.Combobox(field_frame, textvariable=var, values=columns, 
                                   state='readonly', width=20)
                combo.pack(side=tk.LEFT, padx=(0, 10))

                # Auto-select if column name matches
                for col in columns:
                    if col.lower().replace(' ', '').replace('_', '') == field_key.replace('_', ''):
                        var.set(col)
                        break

                # Description
                desc_label = ttk.Label(field_frame, text=field_desc, foreground='gray')
                desc_label.pack(side=tk.LEFT)

            # Button frame
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            result = [None]

            def on_ok():
                # Validate required fields
                manufacturer_col = mapping_vars['manufacturer'].get()
                part_number_col = mapping_vars['part_number'].get()

                if manufacturer_col == '(None)' or part_number_col == '(None)':
                    self._show_messagebox_on_main("error", "Missing Required Fields",
                        "Please select columns for both Manufacturer and Part Number.")
                    return

                # Build mapping result
                mapping = {}
                for field_key, var in mapping_vars.items():
                    col_name = var.get()
                    if col_name != '(None)':
                        mapping[field_key] = col_name

                result[0] = mapping
                dialog.destroy()

            def on_cancel():
                result[0] = None
                dialog.destroy()

            ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT)

            # Wait for dialog to close
            dialog.wait_window()

            return result[0]

        except Exception as e:
            self.add_comment(f"❌ Error in column mapping dialog: {str(e)}")
            self._show_messagebox_on_main("error", "Error", f"Column mapping error:\n{str(e)}")
            return None

    def show_part_number_correction_dialog(self, manufacturer, part_number, pdf_file):
        """Show dialog to let user correct part number or continue."""
        print(f"\n⚠️ DEBUG: show_part_number_correction_dialog called")
        print(f"⚠️ DEBUG: Manufacturer: {manufacturer}, Part: {part_number}")
        print(f"⚠️ DEBUG: PDF File: {pdf_file}")

        try:
            # Check if alternative dialog is disabled
            if hasattr(self, 'show_alternative_dialog') and not self.show_alternative_dialog.get():
                print(f"⚠️ DEBUG: Alternative dialog disabled - returning original part")
                self.add_comment(f"⚠️ Part number correction dialog disabled - continuing with original part: {part_number}")
                return part_number

            # Normalize manufacturer name for display (fix National Semiconductor -> Texas Instruments)
            display_manufacturer = manufacturer
            if manufacturer.lower() in ['national semiconductor', 'national']:
                display_manufacturer = 'Texas Instruments'
                print(f"⚠️ DEBUG: Normalized manufacturer: {display_manufacturer}")

            # Create Visual Studio Forms styled dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("Part Number Not Found")
            dialog.geometry("500x350")
            dialog.resizable(False, False)
            dialog.configure(bg=self.vs_colors['form_bg'])
            dialog.transient(self.root)
            dialog.grab_set()
            print(f"⚠️ DEBUG: Created 'Part Number Not Found' dialog")

            # Position dialog using our positioning method
            print(f"⚠️ DEBUG: Calling _position_dialog_on_main_window for part number correction")
            self._position_dialog_on_main_window(dialog, 500, 350)

            # Main frame
            main_frame = tk.Frame(dialog, bg=self.vs_colors['form_bg'], padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title
            title_label = tk.Label(main_frame, text="⚠️ Part Number Not Found",
                                 font=('Arial', 14, 'bold'),
                                 bg=self.vs_colors['form_bg'], fg='#d32f2f')
            title_label.pack(pady=(0, 15))

            # Info text
            info_text = f"Could not find part number '{part_number}' for {display_manufacturer}."
            info_label = tk.Label(main_frame, text=info_text,
                                bg=self.vs_colors['form_bg'],
                                wraplength=450, justify=tk.LEFT)
            info_label.pack(pady=(0, 15))

            # Entry frame
            entry_frame = tk.Frame(main_frame, bg=self.vs_colors['form_bg'])
            entry_frame.pack(fill=tk.X, pady=(0, 15))

            tk.Label(entry_frame, text="Corrected Part Number:",
                    bg=self.vs_colors['form_bg']).pack(anchor='w')

            corrected_var = tk.StringVar(value=part_number)
            entry = tk.Entry(entry_frame, textvariable=corrected_var,
                           font=('Arial', 11), width=40)
            entry.pack(fill=tk.X, pady=(5, 0))
            entry.select_range(0, tk.END)
            entry.focus()

            # Button frame
            button_frame = tk.Frame(main_frame, bg=self.vs_colors['form_bg'])
            button_frame.pack(fill=tk.X, pady=(15, 0))

            result = [None]

            def on_use_corrected():
                corrected = corrected_var.get().strip()
                if corrected:
                    result[0] = corrected
                    self.add_comment(f"✏️ Using corrected part number: {part_number} → {corrected}")
                    dialog.destroy()

            def on_use_original():
                result[0] = part_number
                self.add_comment(f"➡️ Continuing with original part number: {part_number}")
                dialog.destroy()

            def on_skip():
                result[0] = None
                self.add_comment(f"⏭️ Skipping part: {part_number}")
                dialog.destroy()

            # Buttons
            tk.Button(button_frame, text="Use Corrected", command=on_use_corrected,
                     bg=self.vs_colors['accent_blue'], fg='white',
                     font=('Arial', 10), width=12).pack(side=tk.LEFT, padx=(0, 5))

            tk.Button(button_frame, text="Use Original", command=on_use_original,
                     bg=self.vs_colors['button_bg'], font=('Arial', 10),
                     width=12).pack(side=tk.LEFT, padx=5)

            tk.Button(button_frame, text="Skip Part", command=on_skip,
                     bg=self.vs_colors['button_bg'], font=('Arial', 10),
                     width=12).pack(side=tk.RIGHT)

            # Bind Enter key
            entry.bind('<Return>', lambda e: on_use_corrected())
            dialog.bind('<Escape>', lambda e: on_use_original())

            # Wait for dialog to close
            dialog.wait_window()

            return result[0]

        except Exception as e:
            print(f"❌ Error in part number correction dialog: {str(e)}")
            self.add_comment(f"❌ Error in part number correction dialog: {str(e)}")
            return part_number

    def test_part_correction(self):
        """Test the part number correction dialog"""
        manufacturer = self.manufacturer_var.get() or "Texas Instruments"
        part_number = self.part_number_var.get() or "TEST123"
        result = self.show_part_number_correction_dialog(manufacturer, part_number, "test.pdf")
        print(f"Part correction result: {result}")

    def test_alternative_parts(self):
        """Test alternative parts dialog"""
        manufacturer = self.manufacturer_var.get() or "Texas Instruments"
        part_number = self.part_number_var.get() or "TEST123"

        alternates = ["TEST123-ALT", "TEST123-V2", "TEST123A"]
        alternate_list = "\n".join([f"  • {alt}" for alt in alternates[:5]])

        print(f"🔄 DEBUG: Showing alternate part numbers dialog")
        response = self._show_messagebox_on_main("yesno",
            "Alternate Part Numbers Found",
            f"Original part number '{part_number}' not found for {manufacturer}.\n\n"
            f"Found these alternate part numbers:\n{alternate_list}\n\n"
            f"Would you like to accept one of these alternates?\n\n"
            f"YES = Select an alternate\n"
            f"NO = Continue with original part number"
        )
        print(f"Alternative parts result: {response}")

    def show_manual_entry_dialog(self):
        """Test manual entry dialog"""
        print(f"\n📝 DEBUG: show_manual_entry_dialog called")

        try:
            dialog = tk.Toplevel(self.root)
            dialog.title("📝 Manual Entry")
            dialog.geometry("600x400")
            dialog.configure(bg=self.vs_colors['form_bg'])
            dialog.transient(self.root)
            dialog.grab_set()
            print(f"📝 DEBUG: Created manual entry dialog")

            print(f"📝 DEBUG: Calling _position_dialog_on_main_window for manual entry")
            self._position_dialog_on_main_window(dialog, 600, 400)

            # Main frame
            main_frame = tk.Frame(dialog, bg=self.vs_colors['form_bg'], padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title
            title_label = tk.Label(main_frame, text="📝 Manual File Entry",
                                 font=('Arial', 14, 'bold'),
                                 bg=self.vs_colors['form_bg'])
            title_label.pack(pady=(0, 15))

            # Info
            info_label = tk.Label(main_frame, text="Manually specify file information:",
                                bg=self.vs_colors['form_bg'])
            info_label.pack(pady=(0, 15))

            # Entry fields
            filename_var = tk.StringVar()
            tk.Label(main_frame, text="Filename:", bg=self.vs_colors['form_bg']).pack(anchor='w')
            tk.Entry(main_frame, textvariable=filename_var, width=50).pack(fill=tk.X, pady=(5, 15))

            # Button frame
            button_frame = tk.Frame(main_frame, bg=self.vs_colors['form_bg'])
            button_frame.pack(fill=tk.X, pady=(15, 0))

            result = [None]

            def on_ok():
                result[0] = filename_var.get()
                dialog.destroy()

            def on_cancel():
                result[0] = None
                dialog.destroy()

            tk.Button(button_frame, text="OK", command=on_ok,
                     bg=self.vs_colors['accent_blue'], fg='white',
                     font=('Arial', 10), width=12).pack(side=tk.RIGHT, padx=(5, 0))

            tk.Button(button_frame, text="Cancel", command=on_cancel,
                     bg=self.vs_colors['button_bg'], font=('Arial', 10),
                     width=12).pack(side=tk.RIGHT)

            # Wait for dialog to close
            dialog.wait_window()

            return result[0]

        except Exception as e:
            print(f"❌ Error in manual entry dialog: {str(e)}")
            self._show_messagebox_on_main("error", "Error", f"Manual entry dialog error:\n{str(e)}")
            return None

    def test_error(self):
        """Test error dialog"""
        self._show_messagebox_on_main("error", "Test Error", "This is a test error message")

    def test_info(self):
        """Test info dialog"""
        self._show_messagebox_on_main("info", "Test Info", "This is a test info message")

    def test_file_dialog(self):
        """Test file dialog"""
        print(f"\n📁 DEBUG: Testing file dialog")
        try:
            main_x = self.root.winfo_rootx()
            main_y = self.root.winfo_rooty()
            print(f"📁 DEBUG: Main window at ({main_x}, {main_y})")
        except:
            pass

        result = filedialog.askopenfilename(parent=self.root, title="Test File Dialog")
        print(f"📁 DEBUG: File dialog result: {result}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ComponentFinderGUI(root)
    root.mainloop()
