#!/usr/bin/env python3
"""
Fix Dialog Positioning - Extract and Test Dialog Positioning Code
This script isolates the dialog positioning problem so we can fix it properly
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
import time

class DialogPositioningTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Dialog Positioning Test - Move me to secondary monitor")
        self.root.geometry("500x400+100+100")
        
        # Create test interface
        self.create_test_interface()
    
    def create_test_interface(self):
        """Create test buttons for different dialog types"""
        
        tk.Label(self.root, text="Dialog Positioning Test", 
                font=('Arial', 16, 'bold')).pack(pady=20)
        
        tk.Label(self.root, text="1. Move this window to your secondary monitor\n"
                                "2. Click buttons to test where dialogs appear\n"
                                "3. Dialogs should appear on SAME screen as this window",
                justify=tk.CENTER).pack(pady=10)
        
        # Test buttons
        tk.Button(self.root, text="Test Simple Messagebox", 
                 command=self.test_simple_messagebox, width=25).pack(pady=5)
        
        tk.Button(self.root, text="Test Positioned Messagebox", 
                 command=self.test_positioned_messagebox, width=25).pack(pady=5)
        
        tk.Button(self.root, text="Test Custom Dialog", 
                 command=self.test_custom_dialog, width=25).pack(pady=5)
        
        tk.Button(self.root, text="Test Alternate Part Dialog", 
                 command=self.test_alternate_dialog, width=25).pack(pady=5)
        
        tk.Button(self.root, text="Quit", command=self.root.quit, width=25).pack(pady=20)
        
        # Status display
        self.status_text = tk.Text(self.root, height=8, width=60)
        self.status_text.pack(pady=10, fill=tk.BOTH, expand=True)
        
        self.log("Dialog Positioning Test Ready")
        self.log("Move this window to secondary monitor and test dialogs")
    
    def log(self, message):
        """Add message to status display"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def get_main_window_info(self):
        """Get main window position and size"""
        self.root.update_idletasks()
        x = self.root.winfo_rootx()
        y = self.root.winfo_rooty()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        return x, y, width, height
    
    def test_simple_messagebox(self):
        """Test simple messagebox with parent"""
        self.log("=== Testing Simple Messagebox ===")
        x, y, width, height = self.get_main_window_info()
        self.log(f"Main window at: ({x}, {y}) size {width}x{height}")
        
        # Simple messagebox with parent
        result = messagebox.showinfo("Simple Test", 
                                   "This is a simple messagebox with parent=self.root", 
                                   parent=self.root)
        self.log(f"Simple messagebox result: {result}")
    
    def test_positioned_messagebox(self):
        """Test messagebox with positioning trick"""
        self.log("=== Testing Positioned Messagebox ===")
        x, y, width, height = self.get_main_window_info()
        self.log(f"Main window at: ({x}, {y}) size {width}x{height}")
        
        try:
            # Create temporary invisible window positioned on main window's screen
            temp_window = tk.Toplevel(self.root)
            temp_window.withdraw()  # Make it invisible
            temp_x = x + width // 2
            temp_y = y + height // 2
            temp_window.geometry(f"1x1+{temp_x}+{temp_y}")
            temp_window.transient(self.root)
            temp_window.update_idletasks()
            
            self.log(f"Created temp window at: ({temp_x}, {temp_y})")
            
            # Use temp window as parent
            result = messagebox.showinfo("Positioned Test", 
                                       "This messagebox uses a positioned temporary window as parent", 
                                       parent=temp_window)
            
            # Clean up
            temp_window.destroy()
            self.log(f"Positioned messagebox result: {result}")
            
        except Exception as e:
            self.log(f"Error in positioned messagebox: {e}")
    
    def test_custom_dialog(self):
        """Test custom dialog with explicit positioning"""
        self.log("=== Testing Custom Dialog ===")
        x, y, width, height = self.get_main_window_info()
        self.log(f"Main window at: ({x}, {y}) size {width}x{height}")
        
        # Create custom dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Custom Dialog Test")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Calculate center position
        dialog_width = 300
        dialog_height = 200
        dialog_x = x + (width - dialog_width) // 2
        dialog_y = y + (height - dialog_height) // 2
        
        # Set position explicitly
        dialog.geometry(f"{dialog_width}x{dialog_height}+{dialog_x}+{dialog_y}")
        
        self.log(f"Custom dialog positioned at: ({dialog_x}, {dialog_y})")
        
        # Add content
        tk.Label(dialog, text="Custom Dialog\nPositioned relative to main window", 
                justify=tk.CENTER).pack(expand=True)
        tk.Button(dialog, text="Close", command=dialog.destroy).pack(pady=10)
        
        # Check actual position
        dialog.update_idletasks()
        actual_x = dialog.winfo_rootx()
        actual_y = dialog.winfo_rooty()
        self.log(f"Actual dialog position: ({actual_x}, {actual_y})")
    
    def test_alternate_dialog(self):
        """Test alternate part selection dialog"""
        self.log("=== Testing Alternate Part Dialog ===")
        x, y, width, height = self.get_main_window_info()
        self.log(f"Main window at: ({x}, {y}) size {width}x{height}")
        
        # Simulate alternate part selection
        alternates = ["PART-001-ALT", "PART-002-ALT", "PART-003-ALT"]
        original_part = "ORIGINAL-PART-123"
        
        # First show yes/no dialog
        response = messagebox.askyesno("Alternate Parts Found",
                                     f"Original part '{original_part}' not found.\n\n"
                                     f"Found these alternates:\n"
                                     f"  • {alternates[0]}\n"
                                     f"  • {alternates[1]}\n"
                                     f"  • {alternates[2]}\n\n"
                                     f"Accept an alternate?\n\n"
                                     f"YES = Select alternate\n"
                                     f"NO = Continue with original",
                                     parent=self.root)
        
        self.log(f"User response to alternate question: {response}")
        
        if response:  # YES - show selection dialog
            # Create selection dialog
            choice_dialog = tk.Toplevel(self.root)
            choice_dialog.title("Select Alternate Part")
            choice_dialog.geometry("400x300")
            choice_dialog.transient(self.root)
            choice_dialog.grab_set()
            
            # Position dialog
            dialog_width = 400
            dialog_height = 300
            dialog_x = x + (width - dialog_width) // 2
            dialog_y = y + (height - dialog_height) // 2
            choice_dialog.geometry(f"{dialog_width}x{dialog_height}+{dialog_x}+{dialog_y}")
            
            self.log(f"Selection dialog positioned at: ({dialog_x}, {dialog_y})")
            
            selected_part = tk.StringVar()
            
            # Title
            tk.Label(choice_dialog, text=f"Select Alternate for {original_part}", 
                    font=('Arial', 12, 'bold')).pack(pady=10)
            
            # Listbox
            listbox_frame = tk.Frame(choice_dialog)
            listbox_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
            
            listbox = tk.Listbox(listbox_frame)
            scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
            listbox.config(yscrollcommand=scrollbar.set)
            scrollbar.config(command=listbox.yview)
            
            for alt in alternates:
                listbox.insert(tk.END, alt)
            
            listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Buttons
            button_frame = tk.Frame(choice_dialog)
            button_frame.pack(pady=10)
            
            def on_select():
                selection = listbox.curselection()
                if selection:
                    selected_part.set(alternates[selection[0]])
                    self.log(f"User selected: {selected_part.get()}")
                    choice_dialog.destroy()
                else:
                    self.log("No selection made")
            
            def on_cancel():
                selected_part.set("")
                self.log("User cancelled selection")
                choice_dialog.destroy()
            
            tk.Button(button_frame, text="Select", command=on_select).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)
            
            # Wait for dialog
            choice_dialog.wait_window()
            
            chosen_part = selected_part.get()
            if chosen_part:
                self.log(f"SUCCESS: Selected alternate: {original_part} → {chosen_part}")
            else:
                self.log(f"CANCELLED: Continuing with original: {original_part}")
        else:
            self.log("User chose to continue with original part")
    
    def run(self):
        """Run the test"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🔧 DIALOG POSITIONING FIX TEST")
    print("=" * 50)
    print("This script tests dialog positioning to fix the screen following issue")
    print("1. Move the test window to your secondary monitor")
    print("2. Test each dialog type")
    print("3. Check if dialogs appear on same screen as main window")
    print("")
    
    test = DialogPositioningTest()
    test.run()

if __name__ == "__main__":
    main()
