ISO-10303-21;
HEADER;
FILE_DESCRIPTION (( 'STEP AP214' ),
    '1' );
FILE_NAME ('ESD441DPLR.STEP',
    '2024-11-04T04:34:08',
    ( '' ),
    ( '' ),
    'SwSTEP 2.0',
    'SolidWorks 2023',
    '' );
FILE_SCHEMA (( 'AUTOMOTIVE_DESIGN' ));
ENDSEC;

DATA;
#1 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#2 = PRESENTATION_STYLE_ASSIGNMENT (( #509 ) ) ;
#3 = EDGE_LOOP ( 'NONE', ( #594, #604, #779, #634 ) ) ;
#4 = PRESENTATION_STYLE_ASSIGNMENT (( #390 ) ) ;
#5 = SURFACE_STYLE_FILL_AREA ( #532 ) ;
#6 = PRESENTATION_STYLE_ASSIGNMENT (( #716 ) ) ;
#7 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#8 = VECTOR ( 'NONE', #567, 1000.000000000000000 ) ;
#9 = ORIENTED_EDGE ( 'NONE', *, *, #244, .F. ) ;
#10 = STYLED_ITEM ( 'NONE', ( #616 ), #628 ) ;
#11 = STYLED_ITEM ( 'NONE', ( #140 ), #787 ) ;
#12 = LINE ( 'NONE', #549, #690 ) ;
#13 = VERTEX_POINT ( 'NONE', #627 ) ;
#14 = FILL_AREA_STYLE_COLOUR ( '', #542 ) ;
#15 = SURFACE_STYLE_FILL_AREA ( #74 ) ;
#16 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', 'automotive_design', 1998, #626 ) ;
#17 = EDGE_CURVE ( 'NONE', #769, #581, #183, .T. ) ;
#18 = PRESENTATION_STYLE_ASSIGNMENT (( #598 ) ) ;
#19 = ORIENTED_EDGE ( 'NONE', *, *, #398, .T. ) ;
#20 = FILL_AREA_STYLE_COLOUR ( '', #327 ) ;
#21 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#22 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#23 = ORIENTED_EDGE ( 'NONE', *, *, #267, .T. ) ;
#24 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #556 ) ) ;
#25 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#26 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#27 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, -0.1249999999999999722 ) ) ;
#28 = PRODUCT_DEFINITION ( 'UNKNOWN', '', #263, #116 ) ;
#29 = VERTEX_POINT ( 'NONE', #45 ) ;
#30 = EDGE_CURVE ( 'NONE', #534, #283, #239, .T. ) ;
#31 = LINE ( 'NONE', #552, #395 ) ;
#32 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #159, 'distance_accuracy_value', 'NONE');
#33 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#34 = EDGE_LOOP ( 'NONE', ( #57, #378, #269, #611 ) ) ;
#35 = LINE ( 'NONE', #797, #436 ) ;
#36 = SURFACE_STYLE_FILL_AREA ( #363 ) ;
#37 = VERTEX_POINT ( 'NONE', #491 ) ;
#38 = LINE ( 'NONE', #305, #816 ) ;
#39 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#40 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#41 = LINE ( 'NONE', #504, #672 ) ;
#42 = FACE_OUTER_BOUND ( 'NONE', #479, .T. ) ;
#43 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #123, 'distance_accuracy_value', 'NONE');
#44 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#45 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.05000000000000000278, -0.1250000000000000000 ) ) ;
#46 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, -1.524659305057740610E-17, 0.07500000000000001110 ) ) ;
#47 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #475 ) ) ;
#48 = FACE_OUTER_BOUND ( 'NONE', #725, .T. ) ;
#49 = ADVANCED_FACE ( 'NONE', ( #42 ), #308, .F. ) ;
#50 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #752, 'distance_accuracy_value', 'NONE');
#51 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#52 = MANIFOLD_SOLID_BREP ( 'Boss-Extrude1[2]', #121 ) ;
#53 = FACE_OUTER_BOUND ( 'NONE', #406, .T. ) ;
#54 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#55 = SURFACE_STYLE_USAGE ( .BOTH. , #389 ) ;
#56 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#57 = ORIENTED_EDGE ( 'NONE', *, *, #30, .T. ) ;
#58 = FILL_AREA_STYLE_COLOUR ( '', #806 ) ;
#59 = EDGE_CURVE ( 'NONE', #550, #510, #374, .T. ) ;
#60 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#61 = PLANE ( 'NONE',  #396 ) ;
#62 = AXIS2_PLACEMENT_3D ( 'NONE', #426, #362, #802 ) ;
#63 = ORIENTED_EDGE ( 'NONE', *, *, #513, .F. ) ;
#64 = LINE ( 'NONE', #582, #179 ) ;
#65 = CARTESIAN_POINT ( 'NONE',  ( -0.1350000000000000366, -7.623296525288703052E-18, 0.1250000000000000000 ) ) ;
#66 = ORIENTED_EDGE ( 'NONE', *, *, #546, .T. ) ;
#67 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#68 = SURFACE_SIDE_STYLE ('',( #402 ) ) ;
#69 = SURFACE_SIDE_STYLE ('',( #713 ) ) ;
#70 = SURFACE_STYLE_FILL_AREA ( #768 ) ;
#71 = ORIENTED_EDGE ( 'NONE', *, *, #412, .F. ) ;
#72 = SURFACE_SIDE_STYLE ('',( #584 ) ) ;
#73 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 9.035018104045870694E-17 ) ) ;
#74 = FILL_AREA_STYLE ('',( #528 ) ) ;
#75 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#76 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#77 = ORIENTED_EDGE ( 'NONE', *, *, #30, .F. ) ;
#78 = VECTOR ( 'NONE', #668, 1000.000000000000000 ) ;
#79 = ADVANCED_FACE ( 'NONE', ( #178 ), #434, .F. ) ;
#80 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #453 ) ) ;
#81 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#82 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#83 = ORIENTED_EDGE ( 'NONE', *, *, #204, .F. ) ;
#84 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#85 = ORIENTED_EDGE ( 'NONE', *, *, #398, .F. ) ;
#86 = DIRECTION ( 'NONE',  ( -0.7071067811865474617, 0.000000000000000000, 0.7071067811865474617 ) ) ;
#87 = FILL_AREA_STYLE_COLOUR ( '', #409 ) ;
#88 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#89 = ADVANCED_FACE ( 'NONE', ( #48 ), #697, .T. ) ;
#90 = ORIENTED_EDGE ( 'NONE', *, *, #739, .T. ) ;
#91 = STYLED_ITEM ( 'NONE', ( #563 ), #52 ) ;
#92 = STYLED_ITEM ( 'NONE', ( #726 ), #498 ) ;
#93 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 1.310820393249936711, 0.1499999999999999944 ) ) ;
#94 = AXIS2_PLACEMENT_3D ( 'NONE', #148, #219, #81 ) ;
#95 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #247 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #56, #824, #517 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#96 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.05000000000000000278, 0.07500000000000001110 ) ) ;
#97 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #560 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #377, #620, #314 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#98 = ORIENTED_EDGE ( 'NONE', *, *, #771, .T. ) ;
#99 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 1.310820393249936711, -0.1499999999999999389 ) ) ;
#100 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#101 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #447, 'distance_accuracy_value', 'NONE');
#102 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#103 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#104 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#105 = PRESENTATION_STYLE_ASSIGNMENT (( #339 ) ) ;
#106 = FILL_AREA_STYLE_COLOUR ( '', #753 ) ;
#107 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#108 = PRESENTATION_STYLE_ASSIGNMENT (( #748 ) ) ;
#109 = EDGE_CURVE ( 'NONE', #638, #340, #596, .T. ) ;
#110 = SURFACE_STYLE_FILL_AREA ( #433 ) ;
#111 = FILL_AREA_STYLE ('',( #245 ) ) ;
#112 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#113 = PRESENTATION_STYLE_ASSIGNMENT (( #256 ) ) ;
#114 = SURFACE_STYLE_USAGE ( .BOTH. , #364 ) ;
#115 = FACE_OUTER_BOUND ( 'NONE', #583, .T. ) ;
#116 = PRODUCT_DEFINITION_CONTEXT ( 'detailed design', #626, 'design' ) ;
#117 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 9.035018104045870694E-17 ) ) ;
#118 = EDGE_CURVE ( 'NONE', #576, #368, #64, .T. ) ;
#119 = SURFACE_SIDE_STYLE ('',( #756 ) ) ;
#120 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.05000000000000000278, -0.1250000000000000278 ) ) ;
#121 = CLOSED_SHELL ( 'NONE', ( #49, #665, #79, #814, #132, #589 ) ) ;
#122 = STYLED_ITEM ( 'NONE', ( #442 ), #324 ) ;
#123 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#124 = STYLED_ITEM ( 'NONE', ( #4 ), #618 ) ;
#125 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#126 = PLANE ( 'NONE',  #492 ) ;
#127 = EDGE_CURVE ( 'NONE', #261, #523, #222, .T. ) ;
#128 = ORIENTED_EDGE ( 'NONE', *, *, #484, .T. ) ;
#129 = SURFACE_STYLE_FILL_AREA ( #196 ) ;
#130 = VERTEX_POINT ( 'NONE', #275 ) ;
#131 = FILL_AREA_STYLE ('',( #337 ) ) ;
#132 = ADVANCED_FACE ( 'NONE', ( #520 ), #772, .F. ) ;
#133 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#134 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#135 = SURFACE_STYLE_FILL_AREA ( #271 ) ;
#136 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #399 ) ) ;
#137 = ORIENTED_EDGE ( 'NONE', *, *, #174, .F. ) ;
#138 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#139 = FILL_AREA_STYLE ('',( #712 ) ) ;
#140 = PRESENTATION_STYLE_ASSIGNMENT (( #465 ) ) ;
#141 = VERTEX_POINT ( 'NONE', #21 ) ;
#142 = LINE ( 'NONE', #591, #265 ) ;
#143 = PRESENTATION_STYLE_ASSIGNMENT (( #286 ) ) ;
#144 = EDGE_CURVE ( 'NONE', #769, #469, #306, .T. ) ;
#145 = ORIENTED_EDGE ( 'NONE', *, *, #185, .T. ) ;
#146 = PRESENTATION_STYLE_ASSIGNMENT (( #708 ) ) ;
#147 = PRESENTATION_STYLE_ASSIGNMENT (( #413 ) ) ;
#148 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#149 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #101 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #447, #285, #650 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#150 = ORIENTED_EDGE ( 'NONE', *, *, #771, .F. ) ;
#151 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #11 ), #701 ) ;
#152 = AXIS2_PLACEMENT_3D ( 'NONE', #595, #649, #73 ) ;
#153 = EDGE_CURVE ( 'NONE', #534, #638, #497, .T. ) ;
#154 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #785 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #75, #7, #529 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#155 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #625 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #637, #714, #335 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#156 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#157 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#158 = ORIENTED_EDGE ( 'NONE', *, *, #311, .T. ) ;
#159 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#160 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, -0.1250000000000000278 ) ) ;
#161 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#162 = ORIENTED_EDGE ( 'NONE', *, *, #361, .T. ) ;
#163 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.000000000000000000, -0.1249999999999999722 ) ) ;
#164 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#165 = ORIENTED_EDGE ( 'NONE', *, *, #59, .T. ) ;
#166 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #553 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #125, #397, #157 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#167 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #371 ), #830 ) ;
#168 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.04999999999999993339, 0.1500000000000000222 ) ) ;
#169 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #10 ) ) ;
#170 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#171 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#172 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#173 = LINE ( 'NONE', #671, #501 ) ;
#174 = EDGE_CURVE ( 'NONE', #576, #469, #607, .T. ) ;
#175 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#176 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.000000000000000000, 0.1250000000000000278 ) ) ;
#177 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#178 = FACE_OUTER_BOUND ( 'NONE', #832, .T. ) ;
#179 = VECTOR ( 'NONE', #653, 1000.000000000000000 ) ;
#180 = VECTOR ( 'NONE', #86, 1000.000000000000114 ) ;
#181 = VECTOR ( 'NONE', #289, 1000.000000000000000 ) ;
#182 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #609 ), #155 ) ;
#183 = LINE ( 'NONE', #431, #775 ) ;
#184 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#185 = EDGE_CURVE ( 'NONE', #130, #211, #745, .T. ) ;
#186 = PLANE ( 'NONE',  #592 ) ;
#187 = EDGE_LOOP ( 'NONE', ( #531, #66, #676, #351 ) ) ;
#188 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#189 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#190 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#191 = ORIENTED_EDGE ( 'NONE', *, *, #144, .T. ) ;
#192 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 0.3199999999999999512, 0.1500000000000000500 ) ) ;
#193 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #556 ), #149 ) ;
#194 = AXIS2_PLACEMENT_3D ( 'NONE', #248, #44, #177 ) ;
#195 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #92 ) ) ;
#196 = FILL_AREA_STYLE ('',( #760 ) ) ;
#197 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #358 ) ) ;
#198 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #578 ), #624 ) ;
#199 = EDGE_LOOP ( 'NONE', ( #230, #191, #137, #796 ) ) ;
#200 = ORIENTED_EDGE ( 'NONE', *, *, #472, .F. ) ;
#201 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#202 = SURFACE_STYLE_USAGE ( .BOTH. , #386 ) ;
#203 = ORIENTED_EDGE ( 'NONE', *, *, #109, .T. ) ;
#204 = EDGE_CURVE ( 'NONE', #510, #130, #173, .T. ) ;
#205 = FACE_OUTER_BOUND ( 'NONE', #518, .T. ) ;
#206 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#207 = FILL_AREA_STYLE_COLOUR ( '', #450 ) ;
#208 = VECTOR ( 'NONE', #175, 1000.000000000000000 ) ;
#209 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#210 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #578 ) ) ;
#211 = VERTEX_POINT ( 'NONE', #430 ) ;
#212 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#213 = SURFACE_STYLE_USAGE ( .BOTH. , #217 ) ;
#214 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', 'automotive_design', 1998, #795 ) ;
#215 = EDGE_CURVE ( 'NONE', #638, #729, #427, .T. ) ;
#216 = EDGE_LOOP ( 'NONE', ( #767, #404, #287, #483 ) ) ;
#217 = SURFACE_SIDE_STYLE ('',( #5 ) ) ;
#218 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -1.505836350674311988E-16 ) ) ;
#219 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#220 = LINE ( 'NONE', #291, #805 ) ;
#221 = ORIENTED_EDGE ( 'NONE', *, *, #561, .F. ) ;
#222 = LINE ( 'NONE', #758, #721 ) ;
#223 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, -0.1249999999999999722 ) ) ;
#224 = FACE_OUTER_BOUND ( 'NONE', #401, .T. ) ;
#225 = ORIENTED_EDGE ( 'NONE', *, *, #561, .T. ) ;
#226 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #826 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #241, #617, #741 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#227 = EDGE_CURVE ( 'NONE', #261, #545, #666, .T. ) ;
#228 = SURFACE_SIDE_STYLE ('',( #536 ) ) ;
#229 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#230 = ORIENTED_EDGE ( 'NONE', *, *, #17, .F. ) ;
#231 = FACE_OUTER_BOUND ( 'NONE', #459, .T. ) ;
#232 = LINE ( 'NONE', #485, #557 ) ;
#233 = FACE_OUTER_BOUND ( 'NONE', #643, .T. ) ;
#234 = EDGE_LOOP ( 'NONE', ( #325, #466, #98, #225 ) ) ;
#235 = PLANE ( 'NONE',  #62 ) ;
#236 = EDGE_CURVE ( 'NONE', #729, #37, #538, .T. ) ;
#237 = FILL_AREA_STYLE_COLOUR ( '', #134 ) ;
#238 = FILL_AREA_STYLE ('',( #106 ) ) ;
#239 = LINE ( 'NONE', #811, #8 ) ;
#240 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #343 ), #588 ) ;
#241 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#242 = DIRECTION ( 'NONE',  ( -3.011672701348623976E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#243 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#244 = EDGE_CURVE ( 'NONE', #13, #764, #612, .T. ) ;
#245 = FILL_AREA_STYLE_COLOUR ( '', #460 ) ;
#246 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#247 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #56, 'distance_accuracy_value', 'NONE');
#248 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#249 = SURFACE_SIDE_STYLE ('',( #110 ) ) ;
#250 = FILL_AREA_STYLE_COLOUR ( '', #251 ) ;
#251 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#252 = LINE ( 'NONE', #695, #280 ) ;
#253 = EDGE_CURVE ( 'NONE', #283, #510, #296, .T. ) ;
#254 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #122 ) ) ;
#255 = AXIS2_PLACEMENT_3D ( 'NONE', #332, #519, #835 ) ;
#256 = SURFACE_STYLE_USAGE ( .BOTH. , #249 ) ;
#257 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#258 = PLANE ( 'NONE',  #761 ) ;
#259 = LINE ( 'NONE', #192, #777 ) ;
#260 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #774, 'distance_accuracy_value', 'NONE');
#261 = VERTEX_POINT ( 'NONE', #793 ) ;
#262 = VECTOR ( 'NONE', #784, 1000.000000000000000 ) ;
#263 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE ( 'ANY', '', #365, .NOT_KNOWN. ) ;
#264 = VECTOR ( 'NONE', #104, 1000.000000000000000 ) ;
#265 = VECTOR ( 'NONE', #405, 1000.000000000000114 ) ;
#266 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#267 = EDGE_CURVE ( 'NONE', #211, #550, #232, .T. ) ;
#268 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #621 ) ) ;
#269 = ORIENTED_EDGE ( 'NONE', *, *, #109, .F. ) ;
#270 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #481, 'distance_accuracy_value', 'NONE');
#271 = FILL_AREA_STYLE ('',( #833 ) ) ;
#272 = ORIENTED_EDGE ( 'NONE', *, *, #455, .T. ) ;
#273 = FILL_AREA_STYLE ('',( #657 ) ) ;
#274 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#275 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, -0.1250000000000000000 ) ) ;
#276 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#277 = ORIENTED_EDGE ( 'NONE', *, *, #484, .F. ) ;
#278 = SURFACE_SIDE_STYLE ('',( #515 ) ) ;
#279 = VECTOR ( 'NONE', #605, 1000.000000000000000 ) ;
#280 = VECTOR ( 'NONE', #184, 1000.000000000000000 ) ;
#281 = PRODUCT_DEFINITION_SHAPE ( 'NONE', 'NONE',  #28 ) ;
#282 = LINE ( 'NONE', #88, #778 ) ;
#283 = VERTEX_POINT ( 'NONE', #96 ) ;
#284 = FILL_AREA_STYLE ('',( #14 ) ) ;
#285 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#286 = SURFACE_STYLE_USAGE ( .BOTH. , #468 ) ;
#287 = ORIENTED_EDGE ( 'NONE', *, *, #674, .T. ) ;
#288 = FILL_AREA_STYLE ('',( #20 ) ) ;
#289 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 2.085004177856739808E-16 ) ) ;
#290 = STYLED_ITEM ( 'NONE', ( #105 ), #132 ) ;
#291 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, -0.1250000000000000278 ) ) ;
#292 = VECTOR ( 'NONE', #164, 1000.000000000000000 ) ;
#293 = EDGE_LOOP ( 'NONE', ( #9, #356, #331, #344 ) ) ;
#294 = STYLED_ITEM ( 'NONE', ( #18 ), #693 ) ;
#295 = LINE ( 'NONE', #163, #688 ) ;
#296 = LINE ( 'NONE', #548, #292 ) ;
#297 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#298 = ORIENTED_EDGE ( 'NONE', *, *, #692, .F. ) ;
#299 = LINE ( 'NONE', #99, #408 ) ;
#300 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, -0.1250000000000000000 ) ) ;
#301 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#302 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, -0.1250000000000000000 ) ) ;
#303 = SURFACE_SIDE_STYLE ('',( #36 ) ) ;
#304 = VECTOR ( 'NONE', #547, 1000.000000000000000 ) ;
#305 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.000000000000000000, 0.1250000000000000278 ) ) ;
#306 = LINE ( 'NONE', #809, #539 ) ;
#307 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 0.04999999999999993339, -0.1499999999999999667 ) ) ;
#308 = PLANE ( 'NONE',  #789 ) ;
#309 = LINE ( 'NONE', #320, #208 ) ;
#310 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #655, 'distance_accuracy_value', 'NONE');
#311 = EDGE_CURVE ( 'NONE', #457, #13, #31, .T. ) ;
#312 = FILL_AREA_STYLE_COLOUR ( '', #40 ) ;
#313 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#314 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#315 = FILL_AREA_STYLE ('',( #250 ) ) ;
#316 = AXIS2_PLACEMENT_3D ( 'NONE', #190, #780, #218 ) ;
#317 = VECTOR ( 'NONE', #449, 1000.000000000000000 ) ;
#318 = STYLED_ITEM ( 'NONE', ( #143 ), #665 ) ;
#319 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#320 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.3199999999999999512, 0.1499999999999999944 ) ) ;
#321 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#322 = ORIENTED_EDGE ( 'NONE', *, *, #373, .F. ) ;
#323 = SURFACE_SIDE_STYLE ('',( #770 ) ) ;
#324 = ADVANCED_FACE ( 'NONE', ( #420 ), #799, .F. ) ;
#325 = ORIENTED_EDGE ( 'NONE', *, *, #227, .T. ) ;
#326 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #270 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #481, #735, #488 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#327 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#328 = SURFACE_SIDE_STYLE ('',( #446 ) ) ;
#329 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #294 ) ) ;
#330 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #621 ), #97 ) ;
#331 = ORIENTED_EDGE ( 'NONE', *, *, #546, .F. ) ;
#332 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 1.310820393249936711, -0.1499999999999999944 ) ) ;
#333 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#334 = PRESENTATION_STYLE_ASSIGNMENT (( #516 ) ) ;
#335 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#336 = MANIFOLD_SOLID_BREP ( 'Boss-Extrude2', #642 ) ;
#337 = FILL_AREA_STYLE_COLOUR ( '', #763 ) ;
#338 = CARTESIAN_POINT ( 'NONE',  ( -0.1350000000000000366, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#339 = SURFACE_STYLE_USAGE ( .BOTH. , #68 ) ;
#340 = VERTEX_POINT ( 'NONE', #338 ) ;
#341 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#342 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#343 = STYLED_ITEM ( 'NONE', ( #680 ), #49 ) ;
#344 = ORIENTED_EDGE ( 'NONE', *, *, #739, .F. ) ;
#345 = VECTOR ( 'NONE', #117, 1000.000000000000000 ) ;
#346 = FILL_AREA_STYLE ('',( #689 ) ) ;
#347 = SURFACE_STYLE_FILL_AREA ( #353 ) ;
#348 = ORIENTED_EDGE ( 'NONE', *, *, #506, .T. ) ;
#349 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#350 = ADVANCED_FACE ( 'NONE', ( #205 ), #461, .F. ) ;
#351 = ORIENTED_EDGE ( 'NONE', *, *, #227, .F. ) ;
#352 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#353 = FILL_AREA_STYLE ('',( #87 ) ) ;
#354 = CLOSED_SHELL ( 'NONE', ( #89, #498, #639, #747, #663, #810, #707 ) ) ;
#355 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #704 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #572, #67, #341 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#356 = ORIENTED_EDGE ( 'NONE', *, *, #311, .F. ) ;
#357 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #124 ), #478 ) ;
#358 = STYLED_ITEM ( 'NONE', ( #647 ), #810 ) ;
#359 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#360 = EDGE_LOOP ( 'NONE', ( #162, #272, #717, #776 ) ) ;
#361 = EDGE_CURVE ( 'NONE', #368, #473, #38, .T. ) ;
#362 = DIRECTION ( 'NONE',  ( 3.011672701348623976E-16, -0.000000000000000000, 1.000000000000000000 ) ) ;
#363 = FILL_AREA_STYLE ('',( #237 ) ) ;
#364 = SURFACE_SIDE_STYLE ('',( #740 ) ) ;
#365 = PRODUCT ( 'ESD441DPLR', 'ESD441DPLR', '', ( #601 ) ) ;
#366 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#367 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #629, 'distance_accuracy_value', 'NONE');
#368 = VERTEX_POINT ( 'NONE', #493 ) ;
#369 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #290 ) ) ;
#370 = STYLED_ITEM ( 'NONE', ( #732 ), #814 ) ;
#371 = STYLED_ITEM ( 'NONE', ( #146 ), #336 ) ;
#372 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#373 = EDGE_CURVE ( 'NONE', #457, #545, #35, .T. ) ;
#374 = LINE ( 'NONE', #746, #524 ) ;
#375 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.000000000000000000, 0.1250000000000000000 ) ) ;
#376 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #794, 'distance_accuracy_value', 'NONE');
#377 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#378 = ORIENTED_EDGE ( 'NONE', *, *, #834, .F. ) ;
#379 = PLANE ( 'NONE',  #255 ) ;
#380 = SURFACE_STYLE_USAGE ( .BOTH. , #119 ) ;
#381 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#382 = SURFACE_SIDE_STYLE ('',( #754 ) ) ;
#383 = STYLED_ITEM ( 'NONE', ( #108 ), #589 ) ;
#384 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #370 ), #541 ) ;
#385 = VECTOR ( 'NONE', #800, 1000.000000000000000 ) ;
#386 = SURFACE_SIDE_STYLE ('',( #70 ) ) ;
#387 = ORIENTED_EDGE ( 'NONE', *, *, #603, .F. ) ;
#388 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.3199999999999999512, 0.1500000000000000222 ) ) ;
#389 = SURFACE_SIDE_STYLE ('',( #129 ) ) ;
#390 = SURFACE_STYLE_USAGE ( .BOTH. , #72 ) ;
#391 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#392 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#393 = ORIENTED_EDGE ( 'NONE', *, *, #506, .F. ) ;
#394 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#395 = VECTOR ( 'NONE', #359, 1000.000000000000000 ) ;
#396 = AXIS2_PLACEMENT_3D ( 'NONE', #781, #593, #76 ) ;
#397 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#398 = EDGE_CURVE ( 'NONE', #764, #613, #299, .T. ) ;
#399 = STYLED_ITEM ( 'NONE', ( #738 ), #707 ) ;
#400 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#401 = EDGE_LOOP ( 'NONE', ( #23, #424, #662, #587, #599 ) ) ;
#402 = SURFACE_STYLE_FILL_AREA ( #139 ) ;
#403 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#404 = ORIENTED_EDGE ( 'NONE', *, *, #692, .T. ) ;
#405 = DIRECTION ( 'NONE',  ( 0.7071067811865474617, 0.000000000000000000, -0.7071067811865474617 ) ) ;
#406 = EDGE_LOOP ( 'NONE', ( #165, #699, #77, #792 ) ) ;
#407 = PLANE ( 'NONE',  #632 ) ;
#408 = VECTOR ( 'NONE', #84, 1000.000000000000000 ) ;
#409 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#410 = ORIENTED_EDGE ( 'NONE', *, *, #253, .T. ) ;
#411 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.000000000000000000, 0.1250000000000000000 ) ) ;
#412 = EDGE_CURVE ( 'NONE', #729, #211, #295, .T. ) ;
#413 = SURFACE_STYLE_USAGE ( .BOTH. , #718 ) ;
#414 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#415 = ORIENTED_EDGE ( 'NONE', *, *, #185, .F. ) ;
#416 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.3199999999999999512, 0.1499999999999999944 ) ) ;
#417 = SHAPE_DEFINITION_REPRESENTATION ( #281, #730 ) ;
#418 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#419 = ORIENTED_EDGE ( 'NONE', *, *, #236, .T. ) ;
#420 = FACE_OUTER_BOUND ( 'NONE', #187, .T. ) ;
#421 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#422 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #50 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #752, #807, #744 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#423 = VECTOR ( 'NONE', #445, 1000.000000000000000 ) ;
#424 = ORIENTED_EDGE ( 'NONE', *, *, #495, .T. ) ;
#425 = AXIS2_PLACEMENT_3D ( 'NONE', #564, #257, #815 ) ;
#426 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, -0.1249999999999999722 ) ) ;
#427 = LINE ( 'NONE', #176, #181 ) ;
#428 = ORIENTED_EDGE ( 'NONE', *, *, #244, .T. ) ;
#429 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#430 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.000000000000000000, -0.1250000000000000000 ) ) ;
#431 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#432 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.000000000000000000, 0.1250000000000000000 ) ) ;
#433 = FILL_AREA_STYLE ('',( #312 ) ) ;
#434 = PLANE ( 'NONE',  #316 ) ;
#435 = AXIS2_PLACEMENT_3D ( 'NONE', #160, #242, #727 ) ;
#436 = VECTOR ( 'NONE', #172, 1000.000000000000000 ) ;
#437 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#438 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #310 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #655, #33, #602 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#439 = FACE_OUTER_BOUND ( 'NONE', #34, .T. ) ;
#440 = ORIENTED_EDGE ( 'NONE', *, *, #574, .F. ) ;
#441 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 0.3199999999999999512, -0.1499999999999999667 ) ) ;
#442 = PRESENTATION_STYLE_ASSIGNMENT (( #555 ) ) ;
#443 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.3199999999999999512, -0.1499999999999999944 ) ) ;
#444 = ORIENTED_EDGE ( 'NONE', *, *, #603, .T. ) ;
#445 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#446 = SURFACE_STYLE_FILL_AREA ( #521 ) ;
#447 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#448 = FILL_AREA_STYLE ('',( #530 ) ) ;
#449 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#450 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#451 = VECTOR ( 'NONE', #429, 1000.000000000000000 ) ;
#452 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#453 = STYLED_ITEM ( 'NONE', ( #334 ), #798 ) ;
#454 = SURFACE_STYLE_FILL_AREA ( #448 ) ;
#455 = EDGE_CURVE ( 'NONE', #473, #581, #474, .T. ) ;
#456 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#457 = VERTEX_POINT ( 'NONE', #168 ) ;
#458 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#459 = EDGE_LOOP ( 'NONE', ( #415, #83, #514, #705 ) ) ;
#460 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#461 = PLANE ( 'NONE',  #502 ) ;
#462 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#463 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #370 ) ) ;
#464 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #609 ) ) ;
#465 = SURFACE_STYLE_USAGE ( .BOTH. , #69 ) ;
#466 = ORIENTED_EDGE ( 'NONE', *, *, #513, .T. ) ;
#467 = SURFACE_STYLE_USAGE ( .BOTH. , #323 ) ;
#468 = SURFACE_SIDE_STYLE ('',( #135 ) ) ;
#469 = VERTEX_POINT ( 'NONE', #302 ) ;
#470 = PLANE ( 'NONE',  #623 ) ;
#471 = SURFACE_STYLE_FILL_AREA ( #554 ) ;
#472 = EDGE_CURVE ( 'NONE', #29, #141, #790, .T. ) ;
#473 = VERTEX_POINT ( 'NONE', #551 ) ;
#474 = LINE ( 'NONE', #411, #304 ) ;
#475 = STYLED_ITEM ( 'NONE', ( #6 ), #79 ) ;
#476 = AXIS2_PLACEMENT_3D ( 'NONE', #223, #669, #654 ) ;
#477 = SURFACE_SIDE_STYLE ('',( #471 ) ) ;
#478 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #494 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #706, #212, #394 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#479 = EDGE_LOOP ( 'NONE', ( #715, #200, #298, #733 ) ) ;
#480 = SURFACE_STYLE_USAGE ( .BOTH. , #228 ) ;
#481 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#482 = FACE_OUTER_BOUND ( 'NONE', #3, .T. ) ;
#483 = ORIENTED_EDGE ( 'NONE', *, *, #118, .F. ) ;
#484 = EDGE_CURVE ( 'NONE', #130, #37, #664, .T. ) ;
#485 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.000000000000000000, -0.1249999999999999722 ) ) ;
#486 = ORIENTED_EDGE ( 'NONE', *, *, #17, .T. ) ;
#487 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#488 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#489 = SURFACE_SIDE_STYLE ('',( #751 ) ) ;
#490 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #383 ) ) ;
#491 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#492 = AXIS2_PLACEMENT_3D ( 'NONE', #321, #709, #512 ) ;
#493 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.000000000000000000, -0.1250000000000000000 ) ) ;
#494 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #706, 'distance_accuracy_value', 'NONE');
#495 = EDGE_CURVE ( 'NONE', #550, #534, #734, .T. ) ;
#496 = CARTESIAN_POINT ( 'NONE',  ( -0.1350000000000000089, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#497 = LINE ( 'NONE', #742, #180 ) ;
#498 = ADVANCED_FACE ( 'NONE', ( #231 ), #235, .F. ) ;
#499 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 0.04999999999999993339, 0.1500000000000000500 ) ) ;
#500 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#501 = VECTOR ( 'NONE', #170, 1000.000000000000000 ) ;
#502 = AXIS2_PLACEMENT_3D ( 'NONE', #93, #783, #25 ) ;
#503 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #683 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #675, #301, #786 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#504 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 1.310820393249936711, -0.1499999999999999944 ) ) ;
#505 = ORIENTED_EDGE ( 'NONE', *, *, #236, .F. ) ;
#506 = EDGE_CURVE ( 'NONE', #13, #782, #41, .T. ) ;
#507 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, 0.1500000000000000500 ) ) ;
#508 = PLANE ( 'NONE',  #476 ) ;
#509 = SURFACE_STYLE_USAGE ( .BOTH. , #522 ) ;
#510 = VERTEX_POINT ( 'NONE', #120 ) ;
#511 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #43 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #123, #456, #51 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#512 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 2.085004177856739808E-16 ) ) ;
#513 = EDGE_CURVE ( 'NONE', #545, #782, #309, .T. ) ;
#514 = ORIENTED_EDGE ( 'NONE', *, *, #59, .F. ) ;
#515 = SURFACE_STYLE_FILL_AREA ( #284 ) ;
#516 = SURFACE_STYLE_USAGE ( .BOTH. , #328 ) ;
#517 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#518 = EDGE_LOOP ( 'NONE', ( #322, #158, #348, #63 ) ) ;
#519 = DIRECTION ( 'NONE',  ( 9.035018104045870694E-17, -0.000000000000000000, 1.000000000000000000 ) ) ;
#520 = FACE_OUTER_BOUND ( 'NONE', #216, .T. ) ;
#521 = FILL_AREA_STYLE ('',( #58 ) ) ;
#522 = SURFACE_SIDE_STYLE ('',( #454 ) ) ;
#523 = VERTEX_POINT ( 'NONE', #499 ) ;
#524 = VECTOR ( 'NONE', #171, 1000.000000000000000 ) ;
#525 = PRESENTATION_STYLE_ASSIGNMENT (( #213 ) ) ;
#526 = VECTOR ( 'NONE', #100, 1000.000000000000000 ) ;
#527 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #318 ) ) ;
#528 = FILL_AREA_STYLE_COLOUR ( '', #381 ) ;
#529 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#530 = FILL_AREA_STYLE_COLOUR ( '', #568 ) ;
#531 = ORIENTED_EDGE ( 'NONE', *, *, #127, .T. ) ;
#532 = FILL_AREA_STYLE ('',( #585 ) ) ;
#533 = EDGE_CURVE ( 'NONE', #581, #576, #700, .T. ) ;
#534 = VERTEX_POINT ( 'NONE', #46 ) ;
#535 = EDGE_LOOP ( 'NONE', ( #419, #277, #145, #71 ) ) ;
#536 = SURFACE_STYLE_FILL_AREA ( #288 ) ;
#537 = VECTOR ( 'NONE', #246, 1000.000000000000000 ) ;
#538 = LINE ( 'NONE', #366, #78 ) ;
#539 = VECTOR ( 'NONE', #352, 1000.000000000000000 ) ;
#540 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#541 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #376 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #794, #414, #22 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#542 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#543 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 1.310820393249936711, 0.1500000000000000500 ) ) ;
#544 = AXIS2_PLACEMENT_3D ( 'NONE', #543, #161, #82 ) ;
#545 = VERTEX_POINT ( 'NONE', #388 ) ;
#546 = EDGE_CURVE ( 'NONE', #523, #457, #569, .T. ) ;
#547 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -1.505836350674311988E-16 ) ) ;
#548 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#549 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.05000000000000000278, -0.1249999999999999722 ) ) ;
#550 = VERTEX_POINT ( 'NONE', #635 ) ;
#551 = CARTESIAN_POINT ( 'NONE',  ( 0.2650000000000000688, 0.000000000000000000, 0.1250000000000000278 ) ) ;
#552 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#553 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #125, 'distance_accuracy_value', 'NONE');
#554 = FILL_AREA_STYLE ('',( #728 ) ) ;
#555 = SURFACE_STYLE_USAGE ( .BOTH. , #303 ) ;
#556 = STYLED_ITEM ( 'NONE', ( #147 ), #89 ) ;
#557 = VECTOR ( 'NONE', #803, 1000.000000000000000 ) ;
#558 = PLANE ( 'NONE',  #425 ) ;
#559 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#560 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #377, 'distance_accuracy_value', 'NONE');
#561 = EDGE_CURVE ( 'NONE', #613, #261, #259, .T. ) ;
#562 = FILL_AREA_STYLE ('',( #808 ) ) ;
#563 = PRESENTATION_STYLE_ASSIGNMENT (( #380 ) ) ;
#564 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#565 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #827, 'distance_accuracy_value', 'NONE');
#566 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #91 ) ) ;
#567 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#568 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#569 = LINE ( 'NONE', #507, #659 ) ;
#570 = CARTESIAN_POINT ( 'NONE',  ( -0.1350000000000000089, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#571 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.000000000000000000, -0.1250000000000000000 ) ) ;
#572 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#573 = FILL_AREA_STYLE ('',( #207 ) ) ;
#574 = EDGE_CURVE ( 'NONE', #141, #473, #822, .T. ) ;
#575 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.05000000000000000278, -0.1250000000000000278 ) ) ;
#576 = VERTEX_POINT ( 'NONE', #571 ) ;
#577 = STYLED_ITEM ( 'NONE', ( #525 ), #350 ) ;
#578 = STYLED_ITEM ( 'NONE', ( #648 ), #663 ) ;
#579 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #103, 'distance_accuracy_value', 'NONE');
#580 = PLANE ( 'NONE',  #658 ) ;
#581 = VERTEX_POINT ( 'NONE', #375 ) ;
#582 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.000000000000000000, -0.1250000000000000278 ) ) ;
#583 = EDGE_LOOP ( 'NONE', ( #393, #428, #19, #150 ) ) ;
#584 = SURFACE_STYLE_FILL_AREA ( #131 ) ;
#585 = FILL_AREA_STYLE_COLOUR ( '', #462 ) ;
#586 = SURFACE_SIDE_STYLE ('',( #821 ) ) ;
#587 = ORIENTED_EDGE ( 'NONE', *, *, #215, .T. ) ;
#588 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #579 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #103, #615, #107 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#589 = ADVANCED_FACE ( 'NONE', ( #819 ), #558, .T. ) ;
#590 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #383 ), #804 ) ;
#591 = CARTESIAN_POINT ( 'NONE',  ( -0.1350000000000000089, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#592 = AXIS2_PLACEMENT_3D ( 'NONE', #496, #743, #684 ) ;
#593 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#594 = ORIENTED_EDGE ( 'NONE', *, *, #674, .F. ) ;
#595 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 1.310820393249936711, 0.1499999999999999944 ) ) ;
#596 = LINE ( 'NONE', #570, #262 ) ;
#597 = SURFACE_STYLE_USAGE ( .BOTH. , #673 ) ;
#598 = SURFACE_STYLE_USAGE ( .BOTH. , #477 ) ;
#599 = ORIENTED_EDGE ( 'NONE', *, *, #412, .T. ) ;
#600 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.000000000000000000, -0.1250000000000000278 ) ) ;
#601 = PRODUCT_CONTEXT ( 'NONE', #795, 'mechanical' ) ;
#602 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#603 = EDGE_CURVE ( 'NONE', #37, #340, #252, .T. ) ;
#604 = ORIENTED_EDGE ( 'NONE', *, *, #472, .T. ) ;
#605 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#606 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #475 ), #166 ) ;
#607 = LINE ( 'NONE', #300, #537 ) ;
#608 = VECTOR ( 'NONE', #682, 1000.000000000000000 ) ;
#609 = STYLED_ITEM ( 'NONE', ( #2 ), #639 ) ;
#610 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #11 ) ) ;
#611 = ORIENTED_EDGE ( 'NONE', *, *, #153, .F. ) ;
#612 = LINE ( 'NONE', #678, #526 ) ;
#613 = VERTEX_POINT ( 'NONE', #441 ) ;
#614 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #812, 'distance_accuracy_value', 'NONE');
#615 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#616 = PRESENTATION_STYLE_ASSIGNMENT (( #467 ) ) ;
#617 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#618 = ADVANCED_FACE ( 'NONE', ( #115 ), #379, .F. ) ;
#619 = AXIS2_PLACEMENT_3D ( 'NONE', #319, #333, #1 ) ;
#620 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#621 = STYLED_ITEM ( 'NONE', ( #113 ), #747 ) ;
#622 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #92 ), #791 ) ;
#623 = AXIS2_PLACEMENT_3D ( 'NONE', #540, #297, #421 ) ;
#624 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #260 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #774, #633, #209 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#625 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #637, 'distance_accuracy_value', 'NONE');
#626 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#627 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.04999999999999993339, -0.1499999999999999944 ) ) ;
#628 = MANIFOLD_SOLID_BREP ( 'Boss-Extrude1[1]', #354 ) ;
#629 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#630 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #343 ) ) ;
#631 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#632 = AXIS2_PLACEMENT_3D ( 'NONE', #575, #400, #403 ) ;
#633 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#634 = ORIENTED_EDGE ( 'NONE', *, *, #361, .F. ) ;
#635 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.000000000000000000, -0.1250000000000000278 ) ) ;
#636 = FACE_OUTER_BOUND ( 'NONE', #293, .T. ) ;
#637 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#638 = VERTEX_POINT ( 'NONE', #65 ) ;
#639 = ADVANCED_FACE ( 'NONE', ( #53 ), #407, .F. ) ;
#640 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#641 = FACE_OUTER_BOUND ( 'NONE', #199, .T. ) ;
#642 = CLOSED_SHELL ( 'NONE', ( #798, #693, #618, #350, #324, #787 ) ) ;
#643 = EDGE_LOOP ( 'NONE', ( #85, #90, #788, #221 ) ) ;
#644 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #577 ) ) ;
#645 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #122 ), #355 ) ;
#646 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #294 ), #503 ) ;
#647 = PRESENTATION_STYLE_ASSIGNMENT (( #55 ) ) ;
#648 = PRESENTATION_STYLE_ASSIGNMENT (( #202 ) ) ;
#649 = DIRECTION ( 'NONE',  ( -9.035018104045870694E-17, 0.000000000000000000, -1.000000000000000000 ) ) ;
#650 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#651 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 0.3199999999999999512, -0.1499999999999999944 ) ) ;
#652 = FACE_OUTER_BOUND ( 'NONE', #656, .T. ) ;
#653 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 3.011672701348623976E-16 ) ) ;
#654 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#655 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#656 = EDGE_LOOP ( 'NONE', ( #203, #387, #505, #703 ) ) ;
#657 = FILL_AREA_STYLE_COLOUR ( '', #660 ) ;
#658 = AXIS2_PLACEMENT_3D ( 'NONE', #458, #201, #823 ) ;
#659 = VECTOR ( 'NONE', #188, 1000.000000000000000 ) ;
#660 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#661 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#662 = ORIENTED_EDGE ( 'NONE', *, *, #153, .T. ) ;
#663 = ADVANCED_FACE ( 'NONE', ( #652 ), #126, .F. ) ;
#664 = LINE ( 'NONE', #349, #279 ) ;
#665 = ADVANCED_FACE ( 'NONE', ( #641 ), #61, .F. ) ;
#666 = LINE ( 'NONE', #416, #608 ) ;
#667 = ORIENTED_EDGE ( 'NONE', *, *, #681, .T. ) ;
#668 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#669 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#670 = ORIENTED_EDGE ( 'NONE', *, *, #204, .T. ) ;
#671 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, -0.1250000000000000278 ) ) ;
#672 = VECTOR ( 'NONE', #685, 1000.000000000000000 ) ;
#673 = SURFACE_SIDE_STYLE ('',( #347 ) ) ;
#674 = EDGE_CURVE ( 'NONE', #29, #368, #12, .T. ) ;
#675 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#676 = ORIENTED_EDGE ( 'NONE', *, *, #373, .T. ) ;
#677 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#678 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, -0.1499999999999999944 ) ) ;
#679 = LINE ( 'NONE', #102, #385 ) ;
#680 = PRESENTATION_STYLE_ASSIGNMENT (( #114 ) ) ;
#681 = EDGE_CURVE ( 'NONE', #141, #769, #282, .T. ) ;
#682 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -9.035018104045870694E-17 ) ) ;
#683 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #675, 'distance_accuracy_value', 'NONE');
#684 = DIRECTION ( 'NONE',  ( -0.7071067811865474617, 0.000000000000000000, 0.7071067811865474617 ) ) ;
#685 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#686 = CARTESIAN_POINT ( 'NONE',  ( -0.2650000000000000688, 0.000000000000000000, 0.1250000000000000278 ) ) ;
#687 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #358 ), #829 ) ;
#688 = VECTOR ( 'NONE', #722, 1000.000000000000000 ) ;
#689 = FILL_AREA_STYLE_COLOUR ( '', #206 ) ;
#690 = VECTOR ( 'NONE', #661, 1000.000000000000000 ) ;
#691 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #189, 'distance_accuracy_value', 'NONE');
#692 = EDGE_CURVE ( 'NONE', #469, #29, #220, .T. ) ;
#693 = ADVANCED_FACE ( 'NONE', ( #233 ), #719, .F. ) ;
#694 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#695 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.05000000000000000278, 0.1250000000000000278 ) ) ;
#696 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #91 ), #422 ) ;
#697 = PLANE ( 'NONE',  #619 ) ;
#698 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #399 ), #818 ) ;
#699 = ORIENTED_EDGE ( 'NONE', *, *, #253, .F. ) ;
#700 = LINE ( 'NONE', #432, #317 ) ;
#701 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #691 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #189, #452, #820 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#702 = FACE_OUTER_BOUND ( 'NONE', #535, .T. ) ;
#703 = ORIENTED_EDGE ( 'NONE', *, *, #215, .F. ) ;
#704 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #572, 'distance_accuracy_value', 'NONE');
#705 = ORIENTED_EDGE ( 'NONE', *, *, #267, .F. ) ;
#706 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#707 = ADVANCED_FACE ( 'NONE', ( #224 ), #813, .F. ) ;
#708 = SURFACE_STYLE_USAGE ( .BOTH. , #278 ) ;
#709 = DIRECTION ( 'NONE',  ( -2.085004177856739808E-16, 0.000000000000000000, -1.000000000000000000 ) ) ;
#710 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #124 ) ) ;
#711 = PRODUCT_RELATED_PRODUCT_CATEGORY ( 'part', '', ( #365 ) ) ;
#712 = FILL_AREA_STYLE_COLOUR ( '', #60 ) ;
#713 = SURFACE_STYLE_FILL_AREA ( #573 ) ;
#714 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#715 = ORIENTED_EDGE ( 'NONE', *, *, #681, .F. ) ;
#716 = SURFACE_STYLE_USAGE ( .BOTH. , #586 ) ;
#717 = ORIENTED_EDGE ( 'NONE', *, *, #533, .T. ) ;
#718 = SURFACE_SIDE_STYLE ('',( #15 ) ) ;
#719 = PLANE ( 'NONE',  #544 ) ;
#720 = ORIENTED_EDGE ( 'NONE', *, *, #834, .T. ) ;
#721 = VECTOR ( 'NONE', #342, 1000.000000000000000 ) ;
#722 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#723 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #318 ), #438 ) ;
#724 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #453 ), #511 ) ;
#725 = EDGE_LOOP ( 'NONE', ( #410, #670, #128, #444, #720 ) ) ;
#726 = PRESENTATION_STYLE_ASSIGNMENT (( #480 ) ) ;
#727 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 3.011672701348623976E-16 ) ) ;
#728 = FILL_AREA_STYLE_COLOUR ( '', #737 ) ;
#729 = VERTEX_POINT ( 'NONE', #686 ) ;
#730 = ADVANCED_BREP_SHAPE_REPRESENTATION ( 'ESD441DPLR', ( #52, #628, #336, #94 ), #154 ) ;
#731 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#732 = PRESENTATION_STYLE_ASSIGNMENT (( #597 ) ) ;
#733 = ORIENTED_EDGE ( 'NONE', *, *, #144, .F. ) ;
#734 = LINE ( 'NONE', #600, #451 ) ;
#735 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#736 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#737 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#738 = PRESENTATION_STYLE_ASSIGNMENT (( #755 ) ) ;
#739 = EDGE_CURVE ( 'NONE', #764, #523, #679, .T. ) ;
#740 = SURFACE_STYLE_FILL_AREA ( #315 ) ;
#741 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#742 = CARTESIAN_POINT ( 'NONE',  ( -0.1350000000000000089, -2.710505431213761085E-17, 0.1250000000000000000 ) ) ;
#743 = DIRECTION ( 'NONE',  ( -0.7071067811865475727, 0.000000000000000000, -0.7071067811865475727 ) ) ;
#744 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#745 = LINE ( 'NONE', #27, #749 ) ;
#746 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.05000000000000000278, -0.1250000000000000278 ) ) ;
#747 = ADVANCED_FACE ( 'NONE', ( #439 ), #186, .F. ) ;
#748 = SURFACE_STYLE_USAGE ( .BOTH. , #382 ) ;
#749 = VECTOR ( 'NONE', #559, 1000.000000000000000 ) ;
#750 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #577 ), #226 ) ;
#751 = SURFACE_STYLE_FILL_AREA ( #238 ) ;
#752 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#753 = COLOUR_RGB ( '',0.7529411764705882248, 0.7529411764705882248, 0.7529411764705882248 ) ;
#754 = SURFACE_STYLE_FILL_AREA ( #562 ) ;
#755 = SURFACE_STYLE_USAGE ( .BOTH. , #489 ) ;
#756 = SURFACE_STYLE_FILL_AREA ( #111 ) ;
#757 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.3200000000000000067, 0.000000000000000000 ) ) ;
#758 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 1.310820393249936711, 0.1500000000000000500 ) ) ;
#759 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #391, 'distance_accuracy_value', 'NONE');
#760 = FILL_AREA_STYLE_COLOUR ( '', #138 ) ;
#761 = AXIS2_PLACEMENT_3D ( 'NONE', #757, #640, #276 ) ;
#762 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #371 ) ) ;
#763 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#764 = VERTEX_POINT ( 'NONE', #307 ) ;
#765 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#766 = FILL_AREA_STYLE_COLOUR ( '', #26 ) ;
#767 = ORIENTED_EDGE ( 'NONE', *, *, #174, .T. ) ;
#768 = FILL_AREA_STYLE ('',( #766 ) ) ;
#769 = VERTEX_POINT ( 'NONE', #500 ) ;
#770 = SURFACE_STYLE_FILL_AREA ( #273 ) ;
#771 = EDGE_CURVE ( 'NONE', #782, #613, #817, .T. ) ;
#772 = PLANE ( 'NONE',  #435 ) ;
#773 = FACE_OUTER_BOUND ( 'NONE', #234, .T. ) ;
#774 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#775 = VECTOR ( 'NONE', #313, 1000.000000000000000 ) ;
#776 = ORIENTED_EDGE ( 'NONE', *, *, #118, .T. ) ;
#777 = VECTOR ( 'NONE', #54, 1000.000000000000000 ) ;
#778 = VECTOR ( 'NONE', #418, 1000.000000000000000 ) ;
#779 = ORIENTED_EDGE ( 'NONE', *, *, #574, .T. ) ;
#780 = DIRECTION ( 'NONE',  ( 1.505836350674311988E-16, 0.000000000000000000, -1.000000000000000000 ) ) ;
#781 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, 0.1250000000000000000 ) ) ;
#782 = VERTEX_POINT ( 'NONE', #651 ) ;
#783 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#784 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#785 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #75, 'distance_accuracy_value', 'NONE');
#786 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#787 = ADVANCED_FACE ( 'NONE', ( #773 ), #258, .T. ) ;
#788 = ORIENTED_EDGE ( 'NONE', *, *, #127, .F. ) ;
#789 = AXIS2_PLACEMENT_3D ( 'NONE', #631, #694, #801 ) ;
#790 = LINE ( 'NONE', #156, #264 ) ;
#791 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #32 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #159, #487, #677 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#792 = ORIENTED_EDGE ( 'NONE', *, *, #495, .F. ) ;
#793 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999999334, 0.3199999999999999512, 0.1500000000000000500 ) ) ;
#794 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#795 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#796 = ORIENTED_EDGE ( 'NONE', *, *, #533, .F. ) ;
#797 = CARTESIAN_POINT ( 'NONE',  ( 0.3000000000000000444, 1.310820393249936711, 0.1499999999999999944 ) ) ;
#798 = ADVANCED_FACE ( 'NONE', ( #636 ), #580, .F. ) ;
#799 = PLANE ( 'NONE',  #152 ) ;
#800 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#801 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#802 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -3.011672701348623976E-16 ) ) ;
#803 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -3.011672701348623976E-16 ) ) ;
#804 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #614 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #812, #372, #437 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#805 = VECTOR ( 'NONE', #229, 1000.000000000000000 ) ;
#806 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#807 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#808 = FILL_AREA_STYLE_COLOUR ( '', #736 ) ;
#809 = CARTESIAN_POINT ( 'NONE',  ( 0.08500000000000000611, 0.05000000000000000278, 0.000000000000000000 ) ) ;
#810 = ADVANCED_FACE ( 'NONE', ( #702 ), #508, .F. ) ;
#811 = CARTESIAN_POINT ( 'NONE',  ( -0.08500000000000000611, 0.05000000000000000278, 0.07500000000000001110 ) ) ;
#812 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#813 = PLANE ( 'NONE',  #194 ) ;
#814 = ADVANCED_FACE ( 'NONE', ( #482 ), #470, .F. ) ;
#815 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#816 = VECTOR ( 'NONE', #731, 1000.000000000000000 ) ;
#817 = LINE ( 'NONE', #443, #345 ) ;
#818 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #367 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #629, #39, #243 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#819 = FACE_OUTER_BOUND ( 'NONE', #360, .T. ) ;
#820 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#821 = SURFACE_STYLE_FILL_AREA ( #346 ) ;
#822 = LINE ( 'NONE', #112, #423 ) ;
#823 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#824 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#825 = ORIENTED_EDGE ( 'NONE', *, *, #455, .F. ) ;
#826 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #241, 'distance_accuracy_value', 'NONE');
#827 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#828 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #290 ), #326 ) ;
#829 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #565 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #827, #765, #392 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#830 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #759 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #391, #133, #274 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#831 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #10 ), #95 ) ;
#832 = EDGE_LOOP ( 'NONE', ( #440, #667, #486, #825 ) ) ;
#833 = FILL_AREA_STYLE_COLOUR ( '', #266 ) ;
#834 = EDGE_CURVE ( 'NONE', #340, #283, #142, .T. ) ;
#835 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -9.035018104045870694E-17 ) ) ;
ENDSEC;
END-ISO-10303-21;
