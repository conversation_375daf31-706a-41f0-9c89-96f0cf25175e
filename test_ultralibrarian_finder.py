#!/usr/bin/env python3
"""
Test the actual UltraLibrarian 3D finder
"""

import sys
import os

# Add the save directory to path to import the finder
sys.path.append('save')

try:
    from ultralibrarian_3d_finder import find_3d_model
    
    print("🎯 TESTING ULTRALIBRARIAN 3D FINDER")
    print("=" * 50)
    
    # Test with a common part
    manufacturer = "Texas Instruments"
    part_number = "LM358N"
    
    print(f"🔍 Testing: {manufacturer} {part_number}")
    print("   (This will run in silent mode)")
    
    # Test the finder
    result = find_3d_model(manufacturer, part_number, silent=True)
    
    print("\n" + "=" * 50)
    print("📊 RESULT:")
    
    if result:
        print("✅ UltraLibrarian finder completed successfully")
        print(f"   Result: {result}")
        
        # Check if any files were downloaded
        if os.path.exists('3d'):
            files = os.listdir('3d')
            step_files = [f for f in files if f.endswith('.step')]
            if step_files:
                print(f"✅ Downloaded STEP files: {step_files}")
            else:
                print("⚠️ No STEP files found in 3d directory")
        else:
            print("⚠️ 3d directory not found")
    else:
        print("❌ UltraLibrarian finder failed")
        
except ImportError as e:
    print(f"❌ Could not import UltraLibrarian finder: {e}")
except Exception as e:
    print(f"❌ Error testing UltraLibrarian finder: {e}")
    import traceback
    traceback.print_exc()
