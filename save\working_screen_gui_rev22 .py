#!/usr/bin/env python3
"""
Working Screen GUI - Simple, functional GUI that shows browser screens
This GUI actually works and has a continue button that responds
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

class WorkingScreenGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("3D Model Download Screen Tracker")
        self.root.geometry("1000x700")
        self.root.configure(bg='white')
        
        # State variables
        self.continue_pressed = False
        self.current_screen = 0
        self.current_vendor = ""
        
        # Create GUI elements
        self.create_widgets()
        
        # Make sure window appears on top
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after(100, lambda: self.root.attributes('-topmost', False))
        
    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Title
        title_frame = tk.Frame(self.root, bg='blue', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)
        
        self.title_label = tk.Label(title_frame, text="3D Model Download Screen Tracker", 
                                   font=('Arial', 18, 'bold'), bg='blue', fg='white')
        self.title_label.pack(expand=True)
        
        # Vendor and Screen info
        info_frame = tk.Frame(self.root, bg='white')
        info_frame.pack(fill='x', padx=20, pady=10)
        
        self.vendor_label = tk.Label(info_frame, text="Vendor: Not Started", 
                                    font=('Arial', 14, 'bold'), bg='white', fg='green')
        self.vendor_label.pack(anchor='w')
        
        self.screen_label = tk.Label(info_frame, text="Screen: Not Started", 
                                    font=('Arial', 16, 'bold'), bg='lightblue', 
                                    relief='solid', bd=2, pady=5)
        self.screen_label.pack(fill='x', pady=5)
        
        # Current URL section
        url_frame = tk.Frame(self.root, bg='white')
        url_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(url_frame, text="Current Browser URL:", 
                font=('Arial', 12, 'bold'), bg='white').pack(anchor='w')
        
        self.url_label = tk.Label(url_frame, text="No URL loaded yet", 
                                 font=('Arial', 10), bg='lightyellow', 
                                 relief='sunken', bd=2, anchor='w', pady=5)
        self.url_label.pack(fill='x', pady=2)
        
        # What you should see section
        see_frame = tk.Frame(self.root, bg='white')
        see_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(see_frame, text="What You Should See on This Screen:", 
                font=('Arial', 12, 'bold'), bg='white', fg='red').pack(anchor='w')
        
        # Text area with scrollbar
        text_frame = tk.Frame(see_frame)
        text_frame.pack(fill='both', expand=True, pady=5)
        
        self.see_text = tk.Text(text_frame, height=12, font=('Arial', 11), 
                               bg='lightgreen', relief='sunken', bd=2, wrap='word')
        scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.see_text.yview)
        self.see_text.configure(yscrollcommand=scrollbar.set)
        
        self.see_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Continue button - LARGE and OBVIOUS
        button_frame = tk.Frame(self.root, bg='white')
        button_frame.pack(fill='x', padx=20, pady=20)
        
        self.continue_button = tk.Button(button_frame, text="CONTINUE TO NEXT SCREEN", 
                                        font=('Arial', 18, 'bold'), bg='orange', 
                                        fg='white', command=self.continue_clicked,
                                        relief='raised', bd=5, height=2)
        self.continue_button.pack(fill='x')
        
        # Status bar
        self.status_label = tk.Label(self.root, text="Ready - Waiting for process to start", 
                                    font=('Arial', 10), bg='lightgray', fg='black',
                                    relief='sunken', bd=1)
        self.status_label.pack(fill='x', side='bottom')
        
    def update_screen(self, vendor, screen_num, screen_title, url, what_to_see):
        """Update the GUI with new screen information"""
        self.current_vendor = vendor.upper()
        self.current_screen = screen_num
        self.continue_pressed = False
        
        # Update all display elements
        self.vendor_label.config(text=f"Vendor: {self.current_vendor}")
        self.screen_label.config(text=f"Screen {screen_num}: {screen_title}")
        self.url_label.config(text=url if url else "URL not available")
        
        # Update what to see text
        self.see_text.delete(1.0, tk.END)
        self.see_text.insert(1.0, what_to_see)
        
        # Enable continue button and update status
        self.continue_button.config(state='normal', bg='orange')
        self.status_label.config(text=f"Showing {vendor} Screen {screen_num} - Click CONTINUE when ready", fg='black')
        
        # Force GUI update
        self.root.update_idletasks()
        self.root.update()
        
        # Bring window to front
        self.root.lift()
        
    def continue_clicked(self):
        """Handle continue button click"""
        self.continue_pressed = True
        self.continue_button.config(state='disabled', bg='gray')
        self.status_label.config(text="Continuing to next screen...", fg='blue')
        self.root.update()
        
    def wait_for_continue(self):
        """Wait for user to click continue button"""
        while not self.continue_pressed:
            self.root.update()
            time.sleep(0.1)
        return True
        
    def show_error(self, error_msg):
        """Show error message"""
        self.status_label.config(text=f"ERROR: {error_msg}", fg='red')
        self.root.update()
        
    def run(self):
        """Start the GUI main loop"""
        self.root.mainloop()

# Global GUI instance
_gui_instance = None

def get_working_gui():
    """Get GUI instance, create if needed (no threading)"""
    global _gui_instance

    if _gui_instance is None:
        print("🖥️ Creating GUI...")
        _gui_instance = WorkingScreenGUI()
        print("✅ GUI created and ready")

        # Make sure the GUI window appears
        _gui_instance.root.update()
        _gui_instance.root.deiconify()  # Make sure window is visible
        _gui_instance.root.lift()       # Bring to front
        _gui_instance.root.focus_force() # Give it focus

    return _gui_instance

def start_gui_loop():
    """Start the GUI main loop - call this from main thread"""
    gui = get_working_gui()
    gui.run()

def test_gui():
    """Test the GUI with sample data"""
    gui = get_working_gui()
    
    # Test screen 1
    gui.update_screen("SnapEDA", 1, "Login Page", 
                     "https://www.snapeda.com/login", 
                     "LOGIN PAGE:\n- Email input field\n- Password input field\n- Login button\n- SnapEDA logo at top")
    gui.wait_for_continue()
    
    # Test screen 2
    gui.update_screen("SnapEDA", 2, "Credentials Entered", 
                     "https://www.snapeda.com/login", 
                     "LOGIN PAGE WITH FILLED FIELDS:\n- Email: <EMAIL>\n- Password: ********\n- Ready to click login button")
    gui.wait_for_continue()
    
    print("✅ GUI test completed!")

if __name__ == "__main__":
    test_gui()
