#!/usr/bin/env python3
"""
Debug SnapEDA download button href and behavior
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def debug_snapeda_href():
    print("🔍 DEBUGGING SNAPEDA DOWNLOAD BUTTON HREF")
    print("=" * 60)
    
    # Setup Chrome with visible browser
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Login to SnapEDA
        print("🔐 Logging into SnapEDA...")
        driver.get("https://www.snapeda.com/account/login/")
        time.sleep(3)
        
        # Fill login form
        try:
            import json
            with open('save/component_site_credentials.json', 'r') as f:
                creds = json.load(f)
                snapeda_creds = creds.get('snapeda', {})
                email = snapeda_creds.get('email', '')
                password = snapeda_creds.get('password', '')
        except:
            print("❌ Could not load credentials")
            return
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        password_field = driver.find_element(By.CSS_SELECTOR, "#id_password")
        
        email_field.send_keys(email)
        password_field.send_keys(password)
        
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(5)
        
        print("✅ Login completed")
        
        # Go to part page
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        print(f"🔸 Going to part page: {part_url}")
        driver.get(part_url)
        time.sleep(5)
        
        # Click 3D Model tab
        print("🔸 Looking for 3D Model tab...")
        model_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D Model')]")
        for element in model_elements:
            if element.is_displayed():
                print(f"✅ Found 3D Model tab: {element.text}")
                element.click()
                time.sleep(3)
                break
        
        # Find download button and examine its properties
        print("🔸 Examining Download 3D Model button...")
        download_btn = driver.find_element(By.XPATH, "//a[contains(text(), 'Download 3D Model')]")
        
        if download_btn:
            print(f"✅ Found download button")
            print(f"   Text: '{download_btn.text}'")
            
            # Get full href
            href = download_btn.get_attribute('href')
            print(f"   Full href: '{href}'")
            
            # Get all attributes
            class_attr = download_btn.get_attribute('class')
            print(f"   Class: '{class_attr}'")
            
            onclick = download_btn.get_attribute('onclick')
            print(f"   onclick: '{onclick}'")
            
            data_attrs = {}
            for attr in ['data-toggle', 'data-target', 'data-url', 'data-modal']:
                value = download_btn.get_attribute(attr)
                if value:
                    data_attrs[attr] = value
            
            if data_attrs:
                print(f"   Data attributes: {data_attrs}")
            
            # Check if it's a modal trigger
            if 'modal' in class_attr.lower():
                print(f"🎯 This is a MODAL TRIGGER button!")
                
                # Look for modal elements
                print(f"🔸 Looking for modal elements...")
                modals = driver.find_elements(By.CSS_SELECTOR, ".modal, [id*='modal'], [class*='modal']")
                for modal in modals:
                    modal_id = modal.get_attribute('id')
                    modal_class = modal.get_attribute('class')
                    is_displayed = modal.is_displayed()
                    print(f"   Modal: id='{modal_id}' class='{modal_class}' displayed={is_displayed}")
            
            # Now click and see what happens
            print(f"\n🔸 Clicking download button and monitoring...")
            initial_url = driver.current_url
            initial_handles = driver.window_handles
            
            download_btn.click()
            
            # Monitor for 15 seconds
            for i in range(15):
                time.sleep(1)
                current_url = driver.current_url
                current_handles = driver.window_handles
                
                # Check for URL changes
                if current_url != initial_url:
                    print(f"🎯 URL CHANGED after {i+1}s: {current_url}")
                    break
                
                # Check for new windows
                if len(current_handles) > len(initial_handles):
                    print(f"🎯 NEW WINDOW after {i+1}s")
                    for handle in current_handles:
                        if handle not in initial_handles:
                            driver.switch_to.window(handle)
                            new_url = driver.current_url
                            print(f"   New window URL: {new_url}")
                            break
                    break
                
                # Check for modal
                modals = driver.find_elements(By.CSS_SELECTOR, ".modal:not(.fade), .modal.show, .modal.in")
                for modal in modals:
                    if modal.is_displayed():
                        print(f"🎯 MODAL APPEARED after {i+1}s")
                        modal_content = modal.text[:200]
                        print(f"   Modal content: {modal_content}...")
                        break
                
                if i % 3 == 0 and i > 0:
                    print(f"   ⏳ Monitoring... {i}/15 seconds")
            
            print(f"\n🔸 Final status:")
            print(f"   Current URL: {driver.current_url}")
            print(f"   Total windows: {len(driver.window_handles)}")
            
            # Keep browser open for inspection
            print(f"\n🔍 Browser staying open for manual inspection...")
            print(f"🔍 Press Enter to close...")
            input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_snapeda_href()
