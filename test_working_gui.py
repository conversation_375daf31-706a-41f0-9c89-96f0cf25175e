#!/usr/bin/env python3
"""
Test the working GUI to make sure it functions properly
"""

from working_screen_gui import get_working_gui
import time

def test_gui_step_by_step():
    """Test GUI with step by step interaction"""
    print("🧪 Testing Working GUI Step by Step...")

    # Get GUI instance (no threading)
    gui = get_working_gui()
    print("✅ GUI created successfully")

    # Test screen 1
    print("\n📺 Setting up screen 1...")
    gui.update_screen("SnapEDA", 1, "Login Page",
                     "https://www.snapeda.com/login",
                     "LOGIN PAGE:\n- Email input field (empty)\n- Password input field (empty)\n- Login button\n- SnapEDA logo at top\n\nThis is the first screen you see when opening SnapEDA.")

    print("✅ Screen 1 is ready")
    print("🖱️ The GUI window should now be visible")
    print("👆 Click the CONTINUE button to proceed to next screen...")

    # Wait for user to click continue
    gui.wait_for_continue()
    print("✅ Continue button worked! Moving to screen 2...")

    # Test screen 2
    print("\n📺 Setting up screen 2...")
    gui.update_screen("SnapEDA", 2, "Credentials Entered",
                     "https://www.snapeda.com/login",
                     "LOGIN PAGE WITH FILLED FIELDS:\n- Email: <EMAIL>\n- Password: ********\n- Login button ready to click\n\nCredentials have been entered and you're ready to login.")

    print("✅ Screen 2 is ready")
    print("👆 Click CONTINUE to test the final screen...")

    gui.wait_for_continue()
    print("✅ Second continue worked! Moving to final test...")

    # Test screen 3 - different vendor
    print("\n📺 Setting up screen 3 (vendor change test)...")
    gui.update_screen("UltraLibrarian", 3, "Search Page",
                     "https://www.ultralibrarian.com/search",
                     "ULTRALIBRARIAN SEARCH PAGE:\n- Search box visible\n- Component categories\n- Search filters\n- Different vendor interface\n\nThis tests that the GUI can handle different vendors and URLs properly.")

    print("✅ Screen 3 is ready (different vendor)")
    print("👆 Click CONTINUE one final time to complete the test...")

    gui.wait_for_continue()
    print("✅ Final continue worked!")

    print("\n🎉 ALL GUI TESTS COMPLETED SUCCESSFULLY!")
    print("\nThe GUI is working properly with:")
    print("- ✅ Screen numbers display correctly")
    print("- ✅ Vendor names change properly")
    print("- ✅ URLs are shown accurately")
    print("- ✅ Continue button responds every time")
    print("- ✅ Text content updates work")
    print("- ✅ Window appears and stays visible")
    print("- ✅ No threading issues")

    print("\n🚀 Ready for integration with SnapEDA finder!")

if __name__ == "__main__":
    test_gui_step_by_step()
