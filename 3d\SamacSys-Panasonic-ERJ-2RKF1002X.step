ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('/var/www/3D/0T/393534.4.1.stp','2021-05-11T12:28:40',(
    'Author'),(''),'Open CASCADE STEP processor 6.9','FreeCAD','Unknown'
  );
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('ASSEMBLY','ASSEMBLY','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,0.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#34),#364);
#34 = MANIFOLD_SOLID_BREP('',#35);
#35 = CLOSED_SHELL('',(#36,#156,#232,#281,#330,#357));
#36 = ADVANCED_FACE('',(#37),#51,.F.);
#37 = FACE_BOUND('',#38,.F.);
#38 = EDGE_LOOP('',(#39,#74,#102,#130));
#39 = ORIENTED_EDGE('',*,*,#40,.F.);
#40 = EDGE_CURVE('',#41,#43,#45,.T.);
#41 = VERTEX_POINT('',#42);
#42 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#43 = VERTEX_POINT('',#44);
#44 = CARTESIAN_POINT('',(-0.48,-0.25,0.33));
#45 = SURFACE_CURVE('',#46,(#50,#62),.PCURVE_S1.);
#46 = LINE('',#47,#48);
#47 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#48 = VECTOR('',#49,1.);
#49 = DIRECTION('',(0.,0.,1.));
#50 = PCURVE('',#51,#56);
#51 = PLANE('',#52);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#54 = DIRECTION('',(1.,0.,0.));
#55 = DIRECTION('',(0.,0.,1.));
#56 = DEFINITIONAL_REPRESENTATION('',(#57),#61);
#57 = LINE('',#58,#59);
#58 = CARTESIAN_POINT('',(0.,0.));
#59 = VECTOR('',#60,1.);
#60 = DIRECTION('',(1.,0.));
#61 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#62 = PCURVE('',#63,#68);
#63 = PLANE('',#64);
#64 = AXIS2_PLACEMENT_3D('',#65,#66,#67);
#65 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#66 = DIRECTION('',(0.,1.,0.));
#67 = DIRECTION('',(0.,0.,1.));
#68 = DEFINITIONAL_REPRESENTATION('',(#69),#73);
#69 = LINE('',#70,#71);
#70 = CARTESIAN_POINT('',(0.,0.));
#71 = VECTOR('',#72,1.);
#72 = DIRECTION('',(1.,0.));
#73 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#74 = ORIENTED_EDGE('',*,*,#75,.T.);
#75 = EDGE_CURVE('',#41,#76,#78,.T.);
#76 = VERTEX_POINT('',#77);
#77 = CARTESIAN_POINT('',(-0.48,0.25,2.E-02));
#78 = SURFACE_CURVE('',#79,(#83,#90),.PCURVE_S1.);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(0.,1.,0.));
#83 = PCURVE('',#51,#84);
#84 = DEFINITIONAL_REPRESENTATION('',(#85),#89);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,-1.));
#89 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#90 = PCURVE('',#91,#96);
#91 = PLANE('',#92);
#92 = AXIS2_PLACEMENT_3D('',#93,#94,#95);
#93 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#94 = DIRECTION('',(0.,0.,1.));
#95 = DIRECTION('',(1.,0.,0.));
#96 = DEFINITIONAL_REPRESENTATION('',(#97),#101);
#97 = LINE('',#98,#99);
#98 = CARTESIAN_POINT('',(0.,0.));
#99 = VECTOR('',#100,1.);
#100 = DIRECTION('',(0.,1.));
#101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#102 = ORIENTED_EDGE('',*,*,#103,.T.);
#103 = EDGE_CURVE('',#76,#104,#106,.T.);
#104 = VERTEX_POINT('',#105);
#105 = CARTESIAN_POINT('',(-0.48,0.25,0.33));
#106 = SURFACE_CURVE('',#107,(#111,#118),.PCURVE_S1.);
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(-0.48,0.25,2.E-02));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(0.,0.,1.));
#111 = PCURVE('',#51,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,-0.5));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(-0.48,0.25,2.E-02));
#122 = DIRECTION('',(0.,1.,0.));
#123 = DIRECTION('',(0.,0.,1.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.,0.));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#131,.F.);
#131 = EDGE_CURVE('',#43,#104,#132,.T.);
#132 = SURFACE_CURVE('',#133,(#137,#144),.PCURVE_S1.);
#133 = LINE('',#134,#135);
#134 = CARTESIAN_POINT('',(-0.48,-0.25,0.33));
#135 = VECTOR('',#136,1.);
#136 = DIRECTION('',(0.,1.,0.));
#137 = PCURVE('',#51,#138);
#138 = DEFINITIONAL_REPRESENTATION('',(#139),#143);
#139 = LINE('',#140,#141);
#140 = CARTESIAN_POINT('',(0.31,0.));
#141 = VECTOR('',#142,1.);
#142 = DIRECTION('',(0.,-1.));
#143 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#144 = PCURVE('',#145,#150);
#145 = PLANE('',#146);
#146 = AXIS2_PLACEMENT_3D('',#147,#148,#149);
#147 = CARTESIAN_POINT('',(-0.48,-0.25,0.33));
#148 = DIRECTION('',(0.,0.,1.));
#149 = DIRECTION('',(1.,0.,0.));
#150 = DEFINITIONAL_REPRESENTATION('',(#151),#155);
#151 = LINE('',#152,#153);
#152 = CARTESIAN_POINT('',(0.,0.));
#153 = VECTOR('',#154,1.);
#154 = DIRECTION('',(0.,1.));
#155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#156 = ADVANCED_FACE('',(#157),#63,.F.);
#157 = FACE_BOUND('',#158,.F.);
#158 = EDGE_LOOP('',(#159,#182,#183,#206));
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#41,#161,#163,.T.);
#161 = VERTEX_POINT('',#162);
#162 = CARTESIAN_POINT('',(0.48,-0.25,2.E-02));
#163 = SURFACE_CURVE('',#164,(#168,#175),.PCURVE_S1.);
#164 = LINE('',#165,#166);
#165 = CARTESIAN_POINT('',(-0.48,-0.25,2.E-02));
#166 = VECTOR('',#167,1.);
#167 = DIRECTION('',(1.,0.,0.));
#168 = PCURVE('',#63,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(0.,0.));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.,1.));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = PCURVE('',#91,#176);
#176 = DEFINITIONAL_REPRESENTATION('',(#177),#181);
#177 = LINE('',#178,#179);
#178 = CARTESIAN_POINT('',(0.,0.));
#179 = VECTOR('',#180,1.);
#180 = DIRECTION('',(1.,0.));
#181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#182 = ORIENTED_EDGE('',*,*,#40,.T.);
#183 = ORIENTED_EDGE('',*,*,#184,.T.);
#184 = EDGE_CURVE('',#43,#185,#187,.T.);
#185 = VERTEX_POINT('',#186);
#186 = CARTESIAN_POINT('',(0.48,-0.25,0.33));
#187 = SURFACE_CURVE('',#188,(#192,#199),.PCURVE_S1.);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(-0.48,-0.25,0.33));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(1.,0.,0.));
#192 = PCURVE('',#63,#193);
#193 = DEFINITIONAL_REPRESENTATION('',(#194),#198);
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(0.31,0.));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(0.,1.));
#198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#199 = PCURVE('',#145,#200);
#200 = DEFINITIONAL_REPRESENTATION('',(#201),#205);
#201 = LINE('',#202,#203);
#202 = CARTESIAN_POINT('',(0.,0.));
#203 = VECTOR('',#204,1.);
#204 = DIRECTION('',(1.,0.));
#205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#206 = ORIENTED_EDGE('',*,*,#207,.F.);
#207 = EDGE_CURVE('',#161,#185,#208,.T.);
#208 = SURFACE_CURVE('',#209,(#213,#220),.PCURVE_S1.);
#209 = LINE('',#210,#211);
#210 = CARTESIAN_POINT('',(0.48,-0.25,2.E-02));
#211 = VECTOR('',#212,1.);
#212 = DIRECTION('',(0.,0.,1.));
#213 = PCURVE('',#63,#214);
#214 = DEFINITIONAL_REPRESENTATION('',(#215),#219);
#215 = LINE('',#216,#217);
#216 = CARTESIAN_POINT('',(0.,0.96));
#217 = VECTOR('',#218,1.);
#218 = DIRECTION('',(1.,0.));
#219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#220 = PCURVE('',#221,#226);
#221 = PLANE('',#222);
#222 = AXIS2_PLACEMENT_3D('',#223,#224,#225);
#223 = CARTESIAN_POINT('',(0.48,-0.25,2.E-02));
#224 = DIRECTION('',(1.,0.,0.));
#225 = DIRECTION('',(0.,0.,1.));
#226 = DEFINITIONAL_REPRESENTATION('',(#227),#231);
#227 = LINE('',#228,#229);
#228 = CARTESIAN_POINT('',(0.,0.));
#229 = VECTOR('',#230,1.);
#230 = DIRECTION('',(1.,0.));
#231 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#232 = ADVANCED_FACE('',(#233),#145,.T.);
#233 = FACE_BOUND('',#234,.T.);
#234 = EDGE_LOOP('',(#235,#236,#237,#260));
#235 = ORIENTED_EDGE('',*,*,#131,.F.);
#236 = ORIENTED_EDGE('',*,*,#184,.T.);
#237 = ORIENTED_EDGE('',*,*,#238,.T.);
#238 = EDGE_CURVE('',#185,#239,#241,.T.);
#239 = VERTEX_POINT('',#240);
#240 = CARTESIAN_POINT('',(0.48,0.25,0.33));
#241 = SURFACE_CURVE('',#242,(#246,#253),.PCURVE_S1.);
#242 = LINE('',#243,#244);
#243 = CARTESIAN_POINT('',(0.48,-0.25,0.33));
#244 = VECTOR('',#245,1.);
#245 = DIRECTION('',(0.,1.,0.));
#246 = PCURVE('',#145,#247);
#247 = DEFINITIONAL_REPRESENTATION('',(#248),#252);
#248 = LINE('',#249,#250);
#249 = CARTESIAN_POINT('',(0.96,0.));
#250 = VECTOR('',#251,1.);
#251 = DIRECTION('',(0.,1.));
#252 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#253 = PCURVE('',#221,#254);
#254 = DEFINITIONAL_REPRESENTATION('',(#255),#259);
#255 = LINE('',#256,#257);
#256 = CARTESIAN_POINT('',(0.31,0.));
#257 = VECTOR('',#258,1.);
#258 = DIRECTION('',(0.,-1.));
#259 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#260 = ORIENTED_EDGE('',*,*,#261,.F.);
#261 = EDGE_CURVE('',#104,#239,#262,.T.);
#262 = SURFACE_CURVE('',#263,(#267,#274),.PCURVE_S1.);
#263 = LINE('',#264,#265);
#264 = CARTESIAN_POINT('',(-0.48,0.25,0.33));
#265 = VECTOR('',#266,1.);
#266 = DIRECTION('',(1.,0.,0.));
#267 = PCURVE('',#145,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.,0.5));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(1.,0.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = PCURVE('',#119,#275);
#275 = DEFINITIONAL_REPRESENTATION('',(#276),#280);
#276 = LINE('',#277,#278);
#277 = CARTESIAN_POINT('',(0.31,0.));
#278 = VECTOR('',#279,1.);
#279 = DIRECTION('',(0.,1.));
#280 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#281 = ADVANCED_FACE('',(#282),#119,.T.);
#282 = FACE_BOUND('',#283,.T.);
#283 = EDGE_LOOP('',(#284,#307,#308,#309));
#284 = ORIENTED_EDGE('',*,*,#285,.F.);
#285 = EDGE_CURVE('',#76,#286,#288,.T.);
#286 = VERTEX_POINT('',#287);
#287 = CARTESIAN_POINT('',(0.48,0.25,2.E-02));
#288 = SURFACE_CURVE('',#289,(#293,#300),.PCURVE_S1.);
#289 = LINE('',#290,#291);
#290 = CARTESIAN_POINT('',(-0.48,0.25,2.E-02));
#291 = VECTOR('',#292,1.);
#292 = DIRECTION('',(1.,0.,0.));
#293 = PCURVE('',#119,#294);
#294 = DEFINITIONAL_REPRESENTATION('',(#295),#299);
#295 = LINE('',#296,#297);
#296 = CARTESIAN_POINT('',(0.,0.));
#297 = VECTOR('',#298,1.);
#298 = DIRECTION('',(0.,1.));
#299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#300 = PCURVE('',#91,#301);
#301 = DEFINITIONAL_REPRESENTATION('',(#302),#306);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(0.,0.5));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(1.,0.));
#306 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#307 = ORIENTED_EDGE('',*,*,#103,.T.);
#308 = ORIENTED_EDGE('',*,*,#261,.T.);
#309 = ORIENTED_EDGE('',*,*,#310,.F.);
#310 = EDGE_CURVE('',#286,#239,#311,.T.);
#311 = SURFACE_CURVE('',#312,(#316,#323),.PCURVE_S1.);
#312 = LINE('',#313,#314);
#313 = CARTESIAN_POINT('',(0.48,0.25,2.E-02));
#314 = VECTOR('',#315,1.);
#315 = DIRECTION('',(0.,0.,1.));
#316 = PCURVE('',#119,#317);
#317 = DEFINITIONAL_REPRESENTATION('',(#318),#322);
#318 = LINE('',#319,#320);
#319 = CARTESIAN_POINT('',(0.,0.96));
#320 = VECTOR('',#321,1.);
#321 = DIRECTION('',(1.,0.));
#322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#323 = PCURVE('',#221,#324);
#324 = DEFINITIONAL_REPRESENTATION('',(#325),#329);
#325 = LINE('',#326,#327);
#326 = CARTESIAN_POINT('',(0.,-0.5));
#327 = VECTOR('',#328,1.);
#328 = DIRECTION('',(1.,0.));
#329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#330 = ADVANCED_FACE('',(#331),#91,.F.);
#331 = FACE_BOUND('',#332,.F.);
#332 = EDGE_LOOP('',(#333,#334,#335,#356));
#333 = ORIENTED_EDGE('',*,*,#75,.F.);
#334 = ORIENTED_EDGE('',*,*,#160,.T.);
#335 = ORIENTED_EDGE('',*,*,#336,.T.);
#336 = EDGE_CURVE('',#161,#286,#337,.T.);
#337 = SURFACE_CURVE('',#338,(#342,#349),.PCURVE_S1.);
#338 = LINE('',#339,#340);
#339 = CARTESIAN_POINT('',(0.48,-0.25,2.E-02));
#340 = VECTOR('',#341,1.);
#341 = DIRECTION('',(0.,1.,0.));
#342 = PCURVE('',#91,#343);
#343 = DEFINITIONAL_REPRESENTATION('',(#344),#348);
#344 = LINE('',#345,#346);
#345 = CARTESIAN_POINT('',(0.96,0.));
#346 = VECTOR('',#347,1.);
#347 = DIRECTION('',(0.,1.));
#348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#349 = PCURVE('',#221,#350);
#350 = DEFINITIONAL_REPRESENTATION('',(#351),#355);
#351 = LINE('',#352,#353);
#352 = CARTESIAN_POINT('',(0.,0.));
#353 = VECTOR('',#354,1.);
#354 = DIRECTION('',(0.,-1.));
#355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#356 = ORIENTED_EDGE('',*,*,#285,.F.);
#357 = ADVANCED_FACE('',(#358),#221,.T.);
#358 = FACE_BOUND('',#359,.T.);
#359 = EDGE_LOOP('',(#360,#361,#362,#363));
#360 = ORIENTED_EDGE('',*,*,#207,.F.);
#361 = ORIENTED_EDGE('',*,*,#336,.T.);
#362 = ORIENTED_EDGE('',*,*,#310,.T.);
#363 = ORIENTED_EDGE('',*,*,#238,.F.);
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#368)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#365,#366,#367)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#365 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#366 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#367 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#368 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#365,
  'distance_accuracy_value','confusion accuracy');
#369 = SHAPE_DEFINITION_REPRESENTATION(#370,#33);
#370 = PRODUCT_DEFINITION_SHAPE('','',#371);
#371 = PRODUCT_DEFINITION('design','',#372,#375);
#372 = PRODUCT_DEFINITION_FORMATION('','',#373);
#373 = PRODUCT('solidBody','solidBody','',(#374));
#374 = PRODUCT_CONTEXT('',#2,'mechanical');
#375 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#376 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#377,#379);
#377 = ( REPRESENTATION_RELATIONSHIP('','',#33,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#378) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#378 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#379 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#380
  );
#380 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','=>[0:1:1:2]','',#5,#371,$);
#381 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#373));
#382 = SHAPE_REPRESENTATION('',(#11,#383,#1173),#1963);
#383 = MANIFOLD_SOLID_BREP('',#384);
#384 = CLOSED_SHELL('',(#385,#505,#637,#793,#893,#920,#1007,#1056,#1083,
    #1132,#1159,#1166));
#385 = ADVANCED_FACE('',(#386),#400,.F.);
#386 = FACE_BOUND('',#387,.F.);
#387 = EDGE_LOOP('',(#388,#423,#451,#479));
#388 = ORIENTED_EDGE('',*,*,#389,.F.);
#389 = EDGE_CURVE('',#390,#392,#394,.T.);
#390 = VERTEX_POINT('',#391);
#391 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#392 = VERTEX_POINT('',#393);
#393 = CARTESIAN_POINT('',(-0.5,-0.25,0.35));
#394 = SURFACE_CURVE('',#395,(#399,#411),.PCURVE_S1.);
#395 = LINE('',#396,#397);
#396 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#397 = VECTOR('',#398,1.);
#398 = DIRECTION('',(0.,0.,1.));
#399 = PCURVE('',#400,#405);
#400 = PLANE('',#401);
#401 = AXIS2_PLACEMENT_3D('',#402,#403,#404);
#402 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#403 = DIRECTION('',(1.,0.,0.));
#404 = DIRECTION('',(0.,0.,1.));
#405 = DEFINITIONAL_REPRESENTATION('',(#406),#410);
#406 = LINE('',#407,#408);
#407 = CARTESIAN_POINT('',(0.,0.));
#408 = VECTOR('',#409,1.);
#409 = DIRECTION('',(1.,0.));
#410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#411 = PCURVE('',#412,#417);
#412 = PLANE('',#413);
#413 = AXIS2_PLACEMENT_3D('',#414,#415,#416);
#414 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#415 = DIRECTION('',(0.,1.,0.));
#416 = DIRECTION('',(0.,0.,1.));
#417 = DEFINITIONAL_REPRESENTATION('',(#418),#422);
#418 = LINE('',#419,#420);
#419 = CARTESIAN_POINT('',(0.,0.));
#420 = VECTOR('',#421,1.);
#421 = DIRECTION('',(1.,0.));
#422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#390,#425,#427,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#427 = SURFACE_CURVE('',#428,(#432,#439),.PCURVE_S1.);
#428 = LINE('',#429,#430);
#429 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#430 = VECTOR('',#431,1.);
#431 = DIRECTION('',(0.,1.,0.));
#432 = PCURVE('',#400,#433);
#433 = DEFINITIONAL_REPRESENTATION('',(#434),#438);
#434 = LINE('',#435,#436);
#435 = CARTESIAN_POINT('',(0.,0.));
#436 = VECTOR('',#437,1.);
#437 = DIRECTION('',(0.,-1.));
#438 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#439 = PCURVE('',#440,#445);
#440 = PLANE('',#441);
#441 = AXIS2_PLACEMENT_3D('',#442,#443,#444);
#442 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#443 = DIRECTION('',(0.,0.,1.));
#444 = DIRECTION('',(1.,0.,0.));
#445 = DEFINITIONAL_REPRESENTATION('',(#446),#450);
#446 = LINE('',#447,#448);
#447 = CARTESIAN_POINT('',(0.,0.));
#448 = VECTOR('',#449,1.);
#449 = DIRECTION('',(0.,1.));
#450 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#451 = ORIENTED_EDGE('',*,*,#452,.T.);
#452 = EDGE_CURVE('',#425,#453,#455,.T.);
#453 = VERTEX_POINT('',#454);
#454 = CARTESIAN_POINT('',(-0.5,0.25,0.35));
#455 = SURFACE_CURVE('',#456,(#460,#467),.PCURVE_S1.);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(0.,0.,1.));
#460 = PCURVE('',#400,#461);
#461 = DEFINITIONAL_REPRESENTATION('',(#462),#466);
#462 = LINE('',#463,#464);
#463 = CARTESIAN_POINT('',(0.,-0.5));
#464 = VECTOR('',#465,1.);
#465 = DIRECTION('',(1.,0.));
#466 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#467 = PCURVE('',#468,#473);
#468 = PLANE('',#469);
#469 = AXIS2_PLACEMENT_3D('',#470,#471,#472);
#470 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#471 = DIRECTION('',(0.,1.,0.));
#472 = DIRECTION('',(0.,0.,1.));
#473 = DEFINITIONAL_REPRESENTATION('',(#474),#478);
#474 = LINE('',#475,#476);
#475 = CARTESIAN_POINT('',(0.,0.));
#476 = VECTOR('',#477,1.);
#477 = DIRECTION('',(1.,0.));
#478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#479 = ORIENTED_EDGE('',*,*,#480,.F.);
#480 = EDGE_CURVE('',#392,#453,#481,.T.);
#481 = SURFACE_CURVE('',#482,(#486,#493),.PCURVE_S1.);
#482 = LINE('',#483,#484);
#483 = CARTESIAN_POINT('',(-0.5,-0.25,0.35));
#484 = VECTOR('',#485,1.);
#485 = DIRECTION('',(0.,1.,0.));
#486 = PCURVE('',#400,#487);
#487 = DEFINITIONAL_REPRESENTATION('',(#488),#492);
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(0.35,0.));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(0.,-1.));
#492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#493 = PCURVE('',#494,#499);
#494 = PLANE('',#495);
#495 = AXIS2_PLACEMENT_3D('',#496,#497,#498);
#496 = CARTESIAN_POINT('',(-0.5,-0.25,0.35));
#497 = DIRECTION('',(0.,0.,1.));
#498 = DIRECTION('',(1.,0.,0.));
#499 = DEFINITIONAL_REPRESENTATION('',(#500),#504);
#500 = LINE('',#501,#502);
#501 = CARTESIAN_POINT('',(0.,0.));
#502 = VECTOR('',#503,1.);
#503 = DIRECTION('',(0.,1.));
#504 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#505 = ADVANCED_FACE('',(#506),#412,.F.);
#506 = FACE_BOUND('',#507,.F.);
#507 = EDGE_LOOP('',(#508,#538,#559,#560,#583,#611));
#508 = ORIENTED_EDGE('',*,*,#509,.F.);
#509 = EDGE_CURVE('',#510,#512,#514,.T.);
#510 = VERTEX_POINT('',#511);
#511 = CARTESIAN_POINT('',(-0.3,-0.25,0.));
#512 = VERTEX_POINT('',#513);
#513 = CARTESIAN_POINT('',(-0.3,-0.25,5.E-02));
#514 = SURFACE_CURVE('',#515,(#519,#526),.PCURVE_S1.);
#515 = LINE('',#516,#517);
#516 = CARTESIAN_POINT('',(-0.3,-0.25,0.));
#517 = VECTOR('',#518,1.);
#518 = DIRECTION('',(0.,0.,1.));
#519 = PCURVE('',#412,#520);
#520 = DEFINITIONAL_REPRESENTATION('',(#521),#525);
#521 = LINE('',#522,#523);
#522 = CARTESIAN_POINT('',(0.,0.2));
#523 = VECTOR('',#524,1.);
#524 = DIRECTION('',(1.,0.));
#525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#526 = PCURVE('',#527,#532);
#527 = PLANE('',#528);
#528 = AXIS2_PLACEMENT_3D('',#529,#530,#531);
#529 = CARTESIAN_POINT('',(-0.3,-0.25,0.));
#530 = DIRECTION('',(1.,0.,0.));
#531 = DIRECTION('',(0.,0.,1.));
#532 = DEFINITIONAL_REPRESENTATION('',(#533),#537);
#533 = LINE('',#534,#535);
#534 = CARTESIAN_POINT('',(0.,0.));
#535 = VECTOR('',#536,1.);
#536 = DIRECTION('',(1.,0.));
#537 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#538 = ORIENTED_EDGE('',*,*,#539,.F.);
#539 = EDGE_CURVE('',#390,#510,#540,.T.);
#540 = SURFACE_CURVE('',#541,(#545,#552),.PCURVE_S1.);
#541 = LINE('',#542,#543);
#542 = CARTESIAN_POINT('',(-0.5,-0.25,0.));
#543 = VECTOR('',#544,1.);
#544 = DIRECTION('',(1.,0.,0.));
#545 = PCURVE('',#412,#546);
#546 = DEFINITIONAL_REPRESENTATION('',(#547),#551);
#547 = LINE('',#548,#549);
#548 = CARTESIAN_POINT('',(0.,0.));
#549 = VECTOR('',#550,1.);
#550 = DIRECTION('',(0.,1.));
#551 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#552 = PCURVE('',#440,#553);
#553 = DEFINITIONAL_REPRESENTATION('',(#554),#558);
#554 = LINE('',#555,#556);
#555 = CARTESIAN_POINT('',(0.,0.));
#556 = VECTOR('',#557,1.);
#557 = DIRECTION('',(1.,0.));
#558 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#559 = ORIENTED_EDGE('',*,*,#389,.T.);
#560 = ORIENTED_EDGE('',*,*,#561,.T.);
#561 = EDGE_CURVE('',#392,#562,#564,.T.);
#562 = VERTEX_POINT('',#563);
#563 = CARTESIAN_POINT('',(-0.4,-0.25,0.35));
#564 = SURFACE_CURVE('',#565,(#569,#576),.PCURVE_S1.);
#565 = LINE('',#566,#567);
#566 = CARTESIAN_POINT('',(-0.5,-0.25,0.35));
#567 = VECTOR('',#568,1.);
#568 = DIRECTION('',(1.,0.,0.));
#569 = PCURVE('',#412,#570);
#570 = DEFINITIONAL_REPRESENTATION('',(#571),#575);
#571 = LINE('',#572,#573);
#572 = CARTESIAN_POINT('',(0.35,0.));
#573 = VECTOR('',#574,1.);
#574 = DIRECTION('',(0.,1.));
#575 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#576 = PCURVE('',#494,#577);
#577 = DEFINITIONAL_REPRESENTATION('',(#578),#582);
#578 = LINE('',#579,#580);
#579 = CARTESIAN_POINT('',(0.,0.));
#580 = VECTOR('',#581,1.);
#581 = DIRECTION('',(1.,0.));
#582 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#583 = ORIENTED_EDGE('',*,*,#584,.F.);
#584 = EDGE_CURVE('',#585,#562,#587,.T.);
#585 = VERTEX_POINT('',#586);
#586 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#587 = SURFACE_CURVE('',#588,(#592,#599),.PCURVE_S1.);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(0.,0.,1.));
#592 = PCURVE('',#412,#593);
#593 = DEFINITIONAL_REPRESENTATION('',(#594),#598);
#594 = LINE('',#595,#596);
#595 = CARTESIAN_POINT('',(5.E-02,1.E-01));
#596 = VECTOR('',#597,1.);
#597 = DIRECTION('',(1.,0.));
#598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#599 = PCURVE('',#600,#605);
#600 = PLANE('',#601);
#601 = AXIS2_PLACEMENT_3D('',#602,#603,#604);
#602 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#603 = DIRECTION('',(1.,0.,0.));
#604 = DIRECTION('',(0.,0.,1.));
#605 = DEFINITIONAL_REPRESENTATION('',(#606),#610);
#606 = LINE('',#607,#608);
#607 = CARTESIAN_POINT('',(0.,0.));
#608 = VECTOR('',#609,1.);
#609 = DIRECTION('',(1.,0.));
#610 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#611 = ORIENTED_EDGE('',*,*,#612,.T.);
#612 = EDGE_CURVE('',#585,#512,#613,.T.);
#613 = SURFACE_CURVE('',#614,(#618,#625),.PCURVE_S1.);
#614 = LINE('',#615,#616);
#615 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#616 = VECTOR('',#617,1.);
#617 = DIRECTION('',(1.,0.,0.));
#618 = PCURVE('',#412,#619);
#619 = DEFINITIONAL_REPRESENTATION('',(#620),#624);
#620 = LINE('',#621,#622);
#621 = CARTESIAN_POINT('',(5.E-02,1.E-01));
#622 = VECTOR('',#623,1.);
#623 = DIRECTION('',(0.,1.));
#624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#625 = PCURVE('',#626,#631);
#626 = PLANE('',#627);
#627 = AXIS2_PLACEMENT_3D('',#628,#629,#630);
#628 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#629 = DIRECTION('',(0.,0.,1.));
#630 = DIRECTION('',(1.,0.,0.));
#631 = DEFINITIONAL_REPRESENTATION('',(#632),#636);
#632 = LINE('',#633,#634);
#633 = CARTESIAN_POINT('',(0.,0.));
#634 = VECTOR('',#635,1.);
#635 = DIRECTION('',(1.,0.));
#636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#637 = ADVANCED_FACE('',(#638),#494,.T.);
#638 = FACE_BOUND('',#639,.T.);
#639 = EDGE_LOOP('',(#640,#663,#664,#665,#688,#716,#739,#767));
#640 = ORIENTED_EDGE('',*,*,#641,.F.);
#641 = EDGE_CURVE('',#453,#642,#644,.T.);
#642 = VERTEX_POINT('',#643);
#643 = CARTESIAN_POINT('',(-0.4,0.25,0.35));
#644 = SURFACE_CURVE('',#645,(#649,#656),.PCURVE_S1.);
#645 = LINE('',#646,#647);
#646 = CARTESIAN_POINT('',(-0.5,0.25,0.35));
#647 = VECTOR('',#648,1.);
#648 = DIRECTION('',(1.,0.,0.));
#649 = PCURVE('',#494,#650);
#650 = DEFINITIONAL_REPRESENTATION('',(#651),#655);
#651 = LINE('',#652,#653);
#652 = CARTESIAN_POINT('',(0.,0.5));
#653 = VECTOR('',#654,1.);
#654 = DIRECTION('',(1.,0.));
#655 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#656 = PCURVE('',#468,#657);
#657 = DEFINITIONAL_REPRESENTATION('',(#658),#662);
#658 = LINE('',#659,#660);
#659 = CARTESIAN_POINT('',(0.35,0.));
#660 = VECTOR('',#661,1.);
#661 = DIRECTION('',(0.,1.));
#662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#663 = ORIENTED_EDGE('',*,*,#480,.F.);
#664 = ORIENTED_EDGE('',*,*,#561,.T.);
#665 = ORIENTED_EDGE('',*,*,#666,.T.);
#666 = EDGE_CURVE('',#562,#667,#669,.T.);
#667 = VERTEX_POINT('',#668);
#668 = CARTESIAN_POINT('',(-0.4,-0.18,0.35));
#669 = SURFACE_CURVE('',#670,(#674,#681),.PCURVE_S1.);
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(-0.4,-0.25,0.35));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(0.,1.,0.));
#674 = PCURVE('',#494,#675);
#675 = DEFINITIONAL_REPRESENTATION('',(#676),#680);
#676 = LINE('',#677,#678);
#677 = CARTESIAN_POINT('',(1.E-01,0.));
#678 = VECTOR('',#679,1.);
#679 = DIRECTION('',(0.,1.));
#680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#681 = PCURVE('',#600,#682);
#682 = DEFINITIONAL_REPRESENTATION('',(#683),#687);
#683 = LINE('',#684,#685);
#684 = CARTESIAN_POINT('',(0.3,0.));
#685 = VECTOR('',#686,1.);
#686 = DIRECTION('',(0.,-1.));
#687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#688 = ORIENTED_EDGE('',*,*,#689,.T.);
#689 = EDGE_CURVE('',#667,#690,#692,.T.);
#690 = VERTEX_POINT('',#691);
#691 = CARTESIAN_POINT('',(-0.3,-0.18,0.35));
#692 = SURFACE_CURVE('',#693,(#697,#704),.PCURVE_S1.);
#693 = LINE('',#694,#695);
#694 = CARTESIAN_POINT('',(-0.4,-0.18,0.35));
#695 = VECTOR('',#696,1.);
#696 = DIRECTION('',(1.,0.,0.));
#697 = PCURVE('',#494,#698);
#698 = DEFINITIONAL_REPRESENTATION('',(#699),#703);
#699 = LINE('',#700,#701);
#700 = CARTESIAN_POINT('',(1.E-01,7.E-02));
#701 = VECTOR('',#702,1.);
#702 = DIRECTION('',(1.,0.));
#703 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#704 = PCURVE('',#705,#710);
#705 = PLANE('',#706);
#706 = AXIS2_PLACEMENT_3D('',#707,#708,#709);
#707 = CARTESIAN_POINT('',(-0.4,-0.18,5.E-02));
#708 = DIRECTION('',(0.,1.,0.));
#709 = DIRECTION('',(0.,0.,1.));
#710 = DEFINITIONAL_REPRESENTATION('',(#711),#715);
#711 = LINE('',#712,#713);
#712 = CARTESIAN_POINT('',(0.3,0.));
#713 = VECTOR('',#714,1.);
#714 = DIRECTION('',(0.,1.));
#715 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#716 = ORIENTED_EDGE('',*,*,#717,.T.);
#717 = EDGE_CURVE('',#690,#718,#720,.T.);
#718 = VERTEX_POINT('',#719);
#719 = CARTESIAN_POINT('',(-0.3,0.18,0.35));
#720 = SURFACE_CURVE('',#721,(#725,#732),.PCURVE_S1.);
#721 = LINE('',#722,#723);
#722 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#723 = VECTOR('',#724,1.);
#724 = DIRECTION('',(0.,1.,0.));
#725 = PCURVE('',#494,#726);
#726 = DEFINITIONAL_REPRESENTATION('',(#727),#731);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(0.2,0.));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(0.,1.));
#731 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#732 = PCURVE('',#527,#733);
#733 = DEFINITIONAL_REPRESENTATION('',(#734),#738);
#734 = LINE('',#735,#736);
#735 = CARTESIAN_POINT('',(0.35,0.));
#736 = VECTOR('',#737,1.);
#737 = DIRECTION('',(0.,-1.));
#738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#739 = ORIENTED_EDGE('',*,*,#740,.F.);
#740 = EDGE_CURVE('',#741,#718,#743,.T.);
#741 = VERTEX_POINT('',#742);
#742 = CARTESIAN_POINT('',(-0.4,0.18,0.35));
#743 = SURFACE_CURVE('',#744,(#748,#755),.PCURVE_S1.);
#744 = LINE('',#745,#746);
#745 = CARTESIAN_POINT('',(-0.4,0.18,0.35));
#746 = VECTOR('',#747,1.);
#747 = DIRECTION('',(1.,0.,0.));
#748 = PCURVE('',#494,#749);
#749 = DEFINITIONAL_REPRESENTATION('',(#750),#754);
#750 = LINE('',#751,#752);
#751 = CARTESIAN_POINT('',(1.E-01,0.43));
#752 = VECTOR('',#753,1.);
#753 = DIRECTION('',(1.,0.));
#754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#755 = PCURVE('',#756,#761);
#756 = PLANE('',#757);
#757 = AXIS2_PLACEMENT_3D('',#758,#759,#760);
#758 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#759 = DIRECTION('',(0.,1.,0.));
#760 = DIRECTION('',(0.,0.,1.));
#761 = DEFINITIONAL_REPRESENTATION('',(#762),#766);
#762 = LINE('',#763,#764);
#763 = CARTESIAN_POINT('',(0.3,0.));
#764 = VECTOR('',#765,1.);
#765 = DIRECTION('',(0.,1.));
#766 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#767 = ORIENTED_EDGE('',*,*,#768,.T.);
#768 = EDGE_CURVE('',#741,#642,#769,.T.);
#769 = SURFACE_CURVE('',#770,(#774,#781),.PCURVE_S1.);
#770 = LINE('',#771,#772);
#771 = CARTESIAN_POINT('',(-0.4,0.18,0.35));
#772 = VECTOR('',#773,1.);
#773 = DIRECTION('',(0.,1.,0.));
#774 = PCURVE('',#494,#775);
#775 = DEFINITIONAL_REPRESENTATION('',(#776),#780);
#776 = LINE('',#777,#778);
#777 = CARTESIAN_POINT('',(1.E-01,0.43));
#778 = VECTOR('',#779,1.);
#779 = DIRECTION('',(0.,1.));
#780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#781 = PCURVE('',#782,#787);
#782 = PLANE('',#783);
#783 = AXIS2_PLACEMENT_3D('',#784,#785,#786);
#784 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#785 = DIRECTION('',(1.,0.,0.));
#786 = DIRECTION('',(0.,0.,1.));
#787 = DEFINITIONAL_REPRESENTATION('',(#788),#792);
#788 = LINE('',#789,#790);
#789 = CARTESIAN_POINT('',(0.3,0.));
#790 = VECTOR('',#791,1.);
#791 = DIRECTION('',(0.,-1.));
#792 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#793 = ADVANCED_FACE('',(#794),#468,.T.);
#794 = FACE_BOUND('',#795,.T.);
#795 = EDGE_LOOP('',(#796,#821,#842,#843,#844,#867));
#796 = ORIENTED_EDGE('',*,*,#797,.F.);
#797 = EDGE_CURVE('',#798,#800,#802,.T.);
#798 = VERTEX_POINT('',#799);
#799 = CARTESIAN_POINT('',(-0.3,0.25,0.));
#800 = VERTEX_POINT('',#801);
#801 = CARTESIAN_POINT('',(-0.3,0.25,5.E-02));
#802 = SURFACE_CURVE('',#803,(#807,#814),.PCURVE_S1.);
#803 = LINE('',#804,#805);
#804 = CARTESIAN_POINT('',(-0.3,0.25,0.));
#805 = VECTOR('',#806,1.);
#806 = DIRECTION('',(0.,0.,1.));
#807 = PCURVE('',#468,#808);
#808 = DEFINITIONAL_REPRESENTATION('',(#809),#813);
#809 = LINE('',#810,#811);
#810 = CARTESIAN_POINT('',(0.,0.2));
#811 = VECTOR('',#812,1.);
#812 = DIRECTION('',(1.,0.));
#813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#814 = PCURVE('',#527,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(0.,-0.5));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(1.,0.));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = ORIENTED_EDGE('',*,*,#822,.F.);
#822 = EDGE_CURVE('',#425,#798,#823,.T.);
#823 = SURFACE_CURVE('',#824,(#828,#835),.PCURVE_S1.);
#824 = LINE('',#825,#826);
#825 = CARTESIAN_POINT('',(-0.5,0.25,0.));
#826 = VECTOR('',#827,1.);
#827 = DIRECTION('',(1.,0.,0.));
#828 = PCURVE('',#468,#829);
#829 = DEFINITIONAL_REPRESENTATION('',(#830),#834);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(0.,0.));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(0.,1.));
#834 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#835 = PCURVE('',#440,#836);
#836 = DEFINITIONAL_REPRESENTATION('',(#837),#841);
#837 = LINE('',#838,#839);
#838 = CARTESIAN_POINT('',(0.,0.5));
#839 = VECTOR('',#840,1.);
#840 = DIRECTION('',(1.,0.));
#841 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#842 = ORIENTED_EDGE('',*,*,#452,.T.);
#843 = ORIENTED_EDGE('',*,*,#641,.T.);
#844 = ORIENTED_EDGE('',*,*,#845,.F.);
#845 = EDGE_CURVE('',#846,#642,#848,.T.);
#846 = VERTEX_POINT('',#847);
#847 = CARTESIAN_POINT('',(-0.4,0.25,5.E-02));
#848 = SURFACE_CURVE('',#849,(#853,#860),.PCURVE_S1.);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(-0.4,0.25,5.E-02));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(0.,0.,1.));
#853 = PCURVE('',#468,#854);
#854 = DEFINITIONAL_REPRESENTATION('',(#855),#859);
#855 = LINE('',#856,#857);
#856 = CARTESIAN_POINT('',(5.E-02,1.E-01));
#857 = VECTOR('',#858,1.);
#858 = DIRECTION('',(1.,0.));
#859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#860 = PCURVE('',#782,#861);
#861 = DEFINITIONAL_REPRESENTATION('',(#862),#866);
#862 = LINE('',#863,#864);
#863 = CARTESIAN_POINT('',(0.,-7.E-02));
#864 = VECTOR('',#865,1.);
#865 = DIRECTION('',(1.,0.));
#866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#867 = ORIENTED_EDGE('',*,*,#868,.T.);
#868 = EDGE_CURVE('',#846,#800,#869,.T.);
#869 = SURFACE_CURVE('',#870,(#874,#881),.PCURVE_S1.);
#870 = LINE('',#871,#872);
#871 = CARTESIAN_POINT('',(-0.4,0.25,5.E-02));
#872 = VECTOR('',#873,1.);
#873 = DIRECTION('',(1.,0.,0.));
#874 = PCURVE('',#468,#875);
#875 = DEFINITIONAL_REPRESENTATION('',(#876),#880);
#876 = LINE('',#877,#878);
#877 = CARTESIAN_POINT('',(5.E-02,1.E-01));
#878 = VECTOR('',#879,1.);
#879 = DIRECTION('',(0.,1.));
#880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#881 = PCURVE('',#882,#887);
#882 = PLANE('',#883);
#883 = AXIS2_PLACEMENT_3D('',#884,#885,#886);
#884 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#885 = DIRECTION('',(0.,0.,1.));
#886 = DIRECTION('',(1.,0.,0.));
#887 = DEFINITIONAL_REPRESENTATION('',(#888),#892);
#888 = LINE('',#889,#890);
#889 = CARTESIAN_POINT('',(0.,7.E-02));
#890 = VECTOR('',#891,1.);
#891 = DIRECTION('',(1.,0.));
#892 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#893 = ADVANCED_FACE('',(#894),#440,.F.);
#894 = FACE_BOUND('',#895,.F.);
#895 = EDGE_LOOP('',(#896,#897,#898,#919));
#896 = ORIENTED_EDGE('',*,*,#424,.F.);
#897 = ORIENTED_EDGE('',*,*,#539,.T.);
#898 = ORIENTED_EDGE('',*,*,#899,.T.);
#899 = EDGE_CURVE('',#510,#798,#900,.T.);
#900 = SURFACE_CURVE('',#901,(#905,#912),.PCURVE_S1.);
#901 = LINE('',#902,#903);
#902 = CARTESIAN_POINT('',(-0.3,-0.25,0.));
#903 = VECTOR('',#904,1.);
#904 = DIRECTION('',(0.,1.,0.));
#905 = PCURVE('',#440,#906);
#906 = DEFINITIONAL_REPRESENTATION('',(#907),#911);
#907 = LINE('',#908,#909);
#908 = CARTESIAN_POINT('',(0.2,0.));
#909 = VECTOR('',#910,1.);
#910 = DIRECTION('',(0.,1.));
#911 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#912 = PCURVE('',#527,#913);
#913 = DEFINITIONAL_REPRESENTATION('',(#914),#918);
#914 = LINE('',#915,#916);
#915 = CARTESIAN_POINT('',(0.,0.));
#916 = VECTOR('',#917,1.);
#917 = DIRECTION('',(0.,-1.));
#918 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#919 = ORIENTED_EDGE('',*,*,#822,.F.);
#920 = ADVANCED_FACE('',(#921),#527,.T.);
#921 = FACE_BOUND('',#922,.T.);
#922 = EDGE_LOOP('',(#923,#924,#925,#926,#947,#966,#967,#988));
#923 = ORIENTED_EDGE('',*,*,#509,.F.);
#924 = ORIENTED_EDGE('',*,*,#899,.T.);
#925 = ORIENTED_EDGE('',*,*,#797,.T.);
#926 = ORIENTED_EDGE('',*,*,#927,.T.);
#927 = EDGE_CURVE('',#800,#928,#930,.T.);
#928 = VERTEX_POINT('',#929);
#929 = CARTESIAN_POINT('',(-0.3,0.18,5.E-02));
#930 = SURFACE_CURVE('',#931,(#935,#941),.PCURVE_S1.);
#931 = LINE('',#932,#933);
#932 = CARTESIAN_POINT('',(-0.3,-3.5E-02,5.E-02));
#933 = VECTOR('',#934,1.);
#934 = DIRECTION('',(0.,-1.,0.));
#935 = PCURVE('',#527,#936);
#936 = DEFINITIONAL_REPRESENTATION('',(#937),#940);
#937 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#938,#939),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.292,-0.208),.PIECEWISE_BEZIER_KNOTS.);
#938 = CARTESIAN_POINT('',(5.E-02,-0.507));
#939 = CARTESIAN_POINT('',(5.E-02,-0.423));
#940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#941 = PCURVE('',#882,#942);
#942 = DEFINITIONAL_REPRESENTATION('',(#943),#946);
#943 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#944,#945),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.292,-0.208),.PIECEWISE_BEZIER_KNOTS.);
#944 = CARTESIAN_POINT('',(0.1,7.7E-02));
#945 = CARTESIAN_POINT('',(0.1,-7.E-03));
#946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#947 = ORIENTED_EDGE('',*,*,#948,.T.);
#948 = EDGE_CURVE('',#928,#718,#949,.T.);
#949 = SURFACE_CURVE('',#950,(#954,#960),.PCURVE_S1.);
#950 = LINE('',#951,#952);
#951 = CARTESIAN_POINT('',(-0.3,0.18,2.5E-02));
#952 = VECTOR('',#953,1.);
#953 = DIRECTION('',(0.,0.,1.));
#954 = PCURVE('',#527,#955);
#955 = DEFINITIONAL_REPRESENTATION('',(#956),#959);
#956 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#957,#958),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#957 = CARTESIAN_POINT('',(2.E-02,-0.43));
#958 = CARTESIAN_POINT('',(0.38,-0.43));
#959 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#960 = PCURVE('',#756,#961);
#961 = DEFINITIONAL_REPRESENTATION('',(#962),#965);
#962 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#963,#964),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#963 = CARTESIAN_POINT('',(-3.E-02,0.1));
#964 = CARTESIAN_POINT('',(0.33,0.1));
#965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#966 = ORIENTED_EDGE('',*,*,#717,.F.);
#967 = ORIENTED_EDGE('',*,*,#968,.F.);
#968 = EDGE_CURVE('',#969,#690,#971,.T.);
#969 = VERTEX_POINT('',#970);
#970 = CARTESIAN_POINT('',(-0.3,-0.18,5.E-02));
#971 = SURFACE_CURVE('',#972,(#976,#982),.PCURVE_S1.);
#972 = LINE('',#973,#974);
#973 = CARTESIAN_POINT('',(-0.3,-0.18,2.5E-02));
#974 = VECTOR('',#975,1.);
#975 = DIRECTION('',(0.,0.,1.));
#976 = PCURVE('',#527,#977);
#977 = DEFINITIONAL_REPRESENTATION('',(#978),#981);
#978 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#979,#980),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#979 = CARTESIAN_POINT('',(2.E-02,-7.E-02));
#980 = CARTESIAN_POINT('',(0.38,-7.E-02));
#981 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#982 = PCURVE('',#705,#983);
#983 = DEFINITIONAL_REPRESENTATION('',(#984),#987);
#984 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#985,#986),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#985 = CARTESIAN_POINT('',(-3.E-02,0.1));
#986 = CARTESIAN_POINT('',(0.33,0.1));
#987 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#988 = ORIENTED_EDGE('',*,*,#989,.T.);
#989 = EDGE_CURVE('',#969,#512,#990,.T.);
#990 = SURFACE_CURVE('',#991,(#995,#1001),.PCURVE_S1.);
#991 = LINE('',#992,#993);
#992 = CARTESIAN_POINT('',(-0.3,-0.25,5.E-02));
#993 = VECTOR('',#994,1.);
#994 = DIRECTION('',(0.,-1.,0.));
#995 = PCURVE('',#527,#996);
#996 = DEFINITIONAL_REPRESENTATION('',(#997),#1000);
#997 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#998,#999),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-7.7E-02,7.E-03),.PIECEWISE_BEZIER_KNOTS.);
#998 = CARTESIAN_POINT('',(5.E-02,-7.7E-02));
#999 = CARTESIAN_POINT('',(5.E-02,7.E-03));
#1000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1001 = PCURVE('',#626,#1002);
#1002 = DEFINITIONAL_REPRESENTATION('',(#1003),#1006);
#1003 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1004,#1005),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-7.7E-02,7.E-03),.PIECEWISE_BEZIER_KNOTS.);
#1004 = CARTESIAN_POINT('',(0.1,7.7E-02));
#1005 = CARTESIAN_POINT('',(0.1,-7.E-03));
#1006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1007 = ADVANCED_FACE('',(#1008),#626,.T.);
#1008 = FACE_BOUND('',#1009,.T.);
#1009 = EDGE_LOOP('',(#1010,#1033,#1054,#1055));
#1010 = ORIENTED_EDGE('',*,*,#1011,.F.);
#1011 = EDGE_CURVE('',#1012,#969,#1014,.T.);
#1012 = VERTEX_POINT('',#1013);
#1013 = CARTESIAN_POINT('',(-0.4,-0.18,5.E-02));
#1014 = SURFACE_CURVE('',#1015,(#1019,#1026),.PCURVE_S1.);
#1015 = LINE('',#1016,#1017);
#1016 = CARTESIAN_POINT('',(-0.4,-0.18,5.E-02));
#1017 = VECTOR('',#1018,1.);
#1018 = DIRECTION('',(1.,0.,0.));
#1019 = PCURVE('',#626,#1020);
#1020 = DEFINITIONAL_REPRESENTATION('',(#1021),#1025);
#1021 = LINE('',#1022,#1023);
#1022 = CARTESIAN_POINT('',(0.,7.E-02));
#1023 = VECTOR('',#1024,1.);
#1024 = DIRECTION('',(1.,0.));
#1025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1026 = PCURVE('',#705,#1027);
#1027 = DEFINITIONAL_REPRESENTATION('',(#1028),#1032);
#1028 = LINE('',#1029,#1030);
#1029 = CARTESIAN_POINT('',(0.,0.));
#1030 = VECTOR('',#1031,1.);
#1031 = DIRECTION('',(0.,1.));
#1032 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1033 = ORIENTED_EDGE('',*,*,#1034,.F.);
#1034 = EDGE_CURVE('',#585,#1012,#1035,.T.);
#1035 = SURFACE_CURVE('',#1036,(#1040,#1047),.PCURVE_S1.);
#1036 = LINE('',#1037,#1038);
#1037 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#1038 = VECTOR('',#1039,1.);
#1039 = DIRECTION('',(0.,1.,0.));
#1040 = PCURVE('',#626,#1041);
#1041 = DEFINITIONAL_REPRESENTATION('',(#1042),#1046);
#1042 = LINE('',#1043,#1044);
#1043 = CARTESIAN_POINT('',(0.,0.));
#1044 = VECTOR('',#1045,1.);
#1045 = DIRECTION('',(0.,1.));
#1046 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1047 = PCURVE('',#600,#1048);
#1048 = DEFINITIONAL_REPRESENTATION('',(#1049),#1053);
#1049 = LINE('',#1050,#1051);
#1050 = CARTESIAN_POINT('',(0.,0.));
#1051 = VECTOR('',#1052,1.);
#1052 = DIRECTION('',(0.,-1.));
#1053 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1054 = ORIENTED_EDGE('',*,*,#612,.T.);
#1055 = ORIENTED_EDGE('',*,*,#989,.F.);
#1056 = ADVANCED_FACE('',(#1057),#600,.T.);
#1057 = FACE_BOUND('',#1058,.T.);
#1058 = EDGE_LOOP('',(#1059,#1060,#1061,#1082));
#1059 = ORIENTED_EDGE('',*,*,#584,.F.);
#1060 = ORIENTED_EDGE('',*,*,#1034,.T.);
#1061 = ORIENTED_EDGE('',*,*,#1062,.T.);
#1062 = EDGE_CURVE('',#1012,#667,#1063,.T.);
#1063 = SURFACE_CURVE('',#1064,(#1068,#1075),.PCURVE_S1.);
#1064 = LINE('',#1065,#1066);
#1065 = CARTESIAN_POINT('',(-0.4,-0.18,5.E-02));
#1066 = VECTOR('',#1067,1.);
#1067 = DIRECTION('',(0.,0.,1.));
#1068 = PCURVE('',#600,#1069);
#1069 = DEFINITIONAL_REPRESENTATION('',(#1070),#1074);
#1070 = LINE('',#1071,#1072);
#1071 = CARTESIAN_POINT('',(0.,-7.E-02));
#1072 = VECTOR('',#1073,1.);
#1073 = DIRECTION('',(1.,0.));
#1074 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1075 = PCURVE('',#705,#1076);
#1076 = DEFINITIONAL_REPRESENTATION('',(#1077),#1081);
#1077 = LINE('',#1078,#1079);
#1078 = CARTESIAN_POINT('',(0.,0.));
#1079 = VECTOR('',#1080,1.);
#1080 = DIRECTION('',(1.,0.));
#1081 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1082 = ORIENTED_EDGE('',*,*,#666,.F.);
#1083 = ADVANCED_FACE('',(#1084),#782,.T.);
#1084 = FACE_BOUND('',#1085,.T.);
#1085 = EDGE_LOOP('',(#1086,#1109,#1130,#1131));
#1086 = ORIENTED_EDGE('',*,*,#1087,.F.);
#1087 = EDGE_CURVE('',#1088,#741,#1090,.T.);
#1088 = VERTEX_POINT('',#1089);
#1089 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1090 = SURFACE_CURVE('',#1091,(#1095,#1102),.PCURVE_S1.);
#1091 = LINE('',#1092,#1093);
#1092 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1093 = VECTOR('',#1094,1.);
#1094 = DIRECTION('',(0.,0.,1.));
#1095 = PCURVE('',#782,#1096);
#1096 = DEFINITIONAL_REPRESENTATION('',(#1097),#1101);
#1097 = LINE('',#1098,#1099);
#1098 = CARTESIAN_POINT('',(0.,0.));
#1099 = VECTOR('',#1100,1.);
#1100 = DIRECTION('',(1.,0.));
#1101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1102 = PCURVE('',#756,#1103);
#1103 = DEFINITIONAL_REPRESENTATION('',(#1104),#1108);
#1104 = LINE('',#1105,#1106);
#1105 = CARTESIAN_POINT('',(0.,0.));
#1106 = VECTOR('',#1107,1.);
#1107 = DIRECTION('',(1.,0.));
#1108 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1109 = ORIENTED_EDGE('',*,*,#1110,.T.);
#1110 = EDGE_CURVE('',#1088,#846,#1111,.T.);
#1111 = SURFACE_CURVE('',#1112,(#1116,#1123),.PCURVE_S1.);
#1112 = LINE('',#1113,#1114);
#1113 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1114 = VECTOR('',#1115,1.);
#1115 = DIRECTION('',(0.,1.,0.));
#1116 = PCURVE('',#782,#1117);
#1117 = DEFINITIONAL_REPRESENTATION('',(#1118),#1122);
#1118 = LINE('',#1119,#1120);
#1119 = CARTESIAN_POINT('',(0.,0.));
#1120 = VECTOR('',#1121,1.);
#1121 = DIRECTION('',(0.,-1.));
#1122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1123 = PCURVE('',#882,#1124);
#1124 = DEFINITIONAL_REPRESENTATION('',(#1125),#1129);
#1125 = LINE('',#1126,#1127);
#1126 = CARTESIAN_POINT('',(0.,0.));
#1127 = VECTOR('',#1128,1.);
#1128 = DIRECTION('',(0.,1.));
#1129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1130 = ORIENTED_EDGE('',*,*,#845,.T.);
#1131 = ORIENTED_EDGE('',*,*,#768,.F.);
#1132 = ADVANCED_FACE('',(#1133),#756,.T.);
#1133 = FACE_BOUND('',#1134,.T.);
#1134 = EDGE_LOOP('',(#1135,#1156,#1157,#1158));
#1135 = ORIENTED_EDGE('',*,*,#1136,.F.);
#1136 = EDGE_CURVE('',#1088,#928,#1137,.T.);
#1137 = SURFACE_CURVE('',#1138,(#1142,#1149),.PCURVE_S1.);
#1138 = LINE('',#1139,#1140);
#1139 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1140 = VECTOR('',#1141,1.);
#1141 = DIRECTION('',(1.,0.,0.));
#1142 = PCURVE('',#756,#1143);
#1143 = DEFINITIONAL_REPRESENTATION('',(#1144),#1148);
#1144 = LINE('',#1145,#1146);
#1145 = CARTESIAN_POINT('',(0.,0.));
#1146 = VECTOR('',#1147,1.);
#1147 = DIRECTION('',(0.,1.));
#1148 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1149 = PCURVE('',#882,#1150);
#1150 = DEFINITIONAL_REPRESENTATION('',(#1151),#1155);
#1151 = LINE('',#1152,#1153);
#1152 = CARTESIAN_POINT('',(0.,0.));
#1153 = VECTOR('',#1154,1.);
#1154 = DIRECTION('',(1.,0.));
#1155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1156 = ORIENTED_EDGE('',*,*,#1087,.T.);
#1157 = ORIENTED_EDGE('',*,*,#740,.T.);
#1158 = ORIENTED_EDGE('',*,*,#948,.F.);
#1159 = ADVANCED_FACE('',(#1160),#705,.F.);
#1160 = FACE_BOUND('',#1161,.F.);
#1161 = EDGE_LOOP('',(#1162,#1163,#1164,#1165));
#1162 = ORIENTED_EDGE('',*,*,#1011,.F.);
#1163 = ORIENTED_EDGE('',*,*,#1062,.T.);
#1164 = ORIENTED_EDGE('',*,*,#689,.T.);
#1165 = ORIENTED_EDGE('',*,*,#968,.F.);
#1166 = ADVANCED_FACE('',(#1167),#882,.T.);
#1167 = FACE_BOUND('',#1168,.T.);
#1168 = EDGE_LOOP('',(#1169,#1170,#1171,#1172));
#1169 = ORIENTED_EDGE('',*,*,#868,.F.);
#1170 = ORIENTED_EDGE('',*,*,#1110,.F.);
#1171 = ORIENTED_EDGE('',*,*,#1136,.T.);
#1172 = ORIENTED_EDGE('',*,*,#927,.F.);
#1173 = MANIFOLD_SOLID_BREP('',#1174);
#1174 = CLOSED_SHELL('',(#1175,#1399,#1526,#1575,#1624,#1746,#1795,#1844
    ,#1915,#1942,#1949,#1956));
#1175 = ADVANCED_FACE('',(#1176),#1190,.F.);
#1176 = FACE_BOUND('',#1177,.F.);
#1177 = EDGE_LOOP('',(#1178,#1213,#1241,#1269,#1295,#1321,#1349,#1375));
#1178 = ORIENTED_EDGE('',*,*,#1179,.F.);
#1179 = EDGE_CURVE('',#1180,#1182,#1184,.T.);
#1180 = VERTEX_POINT('',#1181);
#1181 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1182 = VERTEX_POINT('',#1183);
#1183 = CARTESIAN_POINT('',(0.3,-0.25,5.E-02));
#1184 = SURFACE_CURVE('',#1185,(#1189,#1201),.PCURVE_S1.);
#1185 = LINE('',#1186,#1187);
#1186 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1187 = VECTOR('',#1188,1.);
#1188 = DIRECTION('',(0.,0.,1.));
#1189 = PCURVE('',#1190,#1195);
#1190 = PLANE('',#1191);
#1191 = AXIS2_PLACEMENT_3D('',#1192,#1193,#1194);
#1192 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1193 = DIRECTION('',(1.,0.,0.));
#1194 = DIRECTION('',(0.,0.,1.));
#1195 = DEFINITIONAL_REPRESENTATION('',(#1196),#1200);
#1196 = LINE('',#1197,#1198);
#1197 = CARTESIAN_POINT('',(0.,0.));
#1198 = VECTOR('',#1199,1.);
#1199 = DIRECTION('',(1.,0.));
#1200 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1201 = PCURVE('',#1202,#1207);
#1202 = PLANE('',#1203);
#1203 = AXIS2_PLACEMENT_3D('',#1204,#1205,#1206);
#1204 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1205 = DIRECTION('',(0.,1.,0.));
#1206 = DIRECTION('',(0.,0.,1.));
#1207 = DEFINITIONAL_REPRESENTATION('',(#1208),#1212);
#1208 = LINE('',#1209,#1210);
#1209 = CARTESIAN_POINT('',(0.,0.));
#1210 = VECTOR('',#1211,1.);
#1211 = DIRECTION('',(1.,0.));
#1212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1213 = ORIENTED_EDGE('',*,*,#1214,.T.);
#1214 = EDGE_CURVE('',#1180,#1215,#1217,.T.);
#1215 = VERTEX_POINT('',#1216);
#1216 = CARTESIAN_POINT('',(0.3,0.25,0.));
#1217 = SURFACE_CURVE('',#1218,(#1222,#1229),.PCURVE_S1.);
#1218 = LINE('',#1219,#1220);
#1219 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1220 = VECTOR('',#1221,1.);
#1221 = DIRECTION('',(0.,1.,0.));
#1222 = PCURVE('',#1190,#1223);
#1223 = DEFINITIONAL_REPRESENTATION('',(#1224),#1228);
#1224 = LINE('',#1225,#1226);
#1225 = CARTESIAN_POINT('',(0.,0.));
#1226 = VECTOR('',#1227,1.);
#1227 = DIRECTION('',(0.,-1.));
#1228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1229 = PCURVE('',#1230,#1235);
#1230 = PLANE('',#1231);
#1231 = AXIS2_PLACEMENT_3D('',#1232,#1233,#1234);
#1232 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1233 = DIRECTION('',(0.,0.,1.));
#1234 = DIRECTION('',(1.,0.,0.));
#1235 = DEFINITIONAL_REPRESENTATION('',(#1236),#1240);
#1236 = LINE('',#1237,#1238);
#1237 = CARTESIAN_POINT('',(0.,0.));
#1238 = VECTOR('',#1239,1.);
#1239 = DIRECTION('',(0.,1.));
#1240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1241 = ORIENTED_EDGE('',*,*,#1242,.T.);
#1242 = EDGE_CURVE('',#1215,#1243,#1245,.T.);
#1243 = VERTEX_POINT('',#1244);
#1244 = CARTESIAN_POINT('',(0.3,0.25,5.E-02));
#1245 = SURFACE_CURVE('',#1246,(#1250,#1257),.PCURVE_S1.);
#1246 = LINE('',#1247,#1248);
#1247 = CARTESIAN_POINT('',(0.3,0.25,0.));
#1248 = VECTOR('',#1249,1.);
#1249 = DIRECTION('',(0.,0.,1.));
#1250 = PCURVE('',#1190,#1251);
#1251 = DEFINITIONAL_REPRESENTATION('',(#1252),#1256);
#1252 = LINE('',#1253,#1254);
#1253 = CARTESIAN_POINT('',(0.,-0.5));
#1254 = VECTOR('',#1255,1.);
#1255 = DIRECTION('',(1.,0.));
#1256 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1257 = PCURVE('',#1258,#1263);
#1258 = PLANE('',#1259);
#1259 = AXIS2_PLACEMENT_3D('',#1260,#1261,#1262);
#1260 = CARTESIAN_POINT('',(0.3,0.25,0.));
#1261 = DIRECTION('',(0.,1.,0.));
#1262 = DIRECTION('',(0.,0.,1.));
#1263 = DEFINITIONAL_REPRESENTATION('',(#1264),#1268);
#1264 = LINE('',#1265,#1266);
#1265 = CARTESIAN_POINT('',(0.,0.));
#1266 = VECTOR('',#1267,1.);
#1267 = DIRECTION('',(1.,0.));
#1268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1269 = ORIENTED_EDGE('',*,*,#1270,.T.);
#1270 = EDGE_CURVE('',#1243,#1271,#1273,.T.);
#1271 = VERTEX_POINT('',#1272);
#1272 = CARTESIAN_POINT('',(0.3,0.18,5.E-02));
#1273 = SURFACE_CURVE('',#1274,(#1278,#1284),.PCURVE_S1.);
#1274 = LINE('',#1275,#1276);
#1275 = CARTESIAN_POINT('',(0.3,-3.5E-02,5.E-02));
#1276 = VECTOR('',#1277,1.);
#1277 = DIRECTION('',(0.,-1.,0.));
#1278 = PCURVE('',#1190,#1279);
#1279 = DEFINITIONAL_REPRESENTATION('',(#1280),#1283);
#1280 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1281,#1282),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.292,-0.208),.PIECEWISE_BEZIER_KNOTS.);
#1281 = CARTESIAN_POINT('',(5.E-02,-0.507));
#1282 = CARTESIAN_POINT('',(5.E-02,-0.423));
#1283 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1284 = PCURVE('',#1285,#1290);
#1285 = PLANE('',#1286);
#1286 = AXIS2_PLACEMENT_3D('',#1287,#1288,#1289);
#1287 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1288 = DIRECTION('',(0.,0.,1.));
#1289 = DIRECTION('',(1.,0.,0.));
#1290 = DEFINITIONAL_REPRESENTATION('',(#1291),#1294);
#1291 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1292,#1293),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.292,-0.208),.PIECEWISE_BEZIER_KNOTS.);
#1292 = CARTESIAN_POINT('',(0.7,7.7E-02));
#1293 = CARTESIAN_POINT('',(0.7,-7.E-03));
#1294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1295 = ORIENTED_EDGE('',*,*,#1296,.T.);
#1296 = EDGE_CURVE('',#1271,#1297,#1299,.T.);
#1297 = VERTEX_POINT('',#1298);
#1298 = CARTESIAN_POINT('',(0.3,0.18,0.35));
#1299 = SURFACE_CURVE('',#1300,(#1304,#1310),.PCURVE_S1.);
#1300 = LINE('',#1301,#1302);
#1301 = CARTESIAN_POINT('',(0.3,0.18,2.5E-02));
#1302 = VECTOR('',#1303,1.);
#1303 = DIRECTION('',(0.,0.,1.));
#1304 = PCURVE('',#1190,#1305);
#1305 = DEFINITIONAL_REPRESENTATION('',(#1306),#1309);
#1306 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1307,#1308),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#1307 = CARTESIAN_POINT('',(2.E-02,-0.43));
#1308 = CARTESIAN_POINT('',(0.38,-0.43));
#1309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1310 = PCURVE('',#1311,#1316);
#1311 = PLANE('',#1312);
#1312 = AXIS2_PLACEMENT_3D('',#1313,#1314,#1315);
#1313 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1314 = DIRECTION('',(0.,1.,0.));
#1315 = DIRECTION('',(0.,0.,1.));
#1316 = DEFINITIONAL_REPRESENTATION('',(#1317),#1320);
#1317 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1318,#1319),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#1318 = CARTESIAN_POINT('',(-3.E-02,0.7));
#1319 = CARTESIAN_POINT('',(0.33,0.7));
#1320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1321 = ORIENTED_EDGE('',*,*,#1322,.F.);
#1322 = EDGE_CURVE('',#1323,#1297,#1325,.T.);
#1323 = VERTEX_POINT('',#1324);
#1324 = CARTESIAN_POINT('',(0.3,-0.18,0.35));
#1325 = SURFACE_CURVE('',#1326,(#1330,#1337),.PCURVE_S1.);
#1326 = LINE('',#1327,#1328);
#1327 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#1328 = VECTOR('',#1329,1.);
#1329 = DIRECTION('',(0.,1.,0.));
#1330 = PCURVE('',#1190,#1331);
#1331 = DEFINITIONAL_REPRESENTATION('',(#1332),#1336);
#1332 = LINE('',#1333,#1334);
#1333 = CARTESIAN_POINT('',(0.35,0.));
#1334 = VECTOR('',#1335,1.);
#1335 = DIRECTION('',(0.,-1.));
#1336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1337 = PCURVE('',#1338,#1343);
#1338 = PLANE('',#1339);
#1339 = AXIS2_PLACEMENT_3D('',#1340,#1341,#1342);
#1340 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#1341 = DIRECTION('',(0.,0.,1.));
#1342 = DIRECTION('',(1.,0.,0.));
#1343 = DEFINITIONAL_REPRESENTATION('',(#1344),#1348);
#1344 = LINE('',#1345,#1346);
#1345 = CARTESIAN_POINT('',(0.,0.));
#1346 = VECTOR('',#1347,1.);
#1347 = DIRECTION('',(0.,1.));
#1348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1349 = ORIENTED_EDGE('',*,*,#1350,.F.);
#1350 = EDGE_CURVE('',#1351,#1323,#1353,.T.);
#1351 = VERTEX_POINT('',#1352);
#1352 = CARTESIAN_POINT('',(0.3,-0.18,5.E-02));
#1353 = SURFACE_CURVE('',#1354,(#1358,#1364),.PCURVE_S1.);
#1354 = LINE('',#1355,#1356);
#1355 = CARTESIAN_POINT('',(0.3,-0.18,2.5E-02));
#1356 = VECTOR('',#1357,1.);
#1357 = DIRECTION('',(0.,0.,1.));
#1358 = PCURVE('',#1190,#1359);
#1359 = DEFINITIONAL_REPRESENTATION('',(#1360),#1363);
#1360 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1361,#1362),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#1361 = CARTESIAN_POINT('',(2.E-02,-7.E-02));
#1362 = CARTESIAN_POINT('',(0.38,-7.E-02));
#1363 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1364 = PCURVE('',#1365,#1370);
#1365 = PLANE('',#1366);
#1366 = AXIS2_PLACEMENT_3D('',#1367,#1368,#1369);
#1367 = CARTESIAN_POINT('',(-0.4,-0.18,5.E-02));
#1368 = DIRECTION('',(0.,1.,0.));
#1369 = DIRECTION('',(0.,0.,1.));
#1370 = DEFINITIONAL_REPRESENTATION('',(#1371),#1374);
#1371 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1372,#1373),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-5.E-03,0.355),.PIECEWISE_BEZIER_KNOTS.);
#1372 = CARTESIAN_POINT('',(-3.E-02,0.7));
#1373 = CARTESIAN_POINT('',(0.33,0.7));
#1374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1375 = ORIENTED_EDGE('',*,*,#1376,.T.);
#1376 = EDGE_CURVE('',#1351,#1182,#1377,.T.);
#1377 = SURFACE_CURVE('',#1378,(#1382,#1388),.PCURVE_S1.);
#1378 = LINE('',#1379,#1380);
#1379 = CARTESIAN_POINT('',(0.3,-0.25,5.E-02));
#1380 = VECTOR('',#1381,1.);
#1381 = DIRECTION('',(0.,-1.,0.));
#1382 = PCURVE('',#1190,#1383);
#1383 = DEFINITIONAL_REPRESENTATION('',(#1384),#1387);
#1384 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1385,#1386),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-7.7E-02,7.E-03),.PIECEWISE_BEZIER_KNOTS.);
#1385 = CARTESIAN_POINT('',(5.E-02,-7.7E-02));
#1386 = CARTESIAN_POINT('',(5.E-02,7.E-03));
#1387 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1388 = PCURVE('',#1389,#1394);
#1389 = PLANE('',#1390);
#1390 = AXIS2_PLACEMENT_3D('',#1391,#1392,#1393);
#1391 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#1392 = DIRECTION('',(0.,0.,1.));
#1393 = DIRECTION('',(1.,0.,0.));
#1394 = DEFINITIONAL_REPRESENTATION('',(#1395),#1398);
#1395 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1396,#1397),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-7.7E-02,7.E-03),.PIECEWISE_BEZIER_KNOTS.);
#1396 = CARTESIAN_POINT('',(0.7,7.7E-02));
#1397 = CARTESIAN_POINT('',(0.7,-7.E-03));
#1398 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1399 = ADVANCED_FACE('',(#1400),#1202,.F.);
#1400 = FACE_BOUND('',#1401,.F.);
#1401 = EDGE_LOOP('',(#1402,#1432,#1453,#1454,#1477,#1505));
#1402 = ORIENTED_EDGE('',*,*,#1403,.F.);
#1403 = EDGE_CURVE('',#1404,#1406,#1408,.T.);
#1404 = VERTEX_POINT('',#1405);
#1405 = CARTESIAN_POINT('',(0.5,-0.25,0.));
#1406 = VERTEX_POINT('',#1407);
#1407 = CARTESIAN_POINT('',(0.5,-0.25,0.35));
#1408 = SURFACE_CURVE('',#1409,(#1413,#1420),.PCURVE_S1.);
#1409 = LINE('',#1410,#1411);
#1410 = CARTESIAN_POINT('',(0.5,-0.25,0.));
#1411 = VECTOR('',#1412,1.);
#1412 = DIRECTION('',(0.,0.,1.));
#1413 = PCURVE('',#1202,#1414);
#1414 = DEFINITIONAL_REPRESENTATION('',(#1415),#1419);
#1415 = LINE('',#1416,#1417);
#1416 = CARTESIAN_POINT('',(0.,0.2));
#1417 = VECTOR('',#1418,1.);
#1418 = DIRECTION('',(1.,0.));
#1419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1420 = PCURVE('',#1421,#1426);
#1421 = PLANE('',#1422);
#1422 = AXIS2_PLACEMENT_3D('',#1423,#1424,#1425);
#1423 = CARTESIAN_POINT('',(0.5,-0.25,0.));
#1424 = DIRECTION('',(1.,0.,0.));
#1425 = DIRECTION('',(0.,0.,1.));
#1426 = DEFINITIONAL_REPRESENTATION('',(#1427),#1431);
#1427 = LINE('',#1428,#1429);
#1428 = CARTESIAN_POINT('',(0.,0.));
#1429 = VECTOR('',#1430,1.);
#1430 = DIRECTION('',(1.,0.));
#1431 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1432 = ORIENTED_EDGE('',*,*,#1433,.F.);
#1433 = EDGE_CURVE('',#1180,#1404,#1434,.T.);
#1434 = SURFACE_CURVE('',#1435,(#1439,#1446),.PCURVE_S1.);
#1435 = LINE('',#1436,#1437);
#1436 = CARTESIAN_POINT('',(0.3,-0.25,0.));
#1437 = VECTOR('',#1438,1.);
#1438 = DIRECTION('',(1.,0.,0.));
#1439 = PCURVE('',#1202,#1440);
#1440 = DEFINITIONAL_REPRESENTATION('',(#1441),#1445);
#1441 = LINE('',#1442,#1443);
#1442 = CARTESIAN_POINT('',(0.,0.));
#1443 = VECTOR('',#1444,1.);
#1444 = DIRECTION('',(0.,1.));
#1445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1446 = PCURVE('',#1230,#1447);
#1447 = DEFINITIONAL_REPRESENTATION('',(#1448),#1452);
#1448 = LINE('',#1449,#1450);
#1449 = CARTESIAN_POINT('',(0.,0.));
#1450 = VECTOR('',#1451,1.);
#1451 = DIRECTION('',(1.,0.));
#1452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1453 = ORIENTED_EDGE('',*,*,#1179,.T.);
#1454 = ORIENTED_EDGE('',*,*,#1455,.T.);
#1455 = EDGE_CURVE('',#1182,#1456,#1458,.T.);
#1456 = VERTEX_POINT('',#1457);
#1457 = CARTESIAN_POINT('',(0.4,-0.25,5.E-02));
#1458 = SURFACE_CURVE('',#1459,(#1463,#1470),.PCURVE_S1.);
#1459 = LINE('',#1460,#1461);
#1460 = CARTESIAN_POINT('',(-0.4,-0.25,5.E-02));
#1461 = VECTOR('',#1462,1.);
#1462 = DIRECTION('',(1.,0.,0.));
#1463 = PCURVE('',#1202,#1464);
#1464 = DEFINITIONAL_REPRESENTATION('',(#1465),#1469);
#1465 = LINE('',#1466,#1467);
#1466 = CARTESIAN_POINT('',(5.E-02,-0.7));
#1467 = VECTOR('',#1468,1.);
#1468 = DIRECTION('',(0.,1.));
#1469 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1470 = PCURVE('',#1389,#1471);
#1471 = DEFINITIONAL_REPRESENTATION('',(#1472),#1476);
#1472 = LINE('',#1473,#1474);
#1473 = CARTESIAN_POINT('',(0.,0.));
#1474 = VECTOR('',#1475,1.);
#1475 = DIRECTION('',(1.,0.));
#1476 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1477 = ORIENTED_EDGE('',*,*,#1478,.T.);
#1478 = EDGE_CURVE('',#1456,#1479,#1481,.T.);
#1479 = VERTEX_POINT('',#1480);
#1480 = CARTESIAN_POINT('',(0.4,-0.25,0.35));
#1481 = SURFACE_CURVE('',#1482,(#1486,#1493),.PCURVE_S1.);
#1482 = LINE('',#1483,#1484);
#1483 = CARTESIAN_POINT('',(0.4,-0.25,5.E-02));
#1484 = VECTOR('',#1485,1.);
#1485 = DIRECTION('',(0.,0.,1.));
#1486 = PCURVE('',#1202,#1487);
#1487 = DEFINITIONAL_REPRESENTATION('',(#1488),#1492);
#1488 = LINE('',#1489,#1490);
#1489 = CARTESIAN_POINT('',(5.E-02,0.1));
#1490 = VECTOR('',#1491,1.);
#1491 = DIRECTION('',(1.,0.));
#1492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1493 = PCURVE('',#1494,#1499);
#1494 = PLANE('',#1495);
#1495 = AXIS2_PLACEMENT_3D('',#1496,#1497,#1498);
#1496 = CARTESIAN_POINT('',(0.4,-0.25,5.E-02));
#1497 = DIRECTION('',(1.,0.,0.));
#1498 = DIRECTION('',(0.,0.,1.));
#1499 = DEFINITIONAL_REPRESENTATION('',(#1500),#1504);
#1500 = LINE('',#1501,#1502);
#1501 = CARTESIAN_POINT('',(0.,0.));
#1502 = VECTOR('',#1503,1.);
#1503 = DIRECTION('',(1.,0.));
#1504 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1505 = ORIENTED_EDGE('',*,*,#1506,.T.);
#1506 = EDGE_CURVE('',#1479,#1406,#1507,.T.);
#1507 = SURFACE_CURVE('',#1508,(#1512,#1519),.PCURVE_S1.);
#1508 = LINE('',#1509,#1510);
#1509 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#1510 = VECTOR('',#1511,1.);
#1511 = DIRECTION('',(1.,0.,0.));
#1512 = PCURVE('',#1202,#1513);
#1513 = DEFINITIONAL_REPRESENTATION('',(#1514),#1518);
#1514 = LINE('',#1515,#1516);
#1515 = CARTESIAN_POINT('',(0.35,0.));
#1516 = VECTOR('',#1517,1.);
#1517 = DIRECTION('',(0.,1.));
#1518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1519 = PCURVE('',#1338,#1520);
#1520 = DEFINITIONAL_REPRESENTATION('',(#1521),#1525);
#1521 = LINE('',#1522,#1523);
#1522 = CARTESIAN_POINT('',(0.,0.));
#1523 = VECTOR('',#1524,1.);
#1524 = DIRECTION('',(1.,0.));
#1525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1526 = ADVANCED_FACE('',(#1527),#1389,.T.);
#1527 = FACE_BOUND('',#1528,.T.);
#1528 = EDGE_LOOP('',(#1529,#1552,#1553,#1554));
#1529 = ORIENTED_EDGE('',*,*,#1530,.F.);
#1530 = EDGE_CURVE('',#1351,#1531,#1533,.T.);
#1531 = VERTEX_POINT('',#1532);
#1532 = CARTESIAN_POINT('',(0.4,-0.18,5.E-02));
#1533 = SURFACE_CURVE('',#1534,(#1538,#1545),.PCURVE_S1.);
#1534 = LINE('',#1535,#1536);
#1535 = CARTESIAN_POINT('',(-0.4,-0.18,5.E-02));
#1536 = VECTOR('',#1537,1.);
#1537 = DIRECTION('',(1.,0.,0.));
#1538 = PCURVE('',#1389,#1539);
#1539 = DEFINITIONAL_REPRESENTATION('',(#1540),#1544);
#1540 = LINE('',#1541,#1542);
#1541 = CARTESIAN_POINT('',(0.,7.E-02));
#1542 = VECTOR('',#1543,1.);
#1543 = DIRECTION('',(1.,0.));
#1544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1545 = PCURVE('',#1365,#1546);
#1546 = DEFINITIONAL_REPRESENTATION('',(#1547),#1551);
#1547 = LINE('',#1548,#1549);
#1548 = CARTESIAN_POINT('',(0.,0.));
#1549 = VECTOR('',#1550,1.);
#1550 = DIRECTION('',(0.,1.));
#1551 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1552 = ORIENTED_EDGE('',*,*,#1376,.T.);
#1553 = ORIENTED_EDGE('',*,*,#1455,.T.);
#1554 = ORIENTED_EDGE('',*,*,#1555,.T.);
#1555 = EDGE_CURVE('',#1456,#1531,#1556,.T.);
#1556 = SURFACE_CURVE('',#1557,(#1561,#1568),.PCURVE_S1.);
#1557 = LINE('',#1558,#1559);
#1558 = CARTESIAN_POINT('',(0.4,-0.25,5.E-02));
#1559 = VECTOR('',#1560,1.);
#1560 = DIRECTION('',(0.,1.,0.));
#1561 = PCURVE('',#1389,#1562);
#1562 = DEFINITIONAL_REPRESENTATION('',(#1563),#1567);
#1563 = LINE('',#1564,#1565);
#1564 = CARTESIAN_POINT('',(0.8,0.));
#1565 = VECTOR('',#1566,1.);
#1566 = DIRECTION('',(0.,1.));
#1567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1568 = PCURVE('',#1494,#1569);
#1569 = DEFINITIONAL_REPRESENTATION('',(#1570),#1574);
#1570 = LINE('',#1571,#1572);
#1571 = CARTESIAN_POINT('',(0.,0.));
#1572 = VECTOR('',#1573,1.);
#1573 = DIRECTION('',(0.,-1.));
#1574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1575 = ADVANCED_FACE('',(#1576),#1365,.F.);
#1576 = FACE_BOUND('',#1577,.F.);
#1577 = EDGE_LOOP('',(#1578,#1579,#1580,#1603));
#1578 = ORIENTED_EDGE('',*,*,#1530,.F.);
#1579 = ORIENTED_EDGE('',*,*,#1350,.T.);
#1580 = ORIENTED_EDGE('',*,*,#1581,.T.);
#1581 = EDGE_CURVE('',#1323,#1582,#1584,.T.);
#1582 = VERTEX_POINT('',#1583);
#1583 = CARTESIAN_POINT('',(0.4,-0.18,0.35));
#1584 = SURFACE_CURVE('',#1585,(#1589,#1596),.PCURVE_S1.);
#1585 = LINE('',#1586,#1587);
#1586 = CARTESIAN_POINT('',(-0.4,-0.18,0.35));
#1587 = VECTOR('',#1588,1.);
#1588 = DIRECTION('',(1.,0.,0.));
#1589 = PCURVE('',#1365,#1590);
#1590 = DEFINITIONAL_REPRESENTATION('',(#1591),#1595);
#1591 = LINE('',#1592,#1593);
#1592 = CARTESIAN_POINT('',(0.3,0.));
#1593 = VECTOR('',#1594,1.);
#1594 = DIRECTION('',(0.,1.));
#1595 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1596 = PCURVE('',#1338,#1597);
#1597 = DEFINITIONAL_REPRESENTATION('',(#1598),#1602);
#1598 = LINE('',#1599,#1600);
#1599 = CARTESIAN_POINT('',(-0.7,7.E-02));
#1600 = VECTOR('',#1601,1.);
#1601 = DIRECTION('',(1.,0.));
#1602 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1603 = ORIENTED_EDGE('',*,*,#1604,.F.);
#1604 = EDGE_CURVE('',#1531,#1582,#1605,.T.);
#1605 = SURFACE_CURVE('',#1606,(#1610,#1617),.PCURVE_S1.);
#1606 = LINE('',#1607,#1608);
#1607 = CARTESIAN_POINT('',(0.4,-0.18,5.E-02));
#1608 = VECTOR('',#1609,1.);
#1609 = DIRECTION('',(0.,0.,1.));
#1610 = PCURVE('',#1365,#1611);
#1611 = DEFINITIONAL_REPRESENTATION('',(#1612),#1616);
#1612 = LINE('',#1613,#1614);
#1613 = CARTESIAN_POINT('',(0.,0.8));
#1614 = VECTOR('',#1615,1.);
#1615 = DIRECTION('',(1.,0.));
#1616 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1617 = PCURVE('',#1494,#1618);
#1618 = DEFINITIONAL_REPRESENTATION('',(#1619),#1623);
#1619 = LINE('',#1620,#1621);
#1620 = CARTESIAN_POINT('',(0.,-7.E-02));
#1621 = VECTOR('',#1622,1.);
#1622 = DIRECTION('',(1.,0.));
#1623 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1624 = ADVANCED_FACE('',(#1625),#1338,.T.);
#1625 = FACE_BOUND('',#1626,.T.);
#1626 = EDGE_LOOP('',(#1627,#1628,#1629,#1650,#1651,#1674,#1697,#1725));
#1627 = ORIENTED_EDGE('',*,*,#1322,.F.);
#1628 = ORIENTED_EDGE('',*,*,#1581,.T.);
#1629 = ORIENTED_EDGE('',*,*,#1630,.F.);
#1630 = EDGE_CURVE('',#1479,#1582,#1631,.T.);
#1631 = SURFACE_CURVE('',#1632,(#1636,#1643),.PCURVE_S1.);
#1632 = LINE('',#1633,#1634);
#1633 = CARTESIAN_POINT('',(0.4,-0.25,0.35));
#1634 = VECTOR('',#1635,1.);
#1635 = DIRECTION('',(0.,1.,0.));
#1636 = PCURVE('',#1338,#1637);
#1637 = DEFINITIONAL_REPRESENTATION('',(#1638),#1642);
#1638 = LINE('',#1639,#1640);
#1639 = CARTESIAN_POINT('',(0.1,0.));
#1640 = VECTOR('',#1641,1.);
#1641 = DIRECTION('',(0.,1.));
#1642 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1643 = PCURVE('',#1494,#1644);
#1644 = DEFINITIONAL_REPRESENTATION('',(#1645),#1649);
#1645 = LINE('',#1646,#1647);
#1646 = CARTESIAN_POINT('',(0.3,0.));
#1647 = VECTOR('',#1648,1.);
#1648 = DIRECTION('',(0.,-1.));
#1649 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1650 = ORIENTED_EDGE('',*,*,#1506,.T.);
#1651 = ORIENTED_EDGE('',*,*,#1652,.T.);
#1652 = EDGE_CURVE('',#1406,#1653,#1655,.T.);
#1653 = VERTEX_POINT('',#1654);
#1654 = CARTESIAN_POINT('',(0.5,0.25,0.35));
#1655 = SURFACE_CURVE('',#1656,(#1660,#1667),.PCURVE_S1.);
#1656 = LINE('',#1657,#1658);
#1657 = CARTESIAN_POINT('',(0.5,-0.25,0.35));
#1658 = VECTOR('',#1659,1.);
#1659 = DIRECTION('',(0.,1.,0.));
#1660 = PCURVE('',#1338,#1661);
#1661 = DEFINITIONAL_REPRESENTATION('',(#1662),#1666);
#1662 = LINE('',#1663,#1664);
#1663 = CARTESIAN_POINT('',(0.2,0.));
#1664 = VECTOR('',#1665,1.);
#1665 = DIRECTION('',(0.,1.));
#1666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1667 = PCURVE('',#1421,#1668);
#1668 = DEFINITIONAL_REPRESENTATION('',(#1669),#1673);
#1669 = LINE('',#1670,#1671);
#1670 = CARTESIAN_POINT('',(0.35,0.));
#1671 = VECTOR('',#1672,1.);
#1672 = DIRECTION('',(0.,-1.));
#1673 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1674 = ORIENTED_EDGE('',*,*,#1675,.F.);
#1675 = EDGE_CURVE('',#1676,#1653,#1678,.T.);
#1676 = VERTEX_POINT('',#1677);
#1677 = CARTESIAN_POINT('',(0.4,0.25,0.35));
#1678 = SURFACE_CURVE('',#1679,(#1683,#1690),.PCURVE_S1.);
#1679 = LINE('',#1680,#1681);
#1680 = CARTESIAN_POINT('',(0.3,0.25,0.35));
#1681 = VECTOR('',#1682,1.);
#1682 = DIRECTION('',(1.,0.,0.));
#1683 = PCURVE('',#1338,#1684);
#1684 = DEFINITIONAL_REPRESENTATION('',(#1685),#1689);
#1685 = LINE('',#1686,#1687);
#1686 = CARTESIAN_POINT('',(0.,0.5));
#1687 = VECTOR('',#1688,1.);
#1688 = DIRECTION('',(1.,0.));
#1689 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1690 = PCURVE('',#1258,#1691);
#1691 = DEFINITIONAL_REPRESENTATION('',(#1692),#1696);
#1692 = LINE('',#1693,#1694);
#1693 = CARTESIAN_POINT('',(0.35,0.));
#1694 = VECTOR('',#1695,1.);
#1695 = DIRECTION('',(0.,1.));
#1696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1697 = ORIENTED_EDGE('',*,*,#1698,.F.);
#1698 = EDGE_CURVE('',#1699,#1676,#1701,.T.);
#1699 = VERTEX_POINT('',#1700);
#1700 = CARTESIAN_POINT('',(0.4,0.18,0.35));
#1701 = SURFACE_CURVE('',#1702,(#1706,#1713),.PCURVE_S1.);
#1702 = LINE('',#1703,#1704);
#1703 = CARTESIAN_POINT('',(0.4,0.18,0.35));
#1704 = VECTOR('',#1705,1.);
#1705 = DIRECTION('',(0.,1.,0.));
#1706 = PCURVE('',#1338,#1707);
#1707 = DEFINITIONAL_REPRESENTATION('',(#1708),#1712);
#1708 = LINE('',#1709,#1710);
#1709 = CARTESIAN_POINT('',(0.1,0.43));
#1710 = VECTOR('',#1711,1.);
#1711 = DIRECTION('',(0.,1.));
#1712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1713 = PCURVE('',#1714,#1719);
#1714 = PLANE('',#1715);
#1715 = AXIS2_PLACEMENT_3D('',#1716,#1717,#1718);
#1716 = CARTESIAN_POINT('',(0.4,0.18,5.E-02));
#1717 = DIRECTION('',(1.,0.,0.));
#1718 = DIRECTION('',(0.,0.,1.));
#1719 = DEFINITIONAL_REPRESENTATION('',(#1720),#1724);
#1720 = LINE('',#1721,#1722);
#1721 = CARTESIAN_POINT('',(0.3,0.));
#1722 = VECTOR('',#1723,1.);
#1723 = DIRECTION('',(0.,-1.));
#1724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1725 = ORIENTED_EDGE('',*,*,#1726,.F.);
#1726 = EDGE_CURVE('',#1297,#1699,#1727,.T.);
#1727 = SURFACE_CURVE('',#1728,(#1732,#1739),.PCURVE_S1.);
#1728 = LINE('',#1729,#1730);
#1729 = CARTESIAN_POINT('',(-0.4,0.18,0.35));
#1730 = VECTOR('',#1731,1.);
#1731 = DIRECTION('',(1.,0.,0.));
#1732 = PCURVE('',#1338,#1733);
#1733 = DEFINITIONAL_REPRESENTATION('',(#1734),#1738);
#1734 = LINE('',#1735,#1736);
#1735 = CARTESIAN_POINT('',(-0.7,0.43));
#1736 = VECTOR('',#1737,1.);
#1737 = DIRECTION('',(1.,0.));
#1738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1739 = PCURVE('',#1311,#1740);
#1740 = DEFINITIONAL_REPRESENTATION('',(#1741),#1745);
#1741 = LINE('',#1742,#1743);
#1742 = CARTESIAN_POINT('',(0.3,0.));
#1743 = VECTOR('',#1744,1.);
#1744 = DIRECTION('',(0.,1.));
#1745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1746 = ADVANCED_FACE('',(#1747),#1311,.T.);
#1747 = FACE_BOUND('',#1748,.T.);
#1748 = EDGE_LOOP('',(#1749,#1772,#1773,#1774));
#1749 = ORIENTED_EDGE('',*,*,#1750,.F.);
#1750 = EDGE_CURVE('',#1271,#1751,#1753,.T.);
#1751 = VERTEX_POINT('',#1752);
#1752 = CARTESIAN_POINT('',(0.4,0.18,5.E-02));
#1753 = SURFACE_CURVE('',#1754,(#1758,#1765),.PCURVE_S1.);
#1754 = LINE('',#1755,#1756);
#1755 = CARTESIAN_POINT('',(-0.4,0.18,5.E-02));
#1756 = VECTOR('',#1757,1.);
#1757 = DIRECTION('',(1.,0.,0.));
#1758 = PCURVE('',#1311,#1759);
#1759 = DEFINITIONAL_REPRESENTATION('',(#1760),#1764);
#1760 = LINE('',#1761,#1762);
#1761 = CARTESIAN_POINT('',(0.,0.));
#1762 = VECTOR('',#1763,1.);
#1763 = DIRECTION('',(0.,1.));
#1764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1765 = PCURVE('',#1285,#1766);
#1766 = DEFINITIONAL_REPRESENTATION('',(#1767),#1771);
#1767 = LINE('',#1768,#1769);
#1768 = CARTESIAN_POINT('',(0.,0.));
#1769 = VECTOR('',#1770,1.);
#1770 = DIRECTION('',(1.,0.));
#1771 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1772 = ORIENTED_EDGE('',*,*,#1296,.T.);
#1773 = ORIENTED_EDGE('',*,*,#1726,.T.);
#1774 = ORIENTED_EDGE('',*,*,#1775,.F.);
#1775 = EDGE_CURVE('',#1751,#1699,#1776,.T.);
#1776 = SURFACE_CURVE('',#1777,(#1781,#1788),.PCURVE_S1.);
#1777 = LINE('',#1778,#1779);
#1778 = CARTESIAN_POINT('',(0.4,0.18,5.E-02));
#1779 = VECTOR('',#1780,1.);
#1780 = DIRECTION('',(0.,0.,1.));
#1781 = PCURVE('',#1311,#1782);
#1782 = DEFINITIONAL_REPRESENTATION('',(#1783),#1787);
#1783 = LINE('',#1784,#1785);
#1784 = CARTESIAN_POINT('',(0.,0.8));
#1785 = VECTOR('',#1786,1.);
#1786 = DIRECTION('',(1.,0.));
#1787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1788 = PCURVE('',#1714,#1789);
#1789 = DEFINITIONAL_REPRESENTATION('',(#1790),#1794);
#1790 = LINE('',#1791,#1792);
#1791 = CARTESIAN_POINT('',(0.,0.));
#1792 = VECTOR('',#1793,1.);
#1793 = DIRECTION('',(1.,0.));
#1794 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1795 = ADVANCED_FACE('',(#1796),#1285,.T.);
#1796 = FACE_BOUND('',#1797,.T.);
#1797 = EDGE_LOOP('',(#1798,#1821,#1822,#1823));
#1798 = ORIENTED_EDGE('',*,*,#1799,.F.);
#1799 = EDGE_CURVE('',#1243,#1800,#1802,.T.);
#1800 = VERTEX_POINT('',#1801);
#1801 = CARTESIAN_POINT('',(0.4,0.25,5.E-02));
#1802 = SURFACE_CURVE('',#1803,(#1807,#1814),.PCURVE_S1.);
#1803 = LINE('',#1804,#1805);
#1804 = CARTESIAN_POINT('',(-0.4,0.25,5.E-02));
#1805 = VECTOR('',#1806,1.);
#1806 = DIRECTION('',(1.,0.,0.));
#1807 = PCURVE('',#1285,#1808);
#1808 = DEFINITIONAL_REPRESENTATION('',(#1809),#1813);
#1809 = LINE('',#1810,#1811);
#1810 = CARTESIAN_POINT('',(0.,7.E-02));
#1811 = VECTOR('',#1812,1.);
#1812 = DIRECTION('',(1.,0.));
#1813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1814 = PCURVE('',#1258,#1815);
#1815 = DEFINITIONAL_REPRESENTATION('',(#1816),#1820);
#1816 = LINE('',#1817,#1818);
#1817 = CARTESIAN_POINT('',(5.E-02,-0.7));
#1818 = VECTOR('',#1819,1.);
#1819 = DIRECTION('',(0.,1.));
#1820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1821 = ORIENTED_EDGE('',*,*,#1270,.T.);
#1822 = ORIENTED_EDGE('',*,*,#1750,.T.);
#1823 = ORIENTED_EDGE('',*,*,#1824,.T.);
#1824 = EDGE_CURVE('',#1751,#1800,#1825,.T.);
#1825 = SURFACE_CURVE('',#1826,(#1830,#1837),.PCURVE_S1.);
#1826 = LINE('',#1827,#1828);
#1827 = CARTESIAN_POINT('',(0.4,0.18,5.E-02));
#1828 = VECTOR('',#1829,1.);
#1829 = DIRECTION('',(0.,1.,0.));
#1830 = PCURVE('',#1285,#1831);
#1831 = DEFINITIONAL_REPRESENTATION('',(#1832),#1836);
#1832 = LINE('',#1833,#1834);
#1833 = CARTESIAN_POINT('',(0.8,0.));
#1834 = VECTOR('',#1835,1.);
#1835 = DIRECTION('',(0.,1.));
#1836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1837 = PCURVE('',#1714,#1838);
#1838 = DEFINITIONAL_REPRESENTATION('',(#1839),#1843);
#1839 = LINE('',#1840,#1841);
#1840 = CARTESIAN_POINT('',(0.,0.));
#1841 = VECTOR('',#1842,1.);
#1842 = DIRECTION('',(0.,-1.));
#1843 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1844 = ADVANCED_FACE('',(#1845),#1258,.T.);
#1845 = FACE_BOUND('',#1846,.T.);
#1846 = EDGE_LOOP('',(#1847,#1870,#1891,#1892,#1893,#1914));
#1847 = ORIENTED_EDGE('',*,*,#1848,.F.);
#1848 = EDGE_CURVE('',#1849,#1653,#1851,.T.);
#1849 = VERTEX_POINT('',#1850);
#1850 = CARTESIAN_POINT('',(0.5,0.25,0.));
#1851 = SURFACE_CURVE('',#1852,(#1856,#1863),.PCURVE_S1.);
#1852 = LINE('',#1853,#1854);
#1853 = CARTESIAN_POINT('',(0.5,0.25,0.));
#1854 = VECTOR('',#1855,1.);
#1855 = DIRECTION('',(0.,0.,1.));
#1856 = PCURVE('',#1258,#1857);
#1857 = DEFINITIONAL_REPRESENTATION('',(#1858),#1862);
#1858 = LINE('',#1859,#1860);
#1859 = CARTESIAN_POINT('',(0.,0.2));
#1860 = VECTOR('',#1861,1.);
#1861 = DIRECTION('',(1.,0.));
#1862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1863 = PCURVE('',#1421,#1864);
#1864 = DEFINITIONAL_REPRESENTATION('',(#1865),#1869);
#1865 = LINE('',#1866,#1867);
#1866 = CARTESIAN_POINT('',(0.,-0.5));
#1867 = VECTOR('',#1868,1.);
#1868 = DIRECTION('',(1.,0.));
#1869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1870 = ORIENTED_EDGE('',*,*,#1871,.F.);
#1871 = EDGE_CURVE('',#1215,#1849,#1872,.T.);
#1872 = SURFACE_CURVE('',#1873,(#1877,#1884),.PCURVE_S1.);
#1873 = LINE('',#1874,#1875);
#1874 = CARTESIAN_POINT('',(0.3,0.25,0.));
#1875 = VECTOR('',#1876,1.);
#1876 = DIRECTION('',(1.,0.,0.));
#1877 = PCURVE('',#1258,#1878);
#1878 = DEFINITIONAL_REPRESENTATION('',(#1879),#1883);
#1879 = LINE('',#1880,#1881);
#1880 = CARTESIAN_POINT('',(0.,0.));
#1881 = VECTOR('',#1882,1.);
#1882 = DIRECTION('',(0.,1.));
#1883 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1884 = PCURVE('',#1230,#1885);
#1885 = DEFINITIONAL_REPRESENTATION('',(#1886),#1890);
#1886 = LINE('',#1887,#1888);
#1887 = CARTESIAN_POINT('',(0.,0.5));
#1888 = VECTOR('',#1889,1.);
#1889 = DIRECTION('',(1.,0.));
#1890 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1891 = ORIENTED_EDGE('',*,*,#1242,.T.);
#1892 = ORIENTED_EDGE('',*,*,#1799,.T.);
#1893 = ORIENTED_EDGE('',*,*,#1894,.T.);
#1894 = EDGE_CURVE('',#1800,#1676,#1895,.T.);
#1895 = SURFACE_CURVE('',#1896,(#1900,#1907),.PCURVE_S1.);
#1896 = LINE('',#1897,#1898);
#1897 = CARTESIAN_POINT('',(0.4,0.25,5.E-02));
#1898 = VECTOR('',#1899,1.);
#1899 = DIRECTION('',(0.,0.,1.));
#1900 = PCURVE('',#1258,#1901);
#1901 = DEFINITIONAL_REPRESENTATION('',(#1902),#1906);
#1902 = LINE('',#1903,#1904);
#1903 = CARTESIAN_POINT('',(5.E-02,0.1));
#1904 = VECTOR('',#1905,1.);
#1905 = DIRECTION('',(1.,0.));
#1906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1907 = PCURVE('',#1714,#1908);
#1908 = DEFINITIONAL_REPRESENTATION('',(#1909),#1913);
#1909 = LINE('',#1910,#1911);
#1910 = CARTESIAN_POINT('',(0.,-7.E-02));
#1911 = VECTOR('',#1912,1.);
#1912 = DIRECTION('',(1.,0.));
#1913 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1914 = ORIENTED_EDGE('',*,*,#1675,.T.);
#1915 = ADVANCED_FACE('',(#1916),#1230,.F.);
#1916 = FACE_BOUND('',#1917,.F.);
#1917 = EDGE_LOOP('',(#1918,#1919,#1920,#1941));
#1918 = ORIENTED_EDGE('',*,*,#1214,.F.);
#1919 = ORIENTED_EDGE('',*,*,#1433,.T.);
#1920 = ORIENTED_EDGE('',*,*,#1921,.T.);
#1921 = EDGE_CURVE('',#1404,#1849,#1922,.T.);
#1922 = SURFACE_CURVE('',#1923,(#1927,#1934),.PCURVE_S1.);
#1923 = LINE('',#1924,#1925);
#1924 = CARTESIAN_POINT('',(0.5,-0.25,0.));
#1925 = VECTOR('',#1926,1.);
#1926 = DIRECTION('',(0.,1.,0.));
#1927 = PCURVE('',#1230,#1928);
#1928 = DEFINITIONAL_REPRESENTATION('',(#1929),#1933);
#1929 = LINE('',#1930,#1931);
#1930 = CARTESIAN_POINT('',(0.2,0.));
#1931 = VECTOR('',#1932,1.);
#1932 = DIRECTION('',(0.,1.));
#1933 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1934 = PCURVE('',#1421,#1935);
#1935 = DEFINITIONAL_REPRESENTATION('',(#1936),#1940);
#1936 = LINE('',#1937,#1938);
#1937 = CARTESIAN_POINT('',(0.,0.));
#1938 = VECTOR('',#1939,1.);
#1939 = DIRECTION('',(0.,-1.));
#1940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1941 = ORIENTED_EDGE('',*,*,#1871,.F.);
#1942 = ADVANCED_FACE('',(#1943),#1421,.T.);
#1943 = FACE_BOUND('',#1944,.T.);
#1944 = EDGE_LOOP('',(#1945,#1946,#1947,#1948));
#1945 = ORIENTED_EDGE('',*,*,#1403,.F.);
#1946 = ORIENTED_EDGE('',*,*,#1921,.T.);
#1947 = ORIENTED_EDGE('',*,*,#1848,.T.);
#1948 = ORIENTED_EDGE('',*,*,#1652,.F.);
#1949 = ADVANCED_FACE('',(#1950),#1494,.F.);
#1950 = FACE_BOUND('',#1951,.F.);
#1951 = EDGE_LOOP('',(#1952,#1953,#1954,#1955));
#1952 = ORIENTED_EDGE('',*,*,#1478,.F.);
#1953 = ORIENTED_EDGE('',*,*,#1555,.T.);
#1954 = ORIENTED_EDGE('',*,*,#1604,.T.);
#1955 = ORIENTED_EDGE('',*,*,#1630,.F.);
#1956 = ADVANCED_FACE('',(#1957),#1714,.F.);
#1957 = FACE_BOUND('',#1958,.F.);
#1958 = EDGE_LOOP('',(#1959,#1960,#1961,#1962));
#1959 = ORIENTED_EDGE('',*,*,#1775,.F.);
#1960 = ORIENTED_EDGE('',*,*,#1824,.T.);
#1961 = ORIENTED_EDGE('',*,*,#1894,.T.);
#1962 = ORIENTED_EDGE('',*,*,#1698,.F.);
#1963 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1967)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1964,#1965,#1966)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1964 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1965 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1966 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1967 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1964,
  'distance_accuracy_value','confusion accuracy');
#1968 = SHAPE_DEFINITION_REPRESENTATION(#1969,#382);
#1969 = PRODUCT_DEFINITION_SHAPE('','',#1970);
#1970 = PRODUCT_DEFINITION('design','',#1971,#1974);
#1971 = PRODUCT_DEFINITION_FORMATION('','',#1972);
#1972 = PRODUCT('Terminal','Terminal','',(#1973));
#1973 = PRODUCT_CONTEXT('',#2,'mechanical');
#1974 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1975 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1976,#1978);
#1976 = ( REPRESENTATION_RELATIONSHIP('','',#382,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1977) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1977 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#1978 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1979);
#1979 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','=>[0:1:1:3]','',#5,#1970,$);
#1980 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1972));
#1981 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1982),#2312);
#1982 = MANIFOLD_SOLID_BREP('',#1983);
#1983 = CLOSED_SHELL('',(#1984,#2104,#2180,#2229,#2278,#2305));
#1984 = ADVANCED_FACE('',(#1985),#1999,.F.);
#1985 = FACE_BOUND('',#1986,.F.);
#1986 = EDGE_LOOP('',(#1987,#2022,#2050,#2078));
#1987 = ORIENTED_EDGE('',*,*,#1988,.F.);
#1988 = EDGE_CURVE('',#1989,#1991,#1993,.T.);
#1989 = VERTEX_POINT('',#1990);
#1990 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#1991 = VERTEX_POINT('',#1992);
#1992 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#1993 = SURFACE_CURVE('',#1994,(#1998,#2010),.PCURVE_S1.);
#1994 = LINE('',#1995,#1996);
#1995 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#1996 = VECTOR('',#1997,1.);
#1997 = DIRECTION('',(0.,0.,1.));
#1998 = PCURVE('',#1999,#2004);
#1999 = PLANE('',#2000);
#2000 = AXIS2_PLACEMENT_3D('',#2001,#2002,#2003);
#2001 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#2002 = DIRECTION('',(1.,0.,0.));
#2003 = DIRECTION('',(0.,0.,1.));
#2004 = DEFINITIONAL_REPRESENTATION('',(#2005),#2009);
#2005 = LINE('',#2006,#2007);
#2006 = CARTESIAN_POINT('',(0.,0.));
#2007 = VECTOR('',#2008,1.);
#2008 = DIRECTION('',(1.,0.));
#2009 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2010 = PCURVE('',#2011,#2016);
#2011 = PLANE('',#2012);
#2012 = AXIS2_PLACEMENT_3D('',#2013,#2014,#2015);
#2013 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#2014 = DIRECTION('',(0.,1.,0.));
#2015 = DIRECTION('',(0.,0.,1.));
#2016 = DEFINITIONAL_REPRESENTATION('',(#2017),#2021);
#2017 = LINE('',#2018,#2019);
#2018 = CARTESIAN_POINT('',(0.,0.));
#2019 = VECTOR('',#2020,1.);
#2020 = DIRECTION('',(1.,0.));
#2021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2022 = ORIENTED_EDGE('',*,*,#2023,.T.);
#2023 = EDGE_CURVE('',#1989,#2024,#2026,.T.);
#2024 = VERTEX_POINT('',#2025);
#2025 = CARTESIAN_POINT('',(-0.3,0.25,0.33));
#2026 = SURFACE_CURVE('',#2027,(#2031,#2038),.PCURVE_S1.);
#2027 = LINE('',#2028,#2029);
#2028 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#2029 = VECTOR('',#2030,1.);
#2030 = DIRECTION('',(0.,1.,0.));
#2031 = PCURVE('',#1999,#2032);
#2032 = DEFINITIONAL_REPRESENTATION('',(#2033),#2037);
#2033 = LINE('',#2034,#2035);
#2034 = CARTESIAN_POINT('',(0.,0.));
#2035 = VECTOR('',#2036,1.);
#2036 = DIRECTION('',(0.,-1.));
#2037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2038 = PCURVE('',#2039,#2044);
#2039 = PLANE('',#2040);
#2040 = AXIS2_PLACEMENT_3D('',#2041,#2042,#2043);
#2041 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#2042 = DIRECTION('',(0.,0.,1.));
#2043 = DIRECTION('',(1.,0.,0.));
#2044 = DEFINITIONAL_REPRESENTATION('',(#2045),#2049);
#2045 = LINE('',#2046,#2047);
#2046 = CARTESIAN_POINT('',(0.,0.));
#2047 = VECTOR('',#2048,1.);
#2048 = DIRECTION('',(0.,1.));
#2049 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2050 = ORIENTED_EDGE('',*,*,#2051,.T.);
#2051 = EDGE_CURVE('',#2024,#2052,#2054,.T.);
#2052 = VERTEX_POINT('',#2053);
#2053 = CARTESIAN_POINT('',(-0.3,0.25,0.35));
#2054 = SURFACE_CURVE('',#2055,(#2059,#2066),.PCURVE_S1.);
#2055 = LINE('',#2056,#2057);
#2056 = CARTESIAN_POINT('',(-0.3,0.25,0.33));
#2057 = VECTOR('',#2058,1.);
#2058 = DIRECTION('',(0.,0.,1.));
#2059 = PCURVE('',#1999,#2060);
#2060 = DEFINITIONAL_REPRESENTATION('',(#2061),#2065);
#2061 = LINE('',#2062,#2063);
#2062 = CARTESIAN_POINT('',(0.,-0.5));
#2063 = VECTOR('',#2064,1.);
#2064 = DIRECTION('',(1.,0.));
#2065 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2066 = PCURVE('',#2067,#2072);
#2067 = PLANE('',#2068);
#2068 = AXIS2_PLACEMENT_3D('',#2069,#2070,#2071);
#2069 = CARTESIAN_POINT('',(-0.3,0.25,0.33));
#2070 = DIRECTION('',(0.,1.,0.));
#2071 = DIRECTION('',(0.,0.,1.));
#2072 = DEFINITIONAL_REPRESENTATION('',(#2073),#2077);
#2073 = LINE('',#2074,#2075);
#2074 = CARTESIAN_POINT('',(0.,0.));
#2075 = VECTOR('',#2076,1.);
#2076 = DIRECTION('',(1.,0.));
#2077 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2078 = ORIENTED_EDGE('',*,*,#2079,.F.);
#2079 = EDGE_CURVE('',#1991,#2052,#2080,.T.);
#2080 = SURFACE_CURVE('',#2081,(#2085,#2092),.PCURVE_S1.);
#2081 = LINE('',#2082,#2083);
#2082 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#2083 = VECTOR('',#2084,1.);
#2084 = DIRECTION('',(0.,1.,0.));
#2085 = PCURVE('',#1999,#2086);
#2086 = DEFINITIONAL_REPRESENTATION('',(#2087),#2091);
#2087 = LINE('',#2088,#2089);
#2088 = CARTESIAN_POINT('',(2.E-02,0.));
#2089 = VECTOR('',#2090,1.);
#2090 = DIRECTION('',(0.,-1.));
#2091 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2092 = PCURVE('',#2093,#2098);
#2093 = PLANE('',#2094);
#2094 = AXIS2_PLACEMENT_3D('',#2095,#2096,#2097);
#2095 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#2096 = DIRECTION('',(0.,0.,1.));
#2097 = DIRECTION('',(1.,0.,0.));
#2098 = DEFINITIONAL_REPRESENTATION('',(#2099),#2103);
#2099 = LINE('',#2100,#2101);
#2100 = CARTESIAN_POINT('',(0.,0.));
#2101 = VECTOR('',#2102,1.);
#2102 = DIRECTION('',(0.,1.));
#2103 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2104 = ADVANCED_FACE('',(#2105),#2011,.F.);
#2105 = FACE_BOUND('',#2106,.F.);
#2106 = EDGE_LOOP('',(#2107,#2130,#2131,#2154));
#2107 = ORIENTED_EDGE('',*,*,#2108,.F.);
#2108 = EDGE_CURVE('',#1989,#2109,#2111,.T.);
#2109 = VERTEX_POINT('',#2110);
#2110 = CARTESIAN_POINT('',(0.3,-0.25,0.33));
#2111 = SURFACE_CURVE('',#2112,(#2116,#2123),.PCURVE_S1.);
#2112 = LINE('',#2113,#2114);
#2113 = CARTESIAN_POINT('',(-0.3,-0.25,0.33));
#2114 = VECTOR('',#2115,1.);
#2115 = DIRECTION('',(1.,0.,0.));
#2116 = PCURVE('',#2011,#2117);
#2117 = DEFINITIONAL_REPRESENTATION('',(#2118),#2122);
#2118 = LINE('',#2119,#2120);
#2119 = CARTESIAN_POINT('',(0.,0.));
#2120 = VECTOR('',#2121,1.);
#2121 = DIRECTION('',(0.,1.));
#2122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2123 = PCURVE('',#2039,#2124);
#2124 = DEFINITIONAL_REPRESENTATION('',(#2125),#2129);
#2125 = LINE('',#2126,#2127);
#2126 = CARTESIAN_POINT('',(0.,0.));
#2127 = VECTOR('',#2128,1.);
#2128 = DIRECTION('',(1.,0.));
#2129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2130 = ORIENTED_EDGE('',*,*,#1988,.T.);
#2131 = ORIENTED_EDGE('',*,*,#2132,.T.);
#2132 = EDGE_CURVE('',#1991,#2133,#2135,.T.);
#2133 = VERTEX_POINT('',#2134);
#2134 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#2135 = SURFACE_CURVE('',#2136,(#2140,#2147),.PCURVE_S1.);
#2136 = LINE('',#2137,#2138);
#2137 = CARTESIAN_POINT('',(-0.3,-0.25,0.35));
#2138 = VECTOR('',#2139,1.);
#2139 = DIRECTION('',(1.,0.,0.));
#2140 = PCURVE('',#2011,#2141);
#2141 = DEFINITIONAL_REPRESENTATION('',(#2142),#2146);
#2142 = LINE('',#2143,#2144);
#2143 = CARTESIAN_POINT('',(2.E-02,0.));
#2144 = VECTOR('',#2145,1.);
#2145 = DIRECTION('',(0.,1.));
#2146 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2147 = PCURVE('',#2093,#2148);
#2148 = DEFINITIONAL_REPRESENTATION('',(#2149),#2153);
#2149 = LINE('',#2150,#2151);
#2150 = CARTESIAN_POINT('',(0.,0.));
#2151 = VECTOR('',#2152,1.);
#2152 = DIRECTION('',(1.,0.));
#2153 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2154 = ORIENTED_EDGE('',*,*,#2155,.F.);
#2155 = EDGE_CURVE('',#2109,#2133,#2156,.T.);
#2156 = SURFACE_CURVE('',#2157,(#2161,#2168),.PCURVE_S1.);
#2157 = LINE('',#2158,#2159);
#2158 = CARTESIAN_POINT('',(0.3,-0.25,0.33));
#2159 = VECTOR('',#2160,1.);
#2160 = DIRECTION('',(0.,0.,1.));
#2161 = PCURVE('',#2011,#2162);
#2162 = DEFINITIONAL_REPRESENTATION('',(#2163),#2167);
#2163 = LINE('',#2164,#2165);
#2164 = CARTESIAN_POINT('',(0.,0.6));
#2165 = VECTOR('',#2166,1.);
#2166 = DIRECTION('',(1.,0.));
#2167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2168 = PCURVE('',#2169,#2174);
#2169 = PLANE('',#2170);
#2170 = AXIS2_PLACEMENT_3D('',#2171,#2172,#2173);
#2171 = CARTESIAN_POINT('',(0.3,-0.25,0.33));
#2172 = DIRECTION('',(1.,0.,0.));
#2173 = DIRECTION('',(0.,0.,1.));
#2174 = DEFINITIONAL_REPRESENTATION('',(#2175),#2179);
#2175 = LINE('',#2176,#2177);
#2176 = CARTESIAN_POINT('',(0.,0.));
#2177 = VECTOR('',#2178,1.);
#2178 = DIRECTION('',(1.,0.));
#2179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2180 = ADVANCED_FACE('',(#2181),#2093,.T.);
#2181 = FACE_BOUND('',#2182,.T.);
#2182 = EDGE_LOOP('',(#2183,#2184,#2185,#2208));
#2183 = ORIENTED_EDGE('',*,*,#2079,.F.);
#2184 = ORIENTED_EDGE('',*,*,#2132,.T.);
#2185 = ORIENTED_EDGE('',*,*,#2186,.T.);
#2186 = EDGE_CURVE('',#2133,#2187,#2189,.T.);
#2187 = VERTEX_POINT('',#2188);
#2188 = CARTESIAN_POINT('',(0.3,0.25,0.35));
#2189 = SURFACE_CURVE('',#2190,(#2194,#2201),.PCURVE_S1.);
#2190 = LINE('',#2191,#2192);
#2191 = CARTESIAN_POINT('',(0.3,-0.25,0.35));
#2192 = VECTOR('',#2193,1.);
#2193 = DIRECTION('',(0.,1.,0.));
#2194 = PCURVE('',#2093,#2195);
#2195 = DEFINITIONAL_REPRESENTATION('',(#2196),#2200);
#2196 = LINE('',#2197,#2198);
#2197 = CARTESIAN_POINT('',(0.6,0.));
#2198 = VECTOR('',#2199,1.);
#2199 = DIRECTION('',(0.,1.));
#2200 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2201 = PCURVE('',#2169,#2202);
#2202 = DEFINITIONAL_REPRESENTATION('',(#2203),#2207);
#2203 = LINE('',#2204,#2205);
#2204 = CARTESIAN_POINT('',(2.E-02,0.));
#2205 = VECTOR('',#2206,1.);
#2206 = DIRECTION('',(0.,-1.));
#2207 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2208 = ORIENTED_EDGE('',*,*,#2209,.F.);
#2209 = EDGE_CURVE('',#2052,#2187,#2210,.T.);
#2210 = SURFACE_CURVE('',#2211,(#2215,#2222),.PCURVE_S1.);
#2211 = LINE('',#2212,#2213);
#2212 = CARTESIAN_POINT('',(-0.3,0.25,0.35));
#2213 = VECTOR('',#2214,1.);
#2214 = DIRECTION('',(1.,0.,0.));
#2215 = PCURVE('',#2093,#2216);
#2216 = DEFINITIONAL_REPRESENTATION('',(#2217),#2221);
#2217 = LINE('',#2218,#2219);
#2218 = CARTESIAN_POINT('',(0.,0.5));
#2219 = VECTOR('',#2220,1.);
#2220 = DIRECTION('',(1.,0.));
#2221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2222 = PCURVE('',#2067,#2223);
#2223 = DEFINITIONAL_REPRESENTATION('',(#2224),#2228);
#2224 = LINE('',#2225,#2226);
#2225 = CARTESIAN_POINT('',(2.E-02,0.));
#2226 = VECTOR('',#2227,1.);
#2227 = DIRECTION('',(0.,1.));
#2228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2229 = ADVANCED_FACE('',(#2230),#2067,.T.);
#2230 = FACE_BOUND('',#2231,.T.);
#2231 = EDGE_LOOP('',(#2232,#2255,#2256,#2257));
#2232 = ORIENTED_EDGE('',*,*,#2233,.F.);
#2233 = EDGE_CURVE('',#2024,#2234,#2236,.T.);
#2234 = VERTEX_POINT('',#2235);
#2235 = CARTESIAN_POINT('',(0.3,0.25,0.33));
#2236 = SURFACE_CURVE('',#2237,(#2241,#2248),.PCURVE_S1.);
#2237 = LINE('',#2238,#2239);
#2238 = CARTESIAN_POINT('',(-0.3,0.25,0.33));
#2239 = VECTOR('',#2240,1.);
#2240 = DIRECTION('',(1.,0.,0.));
#2241 = PCURVE('',#2067,#2242);
#2242 = DEFINITIONAL_REPRESENTATION('',(#2243),#2247);
#2243 = LINE('',#2244,#2245);
#2244 = CARTESIAN_POINT('',(0.,0.));
#2245 = VECTOR('',#2246,1.);
#2246 = DIRECTION('',(0.,1.));
#2247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2248 = PCURVE('',#2039,#2249);
#2249 = DEFINITIONAL_REPRESENTATION('',(#2250),#2254);
#2250 = LINE('',#2251,#2252);
#2251 = CARTESIAN_POINT('',(0.,0.5));
#2252 = VECTOR('',#2253,1.);
#2253 = DIRECTION('',(1.,0.));
#2254 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2255 = ORIENTED_EDGE('',*,*,#2051,.T.);
#2256 = ORIENTED_EDGE('',*,*,#2209,.T.);
#2257 = ORIENTED_EDGE('',*,*,#2258,.F.);
#2258 = EDGE_CURVE('',#2234,#2187,#2259,.T.);
#2259 = SURFACE_CURVE('',#2260,(#2264,#2271),.PCURVE_S1.);
#2260 = LINE('',#2261,#2262);
#2261 = CARTESIAN_POINT('',(0.3,0.25,0.33));
#2262 = VECTOR('',#2263,1.);
#2263 = DIRECTION('',(0.,0.,1.));
#2264 = PCURVE('',#2067,#2265);
#2265 = DEFINITIONAL_REPRESENTATION('',(#2266),#2270);
#2266 = LINE('',#2267,#2268);
#2267 = CARTESIAN_POINT('',(0.,0.6));
#2268 = VECTOR('',#2269,1.);
#2269 = DIRECTION('',(1.,0.));
#2270 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2271 = PCURVE('',#2169,#2272);
#2272 = DEFINITIONAL_REPRESENTATION('',(#2273),#2277);
#2273 = LINE('',#2274,#2275);
#2274 = CARTESIAN_POINT('',(0.,-0.5));
#2275 = VECTOR('',#2276,1.);
#2276 = DIRECTION('',(1.,0.));
#2277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2278 = ADVANCED_FACE('',(#2279),#2039,.F.);
#2279 = FACE_BOUND('',#2280,.F.);
#2280 = EDGE_LOOP('',(#2281,#2282,#2283,#2304));
#2281 = ORIENTED_EDGE('',*,*,#2023,.F.);
#2282 = ORIENTED_EDGE('',*,*,#2108,.T.);
#2283 = ORIENTED_EDGE('',*,*,#2284,.T.);
#2284 = EDGE_CURVE('',#2109,#2234,#2285,.T.);
#2285 = SURFACE_CURVE('',#2286,(#2290,#2297),.PCURVE_S1.);
#2286 = LINE('',#2287,#2288);
#2287 = CARTESIAN_POINT('',(0.3,-0.25,0.33));
#2288 = VECTOR('',#2289,1.);
#2289 = DIRECTION('',(0.,1.,0.));
#2290 = PCURVE('',#2039,#2291);
#2291 = DEFINITIONAL_REPRESENTATION('',(#2292),#2296);
#2292 = LINE('',#2293,#2294);
#2293 = CARTESIAN_POINT('',(0.6,0.));
#2294 = VECTOR('',#2295,1.);
#2295 = DIRECTION('',(0.,1.));
#2296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2297 = PCURVE('',#2169,#2298);
#2298 = DEFINITIONAL_REPRESENTATION('',(#2299),#2303);
#2299 = LINE('',#2300,#2301);
#2300 = CARTESIAN_POINT('',(0.,0.));
#2301 = VECTOR('',#2302,1.);
#2302 = DIRECTION('',(0.,-1.));
#2303 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2304 = ORIENTED_EDGE('',*,*,#2233,.F.);
#2305 = ADVANCED_FACE('',(#2306),#2169,.T.);
#2306 = FACE_BOUND('',#2307,.T.);
#2307 = EDGE_LOOP('',(#2308,#2309,#2310,#2311));
#2308 = ORIENTED_EDGE('',*,*,#2155,.F.);
#2309 = ORIENTED_EDGE('',*,*,#2284,.T.);
#2310 = ORIENTED_EDGE('',*,*,#2258,.T.);
#2311 = ORIENTED_EDGE('',*,*,#2186,.F.);
#2312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2316)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2313,#2314,#2315)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#2313 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#2314 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#2315 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#2316 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#2313,
  'distance_accuracy_value','confusion accuracy');
#2317 = SHAPE_DEFINITION_REPRESENTATION(#2318,#1981);
#2318 = PRODUCT_DEFINITION_SHAPE('','',#2319);
#2319 = PRODUCT_DEFINITION('design','',#2320,#2323);
#2320 = PRODUCT_DEFINITION_FORMATION('','',#2321);
#2321 = PRODUCT('solidTop','solidTop','',(#2322));
#2322 = PRODUCT_CONTEXT('',#2,'mechanical');
#2323 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#2324 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2325,#2327);
#2325 = ( REPRESENTATION_RELATIONSHIP('','',#1981,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2326) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#2326 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#2327 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #2328);
#2328 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','=>[0:1:1:4]','',#5,#2319,$);
#2329 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#2321));
#2330 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #2331),#2312);
#2331 = STYLED_ITEM('color',(#2332),#1982);
#2332 = PRESENTATION_STYLE_ASSIGNMENT((#2333,#2339));
#2333 = SURFACE_STYLE_USAGE(.BOTH.,#2334);
#2334 = SURFACE_SIDE_STYLE('',(#2335));
#2335 = SURFACE_STYLE_FILL_AREA(#2336);
#2336 = FILL_AREA_STYLE('',(#2337));
#2337 = FILL_AREA_STYLE_COLOUR('',#2338);
#2338 = COLOUR_RGB('',0.10000000149,0.10000000149,0.10000000149);
#2339 = CURVE_STYLE('',#2340,POSITIVE_LENGTH_MEASURE(0.1),#2338);
#2340 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#2341 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #2342,#2352),#1963);
#2342 = STYLED_ITEM('color',(#2343),#383);
#2343 = PRESENTATION_STYLE_ASSIGNMENT((#2344,#2350));
#2344 = SURFACE_STYLE_USAGE(.BOTH.,#2345);
#2345 = SURFACE_SIDE_STYLE('',(#2346));
#2346 = SURFACE_STYLE_FILL_AREA(#2347);
#2347 = FILL_AREA_STYLE('',(#2348));
#2348 = FILL_AREA_STYLE_COLOUR('',#2349);
#2349 = COLOUR_RGB('',0.73400002718,0.773000001907,0.79699999094);
#2350 = CURVE_STYLE('',#2351,POSITIVE_LENGTH_MEASURE(0.1),#2349);
#2351 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#2352 = STYLED_ITEM('color',(#2353),#1173);
#2353 = PRESENTATION_STYLE_ASSIGNMENT((#2354,#2359));
#2354 = SURFACE_STYLE_USAGE(.BOTH.,#2355);
#2355 = SURFACE_SIDE_STYLE('',(#2356));
#2356 = SURFACE_STYLE_FILL_AREA(#2357);
#2357 = FILL_AREA_STYLE('',(#2358));
#2358 = FILL_AREA_STYLE_COLOUR('',#2349);
#2359 = CURVE_STYLE('',#2360,POSITIVE_LENGTH_MEASURE(0.1),#2349);
#2360 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#2361 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #2362),#364);
#2362 = STYLED_ITEM('color',(#2363),#34);
#2363 = PRESENTATION_STYLE_ASSIGNMENT((#2364,#2369));
#2364 = SURFACE_STYLE_USAGE(.BOTH.,#2365);
#2365 = SURFACE_SIDE_STYLE('',(#2366));
#2366 = SURFACE_STYLE_FILL_AREA(#2367);
#2367 = FILL_AREA_STYLE('',(#2368));
#2368 = FILL_AREA_STYLE_COLOUR('',#2349);
#2369 = CURVE_STYLE('',#2370,POSITIVE_LENGTH_MEASURE(0.1),#2349);
#2370 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
