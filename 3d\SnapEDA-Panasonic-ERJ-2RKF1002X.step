ISO-10303-21;
HEADER;
FILE_DESCRIPTION (( 'STEP AP214' ),
    '1' );
FILE_NAME ('ERJH2RD1000X.STEP',
    '2021-03-29T07:43:43',
    ( '' ),
    ( '' ),
    'SwSTEP 2.0',
    'SolidWorks 2018',
    '' );
FILE_SCHEMA (( 'AUTOMOTIVE_DESIGN' ));
ENDSEC;

DATA;
#1 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#2 = DIRECTION ( 'NONE',  ( 9.856383386231860060E-17, 1.000000000000000000, 0.000000000000000000 ) ) ;
#3 = EDGE_CURVE ( 'NONE', #263, #658, #1132, .T. ) ;
#4 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, -0.4485249675906648470, 0.2500000000000000000 ) ) ;
#5 = COLOUR_RGB ( '',0.2509803921568627416, 0.2509803921568627416, 0.2509803921568627416 ) ;
#6 = AXIS2_PLACEMENT_3D ( 'NONE', #698, #684, #679 ) ;
#7 = ORIENTED_EDGE ( 'NONE', *, *, #636, .T. ) ;
#8 = VERTEX_POINT ( 'NONE', #399 ) ;
#9 = LINE ( 'NONE', #1069, #849 ) ;
#10 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#11 = CLOSED_SHELL ( 'NONE', ( #535, #1103, #721, #75, #242, #639, #867, #968, #862, #341, #913, #1199, #1007, #318 ) ) ;
#12 = EDGE_LOOP ( 'NONE', ( #503, #598, #674, #941 ) ) ;
#13 = EDGE_CURVE ( 'NONE', #888, #418, #605, .T. ) ;
#14 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3324999999999998512, -0.2500000000000000000 ) ) ;
#15 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#16 = PRODUCT_DEFINITION_SHAPE ( 'NONE', 'NONE',  #806 ) ;
#17 = LINE ( 'NONE', #166, #741 ) ;
#18 = VECTOR ( 'NONE', #54, 1000.000000000000000 ) ;
#19 = VECTOR ( 'NONE', #153, 1000.000000000000000 ) ;
#20 = VECTOR ( 'NONE', #200, 1000.000000000000000 ) ;
#21 = EDGE_CURVE ( 'NONE', #563, #988, #847, .T. ) ;
#22 = VERTEX_POINT ( 'NONE', #1168 ) ;
#23 = ORIENTED_EDGE ( 'NONE', *, *, #161, .F. ) ;
#24 = FILL_AREA_STYLE ('',( #384 ) ) ;
#25 = EDGE_LOOP ( 'NONE', ( #349, #759, #220, #324 ) ) ;
#26 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#27 = VERTEX_POINT ( 'NONE', #969 ) ;
#28 = ORIENTED_EDGE ( 'NONE', *, *, #520, .F. ) ;
#29 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -1.265110831387390000 ) ) ;
#30 = AXIS2_PLACEMENT_3D ( 'NONE', #1012, #1201, #754 ) ;
#31 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3324999999999999623, 0.1699999999999999567 ) ) ;
#32 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -1.265110831387390000 ) ) ;
#33 = LINE ( 'NONE', #1191, #1215 ) ;
#34 = PLANE ( 'NONE',  #798 ) ;
#35 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#36 = LINE ( 'NONE', #980, #1162 ) ;
#37 = VERTEX_POINT ( 'NONE', #1082 ) ;
#38 = EDGE_LOOP ( 'NONE', ( #724, #71, #537, #521 ) ) ;
#39 = PLANE ( 'NONE',  #129 ) ;
#40 = LINE ( 'NONE', #861, #247 ) ;
#41 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#42 = VERTEX_POINT ( 'NONE', #976 ) ;
#43 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#44 = VERTEX_POINT ( 'NONE', #124 ) ;
#45 = ORIENTED_EDGE ( 'NONE', *, *, #911, .T. ) ;
#46 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#47 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#48 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#49 = VECTOR ( 'NONE', #714, 1000.000000000000000 ) ;
#50 = VERTEX_POINT ( 'NONE', #127 ) ;
#51 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#52 = EDGE_LOOP ( 'NONE', ( #398, #464, #713, #1021, #905, #553, #232, #841 ) ) ;
#53 = EDGE_CURVE ( 'NONE', #165, #1153, #310, .T. ) ;
#54 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#55 = ORIENTED_EDGE ( 'NONE', *, *, #126, .F. ) ;
#56 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, -0.1699999999999999012 ) ) ;
#57 = ORIENTED_EDGE ( 'NONE', *, *, #486, .T. ) ;
#58 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 1.714505518806294884E-16, 0.000000000000000000 ) ) ;
#59 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#60 = EDGE_CURVE ( 'NONE', #27, #44, #1195, .T. ) ;
#61 = PLANE ( 'NONE',  #462 ) ;
#62 = VECTOR ( 'NONE', #1124, 1000.000000000000000 ) ;
#63 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, 0.3499999999999999223, -0.2500000000000000000 ) ) ;
#64 = LINE ( 'NONE', #623, #269 ) ;
#65 = ADVANCED_FACE ( 'NONE', ( #999 ), #118, .F. ) ;
#66 = FILL_AREA_STYLE ('',( #637 ) ) ;
#67 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#68 = EDGE_CURVE ( 'NONE', #1067, #731, #738, .T. ) ;
#69 = VERTEX_POINT ( 'NONE', #694 ) ;
#70 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, -3.009265538105056020E-33, -0.2500000000000000000 ) ) ;
#71 = ORIENTED_EDGE ( 'NONE', *, *, #569, .F. ) ;
#72 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#73 = ORIENTED_EDGE ( 'NONE', *, *, #161, .T. ) ;
#74 = DIRECTION ( 'NONE',  ( -9.856383386231858827E-17, -1.000000000000000000, 0.000000000000000000 ) ) ;
#75 = ADVANCED_FACE ( 'NONE', ( #933 ), #790, .F. ) ;
#76 = ADVANCED_FACE ( 'NONE', ( #91 ), #668, .F. ) ;
#77 = EDGE_CURVE ( 'NONE', #568, #263, #791, .T. ) ;
#78 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#79 = LINE ( 'NONE', #90, #439 ) ;
#80 = LINE ( 'NONE', #455, #216 ) ;
#81 = ORIENTED_EDGE ( 'NONE', *, *, #544, .T. ) ;
#82 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -1.265110831387390000 ) ) ;
#83 = ADVANCED_FACE ( 'NONE', ( #670 ), #956, .F. ) ;
#84 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999994893, -0.4485249675906649025, -0.2500000000000000000 ) ) ;
#85 = ORIENTED_EDGE ( 'NONE', *, *, #1131, .T. ) ;
#86 = ORIENTED_EDGE ( 'NONE', *, *, #1107, .F. ) ;
#87 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#88 = EDGE_CURVE ( 'NONE', #321, #418, #434, .T. ) ;
#89 = ORIENTED_EDGE ( 'NONE', *, *, #1174, .F. ) ;
#90 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999996003, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#91 = FACE_OUTER_BOUND ( 'NONE', #159, .T. ) ;
#92 = LINE ( 'NONE', #95, #769 ) ;
#93 = AXIS2_PLACEMENT_3D ( 'NONE', #1177, #47, #236 ) ;
#94 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#95 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, -3.009265538105056020E-33, -0.2500000000000000000 ) ) ;
#96 = ORIENTED_EDGE ( 'NONE', *, *, #874, .T. ) ;
#97 = ORIENTED_EDGE ( 'NONE', *, *, #931, .F. ) ;
#98 = ORIENTED_EDGE ( 'NONE', *, *, #126, .T. ) ;
#99 = EDGE_CURVE ( 'NONE', #773, #794, #762, .T. ) ;
#100 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 1.457260984523527380E-16, 0.000000000000000000 ) ) ;
#101 = EDGE_CURVE ( 'NONE', #192, #140, #859, .T. ) ;
#102 = ADVANCED_FACE ( 'NONE', ( #151 ), #768, .T. ) ;
#103 = LINE ( 'NONE', #1109, #850 ) ;
#104 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#105 = FACE_OUTER_BOUND ( 'NONE', #704, .T. ) ;
#106 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #1180 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #652, #1045, #557 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#107 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#108 = VERTEX_POINT ( 'NONE', #871 ) ;
#109 = ORIENTED_EDGE ( 'NONE', *, *, #636, .F. ) ;
#110 = ORIENTED_EDGE ( 'NONE', *, *, #730, .T. ) ;
#111 = VECTOR ( 'NONE', #363, 1000.000000000000000 ) ;
#112 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#113 = ORIENTED_EDGE ( 'NONE', *, *, #1116, .F. ) ;
#114 = EDGE_CURVE ( 'NONE', #182, #731, #420, .T. ) ;
#115 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#116 = FACE_OUTER_BOUND ( 'NONE', #1081, .T. ) ;
#117 = VECTOR ( 'NONE', #1092, 1000.000000000000000 ) ;
#118 = PLANE ( 'NONE',  #1142 ) ;
#119 = VECTOR ( 'NONE', #26, 1000.000000000000000 ) ;
#120 = EDGE_CURVE ( 'NONE', #504, #182, #163, .T. ) ;
#121 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#122 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -3.388131789017201849E-16 ) ) ;
#123 = ORIENTED_EDGE ( 'NONE', *, *, #1085, .F. ) ;
#124 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#125 = DIRECTION ( 'NONE',  ( 1.457260984523527380E-16, -1.000000000000000000, 0.000000000000000000 ) ) ;
#126 = EDGE_CURVE ( 'NONE', #162, #690, #970, .T. ) ;
#127 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3499999999999998668, -0.1700000000000000122 ) ) ;
#128 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3499999999999999778, 0.2500000000000000000 ) ) ;
#129 = AXIS2_PLACEMENT_3D ( 'NONE', #487, #125, #785 ) ;
#130 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -1.016439536705160604E-15 ) ) ;
#131 = PLANE ( 'NONE',  #548 ) ;
#132 = VECTOR ( 'NONE', #256, 1000.000000000000000 ) ;
#133 = VECTOR ( 'NONE', #48, 1000.000000000000000 ) ;
#134 = LINE ( 'NONE', #1118, #1095 ) ;
#135 = ORIENTED_EDGE ( 'NONE', *, *, #770, .T. ) ;
#136 = EDGE_CURVE ( 'NONE', #1153, #1055, #139, .T. ) ;
#137 = DIRECTION ( 'NONE',  ( 1.714505518806294884E-16, 1.000000000000000000, 0.000000000000000000 ) ) ;
#138 = EDGE_LOOP ( 'NONE', ( #887, #1042, #445, #669 ) ) ;
#139 = LINE ( 'NONE', #453, #685 ) ;
#140 = VERTEX_POINT ( 'NONE', #470 ) ;
#141 = EDGE_LOOP ( 'NONE', ( #733, #225, #113, #1206 ) ) ;
#142 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#143 = EDGE_CURVE ( 'NONE', #1080, #263, #635, .T. ) ;
#144 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#145 = AXIS2_PLACEMENT_3D ( 'NONE', #909, #315, #998 ) ;
#146 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999778, -0.2500000000000000000 ) ) ;
#147 = VECTOR ( 'NONE', #922, 1000.000000000000000 ) ;
#148 = ORIENTED_EDGE ( 'NONE', *, *, #651, .F. ) ;
#149 = ADVANCED_FACE ( 'NONE', ( #366 ), #566, .T. ) ;
#150 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#151 = FACE_OUTER_BOUND ( 'NONE', #591, .T. ) ;
#152 = ORIENTED_EDGE ( 'NONE', *, *, #730, .F. ) ;
#153 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#154 = EDGE_CURVE ( 'NONE', #285, #1151, #597, .T. ) ;
#155 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -1.457260984523527380E-16, 0.000000000000000000 ) ) ;
#156 = LINE ( 'NONE', #752, #132 ) ;
#157 = VECTOR ( 'NONE', #283, 1000.000000000000000 ) ;
#158 = PRESENTATION_STYLE_ASSIGNMENT (( #692 ) ) ;
#159 = EDGE_LOOP ( 'NONE', ( #1221, #955, #813, #1123 ) ) ;
#160 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.2500000000000000000 ) ) ;
#161 = EDGE_CURVE ( 'NONE', #658, #307, #40, .T. ) ;
#162 = VERTEX_POINT ( 'NONE', #191 ) ;
#163 = LINE ( 'NONE', #946, #463 ) ;
#164 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#165 = VERTEX_POINT ( 'NONE', #183 ) ;
#166 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#167 = ORIENTED_EDGE ( 'NONE', *, *, #143, .T. ) ;
#168 = ADVANCED_BREP_SHAPE_REPRESENTATION ( 'ERJH2RD1000X', ( #323, #817, #357, #896, #1046 ), #757 ) ;
#169 = PLANE ( 'NONE',  #432 ) ;
#170 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#171 = AXIS2_PLACEMENT_3D ( 'NONE', #952, #1059, #107 ) ;
#172 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3499999999999998668, -0.2500000000000000000 ) ) ;
#173 = LINE ( 'NONE', #359, #62 ) ;
#174 = FACE_OUTER_BOUND ( 'NONE', #567, .T. ) ;
#175 = LINE ( 'NONE', #594, #231 ) ;
#176 = ORIENTED_EDGE ( 'NONE', *, *, #3, .F. ) ;
#177 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3499999999999999778, 0.1699999999999999845 ) ) ;
#178 = FACE_OUTER_BOUND ( 'NONE', #319, .T. ) ;
#179 = AXIS2_PLACEMENT_3D ( 'NONE', #468, #380, #771 ) ;
#180 = VERTEX_POINT ( 'NONE', #456 ) ;
#181 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3324999999999998512, 0.1699999999999999012 ) ) ;
#182 = VERTEX_POINT ( 'NONE', #571 ) ;
#183 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999889, 0.3324999999999998512, 0.1699999999999998734 ) ) ;
#184 = EDGE_CURVE ( 'NONE', #673, #42, #1203, .T. ) ;
#185 = VECTOR ( 'NONE', #686, 1000.000000000000000 ) ;
#186 = VECTOR ( 'NONE', #58, 1000.000000000000000 ) ;
#187 = VECTOR ( 'NONE', #1105, 1000.000000000000000 ) ;
#188 = LINE ( 'NONE', #671, #644 ) ;
#189 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -1.016439536705160604E-15 ) ) ;
#190 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, 0.000000000000000000 ) ) ;
#191 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#192 = VERTEX_POINT ( 'NONE', #979 ) ;
#193 = EDGE_CURVE ( 'NONE', #764, #690, #831, .T. ) ;
#194 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, -3.009265538105056020E-33, -0.2500000000000000000 ) ) ;
#195 = LINE ( 'NONE', #940, #387 ) ;
#196 = PRESENTATION_STYLE_ASSIGNMENT (( #709 ) ) ;
#197 = LINE ( 'NONE', #519, #629 ) ;
#198 = FACE_OUTER_BOUND ( 'NONE', #551, .T. ) ;
#199 = STYLED_ITEM ( 'NONE', ( #196 ), #168 ) ;
#200 = DIRECTION ( 'NONE',  ( -1.084202172485504434E-16, -0.000000000000000000, -1.000000000000000000 ) ) ;
#201 = ORIENTED_EDGE ( 'NONE', *, *, #640, .T. ) ;
#202 = ORIENTED_EDGE ( 'NONE', *, *, #114, .F. ) ;
#203 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#204 = PLANE ( 'NONE',  #642 ) ;
#205 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#206 = ORIENTED_EDGE ( 'NONE', *, *, #891, .F. ) ;
#207 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, -0.4485249675906648470, 0.2500000000000000000 ) ) ;
#208 = AXIS2_PLACEMENT_3D ( 'NONE', #821, #330, #1189 ) ;
#209 = PLANE ( 'NONE',  #1143 ) ;
#210 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#211 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3324999999999999623, 0.1699999999999999567 ) ) ;
#212 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, -1.265110831387390000 ) ) ;
#213 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#214 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#215 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#216 = VECTOR ( 'NONE', #554, 1000.000000000000000 ) ;
#217 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#218 = LINE ( 'NONE', #977, #407 ) ;
#219 = EDGE_LOOP ( 'NONE', ( #715, #935, #855, #687, #793, #638, #394, #81 ) ) ;
#220 = ORIENTED_EDGE ( 'NONE', *, *, #1000, .F. ) ;
#221 = VECTOR ( 'NONE', #672, 1000.000000000000000 ) ;
#222 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#223 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#224 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#225 = ORIENTED_EDGE ( 'NONE', *, *, #99, .F. ) ;
#226 = VERTEX_POINT ( 'NONE', #406 ) ;
#227 = EDGE_CURVE ( 'NONE', #1153, #1099, #703, .T. ) ;
#228 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', 'automotive_design', 1998, #1176 ) ;
#229 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#230 = FACE_OUTER_BOUND ( 'NONE', #876, .T. ) ;
#231 = VECTOR ( 'NONE', #982, 1000.000000000000000 ) ;
#232 = ORIENTED_EDGE ( 'NONE', *, *, #437, .T. ) ;
#233 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3324999999999998512, -0.1700000000000000122 ) ) ;
#234 = ORIENTED_EDGE ( 'NONE', *, *, #675, .T. ) ;
#235 = ORIENTED_EDGE ( 'NONE', *, *, #295, .T. ) ;
#236 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#237 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3499999999999999778, 0.2500000000000000000 ) ) ;
#238 = EDGE_CURVE ( 'NONE', #921, #1151, #1084, .T. ) ;
#239 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3324999999999998512, 0.1699999999999999012 ) ) ;
#240 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#241 = VECTOR ( 'NONE', #517, 1000.000000000000000 ) ;
#242 = ADVANCED_FACE ( 'NONE', ( #178 ), #1009, .F. ) ;
#243 = LINE ( 'NONE', #625, #49 ) ;
#244 = EDGE_CURVE ( 'NONE', #1006, #42, #1024, .T. ) ;
#245 = ORIENTED_EDGE ( 'NONE', *, *, #1058, .F. ) ;
#246 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #525 ), #411 ) ;
#247 = VECTOR ( 'NONE', #271, 1000.000000000000000 ) ;
#248 = ADVANCED_FACE ( 'NONE', ( #303 ), #508, .F. ) ;
#249 = EDGE_LOOP ( 'NONE', ( #987, #201, #395, #943, #461, #277, #893, #436 ) ) ;
#250 = ADVANCED_FACE ( 'NONE', ( #448 ), #620, .F. ) ;
#251 = ORIENTED_EDGE ( 'NONE', *, *, #3, .T. ) ;
#252 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -1.265110831387390000 ) ) ;
#253 = EDGE_CURVE ( 'NONE', #854, #518, #1031, .T. ) ;
#254 = VECTOR ( 'NONE', #725, 1000.000000000000000 ) ;
#255 = EDGE_LOOP ( 'NONE', ( #251, #73, #1083, #561 ) ) ;
#256 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#257 = EDGE_CURVE ( 'NONE', #180, #673, #881, .T. ) ;
#258 = ORIENTED_EDGE ( 'NONE', *, *, #143, .F. ) ;
#259 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, 0.3499999999999999223, -0.2500000000000000000 ) ) ;
#260 = ORIENTED_EDGE ( 'NONE', *, *, #828, .T. ) ;
#261 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#262 = PRODUCT ( 'ERJH2RD1000X', 'ERJH2RD1000X', '', ( #507 ) ) ;
#263 = VERTEX_POINT ( 'NONE', #146 ) ;
#264 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#265 = SURFACE_STYLE_USAGE ( .BOTH. , #659 ) ;
#266 = AXIS2_PLACEMENT_3D ( 'NONE', #499, #222, #972 ) ;
#267 = VECTOR ( 'NONE', #150, 1000.000000000000000 ) ;
#268 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#269 = VECTOR ( 'NONE', #814, 1000.000000000000000 ) ;
#270 = FACE_OUTER_BOUND ( 'NONE', #1225, .T. ) ;
#271 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -1.807003620809176604E-16, 0.000000000000000000 ) ) ;
#272 = CLOSED_SHELL ( 'NONE', ( #149, #102, #718, #516, #910, #65 ) ) ;
#273 = ORIENTED_EDGE ( 'NONE', *, *, #244, .T. ) ;
#274 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, 0.3499999999999999223, -0.2500000000000000000 ) ) ;
#275 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, 0.000000000000000000 ) ) ;
#276 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996558, -0.4485249675906648470, -0.2500000000000000000 ) ) ;
#277 = ORIENTED_EDGE ( 'NONE', *, *, #1216, .T. ) ;
#278 = ORIENTED_EDGE ( 'NONE', *, *, #136, .T. ) ;
#279 = EDGE_CURVE ( 'NONE', #140, #794, #1185, .T. ) ;
#280 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #765 ), #502 ) ;
#281 = LINE ( 'NONE', #259, #1060 ) ;
#282 = ORIENTED_EDGE ( 'NONE', *, *, #1156, .F. ) ;
#283 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#284 = ORIENTED_EDGE ( 'NONE', *, *, #1029, .F. ) ;
#285 = VERTEX_POINT ( 'NONE', #811 ) ;
#286 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 3.388131789017201849E-16 ) ) ;
#287 = ORIENTED_EDGE ( 'NONE', *, *, #1044, .T. ) ;
#288 = EDGE_CURVE ( 'NONE', #353, #165, #197, .T. ) ;
#289 = CARTESIAN_POINT ( 'NONE',  ( 6.324512672832117005E-17, 0.3499999999999999223, -6.857050379796685797E-33 ) ) ;
#290 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#291 = ORIENTED_EDGE ( 'NONE', *, *, #911, .F. ) ;
#292 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#293 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3499999999999999778, 0.1699999999999999567 ) ) ;
#294 = DIRECTION ( 'NONE',  ( 1.084202172485504434E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#295 = EDGE_CURVE ( 'NONE', #515, #518, #680, .T. ) ;
#296 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#297 = LINE ( 'NONE', #726, #332 ) ;
#298 = VECTOR ( 'NONE', #501, 1000.000000000000000 ) ;
#299 = ORIENTED_EDGE ( 'NONE', *, *, #238, .T. ) ;
#300 = ORIENTED_EDGE ( 'NONE', *, *, #340, .F. ) ;
#301 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3324999999999999623, 0.1699999999999999567 ) ) ;
#302 = ORIENTED_EDGE ( 'NONE', *, *, #874, .F. ) ;
#303 = FACE_OUTER_BOUND ( 'NONE', #138, .T. ) ;
#304 = VERTEX_POINT ( 'NONE', #429 ) ;
#305 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#306 = EDGE_CURVE ( 'NONE', #226, #515, #297, .T. ) ;
#307 = VERTEX_POINT ( 'NONE', #413 ) ;
#308 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#309 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#310 = LINE ( 'NONE', #485, #1106 ) ;
#311 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#312 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#313 = LINE ( 'NONE', #634, #826 ) ;
#314 = EDGE_CURVE ( 'NONE', #392, #504, #772, .T. ) ;
#315 = DIRECTION ( 'NONE',  ( 1.016439536705160604E-15, -1.001841776268995334E-31, 1.000000000000000000 ) ) ;
#316 = LINE ( 'NONE', #424, #254 ) ;
#317 = ORIENTED_EDGE ( 'NONE', *, *, #1049, .F. ) ;
#318 = ADVANCED_FACE ( 'NONE', ( #743 ), #1111, .F. ) ;
#319 = EDGE_LOOP ( 'NONE', ( #148, #1219, #260, #614 ) ) ;
#320 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999334, 0.3324999999999998512, -0.1699999999999999289 ) ) ;
#321 = VERTEX_POINT ( 'NONE', #934 ) ;
#322 = ADVANCED_FACE ( 'NONE', ( #1161 ), #633, .F. ) ;
#323 = MANIFOLD_SOLID_BREP ( 'Boss-Extrude2', #339 ) ;
#324 = ORIENTED_EDGE ( 'NONE', *, *, #1085, .T. ) ;
#325 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#326 = ADVANCED_FACE ( 'NONE', ( #174 ), #344, .T. ) ;
#327 = VECTOR ( 'NONE', #1079, 1000.000000000000000 ) ;
#328 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#329 = LINE ( 'NONE', #4, #185 ) ;
#330 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -9.856383386231858827E-17, 0.000000000000000000 ) ) ;
#331 = PLANE ( 'NONE',  #30 ) ;
#332 = VECTOR ( 'NONE', #421, 1000.000000000000000 ) ;
#333 = LINE ( 'NONE', #700, #457 ) ;
#334 = ORIENTED_EDGE ( 'NONE', *, *, #1029, .T. ) ;
#335 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -1.265110831387390000 ) ) ;
#336 = VECTOR ( 'NONE', #369, 1000.000000000000000 ) ;
#337 = PLANE ( 'NONE',  #570 ) ;
#338 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3324999999999998512, -0.1700000000000000122 ) ) ;
#339 = CLOSED_SHELL ( 'NONE', ( #923, #732, #326, #834, #822, #711 ) ) ;
#340 = EDGE_CURVE ( 'NONE', #1151, #140, #92, .T. ) ;
#341 = ADVANCED_FACE ( 'NONE', ( #915 ), #352, .T. ) ;
#342 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, 0.000000000000000000 ) ) ;
#343 = DIRECTION ( 'NONE',  ( -1.084202172485504557E-16, 0.000000000000000000, -1.000000000000000000 ) ) ;
#344 = PLANE ( 'NONE',  #596 ) ;
#345 = LINE ( 'NONE', #688, #1018 ) ;
#346 = EDGE_CURVE ( 'NONE', #165, #8, #156, .T. ) ;
#347 = VERTEX_POINT ( 'NONE', #1 ) ;
#348 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#349 = ORIENTED_EDGE ( 'NONE', *, *, #184, .T. ) ;
#350 = VERTEX_POINT ( 'NONE', #937 ) ;
#351 = VECTOR ( 'NONE', #309, 1000.000000000000000 ) ;
#352 = PLANE ( 'NONE',  #469 ) ;
#353 = VERTEX_POINT ( 'NONE', #181 ) ;
#354 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #787 ) ) ;
#355 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, -0.2499999999999998890 ) ) ;
#356 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#357 = MANIFOLD_SOLID_BREP ( 'Cut-Extrude1[1]', #1104 ) ;
#358 = VECTOR ( 'NONE', #403, 1000.000000000000000 ) ;
#359 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999889, 0.3499999999999998668, 0.1699999999999998734 ) ) ;
#360 = EDGE_CURVE ( 'NONE', #1148, #8, #173, .T. ) ;
#361 = VERTEX_POINT ( 'NONE', #550 ) ;
#362 = VERTEX_POINT ( 'NONE', #170 ) ;
#363 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 1.714505518806294884E-16, 0.000000000000000000 ) ) ;
#364 = ORIENTED_EDGE ( 'NONE', *, *, #1121, .F. ) ;
#365 = PRODUCT_RELATED_PRODUCT_CATEGORY ( 'part', '', ( #262 ) ) ;
#366 = FACE_OUTER_BOUND ( 'NONE', #832, .T. ) ;
#367 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#368 = EDGE_LOOP ( 'NONE', ( #552, #98, #334, #648 ) ) ;
#369 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#370 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3324999999999999623, 0.1699999999999999567 ) ) ;
#371 = PLANE ( 'NONE',  #734 ) ;
#372 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#373 = VECTOR ( 'NONE', #522, 1000.000000000000000 ) ;
#374 = LINE ( 'NONE', #290, #1122 ) ;
#375 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -0.000000000000000000, -3.388131789017201849E-16 ) ) ;
#376 = ORIENTED_EDGE ( 'NONE', *, *, #430, .T. ) ;
#377 = ORIENTED_EDGE ( 'NONE', *, *, #340, .T. ) ;
#378 = FACE_OUTER_BOUND ( 'NONE', #812, .T. ) ;
#379 = EDGE_CURVE ( 'NONE', #988, #42, #511, .T. ) ;
#380 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#381 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996558, -0.4485249675906648470, -0.2500000000000000000 ) ) ;
#382 = EDGE_LOOP ( 'NONE', ( #775, #1190, #1068, #273 ) ) ;
#383 = ORIENTED_EDGE ( 'NONE', *, *, #613, .T. ) ;
#384 = FILL_AREA_STYLE_COLOUR ( '', #5 ) ;
#385 = EDGE_CURVE ( 'NONE', #69, #764, #545, .T. ) ;
#386 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#387 = VECTOR ( 'NONE', #543, 1000.000000000000000 ) ;
#388 = LINE ( 'NONE', #626, #655 ) ;
#389 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#390 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#391 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -3.388131789017201849E-16 ) ) ;
#392 = VERTEX_POINT ( 'NONE', #744 ) ;
#393 = EDGE_LOOP ( 'NONE', ( #577, #1207, #496, #581, #287, #235, #975, #383 ) ) ;
#394 = ORIENTED_EDGE ( 'NONE', *, *, #918, .T. ) ;
#395 = ORIENTED_EDGE ( 'NONE', *, *, #613, .F. ) ;
#396 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#397 = ORIENTED_EDGE ( 'NONE', *, *, #971, .F. ) ;
#398 = ORIENTED_EDGE ( 'NONE', *, *, #763, .T. ) ;
#399 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999889, 0.3499999999999998668, 0.1699999999999998734 ) ) ;
#400 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#401 = PLANE ( 'NONE',  #835 ) ;
#402 = VECTOR ( 'NONE', #510, 1000.000000000000000 ) ;
#403 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#404 = VECTOR ( 'NONE', #920, 1000.000000000000000 ) ;
#405 = LINE ( 'NONE', #356, #157 ) ;
#406 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#407 = VECTOR ( 'NONE', #778, 1000.000000000000000 ) ;
#408 = DIRECTION ( 'NONE',  ( -9.856383386231858827E-17, -1.000000000000000000, 0.000000000000000000 ) ) ;
#409 = VECTOR ( 'NONE', #853, 1000.000000000000000 ) ;
#410 = EDGE_CURVE ( 'NONE', #8, #1055, #79, .T. ) ;
#411 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #983 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1211, #842, #268 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#412 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#413 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999996003, 0.3499999999999998668, 0.2500000000000000000 ) ) ;
#414 = FACE_OUTER_BOUND ( 'NONE', #889, .T. ) ;
#415 = AXIS2_PLACEMENT_3D ( 'NONE', #289, #1061, #1157 ) ;
#416 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, -4.286263797015737211E-17, -0.2500000000000000000 ) ) ;
#417 = FACE_OUTER_BOUND ( 'NONE', #52, .T. ) ;
#418 = VERTEX_POINT ( 'NONE', #845 ) ;
#419 = ORIENTED_EDGE ( 'NONE', *, *, #13, .T. ) ;
#420 = LINE ( 'NONE', #194, #111 ) ;
#421 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#422 = ORIENTED_EDGE ( 'NONE', *, *, #379, .T. ) ;
#423 = AXIS2_PLACEMENT_3D ( 'NONE', #215, #560, #100 ) ;
#424 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999995448, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#425 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#426 = SURFACE_STYLE_USAGE ( .BOTH. , #926 ) ;
#427 = ADVANCED_FACE ( 'NONE', ( #1145 ), #1047, .F. ) ;
#428 = VECTOR ( 'NONE', #1008, 1000.000000000000000 ) ;
#429 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#430 = EDGE_CURVE ( 'NONE', #361, #37, #524, .T. ) ;
#431 = LINE ( 'NONE', #960, #119 ) ;
#432 = AXIS2_PLACEMENT_3D ( 'NONE', #540, #164, #1197 ) ;
#433 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#434 = LINE ( 'NONE', #1063, #717 ) ;
#435 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#436 = ORIENTED_EDGE ( 'NONE', *, *, #53, .T. ) ;
#437 = EDGE_CURVE ( 'NONE', #773, #350, #195, .T. ) ;
#438 = FILL_AREA_STYLE ('',( #1032 ) ) ;
#439 = VECTOR ( 'NONE', #261, 1000.000000000000000 ) ;
#440 = SURFACE_STYLE_FILL_AREA ( #1014 ) ;
#441 = VECTOR ( 'NONE', #78, 1000.000000000000000 ) ;
#442 = EDGE_CURVE ( 'NONE', #304, #1038, #17, .T. ) ;
#443 = ORIENTED_EDGE ( 'NONE', *, *, #856, .F. ) ;
#444 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999995656, -0.2500000000000000000 ) ) ;
#445 = ORIENTED_EDGE ( 'NONE', *, *, #707, .F. ) ;
#446 = ORIENTED_EDGE ( 'NONE', *, *, #60, .F. ) ;
#447 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#448 = FACE_OUTER_BOUND ( 'NONE', #1138, .T. ) ;
#449 = ORIENTED_EDGE ( 'NONE', *, *, #1223, .F. ) ;
#450 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#451 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#452 = AXIS2_PLACEMENT_3D ( 'NONE', #43, #879, #895 ) ;
#453 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999334, 0.3324999999999998512, -0.1699999999999999289 ) ) ;
#454 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, -0.2500000000000000000 ) ) ;
#455 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, -0.2499999999999998890 ) ) ;
#456 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3499999999999999778, -0.2499999999999998890 ) ) ;
#457 = VECTOR ( 'NONE', #601, 1000.000000000000000 ) ;
#458 = ORIENTED_EDGE ( 'NONE', *, *, #1147, .F. ) ;
#459 = AXIS2_PLACEMENT_3D ( 'NONE', #1169, #815, #224 ) ;
#460 = ORIENTED_EDGE ( 'NONE', *, *, #346, .F. ) ;
#461 = ORIENTED_EDGE ( 'NONE', *, *, #60, .T. ) ;
#462 = AXIS2_PLACEMENT_3D ( 'NONE', #142, #137, #523 ) ;
#463 = VECTOR ( 'NONE', #94, 1000.000000000000000 ) ;
#464 = ORIENTED_EDGE ( 'NONE', *, *, #578, .T. ) ;
#465 = ORIENTED_EDGE ( 'NONE', *, *, #68, .F. ) ;
#466 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #72, 'distance_accuracy_value', 'NONE');
#467 = EDGE_CURVE ( 'NONE', #22, #307, #868, .T. ) ;
#468 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#469 = AXIS2_PLACEMENT_3D ( 'NONE', #1129, #653, #264 ) ;
#470 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, -3.009265538105056020E-33, -0.2500000000000000000 ) ) ;
#471 = LINE ( 'NONE', #144, #186 ) ;
#472 = COLOUR_RGB ( '',1.000000000000000000, 1.000000000000000000, 1.000000000000000000 ) ;
#473 = VECTOR ( 'NONE', #292, 1000.000000000000000 ) ;
#474 = VERTEX_POINT ( 'NONE', #177 ) ;
#475 = ORIENTED_EDGE ( 'NONE', *, *, #1163, .F. ) ;
#476 = ORIENTED_EDGE ( 'NONE', *, *, #891, .T. ) ;
#477 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#478 = ORIENTED_EDGE ( 'NONE', *, *, #1163, .T. ) ;
#479 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#480 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#481 = ORIENTED_EDGE ( 'NONE', *, *, #856, .T. ) ;
#482 = DIRECTION ( 'NONE',  ( -1.714505518806294884E-16, 1.000000000000000000, 0.000000000000000000 ) ) ;
#483 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999889, 0.3324999999999998512, 0.1699999999999998734 ) ) ;
#484 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, -0.2500000000000000000 ) ) ;
#485 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999996003, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#486 = EDGE_CURVE ( 'NONE', #563, #990, #1133, .T. ) ;
#487 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#488 = AXIS2_PLACEMENT_3D ( 'NONE', #31, #973, #780 ) ;
#489 = PLANE ( 'NONE',  #677 ) ;
#490 = SURFACE_STYLE_FILL_AREA ( #66 ) ;
#491 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#492 = VECTOR ( 'NONE', #1154, 1000.000000000000000 ) ;
#493 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#494 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#495 = FACE_OUTER_BOUND ( 'NONE', #1204, .T. ) ;
#496 = ORIENTED_EDGE ( 'NONE', *, *, #120, .T. ) ;
#497 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#498 = EDGE_CURVE ( 'NONE', #1055, #50, #627, .T. ) ;
#499 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#500 = EDGE_LOOP ( 'NONE', ( #202, #967, #85, #1010 ) ) ;
#501 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#502 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #797 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #1115, #529, #67 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#503 = ORIENTED_EDGE ( 'NONE', *, *, #295, .F. ) ;
#504 = VERTEX_POINT ( 'NONE', #274 ) ;
#505 = ORIENTED_EDGE ( 'NONE', *, *, #227, .F. ) ;
#506 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#507 = PRODUCT_CONTEXT ( 'NONE', #527, 'mechanical' ) ;
#508 = PLANE ( 'NONE',  #666 ) ;
#509 = EDGE_CURVE ( 'NONE', #347, #658, #329, .T. ) ;
#510 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#511 = LINE ( 'NONE', #1214, #800 ) ;
#512 = ADVANCED_FACE ( 'NONE', ( #116 ), #587, .F. ) ;
#513 = LINE ( 'NONE', #1097, #402 ) ;
#514 = LINE ( 'NONE', #776, #20 ) ;
#515 = VERTEX_POINT ( 'NONE', #454 ) ;
#516 = ADVANCED_FACE ( 'NONE', ( #378 ), #131, .T. ) ;
#517 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#518 = VERTEX_POINT ( 'NONE', #390 ) ;
#519 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999889, 0.3324999999999998512, 0.1699999999999998734 ) ) ;
#520 = EDGE_CURVE ( 'NONE', #44, #108, #884, .T. ) ;
#521 = ORIENTED_EDGE ( 'NONE', *, *, #21, .T. ) ;
#522 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 3.388131789017201849E-16 ) ) ;
#523 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -1.714505518806294884E-16, 0.000000000000000000 ) ) ;
#524 = LINE ( 'NONE', #355, #900 ) ;
#525 = STYLED_ITEM ( 'NONE', ( #586 ), #357 ) ;
#526 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996558, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#527 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#528 = LINE ( 'NONE', #897, #373 ) ;
#529 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#530 = LINE ( 'NONE', #1181, #902 ) ;
#531 = ORIENTED_EDGE ( 'NONE', *, *, #430, .F. ) ;
#532 = PLANE ( 'NONE',  #942 ) ;
#533 = SURFACE_SIDE_STYLE ('',( #490 ) ) ;
#534 = VECTOR ( 'NONE', #584, 1000.000000000000000 ) ;
#535 = ADVANCED_FACE ( 'NONE', ( #230 ), #1074, .F. ) ;
#536 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.2500000000000000000 ) ) ;
#537 = ORIENTED_EDGE ( 'NONE', *, *, #486, .F. ) ;
#538 = EDGE_CURVE ( 'NONE', #563, #1006, #572, .T. ) ;
#539 = ORIENTED_EDGE ( 'NONE', *, *, #1000, .T. ) ;
#540 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#541 = ADVANCED_FACE ( 'NONE', ( #414 ), #600, .F. ) ;
#542 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#543 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -1.457260984523527380E-16, 0.000000000000000000 ) ) ;
#544 = EDGE_CURVE ( 'NONE', #362, #504, #9, .T. ) ;
#545 = LINE ( 'NONE', #944, #962 ) ;
#546 = PRESENTATION_STYLE_ASSIGNMENT (( #265 ) ) ;
#547 = VECTOR ( 'NONE', #892, 1000.000000000000000 ) ;
#548 = AXIS2_PLACEMENT_3D ( 'NONE', #207, #121, #886 ) ;
#549 = ORIENTED_EDGE ( 'NONE', *, *, #1049, .T. ) ;
#550 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#551 = EDGE_LOOP ( 'NONE', ( #481, #317, #779, #749 ) ) ;
#552 = ORIENTED_EDGE ( 'NONE', *, *, #1147, .T. ) ;
#553 = ORIENTED_EDGE ( 'NONE', *, *, #1116, .T. ) ;
#554 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#555 = AXIS2_PLACEMENT_3D ( 'NONE', #781, #482, #1086 ) ;
#556 = DIRECTION ( 'NONE',  ( 9.856383386231860060E-17, 1.000000000000000000, 0.000000000000000000 ) ) ;
#557 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#558 = LINE ( 'NONE', #716, #133 ) ;
#559 = EDGE_CURVE ( 'NONE', #1038, #921, #823, .T. ) ;
#560 = DIRECTION ( 'NONE',  ( -1.457260984523527380E-16, -1.000000000000000000, 0.000000000000000000 ) ) ;
#561 = ORIENTED_EDGE ( 'NONE', *, *, #77, .T. ) ;
#562 = ORIENTED_EDGE ( 'NONE', *, *, #753, .T. ) ;
#563 = VERTEX_POINT ( 'NONE', #301 ) ;
#564 = VERTEX_POINT ( 'NONE', #691 ) ;
#565 = VECTOR ( 'NONE', #296, 1000.000000000000000 ) ;
#566 = PLANE ( 'NONE',  #1036 ) ;
#567 = EDGE_LOOP ( 'NONE', ( #583, #1173, #843, #55 ) ) ;
#568 = VERTEX_POINT ( 'NONE', #989 ) ;
#569 = EDGE_CURVE ( 'NONE', #990, #474, #218, .T. ) ;
#570 = AXIS2_PLACEMENT_3D ( 'NONE', #160, #646, #1202 ) ;
#571 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, -4.286263797015737211E-17, -0.2500000000000000000 ) ) ;
#572 = LINE ( 'NONE', #493, #565 ) ;
#573 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#574 = DIRECTION ( 'NONE',  ( 3.388131789017201849E-16, -3.339472587563317779E-32, 1.000000000000000000 ) ) ;
#575 = FACE_OUTER_BOUND ( 'NONE', #255, .T. ) ;
#576 = LINE ( 'NONE', #928, #441 ) ;
#577 = ORIENTED_EDGE ( 'NONE', *, *, #931, .T. ) ;
#578 = EDGE_CURVE ( 'NONE', #872, #304, #964, .T. ) ;
#579 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, 0.1699999999999999845 ) ) ;
#580 = ORIENTED_EDGE ( 'NONE', *, *, #193, .F. ) ;
#581 = ORIENTED_EDGE ( 'NONE', *, *, #114, .T. ) ;
#582 = ORIENTED_EDGE ( 'NONE', *, *, #467, .F. ) ;
#583 = ORIENTED_EDGE ( 'NONE', *, *, #1017, .F. ) ;
#584 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -1.000000000000000000, -0.000000000000000000 ) ) ;
#585 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#586 = PRESENTATION_STYLE_ASSIGNMENT (( #858 ) ) ;
#587 = PLANE ( 'NONE',  #452 ) ;
#588 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, -0.1699999999999999012 ) ) ;
#589 = LINE ( 'NONE', #588, #784 ) ;
#590 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#591 = EDGE_LOOP ( 'NONE', ( #1025, #167, #1187, #86 ) ) ;
#592 = ORIENTED_EDGE ( 'NONE', *, *, #865, .T. ) ;
#593 = ORIENTED_EDGE ( 'NONE', *, *, #184, .F. ) ;
#594 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#595 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#596 = AXIS2_PLACEMENT_3D ( 'NONE', #335, #1220, #851 ) ;
#597 = LINE ( 'NONE', #1140, #336 ) ;
#598 = ORIENTED_EDGE ( 'NONE', *, *, #306, .F. ) ;
#599 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#600 = PLANE ( 'NONE',  #555 ) ;
#601 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#602 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#603 = PLANE ( 'NONE',  #6 ) ;
#604 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999334, 0.3499999999999998668, -0.1699999999999999289 ) ) ;
#605 = LINE ( 'NONE', #29, #947 ) ;
#606 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#607 = LINE ( 'NONE', #705, #241 ) ;
#608 = PLANE ( 'NONE',  #145 ) ;
#609 = VECTOR ( 'NONE', #1022, 1000.000000000000000 ) ;
#610 = ORIENTED_EDGE ( 'NONE', *, *, #1017, .T. ) ;
#611 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#612 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#613 = EDGE_CURVE ( 'NONE', #1130, #617, #175, .T. ) ;
#614 = ORIENTED_EDGE ( 'NONE', *, *, #840, .T. ) ;
#615 = ORIENTED_EDGE ( 'NONE', *, *, #1156, .T. ) ;
#616 = VECTOR ( 'NONE', #312, 1000.000000000000000 ) ;
#617 = VERTEX_POINT ( 'NONE', #1159 ) ;
#618 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#619 = EDGE_LOOP ( 'NONE', ( #592, #475, #1101, #1064 ) ) ;
#620 = PLANE ( 'NONE',  #93 ) ;
#621 = VERTEX_POINT ( 'NONE', #203 ) ;
#622 = ORIENTED_EDGE ( 'NONE', *, *, #136, .F. ) ;
#623 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#624 = ADVANCED_FACE ( 'NONE', ( #198 ), #39, .F. ) ;
#625 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#626 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#627 = LINE ( 'NONE', #838, #1108 ) ;
#628 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, -0.2499999999999998890 ) ) ;
#629 = VECTOR ( 'NONE', #916, 1000.000000000000000 ) ;
#630 = VECTOR ( 'NONE', #595, 1000.000000000000000 ) ;
#631 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999995656, -0.2500000000000000000 ) ) ;
#632 = ORIENTED_EDGE ( 'NONE', *, *, #1044, .F. ) ;
#633 = PLANE ( 'NONE',  #1093 ) ;
#634 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3499999999999998668, 0.2499999999999998890 ) ) ;
#635 = LINE ( 'NONE', #381, #1152 ) ;
#636 = EDGE_CURVE ( 'NONE', #1066, #321, #917, .T. ) ;
#637 = FILL_AREA_STYLE_COLOUR ( '', #1183 ) ;
#638 = ORIENTED_EDGE ( 'NONE', *, *, #865, .F. ) ;
#639 = ADVANCED_FACE ( 'NONE', ( #758 ), #401, .F. ) ;
#640 = EDGE_CURVE ( 'NONE', #1099, #617, #530, .T. ) ;
#641 = LINE ( 'NONE', #820, #267 ) ;
#642 = AXIS2_PLACEMENT_3D ( 'NONE', #1166, #497, #590 ) ;
#643 = VECTOR ( 'NONE', #286, 1000.000000000000000 ) ;
#644 = VECTOR ( 'NONE', #190, 1000.000000000000000 ) ;
#645 = ORIENTED_EDGE ( 'NONE', *, *, #257, .T. ) ;
#646 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#647 = ORIENTED_EDGE ( 'NONE', *, *, #101, .F. ) ;
#648 = ORIENTED_EDGE ( 'NONE', *, *, #88, .T. ) ;
#649 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999995448, -0.4485249675906649025, 0.2500000000000000000 ) ) ;
#650 = EDGE_LOOP ( 'NONE', ( #57, #774, #1096, #1113, #788, #376, #539, #996 ) ) ;
#651 = EDGE_CURVE ( 'NONE', #518, #1130, #1200, .T. ) ;
#652 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#653 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#654 = SURFACE_STYLE_FILL_AREA ( #438 ) ;
#655 = VECTOR ( 'NONE', #720, 1000.000000000000000 ) ;
#656 = VECTOR ( 'NONE', #611, 1000.000000000000000 ) ;
#657 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#658 = VERTEX_POINT ( 'NONE', #128 ) ;
#659 = SURFACE_SIDE_STYLE ('',( #697 ) ) ;
#660 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#661 = VECTOR ( 'NONE', #1057, 1000.000000000000000 ) ;
#662 = SURFACE_SIDE_STYLE ('',( #654 ) ) ;
#663 = ORIENTED_EDGE ( 'NONE', *, *, #53, .F. ) ;
#664 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 7.022034795890579217E-18, 0.000000000000000000 ) ) ;
#665 = LINE ( 'NONE', #696, #358 ) ;
#666 = AXIS2_PLACEMENT_3D ( 'NONE', #898, #997, #408 ) ;
#667 = FACE_OUTER_BOUND ( 'NONE', #993, .T. ) ;
#668 = PLANE ( 'NONE',  #171 ) ;
#669 = ORIENTED_EDGE ( 'NONE', *, *, #569, .T. ) ;
#670 = FACE_OUTER_BOUND ( 'NONE', #25, .T. ) ;
#671 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#672 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 1.807003620809176604E-16, -0.000000000000000000 ) ) ;
#673 = VERTEX_POINT ( 'NONE', #1114 ) ;
#674 = ORIENTED_EDGE ( 'NONE', *, *, #739, .T. ) ;
#675 = EDGE_CURVE ( 'NONE', #1098, #22, #316, .T. ) ;
#676 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -9.856383386231858827E-17, 0.000000000000000000 ) ) ;
#677 = AXIS2_PLACEMENT_3D ( 'NONE', #963, #210, #35 ) ;
#678 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3324999999999998512, -0.2500000000000000000 ) ) ;
#679 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#680 = LINE ( 'NONE', #1184, #404 ) ;
#681 = FACE_OUTER_BOUND ( 'NONE', #368, .T. ) ;
#682 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #525 ) ) ;
#683 = ORIENTED_EDGE ( 'NONE', *, *, #88, .F. ) ;
#684 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#685 = VECTOR ( 'NONE', #959, 1000.000000000000000 ) ;
#686 = DIRECTION ( 'NONE',  ( 9.856383386231860060E-17, 1.000000000000000000, 0.000000000000000000 ) ) ;
#687 = ORIENTED_EDGE ( 'NONE', *, *, #410, .F. ) ;
#688 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#689 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#690 = VERTEX_POINT ( 'NONE', #924 ) ;
#691 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#692 = SURFACE_STYLE_USAGE ( .BOTH. , #894 ) ;
#693 = VECTOR ( 'NONE', #599, 1000.000000000000000 ) ;
#694 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#695 = LINE ( 'NONE', #237, #117 ) ;
#696 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#697 = SURFACE_STYLE_FILL_AREA ( #1224 ) ;
#698 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -1.265110831387390000 ) ) ;
#699 = EDGE_CURVE ( 'NONE', #1080, #1098, #869, .T. ) ;
#700 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999995656, 0.2500000000000000000 ) ) ;
#701 = AXIS2_PLACEMENT_3D ( 'NONE', #890, #51, #992 ) ;
#702 = EDGE_LOOP ( 'NONE', ( #562, #97, #829, #728 ) ) ;
#703 = LINE ( 'NONE', #710, #1053 ) ;
#704 = EDGE_LOOP ( 'NONE', ( #580, #615, #7, #284 ) ) ;
#705 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#706 = ORIENTED_EDGE ( 'NONE', *, *, #675, .F. ) ;
#707 = EDGE_CURVE ( 'NONE', #990, #872, #951, .T. ) ;
#708 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 1.714505518806294884E-16, -0.000000000000000000 ) ) ;
#709 = SURFACE_STYLE_USAGE ( .BOTH. , #533 ) ;
#710 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999334, 0.3324999999999998512, -0.1699999999999999289 ) ) ;
#711 = ADVANCED_FACE ( 'NONE', ( #270 ), #532, .T. ) ;
#712 = VECTOR ( 'NONE', #342, 1000.000000000000000 ) ;
#713 = ORIENTED_EDGE ( 'NONE', *, *, #442, .T. ) ;
#714 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#715 = ORIENTED_EDGE ( 'NONE', *, *, #314, .F. ) ;
#716 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, -0.1699999999999998734 ) ) ;
#717 = VECTOR ( 'NONE', #15, 1000.000000000000000 ) ;
#718 = ADVANCED_FACE ( 'NONE', ( #837 ), #736, .T. ) ;
#719 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#720 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#721 = ADVANCED_FACE ( 'NONE', ( #786 ), #169, .F. ) ;
#722 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#723 = VECTOR ( 'NONE', #240, 1000.000000000000000 ) ;
#724 = ORIENTED_EDGE ( 'NONE', *, *, #1058, .T. ) ;
#725 = DIRECTION ( 'NONE',  ( 1.084202172485504557E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#726 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#727 = ORIENTED_EDGE ( 'NONE', *, *, #777, .T. ) ;
#728 = ORIENTED_EDGE ( 'NONE', *, *, #1223, .T. ) ;
#729 = VECTOR ( 'NONE', #375, 1000.000000000000000 ) ;
#730 = EDGE_CURVE ( 'NONE', #564, #621, #576, .T. ) ;
#731 = VERTEX_POINT ( 'NONE', #70 ) ;
#732 = ADVANCED_FACE ( 'NONE', ( #681 ), #337, .F. ) ;
#733 = ORIENTED_EDGE ( 'NONE', *, *, #279, .T. ) ;
#734 = AXIS2_PLACEMENT_3D ( 'NONE', #1056, #585, #573 ) ;
#735 = ORIENTED_EDGE ( 'NONE', *, *, #498, .T. ) ;
#736 = PLANE ( 'NONE',  #880 ) ;
#737 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 1.714505518806294884E-16, -0.000000000000000000 ) ) ;
#738 = LINE ( 'NONE', #981, #1212 ) ;
#739 = EDGE_CURVE ( 'NONE', #226, #854, #134, .T. ) ;
#740 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -9.856383386231860060E-17, -1.084202172485504557E-16 ) ) ;
#741 = VECTOR ( 'NONE', #750, 1000.000000000000000 ) ;
#742 = LINE ( 'NONE', #239, #147 ) ;
#743 = FACE_OUTER_BOUND ( 'NONE', #619, .T. ) ;
#744 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3499999999999999223, -0.2500000000000000000 ) ) ;
#745 = FACE_OUTER_BOUND ( 'NONE', #382, .T. ) ;
#746 = ORIENTED_EDGE ( 'NONE', *, *, #1131, .F. ) ;
#747 = ORIENTED_EDGE ( 'NONE', *, *, #699, .T. ) ;
#748 = ORIENTED_EDGE ( 'NONE', *, *, #288, .F. ) ;
#749 = ORIENTED_EDGE ( 'NONE', *, *, #99, .T. ) ;
#750 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#751 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 9.856383386231860060E-17, 1.084202172485504557E-16 ) ) ;
#752 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999889, 0.3324999999999998512, 0.1699999999999998734 ) ) ;
#753 = EDGE_CURVE ( 'NONE', #50, #392, #1218, .T. ) ;
#754 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#755 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, -0.1699999999999999012 ) ) ;
#756 = FACE_OUTER_BOUND ( 'NONE', #500, .T. ) ;
#757 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #466 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #72, #722, #425 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#758 = FACE_OUTER_BOUND ( 'NONE', #249, .T. ) ;
#759 = ORIENTED_EDGE ( 'NONE', *, *, #244, .F. ) ;
#760 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#761 = ORIENTED_EDGE ( 'NONE', *, *, #899, .F. ) ;
#762 = LINE ( 'NONE', #760, #661 ) ;
#763 = EDGE_CURVE ( 'NONE', #907, #872, #1062, .T. ) ;
#764 = VERTEX_POINT ( 'NONE', #444 ) ;
#765 = STYLED_ITEM ( 'NONE', ( #1217 ), #323 ) ;
#766 = EDGE_LOOP ( 'NONE', ( #957, #89, #282, #1175 ) ) ;
#767 = DIRECTION ( 'NONE',  ( 1.084202172485504557E-16, 0.000000000000000000, 1.000000000000000000 ) ) ;
#768 = PLANE ( 'NONE',  #795 ) ;
#769 = VECTOR ( 'NONE', #737, 1000.000000000000000 ) ;
#770 = EDGE_CURVE ( 'NONE', #362, #1070, #36, .T. ) ;
#771 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#772 = LINE ( 'NONE', #63, #19 ) ;
#773 = VERTEX_POINT ( 'NONE', #789 ) ;
#774 = ORIENTED_EDGE ( 'NONE', *, *, #707, .T. ) ;
#775 = ORIENTED_EDGE ( 'NONE', *, *, #379, .F. ) ;
#776 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999995448, 0.3499999999999998668, -0.2500000000000000000 ) ) ;
#777 = EDGE_CURVE ( 'NONE', #1067, #226, #345, .T. ) ;
#778 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#779 = ORIENTED_EDGE ( 'NONE', *, *, #437, .F. ) ;
#780 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 3.388131789017201849E-16 ) ) ;
#781 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#782 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#783 = SHAPE_DEFINITION_REPRESENTATION ( #16, #168 ) ;
#784 = VECTOR ( 'NONE', #122, 1000.000000000000000 ) ;
#785 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 1.457260984523527380E-16, 0.000000000000000000 ) ) ;
#786 = FACE_OUTER_BOUND ( 'NONE', #932, .T. ) ;
#787 = STYLED_ITEM ( 'NONE', ( #158 ), #817 ) ;
#788 = ORIENTED_EDGE ( 'NONE', *, *, #1065, .T. ) ;
#789 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#790 = PLANE ( 'NONE',  #423 ) ;
#791 = LINE ( 'NONE', #1144, #221 ) ;
#792 = ORIENTED_EDGE ( 'NONE', *, *, #509, .T. ) ;
#793 = ORIENTED_EDGE ( 'NONE', *, *, #360, .F. ) ;
#794 = VERTEX_POINT ( 'NONE', #484 ) ;
#795 = AXIS2_PLACEMENT_3D ( 'NONE', #1052, #660, #864 ) ;
#796 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#797 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1115, 'distance_accuracy_value', 'NONE');
#798 = AXIS2_PLACEMENT_3D ( 'NONE', #213, #984, #494 ) ;
#799 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#800 = VECTOR ( 'NONE', #87, 1000.000000000000000 ) ;
#801 = VECTOR ( 'NONE', #824, 1000.000000000000000 ) ;
#802 = ADVANCED_FACE ( 'NONE', ( #745 ), #204, .F. ) ;
#803 = FACE_OUTER_BOUND ( 'NONE', #958, .T. ) ;
#804 = ORIENTED_EDGE ( 'NONE', *, *, #467, .T. ) ;
#805 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999994893, -0.4485249675906649025, -0.2500000000000000000 ) ) ;
#806 = PRODUCT_DEFINITION ( 'UNKNOWN', '', #1003, #991 ) ;
#807 = ORIENTED_EDGE ( 'NONE', *, *, #1107, .T. ) ;
#808 = ORIENTED_EDGE ( 'NONE', *, *, #994, .T. ) ;
#809 = ADVANCED_FACE ( 'NONE', ( #417 ), #489, .F. ) ;
#810 =( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) );
#811 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, 0.3499999999999999223, -0.2500000000000000000 ) ) ;
#812 = EDGE_LOOP ( 'NONE', ( #761, #804, #23, #919 ) ) ;
#813 = ORIENTED_EDGE ( 'NONE', *, *, #559, .F. ) ;
#814 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#815 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#816 = ADVANCED_FACE ( 'NONE', ( #667 ), #883, .T. ) ;
#817 = MANIFOLD_SOLID_BREP ( 'Boss-Extrude3', #272 ) ;
#818 = AXIS2_PLACEMENT_3D ( 'NONE', #450, #1139, #447 ) ;
#819 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#820 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#821 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3324999999999998512, 0.2499999999999998890 ) ) ;
#822 = ADVANCED_FACE ( 'NONE', ( #495 ), #603, .T. ) ;
#823 = LINE ( 'NONE', #477, #534 ) ;
#824 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -1.457260984523527380E-16, 0.000000000000000000 ) ) ;
#825 = LINE ( 'NONE', #925, #927 ) ;
#826 = VECTOR ( 'NONE', #433, 1000.000000000000000 ) ;
#827 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 9.856383386231858827E-17, 0.000000000000000000 ) ) ;
#828 = EDGE_CURVE ( 'NONE', #854, #27, #374, .T. ) ;
#829 = ORIENTED_EDGE ( 'NONE', *, *, #640, .F. ) ;
#830 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#831 = LINE ( 'NONE', #1087, #616 ) ;
#832 = EDGE_LOOP ( 'NONE', ( #291, #792, #176, #258 ) ) ;
#833 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#834 = ADVANCED_FACE ( 'NONE', ( #105 ), #877, .T. ) ;
#835 = AXIS2_PLACEMENT_3D ( 'NONE', #1089, #115, #719 ) ;
#836 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#837 = FACE_OUTER_BOUND ( 'NONE', #1172, .T. ) ;
#838 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999334, 0.3499999999999998668, -0.1699999999999999289 ) ) ;
#839 = ORIENTED_EDGE ( 'NONE', *, *, #1065, .F. ) ;
#840 = EDGE_CURVE ( 'NONE', #27, #1130, #243, .T. ) ;
#841 = ORIENTED_EDGE ( 'NONE', *, *, #1091, .T. ) ;
#842 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#843 = ORIENTED_EDGE ( 'NONE', *, *, #193, .T. ) ;
#844 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#845 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#846 = EDGE_CURVE ( 'NONE', #307, #568, #514, .T. ) ;
#847 = LINE ( 'NONE', #211, #327 ) ;
#848 = AXIS2_PLACEMENT_3D ( 'NONE', #678, #676, #275 ) ;
#849 = VECTOR ( 'NONE', #396, 1000.000000000000000 ) ;
#850 = VECTOR ( 'NONE', #708, 1000.000000000000000 ) ;
#851 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 7.022034795890579217E-18, 0.000000000000000000 ) ) ;
#852 = VECTOR ( 'NONE', #112, 1000.000000000000000 ) ;
#853 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#854 = VERTEX_POINT ( 'NONE', #606 ) ;
#855 = ORIENTED_EDGE ( 'NONE', *, *, #498, .F. ) ;
#856 = EDGE_CURVE ( 'NONE', #794, #564, #1002, .T. ) ;
#857 = ORIENTED_EDGE ( 'NONE', *, *, #1196, .F. ) ;
#858 = SURFACE_STYLE_USAGE ( .BOTH. , #662 ) ;
#859 = LINE ( 'NONE', #372, #409 ) ;
#860 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #787 ), #1051 ) ;
#861 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3499999999999999778, 0.2500000000000000000 ) ) ;
#862 = ADVANCED_FACE ( 'NONE', ( #803 ), #1023, .F. ) ;
#863 = PLANE ( 'NONE',  #415 ) ;
#864 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#865 = EDGE_CURVE ( 'NONE', #108, #1148, #313, .T. ) ;
#866 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999778, -0.1699999999999999012 ) ) ;
#867 = ADVANCED_FACE ( 'NONE', ( #950 ), #331, .F. ) ;
#868 = LINE ( 'NONE', #649, #1158 ) ;
#869 = LINE ( 'NONE', #526, #712 ) ;
#870 = ORIENTED_EDGE ( 'NONE', *, *, #846, .F. ) ;
#871 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3499999999999998668, 0.2499999999999998890 ) ) ;
#872 = VERTEX_POINT ( 'NONE', #836 ) ;
#873 = VECTOR ( 'NONE', #229, 1000.000000000000000 ) ;
#874 = EDGE_CURVE ( 'NONE', #361, #180, #80, .T. ) ;
#875 = EDGE_LOOP ( 'NONE', ( #1054, #622, #663, #1137 ) ) ;
#876 = EDGE_LOOP ( 'NONE', ( #1222, #1117, #135, #1209 ) ) ;
#877 = PLANE ( 'NONE',  #701 ) ;
#878 = VECTOR ( 'NONE', #294, 1000.000000000000000 ) ;
#879 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#880 = AXIS2_PLACEMENT_3D ( 'NONE', #84, #740, #343 ) ;
#881 = LINE ( 'NONE', #1090, #656 ) ;
#882 = FACE_OUTER_BOUND ( 'NONE', #766, .T. ) ;
#883 = PLANE ( 'NONE',  #1075 ) ;
#884 = LINE ( 'NONE', #995, #1015 ) ;
#885 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999778, -0.2500000000000000000 ) ) ;
#886 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#887 = ORIENTED_EDGE ( 'NONE', *, *, #1196, .T. ) ;
#888 = VERTEX_POINT ( 'NONE', #451 ) ;
#889 = EDGE_LOOP ( 'NONE', ( #377, #647, #1193, #299 ) ) ;
#890 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999995656, -1.265110831387390000 ) ) ;
#891 = EDGE_CURVE ( 'NONE', #180, #285, #281, .T. ) ;
#892 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#893 = ORIENTED_EDGE ( 'NONE', *, *, #288, .T. ) ;
#894 = SURFACE_SIDE_STYLE ('',( #986 ) ) ;
#895 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#896 = MANIFOLD_SOLID_BREP ( 'Cut-Extrude1[2]', #11 ) ;
#897 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999995448, 0.3499999999999999778, 0.1699999999999999567 ) ) ;
#898 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#899 = EDGE_CURVE ( 'NONE', #22, #347, #405, .T. ) ;
#900 = VECTOR ( 'NONE', #367, 1000.000000000000000 ) ;
#901 = LINE ( 'NONE', #1071, #298 ) ;
#902 = VECTOR ( 'NONE', #435, 1000.000000000000000 ) ;
#903 = AXIS2_PLACEMENT_3D ( 'NONE', #536, #348, #1112 ) ;
#904 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#905 = ORIENTED_EDGE ( 'NONE', *, *, #1100, .T. ) ;
#906 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, -0.2500000000000000000 ) ) ;
#907 = VERTEX_POINT ( 'NONE', #1050 ) ;
#908 = ORIENTED_EDGE ( 'NONE', *, *, #154, .F. ) ;
#909 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999999334, 0.3324999999999998512, -0.1699999999999999289 ) ) ;
#910 = ADVANCED_FACE ( 'NONE', ( #575 ), #863, .T. ) ;
#911 = EDGE_CURVE ( 'NONE', #347, #1080, #1171, .T. ) ;
#912 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3499999999999998668, 0.1699999999999999012 ) ) ;
#913 = ADVANCED_FACE ( 'NONE', ( #929 ), #954, .F. ) ;
#914 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996558, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#915 = FACE_OUTER_BOUND ( 'NONE', #393, .T. ) ;
#916 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -3.388131789017201849E-16 ) ) ;
#917 = LINE ( 'NONE', #212, #547 ) ;
#918 = EDGE_CURVE ( 'NONE', #108, #362, #1135, .T. ) ;
#919 = ORIENTED_EDGE ( 'NONE', *, *, #509, .F. ) ;
#920 = DIRECTION ( 'NONE',  ( 1.000000000000000000, -1.457260984523527380E-16, 0.000000000000000000 ) ) ;
#921 = VERTEX_POINT ( 'NONE', #386 ) ;
#922 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#923 = ADVANCED_FACE ( 'NONE', ( #882 ), #371, .T. ) ;
#924 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999995656, 0.2500000000000000000 ) ) ;
#925 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#926 = SURFACE_SIDE_STYLE ('',( #440 ) ) ;
#927 = VECTOR ( 'NONE', #830, 1000.000000000000000 ) ;
#928 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#929 = FACE_OUTER_BOUND ( 'NONE', #702, .T. ) ;
#930 = ORIENTED_EDGE ( 'NONE', *, *, #828, .F. ) ;
#931 = EDGE_CURVE ( 'NONE', #617, #392, #1094, .T. ) ;
#932 = EDGE_LOOP ( 'NONE', ( #632, #465, #727, #1028 ) ) ;
#933 = FACE_OUTER_BOUND ( 'NONE', #12, .T. ) ;
#934 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#935 = ORIENTED_EDGE ( 'NONE', *, *, #753, .F. ) ;
#936 = ORIENTED_EDGE ( 'NONE', *, *, #13, .F. ) ;
#937 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#938 = VECTOR ( 'NONE', #1004, 1000.000000000000000 ) ;
#939 = VECTOR ( 'NONE', #542, 1000.000000000000000 ) ;
#940 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#941 = ORIENTED_EDGE ( 'NONE', *, *, #253, .T. ) ;
#942 = AXIS2_PLACEMENT_3D ( 'NONE', #252, #1210, #833 ) ;
#943 = ORIENTED_EDGE ( 'NONE', *, *, #840, .F. ) ;
#944 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.01749999999999994962, -0.2500000000000000000 ) ) ;
#945 = AXIS2_PLACEMENT_3D ( 'NONE', #755, #574, #1155 ) ;
#946 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, -4.286263797015737211E-17, -0.2500000000000000000 ) ) ;
#947 = VECTOR ( 'NONE', #389, 1000.000000000000000 ) ;
#948 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #199 ) ) ;
#949 = LINE ( 'NONE', #799, #18 ) ;
#950 = FACE_OUTER_BOUND ( 'NONE', #875, .T. ) ;
#951 = LINE ( 'NONE', #1213, #939 ) ;
#952 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#953 = ORIENTED_EDGE ( 'NONE', *, *, #1091, .F. ) ;
#954 = PLANE ( 'NONE',  #848 ) ;
#955 = ORIENTED_EDGE ( 'NONE', *, *, #238, .F. ) ;
#956 = PLANE ( 'NONE',  #945 ) ;
#957 = ORIENTED_EDGE ( 'NONE', *, *, #994, .F. ) ;
#958 = EDGE_LOOP ( 'NONE', ( #1019, #28, #446, #930, #965, #1141, #746, #1005 ) ) ;
#959 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#960 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#961 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #59, 'distance_accuracy_value', 'NONE');
#962 = VECTOR ( 'NONE', #1043, 1000.000000000000000 ) ;
#963 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.2500000000000000000 ) ) ;
#964 = LINE ( 'NONE', #217, #473 ) ;
#965 = ORIENTED_EDGE ( 'NONE', *, *, #739, .F. ) ;
#966 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#967 = ORIENTED_EDGE ( 'NONE', *, *, #1205, .F. ) ;
#968 = ADVANCED_FACE ( 'NONE', ( #1146 ), #1072, .F. ) ;
#969 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#970 = LINE ( 'NONE', #10, #1127 ) ;
#971 = EDGE_CURVE ( 'NONE', #1038, #285, #431, .T. ) ;
#972 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#973 = DIRECTION ( 'NONE',  ( -3.388131789017201849E-16, 3.339472587563317779E-32, -1.000000000000000000 ) ) ;
#974 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #199 ), #1020 ) ;
#975 = ORIENTED_EDGE ( 'NONE', *, *, #651, .T. ) ;
#976 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999778, -0.1699999999999999012 ) ) ;
#977 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, 0.1699999999999999845 ) ) ;
#978 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3324999999999999623, -0.1699999999999999012 ) ) ;
#979 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#980 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#981 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#982 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#983 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #1211, 'distance_accuracy_value', 'NONE');
#984 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#985 = FACE_OUTER_BOUND ( 'NONE', #141, .T. ) ;
#986 = SURFACE_STYLE_FILL_AREA ( #24 ) ;
#987 = ORIENTED_EDGE ( 'NONE', *, *, #227, .T. ) ;
#988 = VERTEX_POINT ( 'NONE', #293 ) ;
#989 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999995448, 0.3499999999999998668, -0.2500000000000000000 ) ) ;
#990 = VERTEX_POINT ( 'NONE', #579 ) ;
#991 = PRODUCT_DEFINITION_CONTEXT ( 'detailed design', #1176, 'design' ) ;
#992 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -1.000000000000000000 ) ) ;
#993 = EDGE_LOOP ( 'NONE', ( #302, #839, #152, #443, #1128, #300, #908, #206 ) ) ;
#994 = EDGE_CURVE ( 'NONE', #888, #69, #188, .T. ) ;
#995 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3324999999999998512, 0.2499999999999998890 ) ) ;
#996 = ORIENTED_EDGE ( 'NONE', *, *, #538, .F. ) ;
#997 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 9.856383386231858827E-17, 0.000000000000000000 ) ) ;
#998 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -1.016439536705160604E-15 ) ) ;
#999 = FACE_OUTER_BOUND ( 'NONE', #1034, .T. ) ;
#1000 = EDGE_CURVE ( 'NONE', #37, #1006, #589, .T. ) ;
#1001 = FACE_OUTER_BOUND ( 'NONE', #1188, .T. ) ;
#1002 = LINE ( 'NONE', #906, #801 ) ;
#1003 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE ( 'ANY', '', #262, .NOT_KNOWN. ) ;
#1004 = DIRECTION ( 'NONE',  ( -0.000000000000000000, 1.000000000000000000, -0.000000000000000000 ) ) ;
#1005 = ORIENTED_EDGE ( 'NONE', *, *, #770, .F. ) ;
#1006 = VERTEX_POINT ( 'NONE', #978 ) ;
#1007 = ADVANCED_FACE ( 'NONE', ( #1110 ), #209, .F. ) ;
#1008 = DIRECTION ( 'NONE',  ( 9.856383386231860060E-17, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1009 = PLANE ( 'NONE',  #459 ) ;
#1010 = ORIENTED_EDGE ( 'NONE', *, *, #68, .T. ) ;
#1011 = ADVANCED_FACE ( 'NONE', ( #1016 ), #1165, .F. ) ;
#1012 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999996003, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#1013 = LINE ( 'NONE', #338, #723 ) ;
#1014 = FILL_AREA_STYLE ('',( #1192 ) ) ;
#1015 = VECTOR ( 'NONE', #796, 1000.000000000000000 ) ;
#1016 = FACE_OUTER_BOUND ( 'NONE', #38, .T. ) ;
#1017 = EDGE_CURVE ( 'NONE', #69, #162, #1198, .T. ) ;
#1018 = VECTOR ( 'NONE', #1167, 1000.000000000000000 ) ;
#1019 = ORIENTED_EDGE ( 'NONE', *, *, #918, .F. ) ;
#1020 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #961 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #59, #223, #810 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#1021 = ORIENTED_EDGE ( 'NONE', *, *, #559, .T. ) ;
#1022 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1023 = PLANE ( 'NONE',  #903 ) ;
#1024 = LINE ( 'NONE', #56, #351 ) ;
#1025 = ORIENTED_EDGE ( 'NONE', *, *, #699, .F. ) ;
#1026 = ADVANCED_FACE ( 'NONE', ( #985 ), #34, .F. ) ;
#1027 = ORIENTED_EDGE ( 'NONE', *, *, #442, .F. ) ;
#1028 = ORIENTED_EDGE ( 'NONE', *, *, #306, .T. ) ;
#1029 = EDGE_CURVE ( 'NONE', #690, #321, #333, .T. ) ;
#1030 = STYLED_ITEM ( 'NONE', ( #546 ), #896 ) ;
#1031 = LINE ( 'NONE', #602, #187 ) ;
#1032 = FILL_AREA_STYLE_COLOUR ( '', #1077 ) ;
#1033 = VECTOR ( 'NONE', #1041, 1000.000000000000000 ) ;
#1034 = EDGE_LOOP ( 'NONE', ( #45, #747, #234, #1088 ) ) ;
#1035 = FILL_AREA_STYLE_COLOUR ( '', #657 ) ;
#1036 = AXIS2_PLACEMENT_3D ( 'NONE', #276, #751, #767 ) ;
#1037 = EDGE_LOOP ( 'NONE', ( #1126, #460, #748, #478 ) ) ;
#1038 = VERTEX_POINT ( 'NONE', #308 ) ;
#1039 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -0.000000000000000000 ) ) ;
#1040 = ORIENTED_EDGE ( 'NONE', *, *, #257, .F. ) ;
#1041 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#1042 = ORIENTED_EDGE ( 'NONE', *, *, #578, .F. ) ;
#1043 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 7.022034795890579217E-18, 0.000000000000000000 ) ) ;
#1044 = EDGE_CURVE ( 'NONE', #731, #515, #901, .T. ) ;
#1045 =( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) );
#1046 = AXIS2_PLACEMENT_3D ( 'NONE', #1119, #904, #311 ) ;
#1047 = PLANE ( 'NONE',  #818 ) ;
#1048 = VECTOR ( 'NONE', #1149, 1000.000000000000000 ) ;
#1049 = EDGE_CURVE ( 'NONE', #350, #564, #665, .T. ) ;
#1050 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#1051 =( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #1120 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #214, #305, #400 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) );
#1052 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996558, -0.4485249675906648470, -0.2500000000000000000 ) ) ;
#1053 = VECTOR ( 'NONE', #130, 1000.000000000000000 ) ;
#1054 = ORIENTED_EDGE ( 'NONE', *, *, #410, .T. ) ;
#1055 = VERTEX_POINT ( 'NONE', #604 ) ;
#1056 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -0.2500000000000000000 ) ) ;
#1057 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#1058 = EDGE_CURVE ( 'NONE', #988, #474, #528, .T. ) ;
#1059 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1060 = VECTOR ( 'NONE', #1039, 1000.000000000000000 ) ;
#1061 = DIRECTION ( 'NONE',  ( 1.807003620809176604E-16, 1.000000000000000000, -1.959157251370482008E-32 ) ) ;
#1062 = LINE ( 'NONE', #844, #609 ) ;
#1063 = CARTESIAN_POINT ( 'NONE',  ( -0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#1064 = ORIENTED_EDGE ( 'NONE', *, *, #520, .T. ) ;
#1065 = EDGE_CURVE ( 'NONE', #621, #361, #64, .T. ) ;
#1066 = VERTEX_POINT ( 'NONE', #506 ) ;
#1067 = VERTEX_POINT ( 'NONE', #618 ) ;
#1068 = ORIENTED_EDGE ( 'NONE', *, *, #538, .T. ) ;
#1069 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#1070 = VERTEX_POINT ( 'NONE', #1102 ) ;
#1071 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, -0.2500000000000000000 ) ) ;
#1072 = PLANE ( 'NONE',  #179 ) ;
#1073 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#1074 = PLANE ( 'NONE',  #266 ) ;
#1075 = AXIS2_PLACEMENT_3D ( 'NONE', #1078, #41, #782 ) ;
#1076 = DIRECTION ( 'NONE',  ( 3.388131789017201849E-16, -3.339472587563317779E-32, -1.000000000000000000 ) ) ;
#1077 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#1078 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -0.2500000000000000000 ) ) ;
#1079 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#1080 = VERTEX_POINT ( 'NONE', #914 ) ;
#1081 = EDGE_LOOP ( 'NONE', ( #110, #364, #953, #549 ) ) ;
#1082 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, -0.1699999999999998734 ) ) ;
#1083 = ORIENTED_EDGE ( 'NONE', *, *, #846, .T. ) ;
#1084 = LINE ( 'NONE', #325, #873 ) ;
#1085 = EDGE_CURVE ( 'NONE', #37, #673, #558, .T. ) ;
#1086 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -1.714505518806294884E-16, 0.000000000000000000 ) ) ;
#1087 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999995656, -1.265110831387390000 ) ) ;
#1088 = ORIENTED_EDGE ( 'NONE', *, *, #899, .T. ) ;
#1089 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#1090 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3499999999999999778, -0.2499999999999998890 ) ) ;
#1091 = EDGE_CURVE ( 'NONE', #350, #907, #607, .T. ) ;
#1092 = DIRECTION ( 'NONE',  ( -0.000000000000000000, -0.000000000000000000, 1.000000000000000000 ) ) ;
#1093 = AXIS2_PLACEMENT_3D ( 'NONE', #628, #827, #74 ) ;
#1094 = LINE ( 'NONE', #14, #492 ) ;
#1095 = VECTOR ( 'NONE', #155, 1000.000000000000000 ) ;
#1096 = ORIENTED_EDGE ( 'NONE', *, *, #763, .F. ) ;
#1097 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999999489, 0.3324999999999998512, 0.2499999999999998890 ) ) ;
#1098 = VERTEX_POINT ( 'NONE', #1178 ) ;
#1099 = VERTEX_POINT ( 'NONE', #233 ) ;
#1100 = EDGE_CURVE ( 'NONE', #921, #192, #103, .T. ) ;
#1101 = ORIENTED_EDGE ( 'NONE', *, *, #1216, .F. ) ;
#1102 = CARTESIAN_POINT ( 'NONE',  ( 0.4999999999999995559, -4.286263797015737211E-17, 0.2500000000000000000 ) ) ;
#1103 = ADVANCED_FACE ( 'NONE', ( #756 ), #61, .F. ) ;
#1104 = CLOSED_SHELL ( 'NONE', ( #76, #541, #1026, #624, #512, #427, #802, #250, #809, #816, #83, #322, #248, #1011 ) ) ;
#1105 = DIRECTION ( 'NONE',  ( 0.000000000000000000, -0.000000000000000000, -1.000000000000000000 ) ) ;
#1106 = VECTOR ( 'NONE', #1073, 1000.000000000000000 ) ;
#1107 = EDGE_CURVE ( 'NONE', #1098, #568, #1160, .T. ) ;
#1108 = VECTOR ( 'NONE', #189, 1000.000000000000000 ) ;
#1109 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, -3.009265538105056020E-33, 0.2500000000000000000 ) ) ;
#1110 = FACE_OUTER_BOUND ( 'NONE', #1037, .T. ) ;
#1111 = PLANE ( 'NONE',  #208 ) ;
#1112 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1113 = ORIENTED_EDGE ( 'NONE', *, *, #1121, .T. ) ;
#1114 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3499999999999999778, -0.1699999999999998734 ) ) ;
#1115 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1116 = EDGE_CURVE ( 'NONE', #192, #773, #33, .T. ) ;
#1117 = ORIENTED_EDGE ( 'NONE', *, *, #544, .F. ) ;
#1118 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#1119 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1120 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #214, 'distance_accuracy_value', 'NONE');
#1121 = EDGE_CURVE ( 'NONE', #907, #621, #388, .T. ) ;
#1122 = VECTOR ( 'NONE', #612, 1000.000000000000000 ) ;
#1123 = ORIENTED_EDGE ( 'NONE', *, *, #971, .T. ) ;
#1124 = DIRECTION ( 'NONE',  ( -1.000000000000000000, -0.000000000000000000, -3.388131789017201849E-16 ) ) ;
#1125 = EDGE_LOOP ( 'NONE', ( #645, #123, #531, #96 ) ) ;
#1126 = ORIENTED_EDGE ( 'NONE', *, *, #360, .T. ) ;
#1127 = VECTOR ( 'NONE', #664, 1000.000000000000000 ) ;
#1128 = ORIENTED_EDGE ( 'NONE', *, *, #279, .F. ) ;
#1129 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, -0.2500000000000000000 ) ) ;
#1130 = VERTEX_POINT ( 'NONE', #1182 ) ;
#1131 = EDGE_CURVE ( 'NONE', #1070, #1067, #471, .T. ) ;
#1132 = LINE ( 'NONE', #885, #878 ) ;
#1133 = LINE ( 'NONE', #370, #643 ) ;
#1134 = VECTOR ( 'NONE', #46, 1000.000000000000000 ) ;
#1135 = LINE ( 'NONE', #966, #852 ) ;
#1136 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #765 ) ) ;
#1137 = ORIENTED_EDGE ( 'NONE', *, *, #346, .T. ) ;
#1138 = EDGE_LOOP ( 'NONE', ( #1027, #857, #245, #422, #593, #1040, #476, #397 ) ) ;
#1139 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1140 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, -4.286263797015737211E-17, -0.2500000000000000000 ) ) ;
#1141 = ORIENTED_EDGE ( 'NONE', *, *, #777, .F. ) ;
#1142 = AXIS2_PLACEMENT_3D ( 'NONE', #32, #480, #689 ) ;
#1143 = AXIS2_PLACEMENT_3D ( 'NONE', #483, #1076, #391 ) ;
#1144 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999778, -0.2500000000000000000 ) ) ;
#1145 = FACE_OUTER_BOUND ( 'NONE', #650, .T. ) ;
#1146 = FACE_OUTER_BOUND ( 'NONE', #219, .T. ) ;
#1147 = EDGE_CURVE ( 'NONE', #418, #162, #949, .T. ) ;
#1148 = VERTEX_POINT ( 'NONE', #912 ) ;
#1149 = DIRECTION ( 'NONE',  ( -1.084202172485504557E-16, 0.000000000000000000, -1.000000000000000000 ) ) ;
#1150 = PRESENTATION_LAYER_ASSIGNMENT (  '', '', ( #1030 ) ) ;
#1151 = VERTEX_POINT ( 'NONE', #416 ) ;
#1152 = VECTOR ( 'NONE', #2, 1000.000000000000000 ) ;
#1153 = VERTEX_POINT ( 'NONE', #320 ) ;
#1154 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, -0.000000000000000000 ) ) ;
#1155 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -3.388131789017201849E-16 ) ) ;
#1156 = EDGE_CURVE ( 'NONE', #764, #1066, #1170, .T. ) ;
#1157 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 1.807003620809176604E-16, 0.000000000000000000 ) ) ;
#1158 = VECTOR ( 'NONE', #556, 1000.000000000000000 ) ;
#1159 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3324999999999998512, -0.2500000000000000000 ) ) ;
#1160 = LINE ( 'NONE', #805, #428 ) ;
#1161 = FACE_OUTER_BOUND ( 'NONE', #1125, .T. ) ;
#1162 = VECTOR ( 'NONE', #479, 1000.000000000000000 ) ;
#1163 = EDGE_CURVE ( 'NONE', #353, #1148, #742, .T. ) ;
#1164 = ORIENTED_EDGE ( 'NONE', *, *, #1174, .T. ) ;
#1165 = PLANE ( 'NONE',  #488 ) ;
#1166 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#1167 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1168 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999996003, 0.3324999999999997402, 0.2500000000000000000 ) ) ;
#1169 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.01749999999999994962, 0.2500000000000000000 ) ) ;
#1170 = LINE ( 'NONE', #631, #1134 ) ;
#1171 = LINE ( 'NONE', #104, #1048 ) ;
#1172 = EDGE_LOOP ( 'NONE', ( #706, #807, #870, #582 ) ) ;
#1173 = ORIENTED_EDGE ( 'NONE', *, *, #385, .T. ) ;
#1174 = EDGE_CURVE ( 'NONE', #1066, #888, #641, .T. ) ;
#1175 = ORIENTED_EDGE ( 'NONE', *, *, #385, .F. ) ;
#1176 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#1177 = CARTESIAN_POINT ( 'NONE',  ( -0.4999999999999995559, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#1178 = CARTESIAN_POINT ( 'NONE',  ( 0.2999999999999995448, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#1179 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, -0.2500000000000000000 ) ) ;
#1180 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000082E-05 ), #652, 'distance_accuracy_value', 'NONE');
#1181 = CARTESIAN_POINT ( 'NONE',  ( 0.3799999999999998934, 0.3324999999999998512, -0.2500000000000000000 ) ) ;
#1182 = CARTESIAN_POINT ( 'NONE',  ( 0.4824999999999995959, 0.3324999999999999623, -0.2500000000000000000 ) ) ;
#1183 = COLOUR_RGB ( '',0.7921568627450980005, 0.8196078431372548767, 0.9333333333333333481 ) ;
#1184 = CARTESIAN_POINT ( 'NONE',  ( 0.2499999999999995837, 0.01749999999999998432, -0.2500000000000000000 ) ) ;
#1185 = LINE ( 'NONE', #1179, #938 ) ;
#1186 = VECTOR ( 'NONE', #819, 1000.000000000000000 ) ;
#1187 = ORIENTED_EDGE ( 'NONE', *, *, #77, .F. ) ;
#1188 = EDGE_LOOP ( 'NONE', ( #735, #449, #505, #278 ) ) ;
#1189 = DIRECTION ( 'NONE',  ( 9.856383386231858827E-17, 1.000000000000000000, 0.000000000000000000 ) ) ;
#1190 = ORIENTED_EDGE ( 'NONE', *, *, #21, .F. ) ;
#1191 = CARTESIAN_POINT ( 'NONE',  ( -0.2499999999999995837, 0.01749999999999998432, 0.2500000000000000000 ) ) ;
#1192 = FILL_AREA_STYLE_COLOUR ( '', #472 ) ;
#1193 = ORIENTED_EDGE ( 'NONE', *, *, #1100, .F. ) ;
#1194 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', 'automotive_design', 1998, #527 ) ;
#1195 = LINE ( 'NONE', #205, #693 ) ;
#1196 = EDGE_CURVE ( 'NONE', #474, #304, #695, .T. ) ;
#1197 = DIRECTION ( 'NONE',  ( 0.000000000000000000, 0.000000000000000000, 1.000000000000000000 ) ) ;
#1198 = LINE ( 'NONE', #82, #630 ) ;
#1199 = ADVANCED_FACE ( 'NONE', ( #1001 ), #608, .F. ) ;
#1200 = LINE ( 'NONE', #412, #1186 ) ;
#1201 = DIRECTION ( 'NONE',  ( 1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#1202 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, -0.000000000000000000 ) ) ;
#1203 = LINE ( 'NONE', #866, #729 ) ;
#1204 = EDGE_LOOP ( 'NONE', ( #109, #1164, #419, #683 ) ) ;
#1205 = EDGE_CURVE ( 'NONE', #1070, #182, #825, .T. ) ;
#1206 = ORIENTED_EDGE ( 'NONE', *, *, #101, .T. ) ;
#1207 = ORIENTED_EDGE ( 'NONE', *, *, #314, .T. ) ;
#1208 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #1030 ), #106 ) ;
#1209 = ORIENTED_EDGE ( 'NONE', *, *, #1205, .T. ) ;
#1210 = DIRECTION ( 'NONE',  ( -1.000000000000000000, 0.000000000000000000, 0.000000000000000000 ) ) ;
#1211 =( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) );
#1212 = VECTOR ( 'NONE', #491, 1000.000000000000000 ) ;
#1213 = CARTESIAN_POINT ( 'NONE',  ( -0.3799999999999995604, 0.3324999999999999623, 0.2500000000000000000 ) ) ;
#1214 = CARTESIAN_POINT ( 'NONE',  ( -0.2999999999999996003, 0.3499999999999999223, 0.2500000000000000000 ) ) ;
#1215 = VECTOR ( 'NONE', #328, 1000.000000000000000 ) ;
#1216 = EDGE_CURVE ( 'NONE', #44, #353, #513, .T. ) ;
#1217 = PRESENTATION_STYLE_ASSIGNMENT (( #426 ) ) ;
#1218 = LINE ( 'NONE', #172, #1033 ) ;
#1219 = ORIENTED_EDGE ( 'NONE', *, *, #253, .F. ) ;
#1220 = DIRECTION ( 'NONE',  ( 7.022034795890579217E-18, -1.000000000000000000, 0.000000000000000000 ) ) ;
#1221 = ORIENTED_EDGE ( 'NONE', *, *, #154, .T. ) ;
#1222 = ORIENTED_EDGE ( 'NONE', *, *, #120, .F. ) ;
#1223 = EDGE_CURVE ( 'NONE', #1099, #50, #1013, .T. ) ;
#1224 = FILL_AREA_STYLE ('',( #1035 ) ) ;
#1225 = EDGE_LOOP ( 'NONE', ( #936, #808, #610, #458 ) ) ;
ENDSEC;
END-ISO-10303-21;
