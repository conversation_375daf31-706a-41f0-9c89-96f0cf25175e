#!/usr/bin/env python3
"""
COMPLETE WORKING ULTRALIBRARIAN AUTOMATION
==========================================
Includes all the missing steps: Download Now -> 3D CAD Model -> Login -> Download
"""

import os
import time
import json
import zipfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def setup_driver(silent=False):
    """Setup Chrome with download preferences"""
    chrome_options = Options()

    # Configure for silent mode
    if silent:
        chrome_options.add_argument('--headless')  # Run in background
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    else:
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')

    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)

    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)

    return webdriver.Chrome(options=chrome_options)

def run_complete_automation(silent=False):
    """Run the complete automation with all steps"""

    def log_print(message):
        """Helper function to print only when not in silent mode"""
        if not silent:
            print(message)

    log_print("🎯 COMPLETE WORKING ULTRALIBRARIAN AUTOMATION")
    log_print("=" * 60)

    driver = setup_driver(silent=silent)
    initial_files = set(os.listdir('3d')) if os.path.exists('3d') else set()
    
    try:
        # Step 1: Load UltraLibrarian homepage
        print("\n🔸 STEP 1: Loading UltraLibrarian homepage...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)

        print(f"✅ Page loaded: {driver.title}")

        # Step 2: Click LOGIN link
        print("\n🔸 STEP 2: Clicking LOGIN link...")
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")

        if not login_links:
            print("❌ No LOGIN link found!")
            return None

        login_link = login_links[0]
        print(f"✅ Found LOGIN link: {login_link.text}")

        # Try regular click first, then JavaScript click if it fails
        try:
            login_link.click()
        except Exception as e:
            print(f"⚠️ Regular click failed: {e}")
            print("🖱️ Trying JavaScript click...")
            driver.execute_script("arguments[0].click();", login_link)

        time.sleep(5)

        # Step 3: Fill login form
        print("\n🔸 STEP 3: Filling login form...")

        # Load credentials
        try:
            with open('component_site_credentials.json', 'r') as f:
                credentials = json.load(f)

            email = credentials['UltraLibrarian']['email']
            password = credentials['UltraLibrarian']['password']
            print(f"✅ Loaded credentials: {email}")
        except Exception as e:
            print(f"❌ Could not load credentials: {e}")
            return None

        # Fill email (Username field with placeholder "Email")
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        if not email_inputs:
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[name='Username']")

        if not email_inputs:
            print("❌ No email input found!")
            return None

        email_input = email_inputs[0]
        email_input.clear()
        email_input.send_keys(email)
        print("✅ Email entered")

        # Fill password
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        if not password_inputs:
            print("❌ No password input found!")
            return None

        password_input = password_inputs[0]
        password_input.clear()
        password_input.send_keys(password)
        print("✅ Password entered")

        # Click login button
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
        if not login_buttons:
            print("❌ No login button found!")
            return None

        login_button = login_buttons[0]
        print(f"✅ Found login button: {login_button.text}")
        login_button.click()
        time.sleep(10)

        print("✅ Login completed")

        # Step 4: Find search box
        print("\n🔸 STEP 2: Finding search box...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        
        search_box = None
        for inp in inputs:
            try:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower():
                        search_box = inp
                        print(f"✅ Found search box: '{placeholder}'")
                        break
            except:
                continue
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        # Step 5: Search for LM358N
        print("\n🔸 STEP 5: Searching for LM358N...")
        search_box.clear()
        search_box.send_keys("LM358N")
        search_box.send_keys(Keys.RETURN)
        
        print("✅ Search submitted, waiting for results...")
        time.sleep(10)
        
        # Step 4: Find Texas Instruments LM358N/NOPB
        print("\n🔸 STEP 4: Finding Texas Instruments LM358N/NOPB...")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        
        ti_link = None
        for link in all_links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''
                
                if (('lm358n/nopb' in text.lower() or 
                     ('texas-instruments' in href.lower() and 'lm358n' in href.lower())) and
                    'details' in href.lower() and 'login' not in href.lower() and
                    link.is_displayed() and link.is_enabled()):
                    ti_link = link
                    print(f"✅ Found TI LM358N: '{text}' -> {href}")
                    break
            except:
                continue
        
        if not ti_link:
            print("❌ No Texas Instruments LM358N found!")
            return None
        
        # Step 5: Click on TI LM358N
        print("\n🔸 STEP 5: Clicking on Texas Instruments LM358N...")
        driver.execute_script("arguments[0].scrollIntoView(true);", ti_link)
        time.sleep(2)
        ti_link.click()
        time.sleep(8)
        
        print(f"✅ Clicked on part, new URL: {driver.current_url}")
        
        # Step 6: Click "Download Now"
        print("\n🔸 STEP 6: Clicking 'Download Now'...")

        # Use the correct ID for Download Now button
        download_button = None
        try:
            download_button = driver.find_element(By.ID, "export-selection-btn")
            if download_button.is_displayed() and download_button.is_enabled():
                print(f"✅ Found Download Now button: {download_button.text}")
            else:
                download_button = None
        except:
            download_button = None

        if not download_button:
            print("❌ No Download Now button found!")
            return None
        
        print(f"✅ Found Download button: {download_button.text or 'Button found'}")

        # Scroll to button and ensure it's visible
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", download_button)
        time.sleep(3)

        # Try multiple click methods
        clicked = False

        # Method 1: Regular click
        try:
            download_button.click()
            print("✅ Clicked 'Download Now' with regular click")
            clicked = True
        except Exception as e:
            print(f"⚠️ Regular click failed: {e}")

        # Method 2: JavaScript click
        if not clicked:
            try:
                driver.execute_script("arguments[0].click();", download_button)
                print("✅ Clicked 'Download Now' with JavaScript click")
                clicked = True
            except Exception as e:
                print(f"⚠️ JavaScript click failed: {e}")

        # Method 3: ActionChains click
        if not clicked:
            try:
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(driver)
                actions.move_to_element(download_button).click().perform()
                print("✅ Clicked 'Download Now' with ActionChains")
                clicked = True
            except Exception as e:
                print(f"⚠️ ActionChains click failed: {e}")

        if not clicked:
            print("❌ All click methods failed!")
            return None

        time.sleep(8)
        print(f"📄 After Download Now click: {driver.current_url}")
        
        # Step 7: Click "3D CAD Model"
        print("\n🔸 STEP 7: Clicking '3D CAD Model'...")
        model_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '3D CAD Model')] | //a[contains(text(), '3D CAD Model')] | //button[contains(text(), '3D Model')] | //a[contains(text(), '3D Model')]")
        
        if not model_buttons:
            print("❌ No '3D CAD Model' button found!")
            # Show available buttons for debugging
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            print("Available buttons:")
            for btn in all_buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return None
        
        model_button = model_buttons[0]
        print(f"✅ Found '3D CAD Model' button: {model_button.text}")
        
        driver.execute_script("arguments[0].scrollIntoView(true);", model_button)
        time.sleep(2)
        model_button.click()
        time.sleep(5)
        
        print("✅ Clicked '3D CAD Model'")

        # Step 8: Handle new screen/window that opens
        print("\n🔸 STEP 8: Handling new screen for STEP selection...")

        # Wait longer for new screen to load
        print("   ⏳ Waiting for screen to load...")
        time.sleep(10)

        # Check if a new window/tab opened
        if len(driver.window_handles) > 1:
            print("✅ New window detected, switching to it...")
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(5)

        print(f"Current URL: {driver.current_url}")

        # Wait for page elements to be ready
        print("   ⏳ Waiting for page elements to load...")
        time.sleep(5)

        # Take screenshot to see what's on screen
        driver.save_screenshot("step_selection_screen.png")
        print("   📸 Screenshot: step_selection_screen.png")

        # Step 9: Select STEP format
        print("\n🔸 STEP 9: Selecting STEP format...")

        # Look for STEP option/button with more comprehensive selectors
        step_selectors = [
            "//button[contains(text(), 'STEP')]",
            "//a[contains(text(), 'STEP')]",
            "//input[@value='STEP']",
            "//option[contains(text(), 'STEP')]",
            "//div[contains(text(), 'STEP')]",
            "//span[contains(text(), 'STEP')]",
            "//label[contains(text(), 'STEP')]",
            "//td[contains(text(), 'STEP')]",
            "//li[contains(text(), 'STEP')]"
        ]

        step_element = None
        for selector in step_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        step_element = element
                        print(f"✅ Found STEP option: {element.text}")
                        break
                if step_element:
                    break
            except:
                continue

        if not step_element:
            print("❌ No STEP option found!")
            # Show available options for debugging
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            all_links = driver.find_elements(By.TAG_NAME, "a")
            print("Available buttons:")
            for btn in all_buttons[:10]:
                try:
                    text = btn.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            print("Available links:")
            for link in all_links[:10]:
                try:
                    text = link.text.strip()
                    if text:
                        print(f"  - '{text}'")
                except:
                    continue
            return None

        # Click STEP option
        driver.execute_script("arguments[0].scrollIntoView(true);", step_element)
        time.sleep(2)
        step_element.click()
        time.sleep(5)

        print("✅ Selected STEP format")

        # Step 10: Click "Download Now" again on the new screen
        print("\n🔸 STEP 10: Clicking 'Download Now' on STEP selection screen...")

        # Look for Download Now button with multiple selectors
        download_selectors_2 = [
            ("xpath", "//button[contains(text(), 'Download Now')]"),
            ("xpath", "//a[contains(text(), 'Download Now')]"),
            ("xpath", "//input[@value='Download Now']"),
            ("xpath", "//button[contains(text(), 'Download')]"),
            ("xpath", "//a[contains(text(), 'Download')]"),
            ("xpath", "//input[@value='Download']"),
            ("css", "button[type='submit']"),
            ("css", "input[type='submit']"),
            ("css", ".btn-primary"),
            ("css", ".download-btn")
        ]

        download_button_2 = None
        for selector_type, selector in download_selectors_2:
            try:
                if selector_type == "xpath":
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        download_button_2 = element
                        print(f"✅ Found Download button with {selector}: '{element.text or 'No text'}'")
                        break

                if download_button_2:
                    break
            except:
                continue

        if not download_button_2:
            print("❌ No Download button found on STEP selection screen!")
            # Show ALL available elements for debugging
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            all_links = driver.find_elements(By.TAG_NAME, "a")

            print("Available buttons:")
            for i, btn in enumerate(all_buttons[:10], 1):
                try:
                    if btn.is_displayed():
                        text = btn.text.strip()
                        classes = btn.get_attribute('class') or 'no-class'
                        print(f"  {i}. '{text}' | Class: '{classes}'")
                except:
                    continue

            print("Available inputs:")
            for i, inp in enumerate(all_inputs[:10], 1):
                try:
                    if inp.is_displayed():
                        input_type = inp.get_attribute('type') or 'text'
                        value = inp.get_attribute('value') or 'no-value'
                        print(f"  {i}. Type: '{input_type}' | Value: '{value}'")
                except:
                    continue

            return None

        # Try multiple methods to click the button
        try:
            # Method 1: Scroll and regular click
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", download_button_2)
            time.sleep(2)
            download_button_2.click()
            print("✅ Clicked using regular click")
        except:
            try:
                # Method 2: JavaScript click
                driver.execute_script("arguments[0].click();", download_button_2)
                print("✅ Clicked using JavaScript click")
            except:
                try:
                    # Method 3: ActionChains click
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(driver)
                    actions.move_to_element(download_button_2).click().perform()
                    print("✅ Clicked using ActionChains")
                except Exception as e:
                    print(f"❌ All click methods failed: {e}")
                    return None

        time.sleep(5)

        print("✅ Clicked 'Download Now' on STEP selection screen")

        # Wait for download to start
        print("\n🔸 Waiting for download to start...")
        time.sleep(10)
        
        # Step 11: Monitor for downloads
        print("\n🔸 STEP 12: Monitoring for downloads...")

        for i in range(24):  # Monitor for 2 minutes
            time.sleep(5)
            current_files = set(os.listdir('3d')) if os.path.exists('3d') else set()
            new_files = current_files - initial_files
            
            if new_files:
                print(f"🎉 New files detected: {list(new_files)}")

                # Process new files
                for new_file in new_files:
                    if new_file.lower().endswith(('.step', '.stp')):
                        print(f"✅ STEP file found: {new_file}")
                        return new_file
                    elif new_file.lower().endswith('.zip'):
                        print(f"📦 ZIP file found: {new_file}")
                        try:
                            zip_path = os.path.join('3d', new_file)
                            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                zip_ref.extractall('3d')

                            # Check for extracted STEP files
                            final_files = set(os.listdir('3d'))
                            extracted_files = final_files - current_files
                            step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                            if step_files:
                                print(f"✅ STEP file extracted: {step_files[0]}")
                                return step_files[0]
                        except Exception as e:
                            print(f"Error extracting ZIP: {e}")
            
            print(f"  Checking... ({(i+1)*5}/120 seconds)")
        
        print("❌ No files downloaded after 2 minutes")
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    result = run_complete_automation()

    if result:
        print(f"\n🎉 COMPLETE SUCCESS: {result} downloaded to 3d/ folder!")
    else:
        print(f"\n❌ AUTOMATION FAILED: No STEP file obtained")

    # Clear screen
    os.system('cls' if os.name == 'nt' else 'clear')
