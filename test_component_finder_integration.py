#!/usr/bin/env python3
"""
Test Component Finder Integration
Quick test to verify the new external manufacturer 3D tester works with component_finder.py
"""

import sys
import os

def test_external_tester_integration():
    """Test that the external tester can be imported and called"""
    print("🧪 TESTING EXTERNAL MANUFACTURER 3D TESTER INTEGRATION")
    print("=" * 60)
    
    try:
        # Test 1: Import the external tester
        print("1️⃣ Testing import of external_manufacturer_3d_tester...")
        from external_manufacturer_3d_tester import call_manufacturer_3d_tester
        print("   ✅ Import successful")
        
        # Test 2: Call the function
        print("\n2️⃣ Testing function call...")
        manufacturer = "Texas Instruments"
        part_number = "LM358N"
        
        print(f"   Testing with: {manufacturer} / {part_number}")
        result = call_manufacturer_3d_tester(manufacturer, part_number)
        
        if result['success']:
            print("   ✅ Function call successful")
            print(f"   📊 Results: {result['accessible_websites']} accessible sites, {result['sites_with_3d_content']} with 3D content")
        else:
            print(f"   ⚠️ Function returned failure: {result.get('error', 'Unknown error')}")
        
        # Test 3: Check if component_finder can import it
        print("\n3️⃣ Testing component_finder.py compatibility...")
        
        # Try to import the component finder class
        try:
            from component_finder import ComponentFinderGUI
            print("   ✅ ComponentFinderGUI import successful")
            
            # Check if the external tester import works in that context
            print("   ✅ Integration should work correctly")
            
        except ImportError as e:
            print(f"   ❌ ComponentFinderGUI import failed: {e}")
            return False
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ External manufacturer 3D tester is ready for use in component_finder.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_simple_3d_test_availability():
    """Test that simple_3d_test.py is available"""
    print("\n🧪 TESTING SIMPLE 3D TEST AVAILABILITY")
    print("=" * 60)
    
    try:
        from simple_3d_test import simple_manufacturer_test
        print("✅ simple_3d_test.py is available and importable")
        
        # Quick test
        result = simple_manufacturer_test("Texas Instruments", "LM358N")
        if result:
            print("✅ simple_manufacturer_test function works correctly")
            return True
        else:
            print("⚠️ simple_manufacturer_test returned no results")
            return False
            
    except ImportError as e:
        print(f"❌ simple_3d_test.py import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ simple_3d_test.py test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 COMPONENT FINDER INTEGRATION TEST")
    print("Testing the new external manufacturer 3D tester integration")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_simple_3d_test_availability()
    test2_passed = test_external_tester_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Your component_finder.py is ready to use the new external manufacturer 3D tester")
        print("\n💡 What this means:")
        print("   • The old complex manufacturer 3D search code has been replaced")
        print("   • New external tester provides website analysis and recommendations")
        print("   • Your existing 3D finders (SnapEDA, UltraLibrarian, SamacSys) remain unchanged")
        print("   • The system is now more modular and easier to debug")
        
        print("\n🎯 Next Steps:")
        print("   1. Test component_finder.py with a real search")
        print("   2. Check that manufacturer 3D analysis appears in the comments")
        print("   3. Verify that existing 3D finders still work normally")
        
    else:
        print("❌ SOME TESTS FAILED")
        if not test1_passed:
            print("   • simple_3d_test.py is not working correctly")
        if not test2_passed:
            print("   • external_manufacturer_3d_tester.py integration failed")
        
        print("\n🔧 Troubleshooting:")
        print("   • Make sure all new files are in the same directory as component_finder.py")
        print("   • Check that required modules (requests, beautifulsoup4) are installed")
        print("   • Verify that manufacturer_3d_patterns.json exists")

if __name__ == "__main__":
    main()
