#!/usr/bin/env python3
"""
Visual Studio Windows Forms Style Demo
Shows exactly what the Component Finder looks like now
"""

import tkinter as tk
from tkinter import ttk, scrolledtext

def create_vs_forms_demo():
    """Create a demo showing the VS Forms style"""
    root = tk.Tk()
    root.title("Form1")
    root.geometry("1000x700")
    
    # Visual Studio Windows Forms colors
    vs_colors = {
        'form_bg': '#f0f0f0',           # Light gray form background
        'control_bg': '#ffffff',        # White control background  
        'button_bg': '#e1e1e1',         # Light gray button background
        'text_color': '#000000',        # Black text
        'group_bg': '#f0f0f0'           # Group box background
    }
    
    root.configure(bg=vs_colors['form_bg'])

    # Configure styles
    style = ttk.Style()
    try:
        style.theme_use('winnative')
    except:
        style.theme_use('clam')

    style.configure('VSButton.TButton',
                   foreground=vs_colors['text_color'],
                   background=vs_colors['button_bg'],
                   font=('Microsoft Sans Serif', 8))
    
    style.configure('VSEntry.TEntry',
                   fieldbackground=vs_colors['control_bg'],
                   font=('Microsoft Sans Serif', 8))
    
    style.configure('VSLabel.TLabel',
                   background=vs_colors['form_bg'],
                   foreground=vs_colors['text_color'],
                   font=('Microsoft Sans Serif', 8))
    
    style.configure('VSGroup.TLabelframe',
                   background=vs_colors['group_bg'])
    
    style.configure('VSGroup.TLabelframe.Label',
                   background=vs_colors['group_bg'],
                   foreground=vs_colors['text_color'],
                   font=('Microsoft Sans Serif', 8, 'bold'))

    # Main frame
    main_frame = tk.Frame(root, bg=vs_colors['form_bg'])
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)

    # Top section
    top_frame = tk.Frame(main_frame, bg=vs_colors['form_bg'])
    top_frame.pack(fill='x', pady=(0, 10))

    # Left group - SINGLE COMPONENT LOOKUP
    left_group = ttk.LabelFrame(top_frame, text="SINGLE COMPONENT LOOKUP", 
                               style='VSGroup.TLabelframe', padding="10")
    left_group.pack(side='left', fill='both', expand=True, padx=(0, 5))

    # Input fields
    ttk.Label(left_group, text="MANUFACTURER NAME", style='VSLabel.TLabel').grid(row=0, column=0, sticky='w', pady=(0, 5))
    mfg_entry = ttk.Entry(left_group, style='VSEntry.TEntry', width=35)
    mfg_entry.insert(0, "Texas Instruments")
    mfg_entry.grid(row=1, column=0, sticky='ew', pady=(0, 10))
    
    ttk.Label(left_group, text="MANUFACTURER WEBSITE ADDRESS", style='VSLabel.TLabel').grid(row=2, column=0, sticky='w', pady=(0, 5))
    web_entry = ttk.Entry(left_group, style='VSEntry.TEntry', width=35)
    web_entry.grid(row=3, column=0, sticky='ew', pady=(0, 10))
    
    ttk.Label(left_group, text="MANUFACTURER FULL PART NUMBER", style='VSLabel.TLabel').grid(row=4, column=0, sticky='w', pady=(0, 5))
    part_entry = ttk.Entry(left_group, style='VSEntry.TEntry', width=35)
    part_entry.insert(0, "LP590722QDQNRQ1")
    part_entry.grid(row=5, column=0, sticky='ew', pady=(0, 10))

    # Add START DATA SEARCH button to left group
    ttk.Button(left_group, text="START DATA SEARCH", style='VSButton.TButton').grid(row=6, column=0, sticky='ew', pady=(10, 5))

    # Add LOAD EXCEL FILE button to left group
    ttk.Button(left_group, text="LOAD EXCEL FILE", style='VSButton.TButton').grid(row=7, column=0, sticky='ew', pady=(5, 0))

    left_group.columnconfigure(0, weight=1)

    # Right group - PROGRAM EXECUTION
    right_group = ttk.LabelFrame(top_frame, text="PROGRAM EXECUTION",
                                style='VSGroup.TLabelframe', padding="10")
    right_group.pack(side='right', fill='both', expand=True, padx=(5, 0))

    # Buttons - reorganized without START DATA SEARCH
    button_width = 20

    btn_frame1 = tk.Frame(right_group, bg=vs_colors['form_bg'])
    btn_frame1.pack(fill='x', pady=(0, 5))

    ttk.Button(btn_frame1, text="SHOW KNOWLEDGE", style='VSButton.TButton', width=button_width).pack(side='left', padx=(0, 5))
    ttk.Button(btn_frame1, text="EDIT WEBSITES", style='VSButton.TButton', width=button_width).pack(side='right')

    btn_frame2 = tk.Frame(right_group, bg=vs_colors['form_bg'])
    btn_frame2.pack(fill='x', pady=(0, 5))

    ttk.Button(btn_frame2, text="CLEAR RESULTS", style='VSButton.TButton', width=button_width).pack(side='left', padx=(0, 5))
    ttk.Button(btn_frame2, text="TEST LEARNING", style='VSButton.TButton', width=button_width).pack(side='right')

    btn_frame3 = tk.Frame(right_group, bg=vs_colors['form_bg'])
    btn_frame3.pack(fill='x', pady=(0, 5))

    ttk.Button(btn_frame3, text="CLEANUP FILES", style='VSButton.TButton', width=button_width).pack(side='left', padx=(0, 5))
    ttk.Button(btn_frame3, text="HELP", style='VSButton.TButton', width=button_width).pack(side='right')

    # Middle section - Program Options (full width)
    middle_frame = tk.Frame(main_frame, bg=vs_colors['form_bg'])
    middle_frame.pack(fill='x', pady=(0, 10))

    # Options group (full width)
    options_group = ttk.LabelFrame(middle_frame, text="PROGRAM OPTIONS",
                                 style='VSGroup.TLabelframe', padding="10")
    options_group.pack(fill='both', expand=True)

    # Create frame for two-column layout
    checkbox_frame = tk.Frame(options_group, bg=vs_colors['group_bg'])
    checkbox_frame.pack(fill='both', expand=True)

    # Left column frame
    left_col = tk.Frame(checkbox_frame, bg=vs_colors['group_bg'])
    left_col.pack(side='left', fill='both', expand=True, padx=(0, 10))

    # Right column frame
    right_col = tk.Frame(checkbox_frame, bg=vs_colors['group_bg'])
    right_col.pack(side='right', fill='both', expand=True, padx=(10, 0))

    # Left column - Datasheet options (ALL ENABLED)
    left_options = [
        "SEARCH DIGIKEY FOR DATA SHEETS",
        "SEARCH MOUSER FOR DATA SHEETS",
        "SEARCH FOR MANUFACTURE AND DATA SHEET"
    ]

    for option in left_options:
        var = tk.BooleanVar(value=True)
        cb = tk.Checkbutton(left_col, text=option, variable=var,
                           bg=vs_colors['group_bg'], fg=vs_colors['text_color'],
                           font=('Microsoft Sans Serif', 8), anchor='w')
        cb.pack(fill='x', pady=2)

    # Right column - 3D Model options (ALL ENABLED)
    right_options = [
        "SEACH MANUFACTURER FOR 3D MODEL",
        "SEACH ULTRALIBRARIAN FOR 3D MODEL",
        "SEACH SAMACSYS FOR 3D MODEL",
        "SEACH SNAPEDA FOR 3D MODEL"
    ]

    for option in right_options:
        var = tk.BooleanVar(value=True)
        cb = tk.Checkbutton(right_col, text=option, variable=var,
                           bg=vs_colors['group_bg'], fg=vs_colors['text_color'],
                           font=('Microsoft Sans Serif', 8), anchor='w')
        cb.pack(fill='x', pady=2)

    # Results section
    results_group = ttk.LabelFrame(main_frame, text="SEARCH RESULTS",
                                 style='VSGroup.TLabelframe', padding="10")
    results_group.pack(fill='both', expand=True)
    
    text_frame = tk.Frame(results_group, bg=vs_colors['group_bg'])
    text_frame.pack(fill='both', expand=True)
    
    results_text = scrolledtext.ScrolledText(
        text_frame,
        wrap=tk.WORD,
        width=100,
        height=20,
        bg=vs_colors['control_bg'],
        fg=vs_colors['text_color'],
        font=('Microsoft Sans Serif', 8),
        relief='sunken',
        borderwidth=1
    )
    results_text.pack(fill='both', expand=True)
    
    # Add some demo text
    demo_text = """COMPONENT DATA SHEET AND 3D MODEL FINDER PROGRAM  TELEDYNE FLIR

  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\\Python\\web-get-files1
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 📊 Master database loaded: Teledyne-Flir Datasheet 3d model database.csv
 11. 🔧 Testing colored emoji image system...
 12. ==================================================
 13. 🔍 Searching for datasheet and 3D Models for Texas Instruments LP590722QDQNRQ1
 14. 📡 Searching API Digi-Key website for Texas Instruments LP590722QDQNRQ1 Datasheet
 15. ✅ Found 1 products on Digi-Key
 16. 📥 Downloading from Digi-Key Texas Instruments LP590722QDQNRQ1 Datasheet
 17. ✅ Downloaded: lp590722qdqnrq1.pdf (2.1 MB)
 18. 🔍 Searching for 3D models...
 19. ✅ Found 3D model on UltraLibrarian
 20. 📥 Downloading 3D model...
 21. ✅ Downloaded: LP590722QDQNRQ1.step (156 KB)
 22. 🎉 Search completed successfully!"""
    
    results_text.insert('1.0', demo_text)

    # Status bar
    status_frame = tk.Frame(root, bg=vs_colors['button_bg'], height=25)
    status_frame.pack(fill='x', side='bottom')
    status_frame.pack_propagate(False)
    
    status_label = tk.Label(status_frame, text="Ready - Visual Studio Forms Style Component Finder",
                           bg=vs_colors['button_bg'], fg=vs_colors['text_color'],
                           font=('Microsoft Sans Serif', 8), anchor='w')
    status_label.pack(side='left', padx=5, pady=2)
    
    progress = ttk.Progressbar(status_frame, mode='determinate', length=200)
    progress.pack(side='right', padx=5, pady=2)

    root.mainloop()

if __name__ == "__main__":
    create_vs_forms_demo()
