#!/usr/bin/env python3
"""
CLEAN ULTRALIBRARIAN 3D MODEL FINDER
====================================
1. Find EXACT part number and download 3D file
2. If exact part NOT found → Find up to 5 alternates
3. Show alternates to user → Let them select one or do nothing
"""

import os
import sys
import time
import json
import zipfile
import csv
import tkinter as tk
from tkinter import messagebox, ttk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def log_to_csv(manufacturer, part_number, source, step_filename):
    """Log the download information to CSV file - prevents duplicates"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")

    # Check for duplicates by reading existing entries
    if os.path.exists(csv_filename):
        try:
            with open(csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if (row['MANUFACTURER'].lower() == manufacturer.lower() and
                        row['PART_NUMBER'].lower() == part_number.lower() and
                        row['SOURCE'].lower() == source.lower()):
                        print(f"ℹ️ Entry already exists in CSV: {manufacturer}, {part_number}, {source}")
                        return
        except Exception as e:
            print(f"⚠️ Could not read CSV for duplicate check: {e}")

    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)

    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header if file is new
            if not file_exists:
                writer.writeheader()
                print(f"✅ Created new CSV file: {csv_filename}")

            # Write the data
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': source,
                'STEP_FILE_NAME': step_filename
            })

            print(f"✅ Logged to CSV: {manufacturer}, {part_number}, {source}, {step_filename}")

    except Exception as e:
        print(f"⚠️ Could not write to CSV: {e}")

def show_alternatives_gui(original_part, alternatives):
    """Show GUI for selecting alternatives"""

    class AlternativeSelector:
        def __init__(self):
            self.selected_index = None
            self.cancelled = False

        def show_dialog(self):
            root = tk.Tk()
            root.title("UltraLibrarian - Select Alternative Part")
            root.geometry("500x400")
            root.resizable(False, False)

            # Center the window
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - (500 // 2)
            y = (root.winfo_screenheight() // 2) - (400 // 2)
            root.geometry(f"500x400+{x}+{y}")

            # Main frame
            main_frame = ttk.Frame(root, padding="20")
            main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

            # Title
            title_label = ttk.Label(main_frame, text="Part Not Available",
                                  font=("Arial", 16, "bold"))
            title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

            # Original part info
            orig_label = ttk.Label(main_frame, text=f"Original part '{original_part}' has no 3D model available.")
            orig_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

            # Alternatives label
            alt_label = ttk.Label(main_frame, text="Available alternatives:",
                                font=("Arial", 12, "bold"))
            alt_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))

            # Checkboxes for alternatives
            checkbox_frame = ttk.Frame(main_frame)
            checkbox_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))

            # Create scrollable frame for checkboxes
            canvas = tk.Canvas(checkbox_frame, height=150)
            scrollbar = ttk.Scrollbar(checkbox_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

            # Create checkboxes for each alternative
            self.checkbox_vars = []
            for i, (link, text, href) in enumerate(alternatives):
                var = tk.BooleanVar()
                checkbox = ttk.Checkbutton(scrollable_frame,
                                         text=f"{i+1}. {text}",
                                         variable=var)
                checkbox.grid(row=i, column=0, sticky=tk.W, pady=2, padx=5)
                self.checkbox_vars.append(var)

            # Buttons frame
            button_frame = ttk.Frame(main_frame)
            button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

            def on_select():
                selected_indices = []
                for i, var in enumerate(self.checkbox_vars):
                    if var.get():
                        selected_indices.append(i)

                if selected_indices:
                    self.selected_index = selected_indices  # Return list of selected indices
                    root.destroy()
                else:
                    messagebox.showwarning("No Selection", "Please select at least one alternative part.")

            def on_cancel():
                self.cancelled = True
                root.destroy()

            # Buttons
            select_btn = ttk.Button(button_frame, text="Use Selected Alternatives",
                                  command=on_select, width=22)
            select_btn.grid(row=0, column=0, padx=(0, 10))

            cancel_btn = ttk.Button(button_frame, text="Cancel",
                                  command=on_cancel, width=15)
            cancel_btn.grid(row=0, column=1)

            # Configure grid weights
            root.columnconfigure(0, weight=1)
            root.rowconfigure(0, weight=1)
            main_frame.columnconfigure(0, weight=1)
            checkbox_frame.columnconfigure(0, weight=1)

            # Show dialog - fix taskbar problem
            root.lift()  # Bring window to front
            root.attributes('-topmost', True)  # Keep on top
            root.after(100, lambda: root.attributes('-topmost', False))  # Remove topmost after 100ms
            root.focus_force()
            root.grab_set()
            root.mainloop()

            return self.selected_index, self.cancelled

    selector = AlternativeSelector()
    return selector.show_dialog()

def find_3d_model(manufacturer, part_number, silent=False):
    if not silent:
        print("🎯 ULTRALIBRARIAN 3D MODEL FINDER")
        print("=" * 50)
        print(f"🔍 Looking for: {manufacturer} {part_number}")

    # Setup Chrome
    chrome_options = Options()

    # Configure for silent mode
    if silent:
        chrome_options.add_argument('--headless')  # Run in background
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    else:
        chrome_options.add_argument("--start-maximized")

    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)

    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    initial_files = set(os.listdir('3d')) if os.path.exists('3d') else set()
    
    try:
        # Step 1: Load homepage and login
        print("\n🔸 STEP 1: Loading and logging in...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(3)
        
        # Click LOGIN
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login']")
        if login_links:
            try:
                login_links[0].click()
            except:
                driver.execute_script("arguments[0].click();", login_links[0])
            time.sleep(3)
        
        # Fill login form
        with open('component_site_credentials.json', 'r') as f:
            credentials = json.load(f)
        
        email_inputs = driver.find_elements(By.XPATH, "//input[contains(@placeholder, 'Email')]")
        if not email_inputs:
            email_inputs = driver.find_elements(By.CSS_SELECTOR, "input[name='Username']")
        
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')]")
        
        if email_inputs and password_inputs and login_buttons:
            email_inputs[0].send_keys(credentials['UltraLibrarian']['email'])
            password_inputs[0].send_keys(credentials['UltraLibrarian']['password'])
            login_buttons[0].click()
            time.sleep(8)
            print("✅ Logged in successfully")
        
        # Step 2: Search for part
        print(f"\n🔸 STEP 2: Searching for {part_number}...")
        
        search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
        search_box = None
        for inp in search_inputs:
            if inp.is_displayed() and inp.is_enabled():
                placeholder = inp.get_attribute('placeholder') or ''
                if 'search' in placeholder.lower():
                    search_box = inp
                    break
        
        if not search_box:
            print("❌ No search box found!")
            return None
        
        # First try exact part number search
        search_box.clear()
        search_box.send_keys(part_number)
        search_box.send_keys(Keys.RETURN)
        time.sleep(10)

        print(f"✅ Search completed: {driver.current_url}")

        # Check if we found the exact part
        exact_part_found = False
        all_links = driver.find_elements(By.TAG_NAME, "a")
        for link in all_links:
            try:
                text = link.text.strip()
                if text.lower() == part_number.lower():
                    exact_part_found = True
                    break
            except:
                continue

        # If exact part not found, try broader search with base part number
        if not exact_part_found:
            base_part = part_number.split('-')[0] if '-' in part_number else part_number
            print(f"🔍 Exact part not found, searching for base part: {base_part}")

            search_box = None
            search_inputs = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='search' i], input[type='text'], input[type='search']")
            for inp in search_inputs:
                if inp.is_displayed() and inp.is_enabled():
                    placeholder = inp.get_attribute('placeholder') or ''
                    if 'search' in placeholder.lower():
                        search_box = inp
                        break

            if search_box:
                search_box.clear()
                search_box.send_keys(base_part)
                search_box.send_keys(Keys.RETURN)
                time.sleep(10)
                print(f"✅ Broader search completed: {driver.current_url}")
        
        # Step 3: Find EXACT part or alternates
        print(f"\n🔸 STEP 3: Looking for EXACT part or alternates...")
        
        all_links = driver.find_elements(By.TAG_NAME, "a")
        base_part = part_number.split('-')[0] if '-' in part_number else part_number
        
        # Find all matching parts - be EXTREMELY flexible
        all_parts = []
        print(f"🔍 Looking for parts containing '{base_part}' or similar...")

        for link in all_links:
            try:
                text = link.text.strip()
                href = link.get_attribute('href') or ''

                # Look for ANY link that might be a part
                if (href and text and link.is_displayed() and link.is_enabled() and
                    'login' not in href.lower() and 'logout' not in href.lower() and
                    len(text) > 3):

                    # Check multiple ways this could be a related part
                    is_related = False

                    # Method 1: Contains base part number
                    if base_part.lower() in text.lower():
                        is_related = True

                    # Method 2: Contains base part in href
                    elif base_part.lower() in href.lower():
                        is_related = True

                    # Method 3: Looks like a part number and has some digits from base part
                    elif (any(char.isdigit() for char in text) and any(char.isalpha() for char in text) and
                          len(text) > 5):
                        # Check if it has some common digits/letters with our part
                        base_digits = ''.join(c for c in base_part if c.isdigit())
                        text_digits = ''.join(c for c in text if c.isdigit())
                        if base_digits and len(base_digits) >= 3 and base_digits[:3] in text_digits:
                            is_related = True

                    if is_related:
                        all_parts.append((link, text, href))
                        print(f"   📋 Found: '{text}' -> {href}")
            except:
                continue
        
        print(f"🔍 Total parts found: {len(all_parts)}")
        
        # Look for EXACT match first
        exact_match = None
        for link, text, href in all_parts:
            if text.lower().strip() == part_number.lower().strip():
                print(f"✅ EXACT MATCH: '{text}'")
                exact_match = link
                break
        
        selected_link = None
        
        if exact_match:
            print(f"🎯 Found exact match")

            # STEP 3: Click on exact match to go to part detail page
            print("🔸 STEP 3: Clicking on exact part to go to detail page...")
            exact_match.click()
            time.sleep(8)
            print(f"✅ Now on part detail page: {driver.current_url}")

            # STEP 4: Search for that part number on that page
            print(f"🔸 STEP 4: Searching for part number '{part_number}' on this page...")
            page_text = driver.page_source.lower()
            part_found_on_page = part_number.lower() in page_text

            if part_found_on_page:
                print(f"✅ Found '{part_number}' on the page")

                # STEP 5: If found check if it has a download button
                print("🔸 STEP 5: Checking if it has a download button...")
                download_button = None
            else:
                print(f"❌ Part number '{part_number}' not found on this page")
                download_button = None

            # Method 1: Try ID selector
            try:
                download_button = driver.find_element(By.ID, "export-selection-btn")
                if download_button.is_displayed() and download_button.is_enabled():
                    print("✅ Found download button by ID")
                else:
                    download_button = None
            except:
                print("❌ No download button found by ID")

            # Method 2: Only try the specific export-selection-btn selectors
            if not download_button:
                selectors = [
                    "button[id='export-selection-btn']",
                    "//button[@id='export-selection-btn']"
                ]

                for selector in selectors:
                    try:
                        if selector.startswith("//"):
                            elements = driver.find_elements(By.XPATH, selector)
                        else:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)

                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                download_button = element
                                print(f"✅ Found export-selection-btn with selector: {selector}")
                                break

                        if download_button:
                            break
                    except:
                        continue

            if download_button:
                print("✅ Exact match has 3D model - proceeding with download")
                print(f"🔍 DEBUG: About to proceed with download process...")
                selected_link = exact_match
            else:
                print("❌ Exact match has NO download button - no 3D model available")
                print(f"🔍 DEBUG: No download button found, about to look for alternatives...")

            if download_button:
                # Exact match has 3D model - use it
                selected_link = exact_match
            else:
                # Exact match has no 3D model - look for "Select" links on this page
                print("⚠️ Exact match has no 3D model - looking for Select links on this page")

                # Look for "Select" links that point to alternative parts
                all_links = driver.find_elements(By.TAG_NAME, "a")
                alternates = []

                for link in all_links:
                    try:
                        text = link.text.strip()
                        href = link.get_attribute('href') or ''

                        # Look for "Select" links that contain part details
                        if (text.lower() == 'select' and href and 'details' in href.lower() and
                            link.is_displayed() and link.is_enabled()):

                            # Extract part number from the URL
                            # URL format: .../Diodes-Inc/APX803L20-25SA-7?open=backlink
                            import re
                            part_match = re.search(r'/([A-Z0-9\-]+)\?', href)
                            if part_match:
                                alt_part_number = part_match.group(1)
                                if alt_part_number.lower() != part_number.lower():
                                    alternates.append((link, alt_part_number, href))
                                    print(f"   📋 Alternative found: '{alt_part_number}' (Select link)")
                    except:
                        continue

                if not alternates:
                    print(f"❌ No alternatives found")
                    return None

                # Show up to 5 alternates
                alternates = alternates[:5]
                print(f"📋 Found {len(alternates)} alternatives:")
                for i, (link, text, href) in enumerate(alternates, 1):
                    print(f"   {i}. {text}")

                print(f"\n💡 Showing GUI for alternative selection...")

                # Show GUI for selection
                selected_indices, cancelled = show_alternatives_gui(part_number, alternates)

                if cancelled or selected_indices is None:
                    print("❌ Cancelled by user")
                    return None

                # Handle multiple selections - try each one until we find one with 3D model
                print(f"✅ Selected {len(selected_indices)} alternatives:")
                for idx in selected_indices:
                    selected_link, selected_text, selected_href = alternates[idx]
                    print(f"   - {selected_text}")

                # Try first selected alternative
                selected_link, selected_text, selected_href = alternates[selected_indices[0]]
                print(f"🔸 Trying first selection: {selected_text}")

                # Click on selected alternative
                print(f"\n🔸 STEP 4: Downloading selected alternative...")
                selected_link.click()
                time.sleep(8)

                # Check if alternative has download button - use same thorough method
                download_button = None
                print("🔍 Checking for download button on alternative page...")

                # Method 1: Try ID selector
                try:
                    download_button = driver.find_element(By.ID, "export-selection-btn")
                    if download_button.is_displayed() and download_button.is_enabled():
                        print("✅ Found download button by ID on alternative")
                    else:
                        download_button = None
                except:
                    print("❌ No download button found by ID on alternative")

                # Method 2: Try other export-selection-btn selectors
                if not download_button:
                    selectors = [
                        "button[id='export-selection-btn']",
                        "//button[@id='export-selection-btn']"
                    ]

                    for selector in selectors:
                        try:
                            if selector.startswith("//"):
                                elements = driver.find_elements(By.XPATH, selector)
                            else:
                                elements = driver.find_elements(By.CSS_SELECTOR, selector)

                            for element in elements:
                                if element.is_displayed() and element.is_enabled():
                                    download_button = element
                                    print(f"✅ Found export-selection-btn on alternative with selector: {selector}")
                                    break

                            if download_button:
                                break
                        except:
                            continue

                if download_button:
                    print("✅ Alternative has 3D model")
                else:
                    print("❌ Alternative has NO download button - no 3D model available")

                if not download_button:
                    print("❌ Selected alternative also has no 3D model")
                    return None
        else:
            print(f"❌ EXACT part {part_number} NOT found on page")
            return None
        
        # Click Download Now
        print(f"🔍 DEBUG: Download button text: '{download_button.text}'")
        print(f"🔍 DEBUG: Download button tag: '{download_button.tag_name}'")
        print(f"🔍 DEBUG: Download button enabled: {download_button.is_enabled()}")
        print(f"🔍 DEBUG: Download button displayed: {download_button.is_displayed()}")

        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", download_button)
        time.sleep(2)

        print(f"🔍 DEBUG: About to click download button...")
        try:
            download_button.click()
            print("✅ Clicked Download Now (regular click)")
        except Exception as e:
            print(f"⚠️ Regular click failed: {e}")
            try:
                driver.execute_script("arguments[0].click();", download_button)
                print("✅ Clicked Download Now (JavaScript click)")
            except Exception as e2:
                print(f"❌ JavaScript click also failed: {e2}")
                return None

        print(f"🔍 DEBUG: Waiting 10 seconds after click...")
        time.sleep(10)
        print(f"🔍 DEBUG: Current URL after click: {driver.current_url}")

        # Handle export dialog - click 3D CAD Model section first
        print("🔍 DEBUG: Looking for 3D CAD Model section to expand...")

        # Step 1: Click "3D CAD Model" link to expand the section
        try:
            cad_model_link = driver.find_element(By.XPATH, "//a[contains(text(), '3D CAD Model')]")
            if cad_model_link.is_displayed() and cad_model_link.is_enabled():
                print("🔍 DEBUG: Found 3D CAD Model link")
                cad_model_link.click()
                print("✅ Clicked 3D CAD Model section")
                time.sleep(3)
            else:
                print("⚠️ 3D CAD Model link not clickable")
        except Exception as e:
            print(f"⚠️ Could not find 3D CAD Model link: {e}")

        # Step 2: Check the ThreeDModel checkbox
        print("🔍 DEBUG: Looking for ThreeDModel checkbox...")
        try:
            threed_checkbox = driver.find_element(By.ID, "ThreeDModel")
            if threed_checkbox.is_enabled():
                print("🔍 DEBUG: Found ThreeDModel checkbox")
                # Use JavaScript to check the checkbox since it might be hidden
                driver.execute_script("arguments[0].checked = true;", threed_checkbox)
                print("✅ Checked ThreeDModel checkbox")
                time.sleep(2)
            else:
                print("⚠️ ThreeDModel checkbox not enabled")
        except Exception as e:
            print(f"⚠️ Could not find ThreeDModel checkbox: {e}")

        # Step 3: Click Download Now link (try multiple methods)
        print("🔍 DEBUG: Looking for Download Now link...")
        download_clicked = False

        # Method 1: Try by ID (submit-export)
        try:
            submit_export = driver.find_element(By.ID, "submit-export")
            if submit_export.is_displayed():
                print("🔍 DEBUG: Found submit-export button")

                # Wait for button to become enabled (remove disabled class)
                print("🔍 DEBUG: Waiting for button to become enabled...")
                for i in range(10):  # Wait up to 10 seconds
                    class_attr = submit_export.get_attribute('class') or ''
                    if 'disabled' not in class_attr:
                        print("✅ Button is now enabled")
                        break
                    time.sleep(1)
                    print(f"   Waiting for enable... ({i+1}/10)")

                # Try JavaScript click to avoid interception
                try:
                    driver.execute_script("arguments[0].click();", submit_export)
                    print("✅ Clicked Download Now link (JavaScript)")
                    download_clicked = True
                except Exception as js_e:
                    print(f"⚠️ JavaScript click failed: {js_e}")
        except Exception as e:
            print(f"⚠️ Could not find submit-export button: {e}")

        # Method 2: Try by text if ID method failed
        if not download_clicked:
            try:
                download_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Download Now')]")

                for link in download_links:
                    if link.is_displayed():
                        print("🔍 DEBUG: Found Download Now link by text")
                        try:
                            # Try JavaScript click
                            driver.execute_script("arguments[0].click();", link)
                            print("✅ Clicked Download Now link (JavaScript)")
                            download_clicked = True
                            break
                        except Exception as js_e:
                            print(f"⚠️ JavaScript click failed: {js_e}")
                            continue
            except Exception as e:
                print(f"⚠️ Could not find Download Now links: {e}")

        if not download_clicked:
            print("❌ Could not click Download Now link with any method")

        time.sleep(5)

        # Monitor for downloads
        print("🔍 Monitoring for downloads...")
        processed_files = set()  # Track files we've already processed

        for i in range(24):  # 2 minutes
            time.sleep(5)
            current_files = set(os.listdir('3d')) if os.path.exists('3d') else set()
            new_files = current_files - initial_files - processed_files

            if new_files:
                print(f"🎉 New files detected: {list(new_files)}")

                # Process each new file
                for new_file in new_files:
                    processed_files.add(new_file)  # Mark as processed

                    if new_file.lower().endswith(('.step', '.stp')):
                        print(f"✅ STEP file: {new_file}")

                        # Log to CSV file
                        log_to_csv(manufacturer, part_number, "ULTRALIBRARIAN", new_file)

                        return new_file

                    elif new_file.lower().endswith('.zip'):
                        print(f"📦 ZIP file: {new_file}")
                        try:
                            zip_path = os.path.join('3d', new_file)
                            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                zip_ref.extractall('3d')

                            # Check for extracted STEP files
                            final_files = set(os.listdir('3d'))
                            extracted_files = final_files - current_files - processed_files
                            step_files = [f for f in extracted_files if f.lower().endswith(('.step', '.stp'))]

                            if step_files:
                                original_step_file = step_files[0]
                                print(f"✅ STEP file extracted: {original_step_file}")

                                # Rename to proper format
                                new_filename = f"Ultralibrarian-{manufacturer}-{part_number}.step"
                                old_path = os.path.join('3d', original_step_file)
                                new_path = os.path.join('3d', new_filename)

                                try:
                                    # If target file exists, remove it first
                                    if os.path.exists(new_path):
                                        os.remove(new_path)
                                        print(f"🔍 Removed existing file: {new_filename}")

                                    os.rename(old_path, new_path)
                                    print(f"✅ Renamed to: {new_filename}")

                                    # Log to CSV file
                                    log_to_csv(manufacturer, part_number, "ULTRALIBRARIAN", new_filename)

                                    # Clean up ZIP file after successful processing
                                    try:
                                        os.remove(zip_path)
                                        print(f"🗑️ Removed ZIP file: {new_file}")
                                    except Exception as e:
                                        print(f"⚠️ Could not remove ZIP file: {e}")

                                    return new_filename
                                except Exception as e:
                                    print(f"⚠️ Could not rename file: {e}")

                                    # Still log to CSV even if rename failed
                                    log_to_csv(manufacturer, part_number, "ULTRALIBRARIAN", original_step_file)

                                    # Clean up ZIP file even if rename failed
                                    try:
                                        os.remove(zip_path)
                                        print(f"🗑️ Removed ZIP file: {new_file}")
                                    except Exception as e:
                                        print(f"⚠️ Could not remove ZIP file: {e}")

                                    return original_step_file
                        except Exception as e:
                            print(f"Error extracting ZIP: {e}")
                            # Clean up ZIP file even if extraction failed
                            try:
                                os.remove(zip_path)
                                print(f"🗑️ Removed failed ZIP file: {new_file}")
                            except Exception as e:
                                print(f"⚠️ Could not remove failed ZIP file: {e}")

            print(f"   Waiting... ({i+1}/24)")

        print("❌ No download detected after 2 minutes")
        return None
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return None
    
    finally:
        print("🔒 Closing browser...")
        driver.quit()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python ultralibrarian_clean.py <manufacturer> <part_number>")
        print("Example: python ultralibrarian_clean.py \"Texas Instruments\" LM358N")
        print("Example: python ultralibrarian_clean.py \"Diodes Inc\" APX803L20-30SA-7")
        sys.exit(1)

    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    result = find_3d_model(manufacturer, part_number)
    
    if result:
        print(f"\n🎉 SUCCESS: {result} downloaded!")
    else:
        print(f"\n❌ FAILED: No 3D model obtained for {part_number}")
