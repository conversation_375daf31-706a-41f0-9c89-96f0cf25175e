#!/usr/bin/env python3
"""
Test Dialog Positioning Issues
This will help diagnose the real problems
"""

import tkinter as tk
from tkinter import messagebox

def test_dialog_positioning():
    """Test how dialogs are positioned"""
    
    # Create main window
    root = tk.Tk()
    root.title("Main Window - Move me to secondary monitor")
    root.geometry("400x300+100+100")
    
    def show_simple_dialog():
        """Show a simple dialog"""
        print("=== SIMPLE DIALOG TEST ===")
        result = messagebox.showinfo("Test", "This is a simple dialog", parent=root)
        print(f"Dialog result: {result}")
    
    def show_custom_dialog():
        """Show a custom dialog with explicit positioning"""
        print("=== CUSTOM DIALOG TEST ===")
        
        # Get main window position
        main_x = root.winfo_rootx()
        main_y = root.winfo_rooty()
        main_width = root.winfo_width()
        main_height = root.winfo_height()
        
        print(f"Main window at: ({main_x}, {main_y}) size {main_width}x{main_height}")
        
        # Create custom dialog
        dialog = tk.Toplevel(root)
        dialog.title("Custom Dialog")
        dialog.geometry("300x200")
        
        # Try different positioning approaches
        dialog.transient(root)
        dialog.grab_set()
        
        # Calculate center position
        dialog_width = 300
        dialog_height = 200
        x = main_x + (main_width - dialog_width) // 2
        y = main_y + (main_height - dialog_height) // 2
        
        print(f"Calculated dialog position: ({x}, {y})")
        
        # Set position explicitly
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        # Add content
        tk.Label(dialog, text="Custom Dialog\nPositioned relative to main window").pack(expand=True)
        tk.Button(dialog, text="Close", command=dialog.destroy).pack(pady=10)
        
        # Check actual position after showing
        dialog.update_idletasks()
        actual_x = dialog.winfo_rootx()
        actual_y = dialog.winfo_rooty()
        print(f"Actual dialog position: ({actual_x}, {actual_y})")
    
    def show_alternate_test():
        """Test alternate part selection logic"""
        print("=== ALTERNATE SELECTION TEST ===")
        
        alternates = ["PART-001", "PART-002", "PART-003"]
        original_part = "ORIGINAL-PART"
        
        # Create selection dialog
        choice_dialog = tk.Toplevel(root)
        choice_dialog.title("Select Alternate Part Number")
        choice_dialog.geometry("400x300")
        choice_dialog.transient(root)
        choice_dialog.grab_set()
        
        selected_part = tk.StringVar()
        
        # Title
        tk.Label(choice_dialog, text=f"Select Alternate for {original_part}", 
                font=('Arial', 12, 'bold')).pack(pady=10)
        
        # Listbox
        listbox_frame = tk.Frame(choice_dialog)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        listbox = tk.Listbox(listbox_frame)
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
        listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=listbox.yview)
        
        for alt in alternates:
            listbox.insert(tk.END, alt)
        
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons
        button_frame = tk.Frame(choice_dialog)
        button_frame.pack(pady=10)
        
        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_part.set(alternates[selection[0]])
                print(f"User selected: {selected_part.get()}")
                choice_dialog.destroy()
            else:
                print("No selection made")
        
        def on_cancel():
            selected_part.set("")
            print("User cancelled")
            choice_dialog.destroy()
        
        tk.Button(button_frame, text="Select", command=on_select).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)
        
        # Wait for dialog to close
        choice_dialog.wait_window()
        
        chosen_part = selected_part.get()
        print(f"Final result: '{chosen_part}'")
        if chosen_part:
            print(f"SUCCESS: User selected alternate: {original_part} → {chosen_part}")
            return chosen_part
        else:
            print(f"CANCELLED: Continuing with original: {original_part}")
            return None
    
    # Create test buttons
    tk.Label(root, text="Dialog Positioning Tests\n\n1. Move this window to secondary monitor\n2. Click buttons to test dialogs", 
             justify=tk.CENTER).pack(pady=20)
    
    tk.Button(root, text="Test Simple Dialog (messagebox)", command=show_simple_dialog).pack(pady=5)
    tk.Button(root, text="Test Custom Dialog (positioned)", command=show_custom_dialog).pack(pady=5)
    tk.Button(root, text="Test Alternate Selection", command=show_alternate_test).pack(pady=5)
    tk.Button(root, text="Quit", command=root.quit).pack(pady=20)
    
    print("=== DIALOG POSITIONING TEST ===")
    print("1. Move the main window to your secondary monitor")
    print("2. Click the test buttons")
    print("3. Observe where the dialogs appear")
    print("4. Check the console output for positioning details")
    
    root.mainloop()

if __name__ == "__main__":
    test_dialog_positioning()
