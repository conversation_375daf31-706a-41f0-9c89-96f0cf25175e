#!/usr/bin/env python3
"""
SamacSys Credentials Management
Handles login credentials for SamacSys/ComponentSearchEngine
"""

import os
import json
from getpass import getpass

CREDENTIALS_FILE = "samacsys_credentials.json"

def get_credentials():
    """Get SamacSys login credentials"""
    if os.path.exists(CREDENTIALS_FILE):
        try:
            with open(CREDENTIALS_FILE, 'r') as f:
                creds = json.load(f)
                if 'email' in creds and 'password' in creds:
                    print("✅ Found saved SamacSys credentials")
                    return creds['email'], creds['password']
        except:
            pass
    
    print("🔐 SamacSys Login Required")
    print("=" * 40)
    print("To download 3D models from SamacSys, you need a free account.")
    print("Register at: https://componentsearchengine.com/")
    print()
    
    email = input("Enter your SamacSys email: ").strip()
    password = getpass("Enter your SamacSys password: ").strip()
    
    if email and password:
        save_choice = input("Save credentials for future use? (y/n): ").strip().lower()
        if save_choice == 'y':
            save_credentials(email, password)
        
        return email, password
    else:
        print("❌ Invalid credentials provided")
        return None, None

def save_credentials(email, password):
    """Save credentials to file"""
    try:
        creds = {
            'email': email,
            'password': password
        }
        with open(CREDENTIALS_FILE, 'w') as f:
            json.dump(creds, f, indent=2)
        print(f"✅ Credentials saved to {CREDENTIALS_FILE}")
    except Exception as e:
        print(f"⚠️ Could not save credentials: {e}")

def clear_credentials():
    """Clear saved credentials"""
    if os.path.exists(CREDENTIALS_FILE):
        os.remove(CREDENTIALS_FILE)
        print("✅ Credentials cleared")
    else:
        print("ℹ️ No credentials to clear")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "clear":
        clear_credentials()
    else:
        email, password = get_credentials()
        if email and password:
            print(f"Email: {email}")
            print("Password: [HIDDEN]")
        else:
            print("No valid credentials obtained")
