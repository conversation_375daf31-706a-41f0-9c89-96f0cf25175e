#!/usr/bin/env python3
"""
Simple test to check if SnapEDA finder can be imported
"""

import sys
import os

print("🔍 Testing SnapEDA import...")

# Add the save directory to path to import the finder
sys.path.append('save')

try:
    print("🔸 Importing SnapEDA finder...")
    from snapeda_3d_finder_final import find_3d_model as snapeda_find
    print("✅ Import successful!")
    
    print("🔸 Testing function call...")
    result = snapeda_find("Texas Instruments", "LM358N", silent=False)
    print(f"📊 Result: {result}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("🔍 Test complete!")
