#!/usr/bin/env python3
"""
Simple SnapEDA Test - Fast and Working
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def simple_snapeda_test():
    """Simple, fast SnapEDA test"""
    print("🚀 Simple SnapEDA Test...")
    
    # Create fast Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Download settings
    download_dir = os.path.join(os.getcwd(), "3d")
    os.makedirs(download_dir, exist_ok=True)
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    
    try:
        # Step 1: Login (FAST)
        print("1. Logging in...")
        driver.get("https://www.snapeda.com/account/login/")
        time.sleep(2)  # Fast delay
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        
        email_field.send_keys("<EMAIL>")
        password_field.send_keys("Lennyai123#")
        
        # Find login button
        login_button = driver.find_element(By.CSS_SELECTOR, "input[type='submit'], button[type='submit']")
        login_button.click()
        time.sleep(3)  # Fast delay
        print("✅ Login completed")
        
        # Step 2: Go to part (FAST)
        print("2. Going to part...")
        part_url = "https://www.snapeda.com/parts/GCM155R71H104KE02D/Murata/view-part/"
        driver.get(part_url)
        time.sleep(2)  # Fast delay
        print("✅ Part page loaded")
        
        # Step 3: Click 3D tab (FAST)
        print("3. Clicking 3D Model tab...")
        three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
        three_d_tab.click()
        time.sleep(2)  # Fast delay
        print("✅ 3D Model tab clicked")
        
        # Step 4: Find download button (FAST)
        print("4. Looking for download button...")
        download_selectors = [
            "//a[contains(@class, '3D-model-download')]",
            "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
            "//a[text()='Download 3D Model' and contains(@class, 'orange')]",
            "//a[contains(text(), 'Download') and contains(text(), '3D')]"
        ]
        
        download_button = None
        for selector in download_selectors:
            try:
                button = driver.find_element(By.XPATH, selector)
                if button.is_displayed():
                    download_button = button
                    print(f"✅ Found download button: '{button.text}'")
                    break
            except:
                continue
        
        if download_button:
            # Step 5: Click download (FAST)
            print("5. Clicking download...")
            download_button.click()
            time.sleep(3)  # Wait for download to start
            
            # Check for downloaded files
            files_before = set(os.listdir(download_dir))
            time.sleep(5)  # Wait for download
            files_after = set(os.listdir(download_dir))
            new_files = files_after - files_before
            
            if new_files:
                print(f"✅ Downloaded: {list(new_files)}")
                return True
            else:
                print("❌ No files downloaded")
                return False
        else:
            print("❌ No download button found")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    result = simple_snapeda_test()
    if result:
        print("🎉 SnapEDA test PASSED!")
    else:
        print("❌ SnapEDA test FAILED!")
