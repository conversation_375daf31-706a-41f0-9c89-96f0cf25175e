#!/usr/bin/env python3
"""
Simple 3D Model Discovery Test
Basic test to validate manufacturer website 3D model discovery
"""

import requests
from bs4 import BeautifulSoup
import os
import json

def simple_manufacturer_test(manufacturer, part_number):
    """Simple test of manufacturer website for 3D models"""
    print(f"🎯 SIMPLE 3D MODEL TEST")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 50)
    
    # Load manufacturer patterns
    patterns = {}
    try:
        with open('manufacturer_3d_patterns.json', 'r') as f:
            patterns = json.load(f)
    except:
        print("⚠️ Could not load manufacturer patterns")
    
    manufacturer_lower = manufacturer.lower()
    manufacturer_patterns = patterns.get(manufacturer_lower, {})
    
    # Get websites to test
    websites = manufacturer_patterns.get('websites', [])
    if not websites:
        base = manufacturer_lower.replace(" ", "").replace(".", "")
        websites = [f"https://www.{base}.com"]
    
    print(f"🌐 Testing {len(websites)} website(s)...")
    
    results = {
        'manufacturer': manufacturer,
        'part_number': part_number,
        'websites_tested': [],
        'accessible_sites': 0,
        'sites_with_3d_content': 0,
        'potential_urls': []
    }
    
    # Test each website
    for website in websites[:2]:  # Test first 2 websites
        print(f"\n🔍 Testing: {website}")
        
        try:
            response = requests.get(website, timeout=10, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                print(f"   ✅ Website accessible")
                results['accessible_sites'] += 1
                
                # Check for 3D content
                content = response.text.lower()
                has_3d = any(keyword in content for keyword in [
                    '3d model', 'cad', 'step', 'download', 'design resources'
                ])
                
                if has_3d:
                    print(f"   ✅ Has 3D-related content")
                    results['sites_with_3d_content'] += 1
                else:
                    print(f"   ⚠️ No obvious 3D content")
                
                # Generate potential URLs for this part
                potential_urls = [
                    f"{website}/product/{part_number}",
                    f"{website}/products/{part_number}",
                    f"{website}/search?q={part_number}",
                    f"{website}/en/products/{part_number}"
                ]
                
                results['potential_urls'].extend(potential_urls)
                
            else:
                print(f"   ❌ Error {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Failed: {str(e)[:50]}")
        
        results['websites_tested'].append(website)
    
    # Test a few potential URLs
    print(f"\n🔗 Testing potential part URLs...")
    working_urls = []
    
    for url in results['potential_urls'][:5]:  # Test first 5 URLs
        try:
            print(f"   Testing: {url}")
            response = requests.get(url, timeout=5, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                # Look for 3D model links
                soup = BeautifulSoup(response.text, 'html.parser')
                model_links = []
                
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text().lower()
                    
                    if any(ext in href for ext in ['.step', '.stl', '.igs']) or \
                       any(keyword in text for keyword in ['3d', 'cad', 'step', 'model']):
                        model_links.append({
                            'text': link.get_text().strip()[:40],
                            'href': href[:60]
                        })
                
                if model_links:
                    print(f"      ✅ Found {len(model_links)} potential 3D links")
                    working_urls.append({
                        'url': url,
                        'links': model_links[:3]  # Show first 3
                    })
                else:
                    print(f"      ⚠️ No 3D links found")
            else:
                print(f"      ❌ Error {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Failed: {str(e)[:30]}")
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    print(f"✅ Accessible websites: {results['accessible_sites']}")
    print(f"🎯 Sites with 3D content: {results['sites_with_3d_content']}")
    print(f"🔗 Working part URLs: {len(working_urls)}")
    
    if working_urls:
        print(f"\n🎯 BEST URLs FOR MANUAL CHECKING:")
        for i, url_info in enumerate(working_urls, 1):
            print(f"{i}. {url_info['url']}")
            for link in url_info['links']:
                print(f"   → {link['text']}")
    
    # Check existing downloads
    print(f"\n📁 EXISTING DOWNLOADS:")
    if os.path.exists('3d'):
        files = [f for f in os.listdir('3d') if f.endswith('.step')]
        part_files = [f for f in files if part_number.lower() in f.lower()]
        
        if part_files:
            print(f"Found {len(part_files)} existing files for this part:")
            for f in part_files:
                print(f"   ✅ {f}")
        else:
            print(f"No existing files for this part")
            print(f"Total STEP files: {len(files)}")
    
    return results

def main():
    """Main function"""
    print("🚀 SIMPLE 3D MODEL DISCOVERY TEST")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        ("Texas Instruments", "LM358N"),
        ("Analog Devices", "AD8606ARZ"),
        ("Murata", "GCM155R71H104KE02D")
    ]
    
    print("Available test cases:")
    for i, (mfg, part) in enumerate(test_cases, 1):
        print(f"{i}. {mfg} / {part}")
    print("4. Custom part")
    
    try:
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice in ['1', '2', '3']:
            manufacturer, part_number = test_cases[int(choice)-1]
        elif choice == '4':
            manufacturer = input("Enter manufacturer: ").strip()
            part_number = input("Enter part number: ").strip()
        else:
            print("Invalid choice")
            return
        
        if manufacturer and part_number:
            simple_manufacturer_test(manufacturer, part_number)
        else:
            print("❌ Both manufacturer and part number required")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
