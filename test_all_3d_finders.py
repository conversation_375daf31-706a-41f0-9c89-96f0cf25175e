#!/usr/bin/env python3
"""
Test all 3D finders to see current status
"""

import sys
import os
import time

# Add the save directory to path to import the finders
sys.path.append('save')

def test_all_finders():
    print("🎯 TESTING ALL 3D FINDERS")
    print("=" * 60)
    
    manufacturer = "Texas Instruments"
    part_number = "LM358N"
    
    print(f"🔍 Testing part: {manufacturer} {part_number}")
    print(f"   All tests will run in silent mode")
    
    results = {}
    
    # Test 1: SamacSys
    print("\n" + "=" * 60)
    print("1️⃣ TESTING SAMACSYS")
    print("=" * 60)
    
    try:
        from samacsys_3d_finder import find_3d_model as samacsys_find
        
        start_time = time.time()
        result = samacsys_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        if result:
            results['SamacSys'] = f"✅ SUCCESS: {result} ({end_time-start_time:.1f}s)"
        else:
            results['SamacSys'] = f"❌ FAILED ({end_time-start_time:.1f}s)"
            
    except Exception as e:
        results['SamacSys'] = f"❌ ERROR: {e}"
    
    # Test 2: UltraLibrarian
    print("\n" + "=" * 60)
    print("2️⃣ TESTING ULTRALIBRARIAN")
    print("=" * 60)
    
    try:
        from ultralibrarian_3d_finder import find_3d_model as ultra_find
        
        start_time = time.time()
        result = ultra_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        if result:
            results['UltraLibrarian'] = f"✅ SUCCESS: {result} ({end_time-start_time:.1f}s)"
        else:
            results['UltraLibrarian'] = f"❌ FAILED ({end_time-start_time:.1f}s)"
            
    except Exception as e:
        results['UltraLibrarian'] = f"❌ ERROR: {e}"
    
    # Test 3: SnapEDA
    print("\n" + "=" * 60)
    print("3️⃣ TESTING SNAPEDA")
    print("=" * 60)
    
    try:
        from snapeda_3d_finder_final import find_3d_model as snapeda_find
        
        start_time = time.time()
        result = snapeda_find(manufacturer, part_number, silent=True)
        end_time = time.time()
        
        if result:
            results['SnapEDA'] = f"✅ SUCCESS: {result} ({end_time-start_time:.1f}s)"
        else:
            results['SnapEDA'] = f"❌ FAILED ({end_time-start_time:.1f}s)"
            
    except Exception as e:
        results['SnapEDA'] = f"❌ ERROR: {e}"
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS SUMMARY")
    print("=" * 60)
    
    success_count = 0
    for name, result in results.items():
        print(f"{name:15}: {result}")
        if "SUCCESS" in result:
            success_count += 1
    
    print(f"\n🎯 SUCCESS RATE: {success_count}/3 finders working")
    
    # Check downloaded files
    if os.path.exists('3d'):
        files = os.listdir('3d')
        step_files = [f for f in files if f.endswith('.step')]
        if step_files:
            print(f"\n📁 DOWNLOADED STEP FILES:")
            for f in step_files:
                print(f"   ✅ {f}")
        else:
            print(f"\n📁 No STEP files found in 3d directory")
    else:
        print(f"\n📁 3d directory not found")
    
    return results

if __name__ == "__main__":
    test_all_finders()
