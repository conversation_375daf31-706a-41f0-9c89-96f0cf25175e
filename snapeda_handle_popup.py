#!/usr/bin/env python3
"""
SnapEDA Handle Popup - Deal with the part number popup that appears during automation
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON><PERSON><PERSON>

def snapeda_handle_popup():
    print("🔍 SNAPEDA HANDLE POPUP - DEAL WITH PART NUMBER POPUP")
    print("=" * 70)
    
    # Setup Chrome with even more anti-detection
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # More anti-detection measures
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")  # Faster loading
    chrome_options.add_argument("--no-sandbox")
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # Remove webdriver traces
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
    driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
    
    try:
        # Step 1: Go to SnapEDA
        print("🔸 Step 1: Going to SnapEDA...")
        driver.get("https://www.snapeda.com/")
        time.sleep(4)
        
        # Step 2: Click login
        print("🔸 Step 2: Clicking login...")
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(4)
        
        # Step 3: Check for any popups or modals BEFORE entering credentials
        print("🔸 Step 3: Checking for popups/modals...")
        
        popup_selectors = [
            ".modal",
            ".popup",
            ".overlay",
            "[role='dialog']",
            ".dialog",
            "#modal",
            ".modal-dialog"
        ]
        
        popup_found = False
        for selector in popup_selectors:
            try:
                popups = driver.find_elements(By.CSS_SELECTOR, selector)
                for popup in popups:
                    if popup.is_displayed():
                        print(f"🎯 POPUP DETECTED with selector: {selector}")
                        popup_text = popup.text[:200]
                        print(f"   Popup content: {popup_text}...")
                        
                        # Look for part number input in popup
                        try:
                            part_input = popup.find_element(By.XPATH, ".//input")
                            if part_input.is_displayed():
                                print(f"🔸 Found input in popup - entering part number...")
                                part_input.clear()
                                part_input.send_keys("LM358N")
                                
                                # Look for submit/OK button in popup
                                popup_buttons = popup.find_elements(By.XPATH, ".//button | .//input[@type='submit']")
                                for btn in popup_buttons:
                                    if btn.is_displayed() and ("ok" in btn.text.lower() or "submit" in btn.text.lower() or "search" in btn.text.lower()):
                                        print(f"🔸 Clicking popup button: {btn.text}")
                                        btn.click()
                                        popup_found = True
                                        break
                        except:
                            # Try to close popup with X or Cancel
                            try:
                                close_buttons = popup.find_elements(By.XPATH, ".//button[contains(text(), 'Close') or contains(text(), 'Cancel') or contains(text(), '×')]")
                                if close_buttons:
                                    print(f"🔸 Closing popup...")
                                    close_buttons[0].click()
                                    popup_found = True
                            except:
                                pass
                        
                        if popup_found:
                            break
                
                if popup_found:
                    break
            except:
                continue
        
        if popup_found:
            print("✅ Handled popup")
            time.sleep(3)
        else:
            print("✅ No popup detected")
        
        # Step 4: Now enter credentials
        print("🔸 Step 4: Entering credentials...")
        
        # Find email field
        email_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "#id_username"))
        )
        email_field.clear()
        email_field.send_keys("<EMAIL>")
        print("✅ Email entered")
        
        # Find password field
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.clear()
        password_field.send_keys("Lennyai123#")
        print("✅ Password entered")
        
        # Step 5: Click Log In button
        print("🔸 Step 5: Clicking Log In button...")
        
        # Wait a moment
        time.sleep(2)
        
        # Try multiple methods to click login
        login_clicked = False
        
        # Method 1: ActionChains click
        try:
            submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            if submit_btn.is_displayed() and submit_btn.is_enabled():
                actions = ActionChains(driver)
                actions.move_to_element(submit_btn).pause(1).click().perform()
                login_clicked = True
                print("✅ Clicked with ActionChains")
        except Exception as e:
            print(f"⚠️ ActionChains failed: {e}")
        
        # Method 2: JavaScript click
        if not login_clicked:
            try:
                submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                driver.execute_script("arguments[0].click();", submit_btn)
                login_clicked = True
                print("✅ Clicked with JavaScript")
            except Exception as e:
                print(f"⚠️ JavaScript click failed: {e}")
        
        # Method 3: Enter key
        if not login_clicked:
            try:
                password_field.send_keys(Keys.RETURN)
                login_clicked = True
                print("✅ Used Enter key")
            except Exception as e:
                print(f"⚠️ Enter key failed: {e}")
        
        if not login_clicked:
            print("❌ Could not click login button")
            return
        
        # Step 6: Wait and check for login success
        print("🔸 Step 6: Waiting for login...")
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"✅ Current URL: {current_url}")
        
        if "login" in current_url.lower():
            print("❌ Still on login page")
            
            # Check for any error messages or additional popups
            print("🔍 Checking for errors or additional popups...")
            
            # Look for error messages
            error_selectors = [".alert", ".error", ".errorlist", "[class*='error']"]
            for selector in error_selectors:
                try:
                    errors = driver.find_elements(By.CSS_SELECTOR, selector)
                    for error in errors:
                        if error.is_displayed() and error.text.strip():
                            print(f"   Error: {error.text.strip()}")
                except:
                    continue
            
            # Look for additional popups that might have appeared
            for selector in popup_selectors:
                try:
                    popups = driver.find_elements(By.CSS_SELECTOR, selector)
                    for popup in popups:
                        if popup.is_displayed():
                            print(f"🎯 ADDITIONAL POPUP: {popup.text[:100]}...")
                except:
                    continue
            
            print(f"\n🔸 Login failed - browser staying open for inspection")
            print(f"🔸 Press Enter to close...")
            input()
            return
        else:
            print("🎉 LOGIN SUCCESSFUL!")
        
        # Step 7: Continue with part search and download
        print("🔸 Step 7: Going to part page...")
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(8)
        
        # Step 8: Look for 3D Model tab
        print("🔸 Step 8: Looking for 3D Model tab...")
        
        three_d_selectors = [
            "//li[text()='3D Model']",
            "//a[text()='3D Model']",
            "//span[text()='3D Model']"
        ]
        
        three_d_tab = None
        for selector in three_d_selectors:
            try:
                tab = driver.find_element(By.XPATH, selector)
                if tab.is_displayed():
                    three_d_tab = tab
                    print("✅ Found 3D Model tab")
                    break
            except:
                continue
        
        if three_d_tab:
            three_d_tab.click()
            time.sleep(8)
            print("✅ 3D Model tab clicked")
            
            # Look for download button
            print("🔸 Step 9: Looking for download button...")
            
            download_selectors = [
                "//a[contains(@class, '3D-model-download')]",
                "//a[contains(text(), 'Download 3D Model')]"
            ]
            
            download_button = None
            for selector in download_selectors:
                try:
                    button = driver.find_element(By.XPATH, selector)
                    if button.is_displayed():
                        download_button = button
                        print("✅ Found download button")
                        break
                except:
                    continue
            
            if download_button:
                before_files = set(os.listdir(download_dir))
                
                print("🔸 Step 10: Clicking download button...")
                download_button.click()
                print("✅ Download button clicked!")
                
                # Monitor downloads
                print("📁 Monitoring downloads...")
                for i in range(30):
                    time.sleep(1)
                    current_files = set(os.listdir(download_dir))
                    new_files = current_files - before_files
                    
                    if new_files:
                        print(f"🎉 Download detected after {i+1} seconds!")
                        for f in new_files:
                            print(f"  📄 {f}")
                        break
                    
                    if i % 5 == 0 and i > 0:
                        print(f"   ⏳ {i+1}/30 seconds...")
                
                if not new_files:
                    print("❌ No download detected")
            else:
                print("❌ Download button not found")
        else:
            print("❌ 3D Model tab not found")
        
        print(f"\n🔸 Process complete - browser staying open")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    snapeda_handle_popup()
