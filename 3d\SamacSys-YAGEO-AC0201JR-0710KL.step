ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('/var/www/3D/0T/20497220.1.1.stp','2025-05-09T09:24:38',(
    'Author'),(''),'Open CASCADE STEP processor 6.9','FreeCAD','Unknown'
  );
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('ASSEMBLY','ASSEMBLY','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(-0.3,-0.15,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.15,-0.15,0.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(-0.15,-0.147,2.6E-03));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#34),#364);
#34 = MANIFOLD_SOLID_BREP('',#35);
#35 = CLOSED_SHELL('',(#36,#156,#256,#303,#350,#357));
#36 = ADVANCED_FACE('',(#37),#51,.F.);
#37 = FACE_BOUND('',#38,.F.);
#38 = EDGE_LOOP('',(#39,#74,#102,#130));
#39 = ORIENTED_EDGE('',*,*,#40,.F.);
#40 = EDGE_CURVE('',#41,#43,#45,.T.);
#41 = VERTEX_POINT('',#42);
#42 = CARTESIAN_POINT('',(0.,0.,0.));
#43 = VERTEX_POINT('',#44);
#44 = CARTESIAN_POINT('',(0.,0.,0.26));
#45 = SURFACE_CURVE('',#46,(#50,#62),.PCURVE_S1.);
#46 = LINE('',#47,#48);
#47 = CARTESIAN_POINT('',(0.,0.,0.));
#48 = VECTOR('',#49,1.);
#49 = DIRECTION('',(0.,0.,1.));
#50 = PCURVE('',#51,#56);
#51 = PLANE('',#52);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,0.,0.));
#54 = DIRECTION('',(1.,0.,-0.));
#55 = DIRECTION('',(0.,0.,1.));
#56 = DEFINITIONAL_REPRESENTATION('',(#57),#61);
#57 = LINE('',#58,#59);
#58 = CARTESIAN_POINT('',(0.,0.));
#59 = VECTOR('',#60,1.);
#60 = DIRECTION('',(1.,0.));
#61 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#62 = PCURVE('',#63,#68);
#63 = PLANE('',#64);
#64 = AXIS2_PLACEMENT_3D('',#65,#66,#67);
#65 = CARTESIAN_POINT('',(0.,0.,0.));
#66 = DIRECTION('',(-0.,1.,0.));
#67 = DIRECTION('',(0.,0.,1.));
#68 = DEFINITIONAL_REPRESENTATION('',(#69),#73);
#69 = LINE('',#70,#71);
#70 = CARTESIAN_POINT('',(0.,0.));
#71 = VECTOR('',#72,1.);
#72 = DIRECTION('',(1.,0.));
#73 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#74 = ORIENTED_EDGE('',*,*,#75,.T.);
#75 = EDGE_CURVE('',#41,#76,#78,.T.);
#76 = VERTEX_POINT('',#77);
#77 = CARTESIAN_POINT('',(0.,0.3,0.));
#78 = SURFACE_CURVE('',#79,(#83,#90),.PCURVE_S1.);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(0.,0.,0.));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(-0.,1.,0.));
#83 = PCURVE('',#51,#84);
#84 = DEFINITIONAL_REPRESENTATION('',(#85),#89);
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(0.,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,-1.));
#89 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#90 = PCURVE('',#91,#96);
#91 = PLANE('',#92);
#92 = AXIS2_PLACEMENT_3D('',#93,#94,#95);
#93 = CARTESIAN_POINT('',(0.,0.,0.));
#94 = DIRECTION('',(0.,0.,1.));
#95 = DIRECTION('',(1.,0.,-0.));
#96 = DEFINITIONAL_REPRESENTATION('',(#97),#101);
#97 = LINE('',#98,#99);
#98 = CARTESIAN_POINT('',(0.,0.));
#99 = VECTOR('',#100,1.);
#100 = DIRECTION('',(0.,1.));
#101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#102 = ORIENTED_EDGE('',*,*,#103,.T.);
#103 = EDGE_CURVE('',#76,#104,#106,.T.);
#104 = VERTEX_POINT('',#105);
#105 = CARTESIAN_POINT('',(0.,0.3,0.26));
#106 = SURFACE_CURVE('',#107,(#111,#118),.PCURVE_S1.);
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(0.,0.3,0.));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(0.,0.,1.));
#111 = PCURVE('',#51,#112);
#112 = DEFINITIONAL_REPRESENTATION('',(#113),#117);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(0.,-0.3));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(1.,0.));
#117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#118 = PCURVE('',#119,#124);
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.3,0.));
#122 = DIRECTION('',(-0.,1.,0.));
#123 = DIRECTION('',(0.,0.,1.));
#124 = DEFINITIONAL_REPRESENTATION('',(#125),#129);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.,0.));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(1.,0.));
#129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#130 = ORIENTED_EDGE('',*,*,#131,.F.);
#131 = EDGE_CURVE('',#43,#104,#132,.T.);
#132 = SURFACE_CURVE('',#133,(#137,#144),.PCURVE_S1.);
#133 = LINE('',#134,#135);
#134 = CARTESIAN_POINT('',(0.,0.,0.26));
#135 = VECTOR('',#136,1.);
#136 = DIRECTION('',(-0.,1.,0.));
#137 = PCURVE('',#51,#138);
#138 = DEFINITIONAL_REPRESENTATION('',(#139),#143);
#139 = LINE('',#140,#141);
#140 = CARTESIAN_POINT('',(0.26,0.));
#141 = VECTOR('',#142,1.);
#142 = DIRECTION('',(0.,-1.));
#143 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#144 = PCURVE('',#145,#150);
#145 = PLANE('',#146);
#146 = AXIS2_PLACEMENT_3D('',#147,#148,#149);
#147 = CARTESIAN_POINT('',(0.,0.,0.26));
#148 = DIRECTION('',(0.,0.,1.));
#149 = DIRECTION('',(1.,0.,-0.));
#150 = DEFINITIONAL_REPRESENTATION('',(#151),#155);
#151 = LINE('',#152,#153);
#152 = CARTESIAN_POINT('',(0.,0.));
#153 = VECTOR('',#154,1.);
#154 = DIRECTION('',(0.,1.));
#155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#156 = ADVANCED_FACE('',(#157),#171,.T.);
#157 = FACE_BOUND('',#158,.T.);
#158 = EDGE_LOOP('',(#159,#189,#212,#235));
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#161,#163,#165,.T.);
#161 = VERTEX_POINT('',#162);
#162 = CARTESIAN_POINT('',(0.15,0.,0.));
#163 = VERTEX_POINT('',#164);
#164 = CARTESIAN_POINT('',(0.15,0.,0.26));
#165 = SURFACE_CURVE('',#166,(#170,#182),.PCURVE_S1.);
#166 = LINE('',#167,#168);
#167 = CARTESIAN_POINT('',(0.15,0.,0.));
#168 = VECTOR('',#169,1.);
#169 = DIRECTION('',(0.,0.,1.));
#170 = PCURVE('',#171,#176);
#171 = PLANE('',#172);
#172 = AXIS2_PLACEMENT_3D('',#173,#174,#175);
#173 = CARTESIAN_POINT('',(0.15,0.,0.));
#174 = DIRECTION('',(1.,0.,-0.));
#175 = DIRECTION('',(0.,0.,1.));
#176 = DEFINITIONAL_REPRESENTATION('',(#177),#181);
#177 = LINE('',#178,#179);
#178 = CARTESIAN_POINT('',(0.,0.));
#179 = VECTOR('',#180,1.);
#180 = DIRECTION('',(1.,0.));
#181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#182 = PCURVE('',#63,#183);
#183 = DEFINITIONAL_REPRESENTATION('',(#184),#188);
#184 = LINE('',#185,#186);
#185 = CARTESIAN_POINT('',(0.,0.15));
#186 = VECTOR('',#187,1.);
#187 = DIRECTION('',(1.,0.));
#188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#189 = ORIENTED_EDGE('',*,*,#190,.T.);
#190 = EDGE_CURVE('',#161,#191,#193,.T.);
#191 = VERTEX_POINT('',#192);
#192 = CARTESIAN_POINT('',(0.15,0.3,0.));
#193 = SURFACE_CURVE('',#194,(#198,#205),.PCURVE_S1.);
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(0.15,0.,0.));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(-0.,1.,0.));
#198 = PCURVE('',#171,#199);
#199 = DEFINITIONAL_REPRESENTATION('',(#200),#204);
#200 = LINE('',#201,#202);
#201 = CARTESIAN_POINT('',(0.,0.));
#202 = VECTOR('',#203,1.);
#203 = DIRECTION('',(0.,-1.));
#204 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#205 = PCURVE('',#91,#206);
#206 = DEFINITIONAL_REPRESENTATION('',(#207),#211);
#207 = LINE('',#208,#209);
#208 = CARTESIAN_POINT('',(0.15,0.));
#209 = VECTOR('',#210,1.);
#210 = DIRECTION('',(0.,1.));
#211 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#212 = ORIENTED_EDGE('',*,*,#213,.T.);
#213 = EDGE_CURVE('',#191,#214,#216,.T.);
#214 = VERTEX_POINT('',#215);
#215 = CARTESIAN_POINT('',(0.15,0.3,0.26));
#216 = SURFACE_CURVE('',#217,(#221,#228),.PCURVE_S1.);
#217 = LINE('',#218,#219);
#218 = CARTESIAN_POINT('',(0.15,0.3,0.));
#219 = VECTOR('',#220,1.);
#220 = DIRECTION('',(0.,0.,1.));
#221 = PCURVE('',#171,#222);
#222 = DEFINITIONAL_REPRESENTATION('',(#223),#227);
#223 = LINE('',#224,#225);
#224 = CARTESIAN_POINT('',(0.,-0.3));
#225 = VECTOR('',#226,1.);
#226 = DIRECTION('',(1.,0.));
#227 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#228 = PCURVE('',#119,#229);
#229 = DEFINITIONAL_REPRESENTATION('',(#230),#234);
#230 = LINE('',#231,#232);
#231 = CARTESIAN_POINT('',(0.,0.15));
#232 = VECTOR('',#233,1.);
#233 = DIRECTION('',(1.,0.));
#234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#235 = ORIENTED_EDGE('',*,*,#236,.F.);
#236 = EDGE_CURVE('',#163,#214,#237,.T.);
#237 = SURFACE_CURVE('',#238,(#242,#249),.PCURVE_S1.);
#238 = LINE('',#239,#240);
#239 = CARTESIAN_POINT('',(0.15,0.,0.26));
#240 = VECTOR('',#241,1.);
#241 = DIRECTION('',(0.,1.,0.));
#242 = PCURVE('',#171,#243);
#243 = DEFINITIONAL_REPRESENTATION('',(#244),#248);
#244 = LINE('',#245,#246);
#245 = CARTESIAN_POINT('',(0.26,0.));
#246 = VECTOR('',#247,1.);
#247 = DIRECTION('',(0.,-1.));
#248 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#249 = PCURVE('',#145,#250);
#250 = DEFINITIONAL_REPRESENTATION('',(#251),#255);
#251 = LINE('',#252,#253);
#252 = CARTESIAN_POINT('',(0.15,0.));
#253 = VECTOR('',#254,1.);
#254 = DIRECTION('',(0.,1.));
#255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#256 = ADVANCED_FACE('',(#257),#63,.F.);
#257 = FACE_BOUND('',#258,.F.);
#258 = EDGE_LOOP('',(#259,#280,#281,#302));
#259 = ORIENTED_EDGE('',*,*,#260,.F.);
#260 = EDGE_CURVE('',#41,#161,#261,.T.);
#261 = SURFACE_CURVE('',#262,(#266,#273),.PCURVE_S1.);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(0.,0.,0.));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(1.,0.,-0.));
#266 = PCURVE('',#63,#267);
#267 = DEFINITIONAL_REPRESENTATION('',(#268),#272);
#268 = LINE('',#269,#270);
#269 = CARTESIAN_POINT('',(0.,0.));
#270 = VECTOR('',#271,1.);
#271 = DIRECTION('',(0.,1.));
#272 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#273 = PCURVE('',#91,#274);
#274 = DEFINITIONAL_REPRESENTATION('',(#275),#279);
#275 = LINE('',#276,#277);
#276 = CARTESIAN_POINT('',(0.,0.));
#277 = VECTOR('',#278,1.);
#278 = DIRECTION('',(1.,0.));
#279 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#280 = ORIENTED_EDGE('',*,*,#40,.T.);
#281 = ORIENTED_EDGE('',*,*,#282,.T.);
#282 = EDGE_CURVE('',#43,#163,#283,.T.);
#283 = SURFACE_CURVE('',#284,(#288,#295),.PCURVE_S1.);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(0.,0.,0.26));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(1.,0.,-0.));
#288 = PCURVE('',#63,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(0.26,0.));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.,1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = PCURVE('',#145,#296);
#296 = DEFINITIONAL_REPRESENTATION('',(#297),#301);
#297 = LINE('',#298,#299);
#298 = CARTESIAN_POINT('',(0.,0.));
#299 = VECTOR('',#300,1.);
#300 = DIRECTION('',(1.,0.));
#301 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#302 = ORIENTED_EDGE('',*,*,#160,.F.);
#303 = ADVANCED_FACE('',(#304),#119,.T.);
#304 = FACE_BOUND('',#305,.T.);
#305 = EDGE_LOOP('',(#306,#327,#328,#349));
#306 = ORIENTED_EDGE('',*,*,#307,.F.);
#307 = EDGE_CURVE('',#76,#191,#308,.T.);
#308 = SURFACE_CURVE('',#309,(#313,#320),.PCURVE_S1.);
#309 = LINE('',#310,#311);
#310 = CARTESIAN_POINT('',(0.,0.3,0.));
#311 = VECTOR('',#312,1.);
#312 = DIRECTION('',(1.,0.,-0.));
#313 = PCURVE('',#119,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#319);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(0.,0.));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(0.,1.));
#319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#320 = PCURVE('',#91,#321);
#321 = DEFINITIONAL_REPRESENTATION('',(#322),#326);
#322 = LINE('',#323,#324);
#323 = CARTESIAN_POINT('',(0.,0.3));
#324 = VECTOR('',#325,1.);
#325 = DIRECTION('',(1.,0.));
#326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#327 = ORIENTED_EDGE('',*,*,#103,.T.);
#328 = ORIENTED_EDGE('',*,*,#329,.T.);
#329 = EDGE_CURVE('',#104,#214,#330,.T.);
#330 = SURFACE_CURVE('',#331,(#335,#342),.PCURVE_S1.);
#331 = LINE('',#332,#333);
#332 = CARTESIAN_POINT('',(0.,0.3,0.26));
#333 = VECTOR('',#334,1.);
#334 = DIRECTION('',(1.,0.,-0.));
#335 = PCURVE('',#119,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(0.26,0.));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(0.,1.));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = PCURVE('',#145,#343);
#343 = DEFINITIONAL_REPRESENTATION('',(#344),#348);
#344 = LINE('',#345,#346);
#345 = CARTESIAN_POINT('',(0.,0.3));
#346 = VECTOR('',#347,1.);
#347 = DIRECTION('',(1.,0.));
#348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#349 = ORIENTED_EDGE('',*,*,#213,.F.);
#350 = ADVANCED_FACE('',(#351),#91,.F.);
#351 = FACE_BOUND('',#352,.F.);
#352 = EDGE_LOOP('',(#353,#354,#355,#356));
#353 = ORIENTED_EDGE('',*,*,#75,.F.);
#354 = ORIENTED_EDGE('',*,*,#260,.T.);
#355 = ORIENTED_EDGE('',*,*,#190,.T.);
#356 = ORIENTED_EDGE('',*,*,#307,.F.);
#357 = ADVANCED_FACE('',(#358),#145,.T.);
#358 = FACE_BOUND('',#359,.T.);
#359 = EDGE_LOOP('',(#360,#361,#362,#363));
#360 = ORIENTED_EDGE('',*,*,#131,.F.);
#361 = ORIENTED_EDGE('',*,*,#282,.T.);
#362 = ORIENTED_EDGE('',*,*,#236,.T.);
#363 = ORIENTED_EDGE('',*,*,#329,.F.);
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#368)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#365,#366,#367)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#365 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#366 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#367 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#368 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#365,
  'distance_accuracy_value','confusion accuracy');
#369 = SHAPE_DEFINITION_REPRESENTATION(#370,#33);
#370 = PRODUCT_DEFINITION_SHAPE('','',#371);
#371 = PRODUCT_DEFINITION('design','',#372,#375);
#372 = PRODUCT_DEFINITION_FORMATION('','',#373);
#373 = PRODUCT('term1','term1','',(#374));
#374 = PRODUCT_CONTEXT('',#2,'mechanical');
#375 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#376 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#377,#379);
#377 = ( REPRESENTATION_RELATIONSHIP('','',#33,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#378) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#378 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#379 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#380
  );
#380 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','=>[0:1:1:2]','',#5,#371,$);
#381 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#373));
#382 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#383),#713);
#383 = MANIFOLD_SOLID_BREP('',#384);
#384 = CLOSED_SHELL('',(#385,#505,#605,#652,#699,#706));
#385 = ADVANCED_FACE('',(#386),#400,.F.);
#386 = FACE_BOUND('',#387,.F.);
#387 = EDGE_LOOP('',(#388,#423,#451,#479));
#388 = ORIENTED_EDGE('',*,*,#389,.F.);
#389 = EDGE_CURVE('',#390,#392,#394,.T.);
#390 = VERTEX_POINT('',#391);
#391 = CARTESIAN_POINT('',(0.,0.,0.));
#392 = VERTEX_POINT('',#393);
#393 = CARTESIAN_POINT('',(0.,0.,0.26));
#394 = SURFACE_CURVE('',#395,(#399,#411),.PCURVE_S1.);
#395 = LINE('',#396,#397);
#396 = CARTESIAN_POINT('',(0.,0.,0.));
#397 = VECTOR('',#398,1.);
#398 = DIRECTION('',(0.,0.,1.));
#399 = PCURVE('',#400,#405);
#400 = PLANE('',#401);
#401 = AXIS2_PLACEMENT_3D('',#402,#403,#404);
#402 = CARTESIAN_POINT('',(0.,0.,0.));
#403 = DIRECTION('',(1.,0.,-0.));
#404 = DIRECTION('',(0.,0.,1.));
#405 = DEFINITIONAL_REPRESENTATION('',(#406),#410);
#406 = LINE('',#407,#408);
#407 = CARTESIAN_POINT('',(0.,0.));
#408 = VECTOR('',#409,1.);
#409 = DIRECTION('',(1.,0.));
#410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#411 = PCURVE('',#412,#417);
#412 = PLANE('',#413);
#413 = AXIS2_PLACEMENT_3D('',#414,#415,#416);
#414 = CARTESIAN_POINT('',(0.,0.,0.));
#415 = DIRECTION('',(-0.,1.,0.));
#416 = DIRECTION('',(0.,0.,1.));
#417 = DEFINITIONAL_REPRESENTATION('',(#418),#422);
#418 = LINE('',#419,#420);
#419 = CARTESIAN_POINT('',(0.,0.));
#420 = VECTOR('',#421,1.);
#421 = DIRECTION('',(1.,0.));
#422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#390,#425,#427,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(0.,0.3,0.));
#427 = SURFACE_CURVE('',#428,(#432,#439),.PCURVE_S1.);
#428 = LINE('',#429,#430);
#429 = CARTESIAN_POINT('',(0.,0.,0.));
#430 = VECTOR('',#431,1.);
#431 = DIRECTION('',(-0.,1.,0.));
#432 = PCURVE('',#400,#433);
#433 = DEFINITIONAL_REPRESENTATION('',(#434),#438);
#434 = LINE('',#435,#436);
#435 = CARTESIAN_POINT('',(0.,0.));
#436 = VECTOR('',#437,1.);
#437 = DIRECTION('',(0.,-1.));
#438 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#439 = PCURVE('',#440,#445);
#440 = PLANE('',#441);
#441 = AXIS2_PLACEMENT_3D('',#442,#443,#444);
#442 = CARTESIAN_POINT('',(0.,0.,0.));
#443 = DIRECTION('',(0.,0.,1.));
#444 = DIRECTION('',(1.,0.,-0.));
#445 = DEFINITIONAL_REPRESENTATION('',(#446),#450);
#446 = LINE('',#447,#448);
#447 = CARTESIAN_POINT('',(0.,0.));
#448 = VECTOR('',#449,1.);
#449 = DIRECTION('',(0.,1.));
#450 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#451 = ORIENTED_EDGE('',*,*,#452,.T.);
#452 = EDGE_CURVE('',#425,#453,#455,.T.);
#453 = VERTEX_POINT('',#454);
#454 = CARTESIAN_POINT('',(0.,0.3,0.26));
#455 = SURFACE_CURVE('',#456,(#460,#467),.PCURVE_S1.);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(0.,0.3,0.));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(0.,0.,1.));
#460 = PCURVE('',#400,#461);
#461 = DEFINITIONAL_REPRESENTATION('',(#462),#466);
#462 = LINE('',#463,#464);
#463 = CARTESIAN_POINT('',(0.,-0.3));
#464 = VECTOR('',#465,1.);
#465 = DIRECTION('',(1.,0.));
#466 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#467 = PCURVE('',#468,#473);
#468 = PLANE('',#469);
#469 = AXIS2_PLACEMENT_3D('',#470,#471,#472);
#470 = CARTESIAN_POINT('',(0.,0.3,0.));
#471 = DIRECTION('',(-0.,1.,0.));
#472 = DIRECTION('',(0.,0.,1.));
#473 = DEFINITIONAL_REPRESENTATION('',(#474),#478);
#474 = LINE('',#475,#476);
#475 = CARTESIAN_POINT('',(0.,0.));
#476 = VECTOR('',#477,1.);
#477 = DIRECTION('',(1.,0.));
#478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#479 = ORIENTED_EDGE('',*,*,#480,.F.);
#480 = EDGE_CURVE('',#392,#453,#481,.T.);
#481 = SURFACE_CURVE('',#482,(#486,#493),.PCURVE_S1.);
#482 = LINE('',#483,#484);
#483 = CARTESIAN_POINT('',(0.,0.,0.26));
#484 = VECTOR('',#485,1.);
#485 = DIRECTION('',(-0.,1.,0.));
#486 = PCURVE('',#400,#487);
#487 = DEFINITIONAL_REPRESENTATION('',(#488),#492);
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(0.26,0.));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(0.,-1.));
#492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#493 = PCURVE('',#494,#499);
#494 = PLANE('',#495);
#495 = AXIS2_PLACEMENT_3D('',#496,#497,#498);
#496 = CARTESIAN_POINT('',(0.,0.,0.26));
#497 = DIRECTION('',(0.,0.,1.));
#498 = DIRECTION('',(1.,0.,-0.));
#499 = DEFINITIONAL_REPRESENTATION('',(#500),#504);
#500 = LINE('',#501,#502);
#501 = CARTESIAN_POINT('',(0.,0.));
#502 = VECTOR('',#503,1.);
#503 = DIRECTION('',(0.,1.));
#504 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#505 = ADVANCED_FACE('',(#506),#520,.T.);
#506 = FACE_BOUND('',#507,.T.);
#507 = EDGE_LOOP('',(#508,#538,#561,#584));
#508 = ORIENTED_EDGE('',*,*,#509,.F.);
#509 = EDGE_CURVE('',#510,#512,#514,.T.);
#510 = VERTEX_POINT('',#511);
#511 = CARTESIAN_POINT('',(0.15,0.,0.));
#512 = VERTEX_POINT('',#513);
#513 = CARTESIAN_POINT('',(0.15,0.,0.26));
#514 = SURFACE_CURVE('',#515,(#519,#531),.PCURVE_S1.);
#515 = LINE('',#516,#517);
#516 = CARTESIAN_POINT('',(0.15,0.,0.));
#517 = VECTOR('',#518,1.);
#518 = DIRECTION('',(0.,0.,1.));
#519 = PCURVE('',#520,#525);
#520 = PLANE('',#521);
#521 = AXIS2_PLACEMENT_3D('',#522,#523,#524);
#522 = CARTESIAN_POINT('',(0.15,0.,0.));
#523 = DIRECTION('',(1.,0.,-0.));
#524 = DIRECTION('',(0.,0.,1.));
#525 = DEFINITIONAL_REPRESENTATION('',(#526),#530);
#526 = LINE('',#527,#528);
#527 = CARTESIAN_POINT('',(0.,0.));
#528 = VECTOR('',#529,1.);
#529 = DIRECTION('',(1.,0.));
#530 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#531 = PCURVE('',#412,#532);
#532 = DEFINITIONAL_REPRESENTATION('',(#533),#537);
#533 = LINE('',#534,#535);
#534 = CARTESIAN_POINT('',(0.,0.15));
#535 = VECTOR('',#536,1.);
#536 = DIRECTION('',(1.,0.));
#537 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#538 = ORIENTED_EDGE('',*,*,#539,.T.);
#539 = EDGE_CURVE('',#510,#540,#542,.T.);
#540 = VERTEX_POINT('',#541);
#541 = CARTESIAN_POINT('',(0.15,0.3,0.));
#542 = SURFACE_CURVE('',#543,(#547,#554),.PCURVE_S1.);
#543 = LINE('',#544,#545);
#544 = CARTESIAN_POINT('',(0.15,0.,0.));
#545 = VECTOR('',#546,1.);
#546 = DIRECTION('',(-0.,1.,0.));
#547 = PCURVE('',#520,#548);
#548 = DEFINITIONAL_REPRESENTATION('',(#549),#553);
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(0.,0.));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,-1.));
#553 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#554 = PCURVE('',#440,#555);
#555 = DEFINITIONAL_REPRESENTATION('',(#556),#560);
#556 = LINE('',#557,#558);
#557 = CARTESIAN_POINT('',(0.15,0.));
#558 = VECTOR('',#559,1.);
#559 = DIRECTION('',(0.,1.));
#560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#561 = ORIENTED_EDGE('',*,*,#562,.T.);
#562 = EDGE_CURVE('',#540,#563,#565,.T.);
#563 = VERTEX_POINT('',#564);
#564 = CARTESIAN_POINT('',(0.15,0.3,0.26));
#565 = SURFACE_CURVE('',#566,(#570,#577),.PCURVE_S1.);
#566 = LINE('',#567,#568);
#567 = CARTESIAN_POINT('',(0.15,0.3,0.));
#568 = VECTOR('',#569,1.);
#569 = DIRECTION('',(0.,0.,1.));
#570 = PCURVE('',#520,#571);
#571 = DEFINITIONAL_REPRESENTATION('',(#572),#576);
#572 = LINE('',#573,#574);
#573 = CARTESIAN_POINT('',(0.,-0.3));
#574 = VECTOR('',#575,1.);
#575 = DIRECTION('',(1.,0.));
#576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#577 = PCURVE('',#468,#578);
#578 = DEFINITIONAL_REPRESENTATION('',(#579),#583);
#579 = LINE('',#580,#581);
#580 = CARTESIAN_POINT('',(0.,0.15));
#581 = VECTOR('',#582,1.);
#582 = DIRECTION('',(1.,0.));
#583 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#584 = ORIENTED_EDGE('',*,*,#585,.F.);
#585 = EDGE_CURVE('',#512,#563,#586,.T.);
#586 = SURFACE_CURVE('',#587,(#591,#598),.PCURVE_S1.);
#587 = LINE('',#588,#589);
#588 = CARTESIAN_POINT('',(0.15,0.,0.26));
#589 = VECTOR('',#590,1.);
#590 = DIRECTION('',(0.,1.,0.));
#591 = PCURVE('',#520,#592);
#592 = DEFINITIONAL_REPRESENTATION('',(#593),#597);
#593 = LINE('',#594,#595);
#594 = CARTESIAN_POINT('',(0.26,0.));
#595 = VECTOR('',#596,1.);
#596 = DIRECTION('',(0.,-1.));
#597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#598 = PCURVE('',#494,#599);
#599 = DEFINITIONAL_REPRESENTATION('',(#600),#604);
#600 = LINE('',#601,#602);
#601 = CARTESIAN_POINT('',(0.15,0.));
#602 = VECTOR('',#603,1.);
#603 = DIRECTION('',(0.,1.));
#604 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#605 = ADVANCED_FACE('',(#606),#412,.F.);
#606 = FACE_BOUND('',#607,.F.);
#607 = EDGE_LOOP('',(#608,#629,#630,#651));
#608 = ORIENTED_EDGE('',*,*,#609,.F.);
#609 = EDGE_CURVE('',#390,#510,#610,.T.);
#610 = SURFACE_CURVE('',#611,(#615,#622),.PCURVE_S1.);
#611 = LINE('',#612,#613);
#612 = CARTESIAN_POINT('',(0.,0.,0.));
#613 = VECTOR('',#614,1.);
#614 = DIRECTION('',(1.,0.,-0.));
#615 = PCURVE('',#412,#616);
#616 = DEFINITIONAL_REPRESENTATION('',(#617),#621);
#617 = LINE('',#618,#619);
#618 = CARTESIAN_POINT('',(0.,0.));
#619 = VECTOR('',#620,1.);
#620 = DIRECTION('',(0.,1.));
#621 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#622 = PCURVE('',#440,#623);
#623 = DEFINITIONAL_REPRESENTATION('',(#624),#628);
#624 = LINE('',#625,#626);
#625 = CARTESIAN_POINT('',(0.,0.));
#626 = VECTOR('',#627,1.);
#627 = DIRECTION('',(1.,0.));
#628 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#629 = ORIENTED_EDGE('',*,*,#389,.T.);
#630 = ORIENTED_EDGE('',*,*,#631,.T.);
#631 = EDGE_CURVE('',#392,#512,#632,.T.);
#632 = SURFACE_CURVE('',#633,(#637,#644),.PCURVE_S1.);
#633 = LINE('',#634,#635);
#634 = CARTESIAN_POINT('',(0.,0.,0.26));
#635 = VECTOR('',#636,1.);
#636 = DIRECTION('',(1.,0.,-0.));
#637 = PCURVE('',#412,#638);
#638 = DEFINITIONAL_REPRESENTATION('',(#639),#643);
#639 = LINE('',#640,#641);
#640 = CARTESIAN_POINT('',(0.26,0.));
#641 = VECTOR('',#642,1.);
#642 = DIRECTION('',(0.,1.));
#643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#644 = PCURVE('',#494,#645);
#645 = DEFINITIONAL_REPRESENTATION('',(#646),#650);
#646 = LINE('',#647,#648);
#647 = CARTESIAN_POINT('',(0.,0.));
#648 = VECTOR('',#649,1.);
#649 = DIRECTION('',(1.,0.));
#650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#651 = ORIENTED_EDGE('',*,*,#509,.F.);
#652 = ADVANCED_FACE('',(#653),#468,.T.);
#653 = FACE_BOUND('',#654,.T.);
#654 = EDGE_LOOP('',(#655,#676,#677,#698));
#655 = ORIENTED_EDGE('',*,*,#656,.F.);
#656 = EDGE_CURVE('',#425,#540,#657,.T.);
#657 = SURFACE_CURVE('',#658,(#662,#669),.PCURVE_S1.);
#658 = LINE('',#659,#660);
#659 = CARTESIAN_POINT('',(0.,0.3,0.));
#660 = VECTOR('',#661,1.);
#661 = DIRECTION('',(1.,0.,-0.));
#662 = PCURVE('',#468,#663);
#663 = DEFINITIONAL_REPRESENTATION('',(#664),#668);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(0.,0.));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(0.,1.));
#668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#669 = PCURVE('',#440,#670);
#670 = DEFINITIONAL_REPRESENTATION('',(#671),#675);
#671 = LINE('',#672,#673);
#672 = CARTESIAN_POINT('',(0.,0.3));
#673 = VECTOR('',#674,1.);
#674 = DIRECTION('',(1.,0.));
#675 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#676 = ORIENTED_EDGE('',*,*,#452,.T.);
#677 = ORIENTED_EDGE('',*,*,#678,.T.);
#678 = EDGE_CURVE('',#453,#563,#679,.T.);
#679 = SURFACE_CURVE('',#680,(#684,#691),.PCURVE_S1.);
#680 = LINE('',#681,#682);
#681 = CARTESIAN_POINT('',(0.,0.3,0.26));
#682 = VECTOR('',#683,1.);
#683 = DIRECTION('',(1.,0.,-0.));
#684 = PCURVE('',#468,#685);
#685 = DEFINITIONAL_REPRESENTATION('',(#686),#690);
#686 = LINE('',#687,#688);
#687 = CARTESIAN_POINT('',(0.26,0.));
#688 = VECTOR('',#689,1.);
#689 = DIRECTION('',(0.,1.));
#690 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#691 = PCURVE('',#494,#692);
#692 = DEFINITIONAL_REPRESENTATION('',(#693),#697);
#693 = LINE('',#694,#695);
#694 = CARTESIAN_POINT('',(0.,0.3));
#695 = VECTOR('',#696,1.);
#696 = DIRECTION('',(1.,0.));
#697 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#698 = ORIENTED_EDGE('',*,*,#562,.F.);
#699 = ADVANCED_FACE('',(#700),#440,.F.);
#700 = FACE_BOUND('',#701,.F.);
#701 = EDGE_LOOP('',(#702,#703,#704,#705));
#702 = ORIENTED_EDGE('',*,*,#424,.F.);
#703 = ORIENTED_EDGE('',*,*,#609,.T.);
#704 = ORIENTED_EDGE('',*,*,#539,.T.);
#705 = ORIENTED_EDGE('',*,*,#656,.F.);
#706 = ADVANCED_FACE('',(#707),#494,.T.);
#707 = FACE_BOUND('',#708,.T.);
#708 = EDGE_LOOP('',(#709,#710,#711,#712));
#709 = ORIENTED_EDGE('',*,*,#480,.F.);
#710 = ORIENTED_EDGE('',*,*,#631,.T.);
#711 = ORIENTED_EDGE('',*,*,#585,.T.);
#712 = ORIENTED_EDGE('',*,*,#678,.F.);
#713 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#717)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#714,#715,#716)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#714 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#715 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#716 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#717 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#714,
  'distance_accuracy_value','confusion accuracy');
#718 = SHAPE_DEFINITION_REPRESENTATION(#719,#382);
#719 = PRODUCT_DEFINITION_SHAPE('','',#720);
#720 = PRODUCT_DEFINITION('design','',#721,#724);
#721 = PRODUCT_DEFINITION_FORMATION('','',#722);
#722 = PRODUCT('term2','term2','',(#723));
#723 = PRODUCT_CONTEXT('',#2,'mechanical');
#724 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#725 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#726,#728);
#726 = ( REPRESENTATION_RELATIONSHIP('','',#382,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#727) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#727 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#728 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#729
  );
#729 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','=>[0:1:1:3]','',#5,#720,$);
#730 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#722));
#731 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#732),#1062);
#732 = MANIFOLD_SOLID_BREP('',#733);
#733 = CLOSED_SHELL('',(#734,#854,#954,#1001,#1048,#1055));
#734 = ADVANCED_FACE('',(#735),#749,.F.);
#735 = FACE_BOUND('',#736,.F.);
#736 = EDGE_LOOP('',(#737,#772,#800,#828));
#737 = ORIENTED_EDGE('',*,*,#738,.F.);
#738 = EDGE_CURVE('',#739,#741,#743,.T.);
#739 = VERTEX_POINT('',#740);
#740 = CARTESIAN_POINT('',(0.,0.,0.));
#741 = VERTEX_POINT('',#742);
#742 = CARTESIAN_POINT('',(0.,0.,0.2548));
#743 = SURFACE_CURVE('',#744,(#748,#760),.PCURVE_S1.);
#744 = LINE('',#745,#746);
#745 = CARTESIAN_POINT('',(0.,0.,0.));
#746 = VECTOR('',#747,1.);
#747 = DIRECTION('',(0.,0.,1.));
#748 = PCURVE('',#749,#754);
#749 = PLANE('',#750);
#750 = AXIS2_PLACEMENT_3D('',#751,#752,#753);
#751 = CARTESIAN_POINT('',(0.,0.,0.));
#752 = DIRECTION('',(1.,0.,-0.));
#753 = DIRECTION('',(0.,0.,1.));
#754 = DEFINITIONAL_REPRESENTATION('',(#755),#759);
#755 = LINE('',#756,#757);
#756 = CARTESIAN_POINT('',(0.,0.));
#757 = VECTOR('',#758,1.);
#758 = DIRECTION('',(1.,0.));
#759 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#760 = PCURVE('',#761,#766);
#761 = PLANE('',#762);
#762 = AXIS2_PLACEMENT_3D('',#763,#764,#765);
#763 = CARTESIAN_POINT('',(0.,0.,0.));
#764 = DIRECTION('',(-0.,1.,0.));
#765 = DIRECTION('',(0.,0.,1.));
#766 = DEFINITIONAL_REPRESENTATION('',(#767),#771);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(0.,0.));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(1.,0.));
#771 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#772 = ORIENTED_EDGE('',*,*,#773,.T.);
#773 = EDGE_CURVE('',#739,#774,#776,.T.);
#774 = VERTEX_POINT('',#775);
#775 = CARTESIAN_POINT('',(0.,0.294,0.));
#776 = SURFACE_CURVE('',#777,(#781,#788),.PCURVE_S1.);
#777 = LINE('',#778,#779);
#778 = CARTESIAN_POINT('',(0.,0.,0.));
#779 = VECTOR('',#780,1.);
#780 = DIRECTION('',(-0.,1.,0.));
#781 = PCURVE('',#749,#782);
#782 = DEFINITIONAL_REPRESENTATION('',(#783),#787);
#783 = LINE('',#784,#785);
#784 = CARTESIAN_POINT('',(0.,0.));
#785 = VECTOR('',#786,1.);
#786 = DIRECTION('',(0.,-1.));
#787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#788 = PCURVE('',#789,#794);
#789 = PLANE('',#790);
#790 = AXIS2_PLACEMENT_3D('',#791,#792,#793);
#791 = CARTESIAN_POINT('',(0.,0.,0.));
#792 = DIRECTION('',(0.,0.,1.));
#793 = DIRECTION('',(1.,0.,-0.));
#794 = DEFINITIONAL_REPRESENTATION('',(#795),#799);
#795 = LINE('',#796,#797);
#796 = CARTESIAN_POINT('',(0.,0.));
#797 = VECTOR('',#798,1.);
#798 = DIRECTION('',(0.,1.));
#799 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#800 = ORIENTED_EDGE('',*,*,#801,.T.);
#801 = EDGE_CURVE('',#774,#802,#804,.T.);
#802 = VERTEX_POINT('',#803);
#803 = CARTESIAN_POINT('',(0.,0.294,0.2548));
#804 = SURFACE_CURVE('',#805,(#809,#816),.PCURVE_S1.);
#805 = LINE('',#806,#807);
#806 = CARTESIAN_POINT('',(0.,0.294,0.));
#807 = VECTOR('',#808,1.);
#808 = DIRECTION('',(0.,0.,1.));
#809 = PCURVE('',#749,#810);
#810 = DEFINITIONAL_REPRESENTATION('',(#811),#815);
#811 = LINE('',#812,#813);
#812 = CARTESIAN_POINT('',(0.,-0.294));
#813 = VECTOR('',#814,1.);
#814 = DIRECTION('',(1.,0.));
#815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#816 = PCURVE('',#817,#822);
#817 = PLANE('',#818);
#818 = AXIS2_PLACEMENT_3D('',#819,#820,#821);
#819 = CARTESIAN_POINT('',(0.,0.294,0.));
#820 = DIRECTION('',(-0.,1.,0.));
#821 = DIRECTION('',(0.,0.,1.));
#822 = DEFINITIONAL_REPRESENTATION('',(#823),#827);
#823 = LINE('',#824,#825);
#824 = CARTESIAN_POINT('',(0.,0.));
#825 = VECTOR('',#826,1.);
#826 = DIRECTION('',(1.,0.));
#827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#828 = ORIENTED_EDGE('',*,*,#829,.F.);
#829 = EDGE_CURVE('',#741,#802,#830,.T.);
#830 = SURFACE_CURVE('',#831,(#835,#842),.PCURVE_S1.);
#831 = LINE('',#832,#833);
#832 = CARTESIAN_POINT('',(0.,0.,0.2548));
#833 = VECTOR('',#834,1.);
#834 = DIRECTION('',(-0.,1.,0.));
#835 = PCURVE('',#749,#836);
#836 = DEFINITIONAL_REPRESENTATION('',(#837),#841);
#837 = LINE('',#838,#839);
#838 = CARTESIAN_POINT('',(0.2548,0.));
#839 = VECTOR('',#840,1.);
#840 = DIRECTION('',(0.,-1.));
#841 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#842 = PCURVE('',#843,#848);
#843 = PLANE('',#844);
#844 = AXIS2_PLACEMENT_3D('',#845,#846,#847);
#845 = CARTESIAN_POINT('',(0.,0.,0.2548));
#846 = DIRECTION('',(0.,0.,1.));
#847 = DIRECTION('',(1.,0.,-0.));
#848 = DEFINITIONAL_REPRESENTATION('',(#849),#853);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(0.,0.));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(0.,1.));
#853 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#854 = ADVANCED_FACE('',(#855),#869,.T.);
#855 = FACE_BOUND('',#856,.T.);
#856 = EDGE_LOOP('',(#857,#887,#910,#933));
#857 = ORIENTED_EDGE('',*,*,#858,.F.);
#858 = EDGE_CURVE('',#859,#861,#863,.T.);
#859 = VERTEX_POINT('',#860);
#860 = CARTESIAN_POINT('',(0.3,0.,0.));
#861 = VERTEX_POINT('',#862);
#862 = CARTESIAN_POINT('',(0.3,0.,0.2548));
#863 = SURFACE_CURVE('',#864,(#868,#880),.PCURVE_S1.);
#864 = LINE('',#865,#866);
#865 = CARTESIAN_POINT('',(0.3,0.,0.));
#866 = VECTOR('',#867,1.);
#867 = DIRECTION('',(0.,0.,1.));
#868 = PCURVE('',#869,#874);
#869 = PLANE('',#870);
#870 = AXIS2_PLACEMENT_3D('',#871,#872,#873);
#871 = CARTESIAN_POINT('',(0.3,0.,0.));
#872 = DIRECTION('',(1.,0.,-0.));
#873 = DIRECTION('',(0.,0.,1.));
#874 = DEFINITIONAL_REPRESENTATION('',(#875),#879);
#875 = LINE('',#876,#877);
#876 = CARTESIAN_POINT('',(0.,0.));
#877 = VECTOR('',#878,1.);
#878 = DIRECTION('',(1.,0.));
#879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#880 = PCURVE('',#761,#881);
#881 = DEFINITIONAL_REPRESENTATION('',(#882),#886);
#882 = LINE('',#883,#884);
#883 = CARTESIAN_POINT('',(0.,0.3));
#884 = VECTOR('',#885,1.);
#885 = DIRECTION('',(1.,0.));
#886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#887 = ORIENTED_EDGE('',*,*,#888,.T.);
#888 = EDGE_CURVE('',#859,#889,#891,.T.);
#889 = VERTEX_POINT('',#890);
#890 = CARTESIAN_POINT('',(0.3,0.294,0.));
#891 = SURFACE_CURVE('',#892,(#896,#903),.PCURVE_S1.);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(0.3,0.,0.));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(-0.,1.,0.));
#896 = PCURVE('',#869,#897);
#897 = DEFINITIONAL_REPRESENTATION('',(#898),#902);
#898 = LINE('',#899,#900);
#899 = CARTESIAN_POINT('',(0.,0.));
#900 = VECTOR('',#901,1.);
#901 = DIRECTION('',(0.,-1.));
#902 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#903 = PCURVE('',#789,#904);
#904 = DEFINITIONAL_REPRESENTATION('',(#905),#909);
#905 = LINE('',#906,#907);
#906 = CARTESIAN_POINT('',(0.3,0.));
#907 = VECTOR('',#908,1.);
#908 = DIRECTION('',(0.,1.));
#909 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#910 = ORIENTED_EDGE('',*,*,#911,.T.);
#911 = EDGE_CURVE('',#889,#912,#914,.T.);
#912 = VERTEX_POINT('',#913);
#913 = CARTESIAN_POINT('',(0.3,0.294,0.2548));
#914 = SURFACE_CURVE('',#915,(#919,#926),.PCURVE_S1.);
#915 = LINE('',#916,#917);
#916 = CARTESIAN_POINT('',(0.3,0.294,0.));
#917 = VECTOR('',#918,1.);
#918 = DIRECTION('',(0.,0.,1.));
#919 = PCURVE('',#869,#920);
#920 = DEFINITIONAL_REPRESENTATION('',(#921),#925);
#921 = LINE('',#922,#923);
#922 = CARTESIAN_POINT('',(0.,-0.294));
#923 = VECTOR('',#924,1.);
#924 = DIRECTION('',(1.,0.));
#925 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#926 = PCURVE('',#817,#927);
#927 = DEFINITIONAL_REPRESENTATION('',(#928),#932);
#928 = LINE('',#929,#930);
#929 = CARTESIAN_POINT('',(0.,0.3));
#930 = VECTOR('',#931,1.);
#931 = DIRECTION('',(1.,0.));
#932 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#933 = ORIENTED_EDGE('',*,*,#934,.F.);
#934 = EDGE_CURVE('',#861,#912,#935,.T.);
#935 = SURFACE_CURVE('',#936,(#940,#947),.PCURVE_S1.);
#936 = LINE('',#937,#938);
#937 = CARTESIAN_POINT('',(0.3,0.,0.2548));
#938 = VECTOR('',#939,1.);
#939 = DIRECTION('',(0.,1.,0.));
#940 = PCURVE('',#869,#941);
#941 = DEFINITIONAL_REPRESENTATION('',(#942),#946);
#942 = LINE('',#943,#944);
#943 = CARTESIAN_POINT('',(0.2548,0.));
#944 = VECTOR('',#945,1.);
#945 = DIRECTION('',(0.,-1.));
#946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#947 = PCURVE('',#843,#948);
#948 = DEFINITIONAL_REPRESENTATION('',(#949),#953);
#949 = LINE('',#950,#951);
#950 = CARTESIAN_POINT('',(0.3,0.));
#951 = VECTOR('',#952,1.);
#952 = DIRECTION('',(0.,1.));
#953 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#954 = ADVANCED_FACE('',(#955),#761,.F.);
#955 = FACE_BOUND('',#956,.F.);
#956 = EDGE_LOOP('',(#957,#978,#979,#1000));
#957 = ORIENTED_EDGE('',*,*,#958,.F.);
#958 = EDGE_CURVE('',#739,#859,#959,.T.);
#959 = SURFACE_CURVE('',#960,(#964,#971),.PCURVE_S1.);
#960 = LINE('',#961,#962);
#961 = CARTESIAN_POINT('',(0.,0.,0.));
#962 = VECTOR('',#963,1.);
#963 = DIRECTION('',(1.,0.,-0.));
#964 = PCURVE('',#761,#965);
#965 = DEFINITIONAL_REPRESENTATION('',(#966),#970);
#966 = LINE('',#967,#968);
#967 = CARTESIAN_POINT('',(0.,0.));
#968 = VECTOR('',#969,1.);
#969 = DIRECTION('',(0.,1.));
#970 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#971 = PCURVE('',#789,#972);
#972 = DEFINITIONAL_REPRESENTATION('',(#973),#977);
#973 = LINE('',#974,#975);
#974 = CARTESIAN_POINT('',(0.,0.));
#975 = VECTOR('',#976,1.);
#976 = DIRECTION('',(1.,0.));
#977 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#978 = ORIENTED_EDGE('',*,*,#738,.T.);
#979 = ORIENTED_EDGE('',*,*,#980,.T.);
#980 = EDGE_CURVE('',#741,#861,#981,.T.);
#981 = SURFACE_CURVE('',#982,(#986,#993),.PCURVE_S1.);
#982 = LINE('',#983,#984);
#983 = CARTESIAN_POINT('',(0.,0.,0.2548));
#984 = VECTOR('',#985,1.);
#985 = DIRECTION('',(1.,0.,-0.));
#986 = PCURVE('',#761,#987);
#987 = DEFINITIONAL_REPRESENTATION('',(#988),#992);
#988 = LINE('',#989,#990);
#989 = CARTESIAN_POINT('',(0.2548,0.));
#990 = VECTOR('',#991,1.);
#991 = DIRECTION('',(0.,1.));
#992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#993 = PCURVE('',#843,#994);
#994 = DEFINITIONAL_REPRESENTATION('',(#995),#999);
#995 = LINE('',#996,#997);
#996 = CARTESIAN_POINT('',(0.,0.));
#997 = VECTOR('',#998,1.);
#998 = DIRECTION('',(1.,0.));
#999 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1000 = ORIENTED_EDGE('',*,*,#858,.F.);
#1001 = ADVANCED_FACE('',(#1002),#817,.T.);
#1002 = FACE_BOUND('',#1003,.T.);
#1003 = EDGE_LOOP('',(#1004,#1025,#1026,#1047));
#1004 = ORIENTED_EDGE('',*,*,#1005,.F.);
#1005 = EDGE_CURVE('',#774,#889,#1006,.T.);
#1006 = SURFACE_CURVE('',#1007,(#1011,#1018),.PCURVE_S1.);
#1007 = LINE('',#1008,#1009);
#1008 = CARTESIAN_POINT('',(0.,0.294,0.));
#1009 = VECTOR('',#1010,1.);
#1010 = DIRECTION('',(1.,0.,-0.));
#1011 = PCURVE('',#817,#1012);
#1012 = DEFINITIONAL_REPRESENTATION('',(#1013),#1017);
#1013 = LINE('',#1014,#1015);
#1014 = CARTESIAN_POINT('',(0.,0.));
#1015 = VECTOR('',#1016,1.);
#1016 = DIRECTION('',(0.,1.));
#1017 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1018 = PCURVE('',#789,#1019);
#1019 = DEFINITIONAL_REPRESENTATION('',(#1020),#1024);
#1020 = LINE('',#1021,#1022);
#1021 = CARTESIAN_POINT('',(0.,0.294));
#1022 = VECTOR('',#1023,1.);
#1023 = DIRECTION('',(1.,0.));
#1024 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1025 = ORIENTED_EDGE('',*,*,#801,.T.);
#1026 = ORIENTED_EDGE('',*,*,#1027,.T.);
#1027 = EDGE_CURVE('',#802,#912,#1028,.T.);
#1028 = SURFACE_CURVE('',#1029,(#1033,#1040),.PCURVE_S1.);
#1029 = LINE('',#1030,#1031);
#1030 = CARTESIAN_POINT('',(0.,0.294,0.2548));
#1031 = VECTOR('',#1032,1.);
#1032 = DIRECTION('',(1.,0.,-0.));
#1033 = PCURVE('',#817,#1034);
#1034 = DEFINITIONAL_REPRESENTATION('',(#1035),#1039);
#1035 = LINE('',#1036,#1037);
#1036 = CARTESIAN_POINT('',(0.2548,0.));
#1037 = VECTOR('',#1038,1.);
#1038 = DIRECTION('',(0.,1.));
#1039 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1040 = PCURVE('',#843,#1041);
#1041 = DEFINITIONAL_REPRESENTATION('',(#1042),#1046);
#1042 = LINE('',#1043,#1044);
#1043 = CARTESIAN_POINT('',(0.,0.294));
#1044 = VECTOR('',#1045,1.);
#1045 = DIRECTION('',(1.,0.));
#1046 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1047 = ORIENTED_EDGE('',*,*,#911,.F.);
#1048 = ADVANCED_FACE('',(#1049),#789,.F.);
#1049 = FACE_BOUND('',#1050,.F.);
#1050 = EDGE_LOOP('',(#1051,#1052,#1053,#1054));
#1051 = ORIENTED_EDGE('',*,*,#773,.F.);
#1052 = ORIENTED_EDGE('',*,*,#958,.T.);
#1053 = ORIENTED_EDGE('',*,*,#888,.T.);
#1054 = ORIENTED_EDGE('',*,*,#1005,.F.);
#1055 = ADVANCED_FACE('',(#1056),#843,.T.);
#1056 = FACE_BOUND('',#1057,.T.);
#1057 = EDGE_LOOP('',(#1058,#1059,#1060,#1061));
#1058 = ORIENTED_EDGE('',*,*,#829,.F.);
#1059 = ORIENTED_EDGE('',*,*,#980,.T.);
#1060 = ORIENTED_EDGE('',*,*,#934,.T.);
#1061 = ORIENTED_EDGE('',*,*,#1027,.F.);
#1062 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1066)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1063,#1064,#1065)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1063 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1064 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1065 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1066 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1063,
  'distance_accuracy_value','confusion accuracy');
#1067 = SHAPE_DEFINITION_REPRESENTATION(#1068,#731);
#1068 = PRODUCT_DEFINITION_SHAPE('','',#1069);
#1069 = PRODUCT_DEFINITION('design','',#1070,#1073);
#1070 = PRODUCT_DEFINITION_FORMATION('','',#1071);
#1071 = PRODUCT('body','body','',(#1072));
#1072 = PRODUCT_CONTEXT('',#2,'mechanical');
#1073 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1074 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1075,#1077);
#1075 = ( REPRESENTATION_RELATIONSHIP('','',#731,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1076) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1076 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#1077 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1078);
#1078 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','=>[0:1:1:4]','',#5,#1069,$);
#1079 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1071));
#1080 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1081),#1062);
#1081 = STYLED_ITEM('color',(#1082),#732);
#1082 = PRESENTATION_STYLE_ASSIGNMENT((#1083,#1089));
#1083 = SURFACE_STYLE_USAGE(.BOTH.,#1084);
#1084 = SURFACE_SIDE_STYLE('',(#1085));
#1085 = SURFACE_STYLE_FILL_AREA(#1086);
#1086 = FILL_AREA_STYLE('',(#1087));
#1087 = FILL_AREA_STYLE_COLOUR('',#1088);
#1088 = COLOUR_RGB('',0.10000000149,0.10000000149,0.10000000149);
#1089 = CURVE_STYLE('',#1090,POSITIVE_LENGTH_MEASURE(0.1),#1088);
#1090 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1091 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1092),#713);
#1092 = STYLED_ITEM('color',(#1093),#383);
#1093 = PRESENTATION_STYLE_ASSIGNMENT((#1094,#1100));
#1094 = SURFACE_STYLE_USAGE(.BOTH.,#1095);
#1095 = SURFACE_SIDE_STYLE('',(#1096));
#1096 = SURFACE_STYLE_FILL_AREA(#1097);
#1097 = FILL_AREA_STYLE('',(#1098));
#1098 = FILL_AREA_STYLE_COLOUR('',#1099);
#1099 = COLOUR_RGB('',0.73400002718,0.773000001907,0.79699999094);
#1100 = CURVE_STYLE('',#1101,POSITIVE_LENGTH_MEASURE(0.1),#1099);
#1101 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1102 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1103),#364);
#1103 = STYLED_ITEM('color',(#1104),#34);
#1104 = PRESENTATION_STYLE_ASSIGNMENT((#1105,#1110));
#1105 = SURFACE_STYLE_USAGE(.BOTH.,#1106);
#1106 = SURFACE_SIDE_STYLE('',(#1107));
#1107 = SURFACE_STYLE_FILL_AREA(#1108);
#1108 = FILL_AREA_STYLE('',(#1109));
#1109 = FILL_AREA_STYLE_COLOUR('',#1099);
#1110 = CURVE_STYLE('',#1111,POSITIVE_LENGTH_MEASURE(0.1),#1099);
#1111 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
