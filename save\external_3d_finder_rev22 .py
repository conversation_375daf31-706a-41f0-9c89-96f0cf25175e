#!/usr/bin/env python3
"""
External 3D Model Finder - Standalone program that can be called from main program
Usage: python external_3d_finder.py <website> <manufacturer> <part_number>
Example: python external_3d_finder.py snapeda Murata GCM155R71H104KE02D
"""

import sys
import os
import json
from pathlib import Path

def find_3d_model(website, manufacturer, part_number):
    """Find 3D model using specified website"""
    print(f"🎯 EXTERNAL 3D FINDER")
    print(f"Website: {website}")
    print(f"Manufacturer: {manufacturer}")
    print(f"Part Number: {part_number}")
    print("=" * 50)
    
    result = {
        'success': False,
        'filename': None,
        'filepath': None,
        'error': None
    }
    
    try:
        if website.lower() == 'snapeda':
            result = find_snapeda_3d(manufacturer, part_number)
        elif website.lower() == 'ultralibrarian':
            result = find_ultralibrarian_3d(manufacturer, part_number)
        elif website.lower() == 'samacsys':
            result = find_samacsys_3d(manufacturer, part_number)
        else:
            result['error'] = f"Unknown website: {website}. Supported: snapeda, ultralibrarian, samacsys"
            
    except Exception as e:
        result['error'] = str(e)
    
    return result

def find_snapeda_3d(manufacturer, part_number):
    """Find 3D model from SnapEDA"""
    try:
        # Import the SnapEDA finder (now in main directory)
        from snapeda_3d_finder import find_3d_model

        # Use the simple function call
        filename = find_3d_model(manufacturer, part_number, silent=False)
        
        if filename:
            filepath = os.path.join("3d", filename)
            return {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'error': None
            }
        else:
            return {'success': False, 'error': 'No 3D model found'}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def find_ultralibrarian_3d(manufacturer, part_number):
    """Find 3D model from UltraLibrarian"""
    try:
        # Import the UltraLibrarian finder (now in main directory)
        from ultralibrarian_3d_finder import find_3d_model
        
        filename = find_3d_model(manufacturer, part_number, silent=False)
        
        if filename:
            filepath = os.path.join("3d", filename)
            return {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'error': None
            }
        else:
            return {'success': False, 'error': 'No 3D model found'}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def find_samacsys_3d(manufacturer, part_number):
    """Find 3D model from SamacSys"""
    try:
        # Import the SamacSys finder (now in main directory)
        from samacsys_3d_finder import find_3d_model

        filename = find_3d_model(manufacturer, part_number, silent=False)

        if filename:
            filepath = os.path.join("3d", filename)
            return {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'error': None
            }
        else:
            return {'success': False, 'error': 'No 3D model found'}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def main():
    """Main function for command line usage"""
    if len(sys.argv) != 4:
        print("Usage: python external_3d_finder.py <website> <manufacturer> <part_number>")
        print("Websites: snapeda, ultralibrarian, samacsys")
        print("Example: python external_3d_finder.py snapeda Murata GCM155R71H104KE02D")
        sys.exit(1)
    
    website = sys.argv[1]
    manufacturer = sys.argv[2]
    part_number = sys.argv[3]
    
    # Create 3d directory if it doesn't exist
    os.makedirs("3d", exist_ok=True)
    
    # Find 3D model
    result = find_3d_model(website, manufacturer, part_number)
    
    # Print result as JSON for easy parsing by main program
    print("RESULT:", json.dumps(result))
    
    # Exit with appropriate code
    sys.exit(0 if result['success'] else 1)

if __name__ == "__main__":
    main()
