#!/usr/bin/env python3
"""
Debug SnapEDA step by step - show exactly what's happening
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_debug_browser():
    """Setup visible browser for debugging"""
    download_dir = os.path.abspath("3d")
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    chrome_options = Options()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    
    # Keep browser visible for debugging
    # chrome_options.add_argument("--headless")  # Commented out to show browser
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def debug_snapeda_step_by_step():
    print("🔍 DEBUG SNAPEDA STEP BY STEP")
    print("=" * 50)
    
    driver = setup_debug_browser()
    
    try:
        # Step 1: Go to SnapEDA
        print("📍 Step 1: Going to SnapEDA...")
        driver.get("https://www.snapeda.com")
        input("Press Enter after you see SnapEDA homepage...")
        
        # Step 2: Search for LM358N
        print("📍 Step 2: Searching for LM358N...")
        search_url = "https://www.snapeda.com/search?q=LM358N"
        driver.get(search_url)
        input("Press Enter after you see search results...")
        
        # Step 3: Look for Texas Instruments LM358N
        print("📍 Step 3: Looking for Texas Instruments LM358N...")
        current_url = driver.current_url
        print(f"Current URL: {current_url}")
        
        page_source = driver.page_source
        if "lm358n" in page_source.lower():
            print("✅ Found LM358N on page")
        else:
            print("❌ LM358N not found on page")
        
        if "texas instruments" in page_source.lower():
            print("✅ Found Texas Instruments on page")
        else:
            print("❌ Texas Instruments not found on page")
        
        # Step 4: Look for part links
        print("📍 Step 4: Looking for part links...")
        part_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'parts/') or contains(text(), 'LM358')]")
        
        print(f"Found {len(part_links)} potential part links:")
        for i, link in enumerate(part_links[:5]):  # Show first 5
            try:
                href = link.get_attribute('href')
                text = link.text.strip()
                print(f"  Link {i+1}: '{text}' -> {href}")
            except:
                print(f"  Link {i+1}: Could not get details")
        
        input("Press Enter to continue...")
        
        # Step 5: Try clicking first relevant link
        if part_links:
            print("📍 Step 5: Clicking first part link...")
            link = part_links[0]
            href = link.get_attribute('href')
            text = link.text.strip()
            print(f"Clicking: '{text}' -> {href}")
            
            link.click()
            time.sleep(3)
            
            current_url = driver.current_url
            print(f"New URL: {current_url}")
            
            input("Press Enter after examining the part page...")
            
            # Step 6: Look for 3D model tab/button
            print("📍 Step 6: Looking for 3D model options...")
            model_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D') or contains(text(), 'model') or contains(text(), 'Model')]")
            
            print(f"Found {len(model_elements)} potential 3D model elements:")
            for i, element in enumerate(model_elements[:5]):
                try:
                    text = element.text.strip()
                    tag = element.tag_name
                    print(f"  Element {i+1}: <{tag}> '{text}'")
                except:
                    print(f"  Element {i+1}: Could not get details")
            
            input("Press Enter when done examining...")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        input("Press Enter to close browser...")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_snapeda_step_by_step()
