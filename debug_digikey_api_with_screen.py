#!/usr/bin/env python3
"""
Debug Digi-Key API with visible browser screen
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from digikey_datasheet_improved import DigikeyDatasheetFinder

def setup_visible_browser():
    """Setup Chrome browser with visible screen for debugging"""
    download_dir = os.path.abspath("datasheets")
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    chrome_options = Options()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    
    # Keep browser visible for debugging
    # chrome_options.add_argument("--headless")  # Commented out to show browser
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def debug_digikey_api_with_screen():
    """Debug the Digi-Key API process with visible browser"""
    print("🎯 DEBUGGING DIGI-KEY API WITH VISIBLE BROWSER")
    print("=" * 50)
    
    # Step 1: Test API to get datasheet URL
    print("📍 Step 1: Getting datasheet URL from Digi-Key API...")
    finder = DigikeyDatasheetFinder()
    
    if not finder.load_credentials():
        print("❌ Failed to load credentials")
        return
    
    result = finder.search_part('Texas Instruments', 'LM358N')
    if not result:
        print("❌ API returned no results")
        return
    
    datasheet_url = result.get('DatasheetUrl')
    if not datasheet_url:
        print("❌ No datasheet URL in API response")
        return
    
    print(f"✅ Got datasheet URL from API: {datasheet_url}")
    
    # Step 2: Open browser and navigate to the URL
    print("📍 Step 2: Opening browser and navigating to datasheet URL...")
    driver = setup_visible_browser()
    
    try:
        # Check files before
        files_before = set(os.listdir("datasheets")) if os.path.exists("datasheets") else set()
        print(f"Files before: {len(files_before)}")
        
        # Navigate to the datasheet URL
        print(f"📍 Navigating to: {datasheet_url}")
        driver.get(datasheet_url)
        
        input("Press Enter after you see what the browser shows...")
        
        # Check current URL
        current_url = driver.current_url
        print(f"Current URL: {current_url}")
        
        # Check page title
        page_title = driver.title
        print(f"Page title: {page_title}")
        
        # Check if it's a PDF or HTML page
        page_source = driver.page_source
        if page_source.startswith('%PDF'):
            print("✅ Browser is showing PDF content")
        elif '<html' in page_source.lower():
            print("⚠️ Browser is showing HTML content")
            print(f"Page starts with: {page_source[:200]}")
        else:
            print("❓ Unknown content type")
        
        # Wait for potential download
        print("📍 Waiting for potential download...")
        time.sleep(5)
        
        # Check files after
        files_after = set(os.listdir("datasheets")) if os.path.exists("datasheets") else set()
        new_files = files_after - files_before
        
        if new_files:
            print(f"✅ New files downloaded: {new_files}")
            for new_file in new_files:
                file_path = os.path.join("datasheets", new_file)
                file_size = os.path.getsize(file_path)
                print(f"  File: {new_file} (Size: {file_size} bytes)")
                
                # Check if it's really a PDF
                with open(file_path, 'rb') as f:
                    header = f.read(10)
                    if header.startswith(b'%PDF'):
                        print(f"  ✅ {new_file} is a valid PDF")
                    else:
                        print(f"  ❌ {new_file} is NOT a PDF! Header: {header}")
        else:
            print("❌ No files were downloaded automatically")
            
            # Try right-clicking to save
            print("📍 You can try right-clicking and 'Save As' to see what happens...")
        
        input("Press Enter when you're done examining the browser...")
        
    except Exception as e:
        print(f"❌ Error during browser test: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_digikey_api_with_screen()
