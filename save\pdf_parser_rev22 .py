#!/usr/bin/env python3
"""
PDF Parser for datasheet validation and package extraction
"""

import os
import re
from typing import Dict, Optional, List

def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text from PDF using multiple methods
    
    Args:
        pdf_path (str): Path to PDF file
        
    Returns:
        str: Extracted text or empty string if failed
    """
    text = ""
    
    # Try pdfplumber first (better for tables and layout)
    try:
        import pdfplumber
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages[:5]:  # Only check first 5 pages
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        if text.strip():
            return text
    except ImportError:
        print("pdfplumber not available, trying PyPDF2...")
    except Exception as e:
        print(f"pdfplumber failed: {e}")
    
    # Try PyPDF2 as fallback
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(min(5, len(pdf_reader.pages))):  # Only check first 5 pages
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        if text.strip():
            return text
    except ImportError:
        print("PyPDF2 not available")
    except Exception as e:
        print(f"PyPDF2 failed: {e}")
    
    return text

def validate_part_number(text: str, expected_part: str) -> Dict[str, any]:
    """
    Validate that the PDF contains the expected part number
    
    Args:
        text (str): Extracted PDF text
        expected_part (str): Expected part number
        
    Returns:
        dict: Validation results
    """
    if not text or not expected_part:
        return {
            'valid': False,
            'confidence': 0,
            'found_parts': [],
            'message': 'No text or part number provided'
        }
    
    text_upper = text.upper()
    expected_upper = expected_part.upper()
    
    # Direct match
    if expected_upper in text_upper:
        return {
            'valid': True,
            'confidence': 100,
            'found_parts': [expected_part],
            'message': f'Exact match found for {expected_part}'
        }
    
    # Try variations (remove/add common suffixes)
    base_part = re.sub(r'[-_](N|NOPB|TR|T&R|RL|RoHS)$', '', expected_upper)
    if base_part in text_upper:
        return {
            'valid': True,
            'confidence': 90,
            'found_parts': [base_part],
            'message': f'Base part number {base_part} found (without suffix)'
        }
    
    # Look for similar part numbers
    found_parts = []
    words = text_upper.split()
    for word in words:
        if len(word) >= 4 and any(char.isdigit() for char in word):
            # Calculate similarity (simple approach)
            if len(set(expected_upper) & set(word)) >= len(expected_upper) * 0.6:
                found_parts.append(word)
    
    if found_parts:
        return {
            'valid': False,
            'confidence': 50,
            'found_parts': found_parts[:5],  # Limit to 5
            'message': f'Similar parts found but no exact match'
        }
    
    return {
        'valid': False,
        'confidence': 0,
        'found_parts': [],
        'message': 'No matching part numbers found'
    }

def extract_package_type(text: str) -> Dict[str, any]:
    """
    Extract package type from PDF text
    
    Args:
        text (str): Extracted PDF text
        
    Returns:
        dict: Package extraction results
    """
    if not text:
        return {
            'package': None,
            'confidence': 0,
            'found_packages': [],
            'message': 'No text provided'
        }
    
    text_upper = text.upper()
    
    # Common package patterns (from existing code)
    package_patterns = [
        r'\b(SOT-?23-?[0-9]*)\b',
        r'\b(SOT-?89-?[0-9]*)\b',
        r'\b(SOIC-?[0-9]+)\b',
        r'\b(QFN-?[0-9]+)\b',
        r'\b(DFN-?[0-9]+)\b',
        r'\b(MSOP-?[0-9]+)\b',
        r'\b(TSSOP-?[0-9]+)\b',
        r'\b(SSOP-?[0-9]+)\b',
        r'\b(PDIP-?[0-9]+)\b',
        r'\b(DIP-?[0-9]+)\b',
        r'\b([0-9]+-?DIP)\b',
        r'\b([0-9]+-?SOP)\b',
        r'\b(BGA-?[0-9]+)\b',
        r'\b(QFP-?[0-9]+)\b',
        r'\b(LQFP-?[0-9]+)\b'
    ]
    
    found_packages = []
    
    # Try regex patterns first
    for pattern in package_patterns:
        matches = re.findall(pattern, text_upper)
        for match in matches:
            if match not in found_packages:
                found_packages.append(match)
    
    # Simple keyword search as fallback
    simple_packages = ['SOT23', 'SOT89', 'SOIC', 'QFN', 'DFN', 'MSOP', 'TSSOP', 'PDIP', 'DIP', 'BGA', 'QFP', 'LQFP']
    for package in simple_packages:
        if package in text_upper and package not in found_packages:
            found_packages.append(package)
    
    if found_packages:
        # Return the first (most likely) package
        return {
            'package': found_packages[0],
            'confidence': 80,
            'found_packages': found_packages,
            'message': f'Found {len(found_packages)} package type(s)'
        }
    
    return {
        'package': None,
        'confidence': 0,
        'found_packages': [],
        'message': 'No package types found'
    }

def parse_datasheet_pdf(pdf_path: str, expected_part: str) -> Dict[str, any]:
    """
    Main function to parse PDF and extract information
    
    Args:
        pdf_path (str): Path to PDF file
        expected_part (str): Expected part number
        
    Returns:
        dict: Complete parsing results
    """
    if not os.path.exists(pdf_path):
        return {
            'success': False,
            'message': f'PDF file not found: {pdf_path}',
            'part_validation': None,
            'package_info': None,
            'text_length': 0
        }
    
    # Extract text
    text = extract_text_from_pdf(pdf_path)
    
    if not text.strip():
        return {
            'success': False,
            'message': 'Could not extract text from PDF',
            'part_validation': None,
            'package_info': None,
            'text_length': 0
        }
    
    # Validate part number
    part_validation = validate_part_number(text, expected_part)
    
    # Extract package type
    package_info = extract_package_type(text)
    
    return {
        'success': True,
        'message': 'PDF parsed successfully',
        'part_validation': part_validation,
        'package_info': package_info,
        'text_length': len(text)
    }

if __name__ == "__main__":
    # Test the parser
    import sys
    if len(sys.argv) >= 3:
        pdf_path = sys.argv[1]
        part_number = sys.argv[2]
        result = parse_datasheet_pdf(pdf_path, part_number)
        print(f"Results: {result}")
    else:
        print("Usage: python pdf_parser.py <pdf_path> <part_number>")
