#!/usr/bin/env python3
"""
Test the external 3D finder
"""

import subprocess
import json
import sys

def test_external_3d_finder():
    """Test the external 3D finder"""
    print("🧪 TESTING EXTERNAL 3D FINDER")
    print("=" * 50)
    
    # Test SnapEDA
    print("Testing SnapEDA...")
    try:
        result = subprocess.run([
            sys.executable, "external_3d_finder.py", 
            "snapeda", "Murata", "GCM155R71H104KE02D"
        ], capture_output=True, text=True, timeout=300)
        
        print(f"Return code: {result.returncode}")
        print(f"Output: {result.stdout}")
        if result.stderr:
            print(f"Errors: {result.stderr}")
            
        # Parse result
        for line in result.stdout.split('\n'):
            if line.startswith('RESULT:'):
                result_json = json.loads(line[7:])  # Remove 'RESULT: '
                print(f"Parsed result: {result_json}")
                
                if result_json['success']:
                    print("✅ SnapEDA test PASSED!")
                else:
                    print(f"❌ SnapEDA test FAILED: {result_json['error']}")
                break
        else:
            print("❌ No result found in output")
            
    except subprocess.TimeoutExpired:
        print("❌ SnapEDA test TIMEOUT (5 minutes)")
    except Exception as e:
        print(f"❌ SnapEDA test ERROR: {e}")

if __name__ == "__main__":
    test_external_3d_finder()
