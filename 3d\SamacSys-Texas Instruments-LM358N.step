ISO-10303-21;
HEADER;
FILE_DESCRIPTION((''),'2;1');
FILE_NAME('P0008A_ASM','2017-12-19T11:56:30',('a0412086'),(''),
'CREO PARAMETRIC BY PTC INC, 2017260','CREO PARAMETRIC BY PTC INC, 2017260','');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#28=DIRECTION('',(-1.E0,0.E0,0.E0));
#29=VECTOR('',#28,2.2E-1);
#30=CARTESIAN_POINT('',(1.1E-1,1.E-2,-1.825E-1));
#31=LINE('',#30,#29);
#32=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#33=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#34=PRESENTATION_STYLE_ASSIGNMENT((#33));
#35=STYLED_ITEM('',(#34),#31);
#36=DIRECTION('',(0.E0,-1.E0,0.E0));
#37=VECTOR('',#36,1.E-2);
#38=CARTESIAN_POINT('',(-1.1E-1,1.E-2,-1.825E-1));
#39=LINE('',#38,#37);
#40=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#41=PRESENTATION_STYLE_ASSIGNMENT((#40));
#42=STYLED_ITEM('',(#41),#39);
#43=CARTESIAN_POINT('',(-1.1E-1,1.E-2,-1.825E-1));
#44=CARTESIAN_POINT('',(-1.100484837122E-1,1.E-2,-1.825E-1));
#45=CARTESIAN_POINT('',(-1.100969087249E-1,1.000167253793E-2,
-1.824997649401E-1));
#46=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#48=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#49=PRESENTATION_STYLE_ASSIGNMENT((#48));
#50=STYLED_ITEM('',(#49),#47);
#51=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#52=CARTESIAN_POINT('',(-1.112568325288E-1,1.008160961858E-2,
-1.824885305161E-1));
#53=CARTESIAN_POINT('',(-1.134753240751E-1,1.021715977403E-2,
-1.822178837341E-1));
#54=CARTESIAN_POINT('',(-1.166148970902E-1,1.035787101740E-2,
-1.811003198989E-1));
#55=CARTESIAN_POINT('',(-1.194536381267E-1,1.043040591138E-2,
-1.793038994189E-1));
#56=CARTESIAN_POINT('',(-1.218038994189E-1,1.043040591138E-2,
-1.769536381267E-1));
#57=CARTESIAN_POINT('',(-1.236003198989E-1,1.035787101740E-2,
-1.741148970902E-1));
#58=CARTESIAN_POINT('',(-1.247178837341E-1,1.021715977403E-2,
-1.709753240751E-1));
#59=CARTESIAN_POINT('',(-1.249885305161E-1,1.008160961858E-2,
-1.687568325288E-1));
#60=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#62=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#63=PRESENTATION_STYLE_ASSIGNMENT((#62));
#64=STYLED_ITEM('',(#63),#61);
#65=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#66=CARTESIAN_POINT('',(-1.249997649401E-1,1.000167253793E-2,
-1.675969087249E-1));
#67=CARTESIAN_POINT('',(-1.25E-1,1.E-2,-1.675484837122E-1));
#68=CARTESIAN_POINT('',(-1.25E-1,1.E-2,-1.675E-1));
#70=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#71=PRESENTATION_STYLE_ASSIGNMENT((#70));
#72=STYLED_ITEM('',(#71),#69);
#73=DIRECTION('',(0.E0,-1.E0,0.E0));
#74=VECTOR('',#73,1.E-2);
#75=CARTESIAN_POINT('',(-1.25E-1,1.E-2,-1.675E-1));
#76=LINE('',#75,#74);
#77=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#78=PRESENTATION_STYLE_ASSIGNMENT((#77));
#79=STYLED_ITEM('',(#78),#76);
#80=CARTESIAN_POINT('',(-3.129592893203E-2,5.75E-2,-1.758243103516E-1));
#81=CARTESIAN_POINT('',(-3.122509832624E-2,5.986270845985E-2,
-1.754922533325E-1));
#82=CARTESIAN_POINT('',(-3.114874947276E-2,6.222524190513E-2,
-1.751602209101E-1));
#83=CARTESIAN_POINT('',(-3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#85=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#86=PRESENTATION_STYLE_ASSIGNMENT((#85));
#87=STYLED_ITEM('',(#86),#84);
#88=CARTESIAN_POINT('',(1.1E-1,1.E-2,-1.825E-1));
#89=CARTESIAN_POINT('',(1.100484837122E-1,1.E-2,-1.825E-1));
#90=CARTESIAN_POINT('',(1.100969087249E-1,1.000167253793E-2,
-1.824997649401E-1));
#91=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#93=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#94=PRESENTATION_STYLE_ASSIGNMENT((#93));
#95=STYLED_ITEM('',(#94),#92);
#96=CARTESIAN_POINT('',(3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#97=CARTESIAN_POINT('',(3.114874947276E-2,6.222524190513E-2,
-1.751602209101E-1));
#98=CARTESIAN_POINT('',(3.122509832624E-2,5.986270845985E-2,
-1.754922533325E-1));
#99=CARTESIAN_POINT('',(3.129592893203E-2,5.75E-2,-1.758243103516E-1));
#101=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#102=PRESENTATION_STYLE_ASSIGNMENT((#101));
#103=STYLED_ITEM('',(#102),#100);
#104=DIRECTION('',(-1.E0,0.E0,0.E0));
#105=VECTOR('',#104,6.259185786406E-2);
#106=CARTESIAN_POINT('',(3.129592893203E-2,5.75E-2,-1.758243103516E-1));
#107=LINE('',#106,#105);
#108=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#109=PRESENTATION_STYLE_ASSIGNMENT((#108));
#110=STYLED_ITEM('',(#109),#107);
#111=CARTESIAN_POINT('',(0.E0,7.75E-2,-1.825E-1));
#112=DIRECTION('',(0.E0,1.E0,0.E0));
#113=DIRECTION('',(-7.102678855759E-1,0.E0,7.039314815517E-1));
#114=AXIS2_PLACEMENT_3D('',#111,#112,#113);
#116=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#117=PRESENTATION_STYLE_ASSIGNMENT((#116));
#118=STYLED_ITEM('',(#117),#115);
#119=CARTESIAN_POINT('',(-8.9E-2,7.75E-2,-1.2E-1));
#120=DIRECTION('',(0.E0,-1.E0,0.E0));
#121=DIRECTION('',(1.E0,0.E0,0.E0));
#122=AXIS2_PLACEMENT_3D('',#119,#120,#121);
#124=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#125=PRESENTATION_STYLE_ASSIGNMENT((#124));
#126=STYLED_ITEM('',(#125),#123);
#127=CARTESIAN_POINT('',(-8.9E-2,7.75E-2,-1.2E-1));
#128=DIRECTION('',(0.E0,-1.E0,0.E0));
#129=DIRECTION('',(-1.E0,0.E0,0.E0));
#130=AXIS2_PLACEMENT_3D('',#127,#128,#129);
#132=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#133=PRESENTATION_STYLE_ASSIGNMENT((#132));
#134=STYLED_ITEM('',(#133),#131);
#135=DIRECTION('',(-1.E0,0.E0,0.E0));
#136=VECTOR('',#135,7.974562025192E-2);
#137=CARTESIAN_POINT('',(1.024741925903E-1,7.75E-2,-1.599741925903E-1));
#138=LINE('',#137,#136);
#139=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#140=PRESENTATION_STYLE_ASSIGNMENT((#139));
#141=STYLED_ITEM('',(#140),#138);
#142=CARTESIAN_POINT('',(2.272857233843E-2,7.75E-2,-1.599741925903E-1));
#143=CARTESIAN_POINT('',(2.371520384649E-2,7.75E-2,-1.609697052126E-1));
#144=CARTESIAN_POINT('',(2.545161209534E-2,7.729703470696E-2,
-1.629857440997E-1));
#145=CARTESIAN_POINT('',(2.738839367688E-2,7.641113334883E-2,
-1.658465495737E-1));
#146=CARTESIAN_POINT('',(2.882068760924E-2,7.496158940455E-2,
-1.685027217854E-1));
#147=CARTESIAN_POINT('',(2.982098178530E-2,7.301351797913E-2,
-1.708189811574E-1));
#148=CARTESIAN_POINT('',(3.049689984033E-2,7.055743036365E-2,
-1.727550567768E-1));
#149=CARTESIAN_POINT('',(3.089726041498E-2,6.774084956721E-2,
-1.741474418960E-1));
#150=CARTESIAN_POINT('',(3.102966853247E-2,6.565638802923E-2,
-1.746780047699E-1));
#151=CARTESIAN_POINT('',(3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#153=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#154=PRESENTATION_STYLE_ASSIGNMENT((#153));
#155=STYLED_ITEM('',(#154),#152);
#156=DIRECTION('',(-1.E0,0.E0,0.E0));
#157=VECTOR('',#156,7.140743070948E-2);
#158=CARTESIAN_POINT('',(1.024741925903E-1,6.458759651440E-2,
-1.748282136215E-1));
#159=LINE('',#158,#157);
#160=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#161=PRESENTATION_STYLE_ASSIGNMENT((#160));
#162=STYLED_ITEM('',(#161),#159);
#163=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#164=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#165=DIRECTION('',(0.E0,1.391731009601E-1,-9.902680687416E-1));
#166=AXIS2_PLACEMENT_3D('',#163,#164,#165);
#168=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#169=PRESENTATION_STYLE_ASSIGNMENT((#168));
#170=STYLED_ITEM('',(#169),#167);
#171=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#172=DIRECTION('',(0.E0,0.E0,1.E0));
#173=DIRECTION('',(9.902680687416E-1,1.391731009601E-1,0.E0));
#174=AXIS2_PLACEMENT_3D('',#171,#172,#173);
#176=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#177=PRESENTATION_STYLE_ASSIGNMENT((#176));
#178=STYLED_ITEM('',(#177),#175);
#179=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#180=DIRECTION('',(-1.E0,0.E0,0.E0));
#181=DIRECTION('',(0.E0,1.E0,0.E0));
#182=AXIS2_PLACEMENT_3D('',#179,#180,#181);
#184=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#185=PRESENTATION_STYLE_ASSIGNMENT((#184));
#186=STYLED_ITEM('',(#185),#183);
#187=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#188=VECTOR('',#187,5.565024920858E-2);
#189=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#190=LINE('',#189,#188);
#191=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#192=PRESENTATION_STYLE_ASSIGNMENT((#191));
#193=STYLED_ITEM('',(#192),#190);
#194=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#195=CARTESIAN_POINT('',(1.112568325288E-1,1.008160961858E-2,
-1.824885305161E-1));
#196=CARTESIAN_POINT('',(1.134753240751E-1,1.021715977403E-2,
-1.822178837341E-1));
#197=CARTESIAN_POINT('',(1.166148970902E-1,1.035787101740E-2,
-1.811003198989E-1));
#198=CARTESIAN_POINT('',(1.194536381267E-1,1.043040591138E-2,
-1.793038994189E-1));
#199=CARTESIAN_POINT('',(1.218038994189E-1,1.043040591138E-2,
-1.769536381267E-1));
#200=CARTESIAN_POINT('',(1.236003198989E-1,1.035787101740E-2,
-1.741148970902E-1));
#201=CARTESIAN_POINT('',(1.247178837341E-1,1.021715977403E-2,
-1.709753240751E-1));
#202=CARTESIAN_POINT('',(1.249885305161E-1,1.008160961858E-2,
-1.687568325288E-1));
#203=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#205=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#206=PRESENTATION_STYLE_ASSIGNMENT((#205));
#207=STYLED_ITEM('',(#206),#204);
#208=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#209=VECTOR('',#208,5.565024920858E-2);
#210=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#211=LINE('',#210,#209);
#212=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#213=PRESENTATION_STYLE_ASSIGNMENT((#212));
#214=STYLED_ITEM('',(#213),#211);
#215=DIRECTION('',(0.E0,1.E0,0.E0));
#216=VECTOR('',#215,1.E-2);
#217=CARTESIAN_POINT('',(1.1E-1,0.E0,-1.825E-1));
#218=LINE('',#217,#216);
#219=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#220=PRESENTATION_STYLE_ASSIGNMENT((#219));
#221=STYLED_ITEM('',(#220),#218);
#222=CARTESIAN_POINT('',(1.1E-1,0.E0,-1.825E-1));
#223=CARTESIAN_POINT('',(1.100484837122E-1,0.E0,-1.825E-1));
#224=CARTESIAN_POINT('',(1.100969087249E-1,-1.672537930245E-6,
-1.824997649401E-1));
#225=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901742E-6,
-1.824992964849E-1));
#227=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#228=PRESENTATION_STYLE_ASSIGNMENT((#227));
#229=STYLED_ITEM('',(#228),#226);
#230=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901823E-6,
-1.824992964849E-1));
#231=CARTESIAN_POINT('',(1.112568325288E-1,-8.160961857811E-5,
-1.824885305161E-1));
#232=CARTESIAN_POINT('',(1.134753240751E-1,-2.171597740327E-4,
-1.822178837341E-1));
#233=CARTESIAN_POINT('',(1.166148970902E-1,-3.578710173970E-4,
-1.811003198989E-1));
#234=CARTESIAN_POINT('',(1.194536381267E-1,-4.304059113846E-4,
-1.793038994189E-1));
#235=CARTESIAN_POINT('',(1.218038994189E-1,-4.304059113846E-4,
-1.769536381267E-1));
#236=CARTESIAN_POINT('',(1.236003198989E-1,-3.578710173969E-4,
-1.741148970902E-1));
#237=CARTESIAN_POINT('',(1.247178837341E-1,-2.171597740329E-4,
-1.709753240751E-1));
#238=CARTESIAN_POINT('',(1.249885305161E-1,-8.160961857792E-5,
-1.687568325288E-1));
#239=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901602E-6,
-1.676452754538E-1));
#241=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#242=PRESENTATION_STYLE_ASSIGNMENT((#241));
#243=STYLED_ITEM('',(#242),#240);
#244=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901631E-6,
-1.676452754538E-1));
#245=CARTESIAN_POINT('',(1.249997649401E-1,-1.672537930126E-6,
-1.675969087249E-1));
#246=CARTESIAN_POINT('',(1.25E-1,0.E0,-1.675484837122E-1));
#247=CARTESIAN_POINT('',(1.25E-1,0.E0,-1.675E-1));
#249=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#250=PRESENTATION_STYLE_ASSIGNMENT((#249));
#251=STYLED_ITEM('',(#250),#248);
#252=DIRECTION('',(0.E0,1.E0,0.E0));
#253=VECTOR('',#252,1.E-2);
#254=CARTESIAN_POINT('',(1.25E-1,0.E0,-1.675E-1));
#255=LINE('',#254,#253);
#256=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#257=PRESENTATION_STYLE_ASSIGNMENT((#256));
#258=STYLED_ITEM('',(#257),#255);
#259=DIRECTION('',(-1.E0,0.E0,0.E0));
#260=VECTOR('',#259,2.2E-1);
#261=CARTESIAN_POINT('',(1.1E-1,0.E0,-1.825E-1));
#262=LINE('',#261,#260);
#263=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#264=PRESENTATION_STYLE_ASSIGNMENT((#263));
#265=STYLED_ITEM('',(#264),#262);
#266=CARTESIAN_POINT('',(-1.1E-1,0.E0,-1.825E-1));
#267=CARTESIAN_POINT('',(-1.100484837122E-1,0.E0,-1.825E-1));
#268=CARTESIAN_POINT('',(-1.100969087249E-1,-1.672537930258E-6,
-1.824997649401E-1));
#269=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901756E-6,
-1.824992964849E-1));
#271=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#272=PRESENTATION_STYLE_ASSIGNMENT((#271));
#273=STYLED_ITEM('',(#272),#270);
#274=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#275=VECTOR('',#274,5.565024920858E-2);
#276=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901814E-6,
-1.824992964849E-1));
#277=LINE('',#276,#275);
#278=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#279=PRESENTATION_STYLE_ASSIGNMENT((#278));
#280=STYLED_ITEM('',(#279),#277);
#281=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901814E-6,
-1.824992964849E-1));
#282=CARTESIAN_POINT('',(-1.112568325288E-1,-8.160961857811E-5,
-1.824885305161E-1));
#283=CARTESIAN_POINT('',(-1.134753240751E-1,-2.171597740326E-4,
-1.822178837341E-1));
#284=CARTESIAN_POINT('',(-1.166148970902E-1,-3.578710173970E-4,
-1.811003198989E-1));
#285=CARTESIAN_POINT('',(-1.194536381267E-1,-4.304059113845E-4,
-1.793038994189E-1));
#286=CARTESIAN_POINT('',(-1.218038994189E-1,-4.304059113847E-4,
-1.769536381267E-1));
#287=CARTESIAN_POINT('',(-1.236003198989E-1,-3.578710173969E-4,
-1.741148970902E-1));
#288=CARTESIAN_POINT('',(-1.247178837341E-1,-2.171597740328E-4,
-1.709753240751E-1));
#289=CARTESIAN_POINT('',(-1.249885305161E-1,-8.160961857793E-5,
-1.687568325288E-1));
#290=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901613E-6,
-1.676452754538E-1));
#292=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#293=PRESENTATION_STYLE_ASSIGNMENT((#292));
#294=STYLED_ITEM('',(#293),#291);
#295=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#296=VECTOR('',#295,5.565024920858E-2);
#297=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901613E-6,
-1.676452754538E-1));
#298=LINE('',#297,#296);
#299=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#300=PRESENTATION_STYLE_ASSIGNMENT((#299));
#301=STYLED_ITEM('',(#300),#298);
#302=DIRECTION('',(0.E0,0.E0,1.E0));
#303=VECTOR('',#302,3.35E-1);
#304=CARTESIAN_POINT('',(-1.25E-1,0.E0,-1.675E-1));
#305=LINE('',#304,#303);
#306=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#307=PRESENTATION_STYLE_ASSIGNMENT((#306));
#308=STYLED_ITEM('',(#307),#305);
#309=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901645E-6,
-1.676452754538E-1));
#310=CARTESIAN_POINT('',(-1.249997649401E-1,-1.672537930140E-6,
-1.675969087249E-1));
#311=CARTESIAN_POINT('',(-1.25E-1,0.E0,-1.675484837122E-1));
#312=CARTESIAN_POINT('',(-1.25E-1,0.E0,-1.675E-1));
#314=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#315=PRESENTATION_STYLE_ASSIGNMENT((#314));
#316=STYLED_ITEM('',(#315),#313);
#317=DIRECTION('',(0.E0,0.E0,1.E0));
#318=VECTOR('',#317,3.35E-1);
#319=CARTESIAN_POINT('',(-1.25E-1,1.E-2,-1.675E-1));
#320=LINE('',#319,#318);
#321=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#322=PRESENTATION_STYLE_ASSIGNMENT((#321));
#323=STYLED_ITEM('',(#322),#320);
#324=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#325=CARTESIAN_POINT('',(-1.249997649401E-1,1.000167253793E-2,
1.675969087249E-1));
#326=CARTESIAN_POINT('',(-1.25E-1,1.E-2,1.675484837122E-1));
#327=CARTESIAN_POINT('',(-1.25E-1,1.E-2,1.675E-1));
#329=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#330=PRESENTATION_STYLE_ASSIGNMENT((#329));
#331=STYLED_ITEM('',(#330),#328);
#332=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#333=VECTOR('',#332,5.565024920858E-2);
#334=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#335=LINE('',#334,#333);
#336=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#337=PRESENTATION_STYLE_ASSIGNMENT((#336));
#338=STYLED_ITEM('',(#337),#335);
#339=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#340=VECTOR('',#339,5.565024920858E-2);
#341=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#342=LINE('',#341,#340);
#343=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#344=PRESENTATION_STYLE_ASSIGNMENT((#343));
#345=STYLED_ITEM('',(#344),#342);
#346=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#347=DIRECTION('',(0.E0,0.E0,1.E0));
#348=DIRECTION('',(0.E0,1.E0,0.E0));
#349=AXIS2_PLACEMENT_3D('',#346,#347,#348);
#351=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#352=PRESENTATION_STYLE_ASSIGNMENT((#351));
#353=STYLED_ITEM('',(#352),#350);
#354=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#355=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#356=DIRECTION('',(-9.902680687416E-1,1.391731009601E-1,0.E0));
#357=AXIS2_PLACEMENT_3D('',#354,#355,#356);
#359=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#360=PRESENTATION_STYLE_ASSIGNMENT((#359));
#361=STYLED_ITEM('',(#360),#358);
#362=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#363=DIRECTION('',(1.E0,0.E0,0.E0));
#364=DIRECTION('',(0.E0,1.391731009601E-1,-9.902680687416E-1));
#365=AXIS2_PLACEMENT_3D('',#362,#363,#364);
#367=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#368=PRESENTATION_STYLE_ASSIGNMENT((#367));
#369=STYLED_ITEM('',(#368),#366);
#370=DIRECTION('',(0.E0,0.E0,1.E0));
#371=VECTOR('',#370,3.199483851807E-1);
#372=CARTESIAN_POINT('',(-1.024741925903E-1,7.75E-2,-1.599741925903E-1));
#373=LINE('',#372,#371);
#374=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#375=PRESENTATION_STYLE_ASSIGNMENT((#374));
#376=STYLED_ITEM('',(#375),#373);
#377=DIRECTION('',(0.E0,0.E0,1.E0));
#378=VECTOR('',#377,3.199483851807E-1);
#379=CARTESIAN_POINT('',(-1.173282136215E-1,6.458759651440E-2,
-1.599741925903E-1));
#380=LINE('',#379,#378);
#381=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#382=PRESENTATION_STYLE_ASSIGNMENT((#381));
#383=STYLED_ITEM('',(#382),#380);
#384=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,1.599741925903E-1));
#385=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#386=DIRECTION('',(0.E0,1.391731009601E-1,9.902680687416E-1));
#387=AXIS2_PLACEMENT_3D('',#384,#385,#386);
#389=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#390=PRESENTATION_STYLE_ASSIGNMENT((#389));
#391=STYLED_ITEM('',(#390),#388);
#392=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,1.599741925903E-1));
#393=DIRECTION('',(0.E0,0.E0,-1.E0));
#394=DIRECTION('',(-9.902680687416E-1,1.391731009601E-1,0.E0));
#395=AXIS2_PLACEMENT_3D('',#392,#393,#394);
#397=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#398=PRESENTATION_STYLE_ASSIGNMENT((#397));
#399=STYLED_ITEM('',(#398),#396);
#400=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,1.599741925903E-1));
#401=DIRECTION('',(1.E0,0.E0,0.E0));
#402=DIRECTION('',(0.E0,1.E0,0.E0));
#403=AXIS2_PLACEMENT_3D('',#400,#401,#402);
#405=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#406=PRESENTATION_STYLE_ASSIGNMENT((#405));
#407=STYLED_ITEM('',(#406),#404);
#408=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#409=VECTOR('',#408,5.565024920858E-2);
#410=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#411=LINE('',#410,#409);
#412=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#413=PRESENTATION_STYLE_ASSIGNMENT((#412));
#414=STYLED_ITEM('',(#413),#411);
#415=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#416=CARTESIAN_POINT('',(-1.112568325288E-1,1.008160961858E-2,
1.824885305161E-1));
#417=CARTESIAN_POINT('',(-1.134753240751E-1,1.021715977403E-2,
1.822178837341E-1));
#418=CARTESIAN_POINT('',(-1.166148970902E-1,1.035787101740E-2,
1.811003198989E-1));
#419=CARTESIAN_POINT('',(-1.194536381267E-1,1.043040591138E-2,
1.793038994189E-1));
#420=CARTESIAN_POINT('',(-1.218038994189E-1,1.043040591138E-2,
1.769536381267E-1));
#421=CARTESIAN_POINT('',(-1.236003198989E-1,1.035787101740E-2,
1.741148970902E-1));
#422=CARTESIAN_POINT('',(-1.247178837341E-1,1.021715977403E-2,
1.709753240751E-1));
#423=CARTESIAN_POINT('',(-1.249885305161E-1,1.008160961858E-2,
1.687568325288E-1));
#424=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#426=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#427=PRESENTATION_STYLE_ASSIGNMENT((#426));
#428=STYLED_ITEM('',(#427),#425);
#429=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#430=VECTOR('',#429,5.565024920858E-2);
#431=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#432=LINE('',#431,#430);
#433=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#434=PRESENTATION_STYLE_ASSIGNMENT((#433));
#435=STYLED_ITEM('',(#434),#432);
#436=CARTESIAN_POINT('',(-1.1E-1,1.E-2,1.825E-1));
#437=CARTESIAN_POINT('',(-1.100484837122E-1,1.E-2,1.825E-1));
#438=CARTESIAN_POINT('',(-1.100969087249E-1,1.000167253793E-2,
1.824997649401E-1));
#439=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#441=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#442=PRESENTATION_STYLE_ASSIGNMENT((#441));
#443=STYLED_ITEM('',(#442),#440);
#444=DIRECTION('',(1.E0,0.E0,0.E0));
#445=VECTOR('',#444,2.049483851807E-1);
#446=CARTESIAN_POINT('',(-1.024741925903E-1,7.75E-2,1.599741925903E-1));
#447=LINE('',#446,#445);
#448=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#449=PRESENTATION_STYLE_ASSIGNMENT((#448));
#450=STYLED_ITEM('',(#449),#447);
#451=DIRECTION('',(1.E0,0.E0,0.E0));
#452=VECTOR('',#451,2.049483851807E-1);
#453=CARTESIAN_POINT('',(-1.024741925903E-1,6.458759651440E-2,
1.748282136215E-1));
#454=LINE('',#453,#452);
#455=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#456=PRESENTATION_STYLE_ASSIGNMENT((#455));
#457=STYLED_ITEM('',(#456),#454);
#458=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,1.599741925903E-1));
#459=DIRECTION('',(0.E0,0.E0,-1.E0));
#460=DIRECTION('',(0.E0,1.E0,0.E0));
#461=AXIS2_PLACEMENT_3D('',#458,#459,#460);
#463=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#464=PRESENTATION_STYLE_ASSIGNMENT((#463));
#465=STYLED_ITEM('',(#464),#462);
#466=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,1.599741925903E-1));
#467=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#468=DIRECTION('',(9.902680687416E-1,1.391731009601E-1,0.E0));
#469=AXIS2_PLACEMENT_3D('',#466,#467,#468);
#471=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#472=PRESENTATION_STYLE_ASSIGNMENT((#471));
#473=STYLED_ITEM('',(#472),#470);
#474=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,1.599741925903E-1));
#475=DIRECTION('',(-1.E0,0.E0,0.E0));
#476=DIRECTION('',(0.E0,1.391731009601E-1,9.902680687416E-1));
#477=AXIS2_PLACEMENT_3D('',#474,#475,#476);
#479=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#480=PRESENTATION_STYLE_ASSIGNMENT((#479));
#481=STYLED_ITEM('',(#480),#478);
#482=DIRECTION('',(0.E0,0.E0,-1.E0));
#483=VECTOR('',#482,3.199483851807E-1);
#484=CARTESIAN_POINT('',(1.024741925903E-1,7.75E-2,1.599741925903E-1));
#485=LINE('',#484,#483);
#486=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#487=PRESENTATION_STYLE_ASSIGNMENT((#486));
#488=STYLED_ITEM('',(#487),#485);
#489=DIRECTION('',(0.E0,0.E0,-1.E0));
#490=VECTOR('',#489,3.199483851807E-1);
#491=CARTESIAN_POINT('',(1.173282136215E-1,6.458759651440E-2,
1.599741925903E-1));
#492=LINE('',#491,#490);
#493=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#494=PRESENTATION_STYLE_ASSIGNMENT((#493));
#495=STYLED_ITEM('',(#494),#492);
#496=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#497=CARTESIAN_POINT('',(1.249997649401E-1,1.000167253793E-2,
-1.675969087249E-1));
#498=CARTESIAN_POINT('',(1.25E-1,1.E-2,-1.675484837122E-1));
#499=CARTESIAN_POINT('',(1.25E-1,1.E-2,-1.675E-1));
#501=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#502=PRESENTATION_STYLE_ASSIGNMENT((#501));
#503=STYLED_ITEM('',(#502),#500);
#504=DIRECTION('',(0.E0,0.E0,-1.E0));
#505=VECTOR('',#504,3.35E-1);
#506=CARTESIAN_POINT('',(1.25E-1,1.E-2,1.675E-1));
#507=LINE('',#506,#505);
#508=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#509=PRESENTATION_STYLE_ASSIGNMENT((#508));
#510=STYLED_ITEM('',(#509),#507);
#511=DIRECTION('',(0.E0,0.E0,-1.E0));
#512=VECTOR('',#511,3.35E-1);
#513=CARTESIAN_POINT('',(1.25E-1,0.E0,1.675E-1));
#514=LINE('',#513,#512);
#515=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#516=PRESENTATION_STYLE_ASSIGNMENT((#515));
#517=STYLED_ITEM('',(#516),#514);
#518=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901645E-6,
1.676452754538E-1));
#519=CARTESIAN_POINT('',(1.249997649401E-1,-1.672537930140E-6,
1.675969087249E-1));
#520=CARTESIAN_POINT('',(1.25E-1,0.E0,1.675484837122E-1));
#521=CARTESIAN_POINT('',(1.25E-1,0.E0,1.675E-1));
#523=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#524=PRESENTATION_STYLE_ASSIGNMENT((#523));
#525=STYLED_ITEM('',(#524),#522);
#526=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#527=VECTOR('',#526,5.565024920858E-2);
#528=CARTESIAN_POINT('',(1.024741925903E-1,-5.458759651440E-2,
-1.748282136215E-1));
#529=LINE('',#528,#527);
#530=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#531=PRESENTATION_STYLE_ASSIGNMENT((#530));
#532=STYLED_ITEM('',(#531),#529);
#533=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#534=VECTOR('',#533,5.565024920858E-2);
#535=CARTESIAN_POINT('',(1.173282136215E-1,-5.458759651440E-2,
-1.599741925903E-1));
#536=LINE('',#535,#534);
#537=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#538=PRESENTATION_STYLE_ASSIGNMENT((#537));
#539=STYLED_ITEM('',(#538),#536);
#540=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#541=DIRECTION('',(0.E0,0.E0,1.E0));
#542=DIRECTION('',(0.E0,-1.E0,0.E0));
#543=AXIS2_PLACEMENT_3D('',#540,#541,#542);
#545=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#546=PRESENTATION_STYLE_ASSIGNMENT((#545));
#547=STYLED_ITEM('',(#546),#544);
#548=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#549=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#550=DIRECTION('',(9.902680687416E-1,-1.391731009601E-1,0.E0));
#551=AXIS2_PLACEMENT_3D('',#548,#549,#550);
#553=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#554=PRESENTATION_STYLE_ASSIGNMENT((#553));
#555=STYLED_ITEM('',(#554),#552);
#556=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#557=DIRECTION('',(-1.E0,0.E0,0.E0));
#558=DIRECTION('',(0.E0,-1.391731009601E-1,-9.902680687416E-1));
#559=AXIS2_PLACEMENT_3D('',#556,#557,#558);
#561=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#562=PRESENTATION_STYLE_ASSIGNMENT((#561));
#563=STYLED_ITEM('',(#562),#560);
#564=DIRECTION('',(0.E0,0.E0,1.E0));
#565=VECTOR('',#564,3.199483851807E-1);
#566=CARTESIAN_POINT('',(1.024741925903E-1,-6.75E-2,-1.599741925903E-1));
#567=LINE('',#566,#565);
#568=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#569=PRESENTATION_STYLE_ASSIGNMENT((#568));
#570=STYLED_ITEM('',(#569),#567);
#571=DIRECTION('',(0.E0,0.E0,1.E0));
#572=VECTOR('',#571,3.199483851807E-1);
#573=CARTESIAN_POINT('',(1.173282136215E-1,-5.458759651440E-2,
-1.599741925903E-1));
#574=LINE('',#573,#572);
#575=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#576=PRESENTATION_STYLE_ASSIGNMENT((#575));
#577=STYLED_ITEM('',(#576),#574);
#578=DIRECTION('',(1.E0,0.E0,0.E0));
#579=VECTOR('',#578,2.049483851807E-1);
#580=CARTESIAN_POINT('',(-1.024741925903E-1,-6.75E-2,-1.599741925903E-1));
#581=LINE('',#580,#579);
#582=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#583=PRESENTATION_STYLE_ASSIGNMENT((#582));
#584=STYLED_ITEM('',(#583),#581);
#585=DIRECTION('',(1.E0,0.E0,0.E0));
#586=VECTOR('',#585,2.049483851807E-1);
#587=CARTESIAN_POINT('',(-1.024741925903E-1,-5.458759651440E-2,
-1.748282136215E-1));
#588=LINE('',#587,#586);
#589=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#590=PRESENTATION_STYLE_ASSIGNMENT((#589));
#591=STYLED_ITEM('',(#590),#588);
#592=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#593=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#594=DIRECTION('',(0.E0,-1.391731009601E-1,-9.902680687416E-1));
#595=AXIS2_PLACEMENT_3D('',#592,#593,#594);
#597=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#598=PRESENTATION_STYLE_ASSIGNMENT((#597));
#599=STYLED_ITEM('',(#598),#596);
#600=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#601=DIRECTION('',(0.E0,0.E0,1.E0));
#602=DIRECTION('',(-9.902680687416E-1,-1.391731009601E-1,0.E0));
#603=AXIS2_PLACEMENT_3D('',#600,#601,#602);
#605=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#606=PRESENTATION_STYLE_ASSIGNMENT((#605));
#607=STYLED_ITEM('',(#606),#604);
#608=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#609=DIRECTION('',(1.E0,0.E0,0.E0));
#610=DIRECTION('',(0.E0,-1.E0,0.E0));
#611=AXIS2_PLACEMENT_3D('',#608,#609,#610);
#613=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#614=PRESENTATION_STYLE_ASSIGNMENT((#613));
#615=STYLED_ITEM('',(#614),#612);
#616=DIRECTION('',(0.E0,0.E0,-1.E0));
#617=VECTOR('',#616,3.199483851807E-1);
#618=CARTESIAN_POINT('',(-1.024741925903E-1,-6.75E-2,1.599741925903E-1));
#619=LINE('',#618,#617);
#620=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#621=PRESENTATION_STYLE_ASSIGNMENT((#620));
#622=STYLED_ITEM('',(#621),#619);
#623=DIRECTION('',(0.E0,0.E0,-1.E0));
#624=VECTOR('',#623,3.199483851807E-1);
#625=CARTESIAN_POINT('',(-1.173282136215E-1,-5.458759651440E-2,
1.599741925903E-1));
#626=LINE('',#625,#624);
#627=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#628=PRESENTATION_STYLE_ASSIGNMENT((#627));
#629=STYLED_ITEM('',(#628),#626);
#630=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#631=DIRECTION('',(0.E0,0.E0,-1.E0));
#632=DIRECTION('',(0.E0,-1.E0,0.E0));
#633=AXIS2_PLACEMENT_3D('',#630,#631,#632);
#635=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#636=PRESENTATION_STYLE_ASSIGNMENT((#635));
#637=STYLED_ITEM('',(#636),#634);
#638=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#639=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#640=DIRECTION('',(-9.902680687416E-1,-1.391731009601E-1,0.E0));
#641=AXIS2_PLACEMENT_3D('',#638,#639,#640);
#643=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#644=PRESENTATION_STYLE_ASSIGNMENT((#643));
#645=STYLED_ITEM('',(#644),#642);
#646=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#647=DIRECTION('',(1.E0,0.E0,0.E0));
#648=DIRECTION('',(0.E0,-1.391731009601E-1,9.902680687416E-1));
#649=AXIS2_PLACEMENT_3D('',#646,#647,#648);
#651=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#652=PRESENTATION_STYLE_ASSIGNMENT((#651));
#653=STYLED_ITEM('',(#652),#650);
#654=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#655=VECTOR('',#654,5.565024920858E-2);
#656=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901612E-6,
1.676452754538E-1));
#657=LINE('',#656,#655);
#658=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#659=PRESENTATION_STYLE_ASSIGNMENT((#658));
#660=STYLED_ITEM('',(#659),#657);
#661=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#662=VECTOR('',#661,5.565024920858E-2);
#663=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901813E-6,
1.824992964849E-1));
#664=LINE('',#663,#662);
#665=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#666=PRESENTATION_STYLE_ASSIGNMENT((#665));
#667=STYLED_ITEM('',(#666),#664);
#668=DIRECTION('',(0.E0,-1.E0,0.E0));
#669=VECTOR('',#668,1.E-2);
#670=CARTESIAN_POINT('',(-1.25E-1,1.E-2,1.675E-1));
#671=LINE('',#670,#669);
#672=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#673=PRESENTATION_STYLE_ASSIGNMENT((#672));
#674=STYLED_ITEM('',(#673),#671);
#675=DIRECTION('',(0.E0,-1.E0,0.E0));
#676=VECTOR('',#675,1.E-2);
#677=CARTESIAN_POINT('',(-1.1E-1,1.E-2,1.825E-1));
#678=LINE('',#677,#676);
#679=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#680=PRESENTATION_STYLE_ASSIGNMENT((#679));
#681=STYLED_ITEM('',(#680),#678);
#682=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901756E-6,
1.824992964849E-1));
#683=CARTESIAN_POINT('',(-1.100969087249E-1,-1.672537930253E-6,
1.824997649401E-1));
#684=CARTESIAN_POINT('',(-1.100484837122E-1,0.E0,1.825E-1));
#685=CARTESIAN_POINT('',(-1.1E-1,0.E0,1.825E-1));
#687=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#688=PRESENTATION_STYLE_ASSIGNMENT((#687));
#689=STYLED_ITEM('',(#688),#686);
#690=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901612E-6,
1.676452754538E-1));
#691=CARTESIAN_POINT('',(-1.249885305161E-1,-8.160961857793E-5,
1.687568325288E-1));
#692=CARTESIAN_POINT('',(-1.247178837341E-1,-2.171597740328E-4,
1.709753240751E-1));
#693=CARTESIAN_POINT('',(-1.236003198989E-1,-3.578710173970E-4,
1.741148970902E-1));
#694=CARTESIAN_POINT('',(-1.218038994189E-1,-4.304059113846E-4,
1.769536381267E-1));
#695=CARTESIAN_POINT('',(-1.194536381267E-1,-4.304059113846E-4,
1.793038994189E-1));
#696=CARTESIAN_POINT('',(-1.166148970902E-1,-3.578710173971E-4,
1.811003198989E-1));
#697=CARTESIAN_POINT('',(-1.134753240751E-1,-2.171597740326E-4,
1.822178837341E-1));
#698=CARTESIAN_POINT('',(-1.112568325288E-1,-8.160961857810E-5,
1.824885305161E-1));
#699=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901813E-6,
1.824992964849E-1));
#701=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#702=PRESENTATION_STYLE_ASSIGNMENT((#701));
#703=STYLED_ITEM('',(#702),#700);
#704=CARTESIAN_POINT('',(-1.25E-1,0.E0,1.675E-1));
#705=CARTESIAN_POINT('',(-1.25E-1,0.E0,1.675484837122E-1));
#706=CARTESIAN_POINT('',(-1.249997649401E-1,-1.672537930148E-6,
1.675969087249E-1));
#707=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901645E-6,
1.676452754538E-1));
#709=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#710=PRESENTATION_STYLE_ASSIGNMENT((#709));
#711=STYLED_ITEM('',(#710),#708);
#712=DIRECTION('',(1.E0,0.E0,0.E0));
#713=VECTOR('',#712,2.2E-1);
#714=CARTESIAN_POINT('',(-1.1E-1,1.E-2,1.825E-1));
#715=LINE('',#714,#713);
#716=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#717=PRESENTATION_STYLE_ASSIGNMENT((#716));
#718=STYLED_ITEM('',(#717),#715);
#719=DIRECTION('',(0.E0,-1.E0,0.E0));
#720=VECTOR('',#719,1.E-2);
#721=CARTESIAN_POINT('',(1.1E-1,1.E-2,1.825E-1));
#722=LINE('',#721,#720);
#723=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#724=PRESENTATION_STYLE_ASSIGNMENT((#723));
#725=STYLED_ITEM('',(#724),#722);
#726=CARTESIAN_POINT('',(1.1E-1,1.E-2,1.825E-1));
#727=CARTESIAN_POINT('',(1.100484837122E-1,1.E-2,1.825E-1));
#728=CARTESIAN_POINT('',(1.100969087249E-1,1.000167253793E-2,
1.824997649401E-1));
#729=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#731=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#732=PRESENTATION_STYLE_ASSIGNMENT((#731));
#733=STYLED_ITEM('',(#732),#730);
#734=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#735=CARTESIAN_POINT('',(1.112568325288E-1,1.008160961858E-2,
1.824885305161E-1));
#736=CARTESIAN_POINT('',(1.134753240751E-1,1.021715977403E-2,
1.822178837341E-1));
#737=CARTESIAN_POINT('',(1.166148970902E-1,1.035787101740E-2,
1.811003198989E-1));
#738=CARTESIAN_POINT('',(1.194536381267E-1,1.043040591138E-2,
1.793038994189E-1));
#739=CARTESIAN_POINT('',(1.218038994189E-1,1.043040591138E-2,
1.769536381267E-1));
#740=CARTESIAN_POINT('',(1.236003198989E-1,1.035787101740E-2,
1.741148970902E-1));
#741=CARTESIAN_POINT('',(1.247178837341E-1,1.021715977403E-2,
1.709753240751E-1));
#742=CARTESIAN_POINT('',(1.249885305161E-1,1.008160961858E-2,
1.687568325288E-1));
#743=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#745=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#746=PRESENTATION_STYLE_ASSIGNMENT((#745));
#747=STYLED_ITEM('',(#746),#744);
#748=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#749=CARTESIAN_POINT('',(1.249997649401E-1,1.000167253793E-2,
1.675969087249E-1));
#750=CARTESIAN_POINT('',(1.25E-1,1.E-2,1.675484837122E-1));
#751=CARTESIAN_POINT('',(1.25E-1,1.E-2,1.675E-1));
#753=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#754=PRESENTATION_STYLE_ASSIGNMENT((#753));
#755=STYLED_ITEM('',(#754),#752);
#756=DIRECTION('',(0.E0,-1.E0,0.E0));
#757=VECTOR('',#756,1.E-2);
#758=CARTESIAN_POINT('',(1.25E-1,1.E-2,1.675E-1));
#759=LINE('',#758,#757);
#760=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#761=PRESENTATION_STYLE_ASSIGNMENT((#760));
#762=STYLED_ITEM('',(#761),#759);
#763=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#764=VECTOR('',#763,5.565024920858E-2);
#765=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#766=LINE('',#765,#764);
#767=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#768=PRESENTATION_STYLE_ASSIGNMENT((#767));
#769=STYLED_ITEM('',(#768),#766);
#770=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#771=VECTOR('',#770,5.565024920858E-2);
#772=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#773=LINE('',#772,#771);
#774=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#775=PRESENTATION_STYLE_ASSIGNMENT((#774));
#776=STYLED_ITEM('',(#775),#773);
#777=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#778=VECTOR('',#777,5.565024920858E-2);
#779=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901814E-6,
1.824992964849E-1));
#780=LINE('',#779,#778);
#781=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#782=PRESENTATION_STYLE_ASSIGNMENT((#781));
#783=STYLED_ITEM('',(#782),#780);
#784=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901814E-6,
1.824992964849E-1));
#785=CARTESIAN_POINT('',(1.112568325288E-1,-8.160961857811E-5,
1.824885305161E-1));
#786=CARTESIAN_POINT('',(1.134753240751E-1,-2.171597740326E-4,
1.822178837341E-1));
#787=CARTESIAN_POINT('',(1.166148970902E-1,-3.578710173970E-4,
1.811003198989E-1));
#788=CARTESIAN_POINT('',(1.194536381267E-1,-4.304059113845E-4,
1.793038994189E-1));
#789=CARTESIAN_POINT('',(1.218038994189E-1,-4.304059113847E-4,
1.769536381267E-1));
#790=CARTESIAN_POINT('',(1.236003198989E-1,-3.578710173969E-4,
1.741148970902E-1));
#791=CARTESIAN_POINT('',(1.247178837341E-1,-2.171597740328E-4,
1.709753240751E-1));
#792=CARTESIAN_POINT('',(1.249885305161E-1,-8.160961857793E-5,
1.687568325288E-1));
#793=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901613E-6,
1.676452754538E-1));
#795=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#796=PRESENTATION_STYLE_ASSIGNMENT((#795));
#797=STYLED_ITEM('',(#796),#794);
#798=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#799=VECTOR('',#798,5.565024920858E-2);
#800=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901613E-6,
1.676452754538E-1));
#801=LINE('',#800,#799);
#802=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#803=PRESENTATION_STYLE_ASSIGNMENT((#802));
#804=STYLED_ITEM('',(#803),#801);
#805=DIRECTION('',(1.E0,0.E0,0.E0));
#806=VECTOR('',#805,2.2E-1);
#807=CARTESIAN_POINT('',(-1.1E-1,0.E0,1.825E-1));
#808=LINE('',#807,#806);
#809=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#810=PRESENTATION_STYLE_ASSIGNMENT((#809));
#811=STYLED_ITEM('',(#810),#808);
#812=CARTESIAN_POINT('',(1.1E-1,0.E0,1.825E-1));
#813=CARTESIAN_POINT('',(1.100484837122E-1,0.E0,1.825E-1));
#814=CARTESIAN_POINT('',(1.100969087249E-1,-1.672537930258E-6,
1.824997649401E-1));
#815=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901756E-6,
1.824992964849E-1));
#817=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#818=PRESENTATION_STYLE_ASSIGNMENT((#817));
#819=STYLED_ITEM('',(#818),#816);
#820=DIRECTION('',(-1.E0,0.E0,0.E0));
#821=VECTOR('',#820,2.049483851807E-1);
#822=CARTESIAN_POINT('',(1.024741925903E-1,-6.75E-2,1.599741925903E-1));
#823=LINE('',#822,#821);
#824=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#825=PRESENTATION_STYLE_ASSIGNMENT((#824));
#826=STYLED_ITEM('',(#825),#823);
#827=DIRECTION('',(-1.E0,0.E0,0.E0));
#828=VECTOR('',#827,2.049483851807E-1);
#829=CARTESIAN_POINT('',(1.024741925903E-1,-5.458759651440E-2,
1.748282136215E-1));
#830=LINE('',#829,#828);
#831=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#832=PRESENTATION_STYLE_ASSIGNMENT((#831));
#833=STYLED_ITEM('',(#832),#830);
#834=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#835=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#836=DIRECTION('',(0.E0,-1.391731009601E-1,9.902680687416E-1));
#837=AXIS2_PLACEMENT_3D('',#834,#835,#836);
#839=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#840=PRESENTATION_STYLE_ASSIGNMENT((#839));
#841=STYLED_ITEM('',(#840),#838);
#842=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#843=DIRECTION('',(0.E0,0.E0,-1.E0));
#844=DIRECTION('',(9.902680687416E-1,-1.391731009601E-1,0.E0));
#845=AXIS2_PLACEMENT_3D('',#842,#843,#844);
#847=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#848=PRESENTATION_STYLE_ASSIGNMENT((#847));
#849=STYLED_ITEM('',(#848),#846);
#850=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#851=DIRECTION('',(-1.E0,0.E0,0.E0));
#852=DIRECTION('',(0.E0,-1.E0,0.E0));
#853=AXIS2_PLACEMENT_3D('',#850,#851,#852);
#855=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#856=PRESENTATION_STYLE_ASSIGNMENT((#855));
#857=STYLED_ITEM('',(#856),#854);
#858=DIRECTION('',(-1.E0,0.E0,0.E0));
#859=VECTOR('',#858,7.974562025192E-2);
#860=CARTESIAN_POINT('',(-2.272857233843E-2,7.75E-2,-1.599741925903E-1));
#861=LINE('',#860,#859);
#862=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#863=PRESENTATION_STYLE_ASSIGNMENT((#862));
#864=STYLED_ITEM('',(#863),#861);
#865=DIRECTION('',(-1.E0,0.E0,0.E0));
#866=VECTOR('',#865,7.140743070948E-2);
#867=CARTESIAN_POINT('',(-3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#868=LINE('',#867,#866);
#869=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#870=PRESENTATION_STYLE_ASSIGNMENT((#869));
#871=STYLED_ITEM('',(#870),#868);
#872=CARTESIAN_POINT('',(-2.272857233843E-2,7.75E-2,-1.599741925903E-1));
#873=CARTESIAN_POINT('',(-2.371520384649E-2,7.75E-2,-1.609697052126E-1));
#874=CARTESIAN_POINT('',(-2.545161209534E-2,7.729703470696E-2,
-1.629857440997E-1));
#875=CARTESIAN_POINT('',(-2.738839367688E-2,7.641113334883E-2,
-1.658465495737E-1));
#876=CARTESIAN_POINT('',(-2.882068760924E-2,7.496158940455E-2,
-1.685027217854E-1));
#877=CARTESIAN_POINT('',(-2.982098178530E-2,7.301351797913E-2,
-1.708189811574E-1));
#878=CARTESIAN_POINT('',(-3.049689984033E-2,7.055743036365E-2,
-1.727550567768E-1));
#879=CARTESIAN_POINT('',(-3.089726041498E-2,6.774084956721E-2,
-1.741474418960E-1));
#880=CARTESIAN_POINT('',(-3.102966853247E-2,6.565638802923E-2,
-1.746780047699E-1));
#881=CARTESIAN_POINT('',(-3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#883=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#884=PRESENTATION_STYLE_ASSIGNMENT((#883));
#885=STYLED_ITEM('',(#884),#882);
#886=DIRECTION('',(0.E0,-1.E0,0.E0));
#887=VECTOR('',#886,5.E-3);
#888=CARTESIAN_POINT('',(-7.65E-2,7.75E-2,-1.2E-1));
#889=LINE('',#888,#887);
#890=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#891=PRESENTATION_STYLE_ASSIGNMENT((#890));
#892=STYLED_ITEM('',(#891),#889);
#893=DIRECTION('',(0.E0,-1.E0,0.E0));
#894=VECTOR('',#893,5.E-3);
#895=CARTESIAN_POINT('',(-1.015E-1,7.75E-2,-1.2E-1));
#896=LINE('',#895,#894);
#897=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#898=PRESENTATION_STYLE_ASSIGNMENT((#897));
#899=STYLED_ITEM('',(#898),#896);
#900=CARTESIAN_POINT('',(-8.9E-2,7.25E-2,-1.2E-1));
#901=DIRECTION('',(0.E0,-1.E0,0.E0));
#902=DIRECTION('',(1.E0,0.E0,0.E0));
#903=AXIS2_PLACEMENT_3D('',#900,#901,#902);
#905=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#906=PRESENTATION_STYLE_ASSIGNMENT((#905));
#907=STYLED_ITEM('',(#906),#904);
#908=CARTESIAN_POINT('',(-8.9E-2,7.25E-2,-1.2E-1));
#909=DIRECTION('',(0.E0,-1.E0,0.E0));
#910=DIRECTION('',(-1.E0,0.E0,0.E0));
#911=AXIS2_PLACEMENT_3D('',#908,#909,#910);
#913=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#914=PRESENTATION_STYLE_ASSIGNMENT((#913));
#915=STYLED_ITEM('',(#914),#912);
#916=CARTESIAN_POINT('',(0.E0,5.75E-2,-1.825E-1));
#917=DIRECTION('',(0.E0,1.E0,0.E0));
#918=DIRECTION('',(-9.779977791260E-1,0.E0,2.086153015114E-1));
#919=AXIS2_PLACEMENT_3D('',#916,#917,#918);
#921=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#3);
#922=PRESENTATION_STYLE_ASSIGNMENT((#921));
#923=STYLED_ITEM('',(#922),#920);
#924=CARTESIAN_POINT('',(-3.129592893203E-2,5.75E-2,-1.758243103516E-1));
#925=CARTESIAN_POINT('',(3.129592893203E-2,5.75E-2,-1.758243103516E-1));
#926=VERTEX_POINT('',#924);
#927=VERTEX_POINT('',#925);
#928=CARTESIAN_POINT('',(-1.024741925903E-1,7.75E-2,-1.599741925903E-1));
#929=CARTESIAN_POINT('',(-1.024741925903E-1,7.75E-2,1.599741925903E-1));
#930=VERTEX_POINT('',#928);
#931=VERTEX_POINT('',#929);
#932=CARTESIAN_POINT('',(-1.173282136215E-1,6.458759651440E-2,
-1.599741925903E-1));
#933=CARTESIAN_POINT('',(-1.173282136215E-1,6.458759651440E-2,
1.599741925903E-1));
#934=VERTEX_POINT('',#932);
#935=VERTEX_POINT('',#933);
#936=CARTESIAN_POINT('',(1.024741925903E-1,7.75E-2,1.599741925903E-1));
#937=CARTESIAN_POINT('',(1.024741925903E-1,7.75E-2,-1.599741925903E-1));
#938=VERTEX_POINT('',#936);
#939=VERTEX_POINT('',#937);
#940=CARTESIAN_POINT('',(1.173282136215E-1,6.458759651440E-2,
1.599741925903E-1));
#941=CARTESIAN_POINT('',(1.173282136215E-1,6.458759651440E-2,
-1.599741925903E-1));
#942=VERTEX_POINT('',#940);
#943=VERTEX_POINT('',#941);
#944=CARTESIAN_POINT('',(1.024741925903E-1,-6.75E-2,-1.599741925903E-1));
#945=CARTESIAN_POINT('',(1.024741925903E-1,-6.75E-2,1.599741925903E-1));
#946=VERTEX_POINT('',#944);
#947=VERTEX_POINT('',#945);
#948=CARTESIAN_POINT('',(1.173282136215E-1,-5.458759651440E-2,
-1.599741925903E-1));
#949=CARTESIAN_POINT('',(1.173282136215E-1,-5.458759651440E-2,
1.599741925903E-1));
#950=VERTEX_POINT('',#948);
#951=VERTEX_POINT('',#949);
#952=CARTESIAN_POINT('',(-1.024741925903E-1,-6.75E-2,1.599741925903E-1));
#953=CARTESIAN_POINT('',(-1.024741925903E-1,-6.75E-2,-1.599741925903E-1));
#954=VERTEX_POINT('',#952);
#955=VERTEX_POINT('',#953);
#956=CARTESIAN_POINT('',(-1.173282136215E-1,-5.458759651440E-2,
1.599741925903E-1));
#957=CARTESIAN_POINT('',(-1.173282136215E-1,-5.458759651440E-2,
-1.599741925903E-1));
#958=VERTEX_POINT('',#956);
#959=VERTEX_POINT('',#957);
#960=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#961=CARTESIAN_POINT('',(-1.024741925903E-1,6.458759651440E-2,
1.748282136215E-1));
#962=VERTEX_POINT('',#960);
#963=VERTEX_POINT('',#961);
#964=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#965=VERTEX_POINT('',#964);
#966=CARTESIAN_POINT('',(-1.25E-1,1.E-2,1.675E-1));
#967=CARTESIAN_POINT('',(-1.25E-1,0.E0,1.675E-1));
#968=VERTEX_POINT('',#966);
#969=VERTEX_POINT('',#967);
#970=CARTESIAN_POINT('',(-1.1E-1,1.E-2,1.825E-1));
#971=CARTESIAN_POINT('',(-1.1E-1,0.E0,1.825E-1));
#972=VERTEX_POINT('',#970);
#973=VERTEX_POINT('',#971);
#974=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901612E-6,
1.676452754538E-1));
#975=VERTEX_POINT('',#974);
#976=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901813E-6,
1.824992964849E-1));
#977=CARTESIAN_POINT('',(-1.024741925903E-1,-5.458759651440E-2,
1.748282136215E-1));
#978=VERTEX_POINT('',#976);
#979=VERTEX_POINT('',#977);
#980=CARTESIAN_POINT('',(-1.101452754538E-1,-5.005769901814E-6,
-1.824992964849E-1));
#981=CARTESIAN_POINT('',(-1.024741925903E-1,-5.458759651440E-2,
-1.748282136215E-1));
#982=VERTEX_POINT('',#980);
#983=VERTEX_POINT('',#981);
#984=CARTESIAN_POINT('',(-1.249992964849E-1,-5.005769901613E-6,
-1.676452754538E-1));
#985=VERTEX_POINT('',#984);
#986=CARTESIAN_POINT('',(-1.1E-1,1.E-2,-1.825E-1));
#987=CARTESIAN_POINT('',(-1.1E-1,0.E0,-1.825E-1));
#988=VERTEX_POINT('',#986);
#989=VERTEX_POINT('',#987);
#990=CARTESIAN_POINT('',(-1.25E-1,1.E-2,-1.675E-1));
#991=CARTESIAN_POINT('',(-1.25E-1,0.E0,-1.675E-1));
#992=VERTEX_POINT('',#990);
#993=VERTEX_POINT('',#991);
#994=CARTESIAN_POINT('',(-1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#995=VERTEX_POINT('',#994);
#996=CARTESIAN_POINT('',(-1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#997=CARTESIAN_POINT('',(-1.024741925903E-1,6.458759651440E-2,
-1.748282136215E-1));
#998=VERTEX_POINT('',#996);
#999=VERTEX_POINT('',#997);
#1000=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
-1.824992964849E-1));
#1001=CARTESIAN_POINT('',(1.024741925903E-1,6.458759651440E-2,
-1.748282136215E-1));
#1002=VERTEX_POINT('',#1000);
#1003=VERTEX_POINT('',#1001);
#1004=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
-1.676452754538E-1));
#1005=VERTEX_POINT('',#1004);
#1006=CARTESIAN_POINT('',(1.1E-1,0.E0,-1.825E-1));
#1007=CARTESIAN_POINT('',(1.1E-1,1.E-2,-1.825E-1));
#1008=VERTEX_POINT('',#1006);
#1009=VERTEX_POINT('',#1007);
#1010=CARTESIAN_POINT('',(1.25E-1,0.E0,-1.675E-1));
#1011=CARTESIAN_POINT('',(1.25E-1,1.E-2,-1.675E-1));
#1012=VERTEX_POINT('',#1010);
#1013=VERTEX_POINT('',#1011);
#1014=CARTESIAN_POINT('',(1.024741925903E-1,-5.458759651440E-2,
-1.748282136215E-1));
#1015=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901825E-6,
-1.824992964849E-1));
#1016=VERTEX_POINT('',#1014);
#1017=VERTEX_POINT('',#1015);
#1018=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901603E-6,
-1.676452754538E-1));
#1019=VERTEX_POINT('',#1018);
#1020=CARTESIAN_POINT('',(1.101452754538E-1,-5.005769901814E-6,
1.824992964849E-1));
#1021=CARTESIAN_POINT('',(1.024741925903E-1,-5.458759651440E-2,
1.748282136215E-1));
#1022=VERTEX_POINT('',#1020);
#1023=VERTEX_POINT('',#1021);
#1024=CARTESIAN_POINT('',(1.249992964849E-1,-5.005769901613E-6,
1.676452754538E-1));
#1025=VERTEX_POINT('',#1024);
#1026=CARTESIAN_POINT('',(1.1E-1,1.E-2,1.825E-1));
#1027=CARTESIAN_POINT('',(1.1E-1,0.E0,1.825E-1));
#1028=VERTEX_POINT('',#1026);
#1029=VERTEX_POINT('',#1027);
#1030=CARTESIAN_POINT('',(1.25E-1,1.E-2,1.675E-1));
#1031=CARTESIAN_POINT('',(1.25E-1,0.E0,1.675E-1));
#1032=VERTEX_POINT('',#1030);
#1033=VERTEX_POINT('',#1031);
#1034=CARTESIAN_POINT('',(1.249992964849E-1,1.000500576990E-2,
1.676452754538E-1));
#1035=VERTEX_POINT('',#1034);
#1036=CARTESIAN_POINT('',(1.101452754538E-1,1.000500576990E-2,
1.824992964849E-1));
#1037=CARTESIAN_POINT('',(1.024741925903E-1,6.458759651440E-2,
1.748282136215E-1));
#1038=VERTEX_POINT('',#1036);
#1039=VERTEX_POINT('',#1037);
#1040=CARTESIAN_POINT('',(-2.272857233843E-2,7.75E-2,-1.599741925903E-1));
#1041=VERTEX_POINT('',#1040);
#1042=CARTESIAN_POINT('',(-3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#1043=VERTEX_POINT('',#1042);
#1044=CARTESIAN_POINT('',(2.272857233843E-2,7.75E-2,-1.599741925903E-1));
#1045=VERTEX_POINT('',#1044);
#1046=CARTESIAN_POINT('',(3.106676188086E-2,6.458759651440E-2,
-1.748282136215E-1));
#1047=VERTEX_POINT('',#1046);
#1048=CARTESIAN_POINT('',(-7.65E-2,7.25E-2,-1.2E-1));
#1049=CARTESIAN_POINT('',(-1.015E-1,7.25E-2,-1.2E-1));
#1050=VERTEX_POINT('',#1048);
#1051=VERTEX_POINT('',#1049);
#1052=CARTESIAN_POINT('',(-7.65E-2,7.75E-2,-1.2E-1));
#1053=CARTESIAN_POINT('',(-1.015E-1,7.75E-2,-1.2E-1));
#1054=VERTEX_POINT('',#1052);
#1055=VERTEX_POINT('',#1053);
#1056=CARTESIAN_POINT('',(1.25E-1,0.E0,-1.825E-1));
#1057=DIRECTION('',(0.E0,0.E0,-1.E0));
#1058=DIRECTION('',(-1.E0,0.E0,0.E0));
#1059=AXIS2_PLACEMENT_3D('',#1056,#1057,#1058);
#1060=PLANE('',#1059);
#1062=ORIENTED_EDGE('',*,*,#1061,.T.);
#1064=ORIENTED_EDGE('',*,*,#1063,.F.);
#1066=ORIENTED_EDGE('',*,*,#1065,.T.);
#1068=ORIENTED_EDGE('',*,*,#1067,.T.);
#1069=EDGE_LOOP('',(#1062,#1064,#1066,#1068));
#1070=FACE_OUTER_BOUND('',#1069,.F.);
#1072=CARTESIAN_POINT('',(-1.1E-1,1.E-2,-1.675E-1));
#1073=DIRECTION('',(0.E0,1.E0,0.E0));
#1074=DIRECTION('',(0.E0,0.E0,-1.E0));
#1075=AXIS2_PLACEMENT_3D('',#1072,#1073,#1074);
#1076=CYLINDRICAL_SURFACE('',#1075,1.5E-2);
#1077=ORIENTED_EDGE('',*,*,#1061,.F.);
#1079=ORIENTED_EDGE('',*,*,#1078,.T.);
#1081=ORIENTED_EDGE('',*,*,#1080,.T.);
#1083=ORIENTED_EDGE('',*,*,#1082,.T.);
#1085=ORIENTED_EDGE('',*,*,#1084,.T.);
#1087=ORIENTED_EDGE('',*,*,#1086,.F.);
#1089=ORIENTED_EDGE('',*,*,#1088,.F.);
#1091=ORIENTED_EDGE('',*,*,#1090,.F.);
#1092=EDGE_LOOP('',(#1077,#1079,#1081,#1083,#1085,#1087,#1089,#1091));
#1093=FACE_OUTER_BOUND('',#1092,.F.);
#1095=CARTESIAN_POINT('',(1.25E-1,9.731931258430E-5,-1.838917310096E-1));
#1096=DIRECTION('',(0.E0,1.391731009601E-1,-9.902680687416E-1));
#1097=DIRECTION('',(-1.E0,0.E0,0.E0));
#1098=AXIS2_PLACEMENT_3D('',#1095,#1096,#1097);
#1099=PLANE('',#1098);
#1101=ORIENTED_EDGE('',*,*,#1100,.T.);
#1103=ORIENTED_EDGE('',*,*,#1102,.T.);
#1105=ORIENTED_EDGE('',*,*,#1104,.F.);
#1106=ORIENTED_EDGE('',*,*,#1078,.F.);
#1107=ORIENTED_EDGE('',*,*,#1067,.F.);
#1109=ORIENTED_EDGE('',*,*,#1108,.T.);
#1111=ORIENTED_EDGE('',*,*,#1110,.T.);
#1113=ORIENTED_EDGE('',*,*,#1112,.T.);
#1115=ORIENTED_EDGE('',*,*,#1114,.T.);
#1117=ORIENTED_EDGE('',*,*,#1116,.T.);
#1118=EDGE_LOOP('',(#1101,#1103,#1105,#1106,#1107,#1109,#1111,#1113,#1115,
#1117));
#1119=FACE_OUTER_BOUND('',#1118,.F.);
#1121=CARTESIAN_POINT('',(0.E0,7.75E-2,-1.825E-1));
#1122=DIRECTION('',(0.E0,1.E0,0.E0));
#1123=DIRECTION('',(1.E0,0.E0,0.E0));
#1124=AXIS2_PLACEMENT_3D('',#1121,#1122,#1123);
#1125=CYLINDRICAL_SURFACE('',#1124,3.2E-2);
#1127=ORIENTED_EDGE('',*,*,#1126,.F.);
#1129=ORIENTED_EDGE('',*,*,#1128,.T.);
#1130=ORIENTED_EDGE('',*,*,#1100,.F.);
#1132=ORIENTED_EDGE('',*,*,#1131,.T.);
#1133=ORIENTED_EDGE('',*,*,#1114,.F.);
#1135=ORIENTED_EDGE('',*,*,#1134,.F.);
#1136=EDGE_LOOP('',(#1127,#1129,#1130,#1132,#1133,#1135));
#1137=FACE_OUTER_BOUND('',#1136,.F.);
#1139=CARTESIAN_POINT('',(0.E0,7.75E-2,0.E0));
#1140=DIRECTION('',(0.E0,1.E0,0.E0));
#1141=DIRECTION('',(1.E0,0.E0,0.E0));
#1142=AXIS2_PLACEMENT_3D('',#1139,#1140,#1141);
#1143=PLANE('',#1142);
#1144=ORIENTED_EDGE('',*,*,#1126,.T.);
#1146=ORIENTED_EDGE('',*,*,#1145,.F.);
#1148=ORIENTED_EDGE('',*,*,#1147,.F.);
#1150=ORIENTED_EDGE('',*,*,#1149,.F.);
#1152=ORIENTED_EDGE('',*,*,#1151,.F.);
#1154=ORIENTED_EDGE('',*,*,#1153,.F.);
#1155=EDGE_LOOP('',(#1144,#1146,#1148,#1150,#1152,#1154));
#1156=FACE_OUTER_BOUND('',#1155,.F.);
#1158=ORIENTED_EDGE('',*,*,#1157,.F.);
#1160=ORIENTED_EDGE('',*,*,#1159,.F.);
#1161=EDGE_LOOP('',(#1158,#1160));
#1162=FACE_BOUND('',#1161,.F.);
#1164=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#1165=DIRECTION('',(-1.E0,0.E0,0.E0));
#1166=DIRECTION('',(0.E0,1.E0,0.E0));
#1167=AXIS2_PLACEMENT_3D('',#1164,#1165,#1166);
#1168=CYLINDRICAL_SURFACE('',#1167,1.5E-2);
#1169=ORIENTED_EDGE('',*,*,#1145,.T.);
#1170=ORIENTED_EDGE('',*,*,#1134,.T.);
#1171=ORIENTED_EDGE('',*,*,#1112,.F.);
#1173=ORIENTED_EDGE('',*,*,#1172,.F.);
#1174=EDGE_LOOP('',(#1169,#1170,#1171,#1173));
#1175=FACE_OUTER_BOUND('',#1174,.F.);
#1177=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#1178=DIRECTION('',(0.E0,-6.560590289905E-1,-7.547095802228E-1));
#1179=DIRECTION('',(0.E0,7.547095802228E-1,-6.560590289905E-1));
#1180=AXIS2_PLACEMENT_3D('',#1177,#1178,#1179);
#1181=SPHERICAL_SURFACE('',#1180,1.5E-2);
#1183=ORIENTED_EDGE('',*,*,#1182,.T.);
#1185=ORIENTED_EDGE('',*,*,#1184,.T.);
#1186=ORIENTED_EDGE('',*,*,#1172,.T.);
#1187=EDGE_LOOP('',(#1183,#1185,#1186));
#1188=FACE_OUTER_BOUND('',#1187,.F.);
#1190=CARTESIAN_POINT('',(1.101459789689E-1,7.912403485599E-3,
-1.676459789689E-1));
#1191=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#1192=DIRECTION('',(0.E0,1.391731009601E-1,-9.902680687416E-1));
#1193=AXIS2_PLACEMENT_3D('',#1190,#1191,#1192);
#1194=CYLINDRICAL_SURFACE('',#1193,1.5E-2);
#1195=ORIENTED_EDGE('',*,*,#1110,.F.);
#1197=ORIENTED_EDGE('',*,*,#1196,.T.);
#1199=ORIENTED_EDGE('',*,*,#1198,.T.);
#1200=ORIENTED_EDGE('',*,*,#1182,.F.);
#1201=EDGE_LOOP('',(#1195,#1197,#1199,#1200));
#1202=FACE_OUTER_BOUND('',#1201,.F.);
#1204=CARTESIAN_POINT('',(1.1E-1,0.E0,-1.675E-1));
#1205=DIRECTION('',(0.E0,-1.E0,0.E0));
#1206=DIRECTION('',(0.E0,0.E0,-1.E0));
#1207=AXIS2_PLACEMENT_3D('',#1204,#1205,#1206);
#1208=CYLINDRICAL_SURFACE('',#1207,1.5E-2);
#1209=ORIENTED_EDGE('',*,*,#1065,.F.);
#1211=ORIENTED_EDGE('',*,*,#1210,.T.);
#1213=ORIENTED_EDGE('',*,*,#1212,.T.);
#1215=ORIENTED_EDGE('',*,*,#1214,.T.);
#1217=ORIENTED_EDGE('',*,*,#1216,.T.);
#1219=ORIENTED_EDGE('',*,*,#1218,.F.);
#1220=ORIENTED_EDGE('',*,*,#1196,.F.);
#1221=ORIENTED_EDGE('',*,*,#1108,.F.);
#1222=EDGE_LOOP('',(#1209,#1211,#1213,#1215,#1217,#1219,#1220,#1221));
#1223=FACE_OUTER_BOUND('',#1222,.F.);
#1225=CARTESIAN_POINT('',(1.25E-1,0.E0,-1.825E-1));
#1226=DIRECTION('',(0.E0,-1.391731009601E-1,-9.902680687416E-1));
#1227=DIRECTION('',(-1.E0,0.E0,0.E0));
#1228=AXIS2_PLACEMENT_3D('',#1225,#1226,#1227);
#1229=PLANE('',#1228);
#1231=ORIENTED_EDGE('',*,*,#1230,.T.);
#1233=ORIENTED_EDGE('',*,*,#1232,.T.);
#1235=ORIENTED_EDGE('',*,*,#1234,.T.);
#1236=ORIENTED_EDGE('',*,*,#1210,.F.);
#1237=ORIENTED_EDGE('',*,*,#1063,.T.);
#1238=ORIENTED_EDGE('',*,*,#1090,.T.);
#1239=EDGE_LOOP('',(#1231,#1233,#1235,#1236,#1237,#1238));
#1240=FACE_OUTER_BOUND('',#1239,.F.);
#1242=CARTESIAN_POINT('',(-1.101459789689E-1,2.087596514401E-3,
-1.676459789689E-1));
#1243=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,-1.378445374916E-1));
#1244=DIRECTION('',(0.E0,-1.391731009601E-1,-9.902680687416E-1));
#1245=AXIS2_PLACEMENT_3D('',#1242,#1243,#1244);
#1246=CYLINDRICAL_SURFACE('',#1245,1.5E-2);
#1247=ORIENTED_EDGE('',*,*,#1230,.F.);
#1248=ORIENTED_EDGE('',*,*,#1088,.T.);
#1250=ORIENTED_EDGE('',*,*,#1249,.T.);
#1252=ORIENTED_EDGE('',*,*,#1251,.F.);
#1253=EDGE_LOOP('',(#1247,#1248,#1250,#1252));
#1254=FACE_OUTER_BOUND('',#1253,.F.);
#1256=CARTESIAN_POINT('',(-1.25E-1,0.E0,-1.825E-1));
#1257=DIRECTION('',(-9.902680687416E-1,-1.391731009601E-1,0.E0));
#1258=DIRECTION('',(0.E0,0.E0,1.E0));
#1259=AXIS2_PLACEMENT_3D('',#1256,#1257,#1258);
#1260=PLANE('',#1259);
#1262=ORIENTED_EDGE('',*,*,#1261,.T.);
#1264=ORIENTED_EDGE('',*,*,#1263,.T.);
#1266=ORIENTED_EDGE('',*,*,#1265,.T.);
#1268=ORIENTED_EDGE('',*,*,#1267,.T.);
#1269=ORIENTED_EDGE('',*,*,#1249,.F.);
#1270=ORIENTED_EDGE('',*,*,#1086,.T.);
#1271=EDGE_LOOP('',(#1262,#1264,#1266,#1268,#1269,#1270));
#1272=FACE_OUTER_BOUND('',#1271,.F.);
#1274=CARTESIAN_POINT('',(-1.25E-1,0.E0,-1.825E-1));
#1275=DIRECTION('',(-1.E0,0.E0,0.E0));
#1276=DIRECTION('',(0.E0,0.E0,1.E0));
#1277=AXIS2_PLACEMENT_3D('',#1274,#1275,#1276);
#1278=PLANE('',#1277);
#1279=ORIENTED_EDGE('',*,*,#1261,.F.);
#1280=ORIENTED_EDGE('',*,*,#1084,.F.);
#1282=ORIENTED_EDGE('',*,*,#1281,.T.);
#1284=ORIENTED_EDGE('',*,*,#1283,.T.);
#1285=EDGE_LOOP('',(#1279,#1280,#1282,#1284));
#1286=FACE_OUTER_BOUND('',#1285,.F.);
#1288=CARTESIAN_POINT('',(-1.263917310096E-1,9.731931258430E-5,-1.825E-1));
#1289=DIRECTION('',(-9.902680687416E-1,1.391731009601E-1,0.E0));
#1290=DIRECTION('',(0.E0,0.E0,1.E0));
#1291=AXIS2_PLACEMENT_3D('',#1288,#1289,#1290);
#1292=PLANE('',#1291);
#1293=ORIENTED_EDGE('',*,*,#1281,.F.);
#1294=ORIENTED_EDGE('',*,*,#1082,.F.);
#1296=ORIENTED_EDGE('',*,*,#1295,.T.);
#1298=ORIENTED_EDGE('',*,*,#1297,.T.);
#1300=ORIENTED_EDGE('',*,*,#1299,.F.);
#1302=ORIENTED_EDGE('',*,*,#1301,.T.);
#1303=EDGE_LOOP('',(#1293,#1294,#1296,#1298,#1300,#1302));
#1304=FACE_OUTER_BOUND('',#1303,.F.);
#1306=CARTESIAN_POINT('',(-1.101459789689E-1,7.912403485599E-3,
-1.676459789689E-1));
#1307=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,-1.378445374916E-1));
#1308=DIRECTION('',(-9.902680687416E-1,1.391731009601E-1,0.E0));
#1309=AXIS2_PLACEMENT_3D('',#1306,#1307,#1308);
#1310=CYLINDRICAL_SURFACE('',#1309,1.5E-2);
#1311=ORIENTED_EDGE('',*,*,#1295,.F.);
#1312=ORIENTED_EDGE('',*,*,#1080,.F.);
#1313=ORIENTED_EDGE('',*,*,#1104,.T.);
#1315=ORIENTED_EDGE('',*,*,#1314,.F.);
#1316=EDGE_LOOP('',(#1311,#1312,#1313,#1315));
#1317=FACE_OUTER_BOUND('',#1316,.F.);
#1319=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,-1.599741925903E-1));
#1320=DIRECTION('',(0.E0,6.560590289905E-1,7.547095802228E-1));
#1321=DIRECTION('',(0.E0,7.547095802228E-1,-6.560590289905E-1));
#1322=AXIS2_PLACEMENT_3D('',#1319,#1320,#1321);
#1323=SPHERICAL_SURFACE('',#1322,1.5E-2);
#1325=ORIENTED_EDGE('',*,*,#1324,.T.);
#1326=ORIENTED_EDGE('',*,*,#1314,.T.);
#1328=ORIENTED_EDGE('',*,*,#1327,.T.);
#1329=EDGE_LOOP('',(#1325,#1326,#1328));
#1330=FACE_OUTER_BOUND('',#1329,.F.);
#1332=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,-1.730134936576E-1));
#1333=DIRECTION('',(0.E0,0.E0,1.E0));
#1334=DIRECTION('',(0.E0,1.E0,0.E0));
#1335=AXIS2_PLACEMENT_3D('',#1332,#1333,#1334);
#1336=CYLINDRICAL_SURFACE('',#1335,1.5E-2);
#1337=ORIENTED_EDGE('',*,*,#1151,.T.);
#1339=ORIENTED_EDGE('',*,*,#1338,.F.);
#1340=ORIENTED_EDGE('',*,*,#1297,.F.);
#1341=ORIENTED_EDGE('',*,*,#1324,.F.);
#1342=EDGE_LOOP('',(#1337,#1339,#1340,#1341));
#1343=FACE_OUTER_BOUND('',#1342,.F.);
#1345=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,1.599741925903E-1));
#1346=DIRECTION('',(0.E0,-6.560590289905E-1,7.547095802228E-1));
#1347=DIRECTION('',(0.E0,7.547095802228E-1,6.560590289905E-1));
#1348=AXIS2_PLACEMENT_3D('',#1345,#1346,#1347);
#1349=SPHERICAL_SURFACE('',#1348,1.5E-2);
#1351=ORIENTED_EDGE('',*,*,#1350,.T.);
#1352=ORIENTED_EDGE('',*,*,#1338,.T.);
#1354=ORIENTED_EDGE('',*,*,#1353,.T.);
#1355=EDGE_LOOP('',(#1351,#1352,#1354));
#1356=FACE_OUTER_BOUND('',#1355,.F.);
#1358=CARTESIAN_POINT('',(-1.101459789689E-1,7.912403485599E-3,
1.676459789689E-1));
#1359=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#1360=DIRECTION('',(0.E0,1.391731009601E-1,9.902680687416E-1));
#1361=AXIS2_PLACEMENT_3D('',#1358,#1359,#1360);
#1362=CYLINDRICAL_SURFACE('',#1361,1.5E-2);
#1364=ORIENTED_EDGE('',*,*,#1363,.F.);
#1366=ORIENTED_EDGE('',*,*,#1365,.T.);
#1367=ORIENTED_EDGE('',*,*,#1299,.T.);
#1368=ORIENTED_EDGE('',*,*,#1350,.F.);
#1369=EDGE_LOOP('',(#1364,#1366,#1367,#1368));
#1370=FACE_OUTER_BOUND('',#1369,.F.);
#1372=CARTESIAN_POINT('',(-1.25E-1,9.731931258430E-5,1.838917310096E-1));
#1373=DIRECTION('',(0.E0,1.391731009601E-1,9.902680687416E-1));
#1374=DIRECTION('',(1.E0,0.E0,0.E0));
#1375=AXIS2_PLACEMENT_3D('',#1372,#1373,#1374);
#1376=PLANE('',#1375);
#1377=ORIENTED_EDGE('',*,*,#1363,.T.);
#1379=ORIENTED_EDGE('',*,*,#1378,.T.);
#1381=ORIENTED_EDGE('',*,*,#1380,.F.);
#1383=ORIENTED_EDGE('',*,*,#1382,.F.);
#1385=ORIENTED_EDGE('',*,*,#1384,.F.);
#1387=ORIENTED_EDGE('',*,*,#1386,.T.);
#1388=EDGE_LOOP('',(#1377,#1379,#1381,#1383,#1385,#1387));
#1389=FACE_OUTER_BOUND('',#1388,.F.);
#1391=CARTESIAN_POINT('',(-1.024741925903E-1,6.25E-2,1.599741925903E-1));
#1392=DIRECTION('',(1.E0,0.E0,0.E0));
#1393=DIRECTION('',(0.E0,1.E0,0.E0));
#1394=AXIS2_PLACEMENT_3D('',#1391,#1392,#1393);
#1395=CYLINDRICAL_SURFACE('',#1394,1.5E-2);
#1396=ORIENTED_EDGE('',*,*,#1149,.T.);
#1398=ORIENTED_EDGE('',*,*,#1397,.F.);
#1399=ORIENTED_EDGE('',*,*,#1378,.F.);
#1400=ORIENTED_EDGE('',*,*,#1353,.F.);
#1401=EDGE_LOOP('',(#1396,#1398,#1399,#1400));
#1402=FACE_OUTER_BOUND('',#1401,.F.);
#1404=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,1.599741925903E-1));
#1405=DIRECTION('',(0.E0,6.560590289905E-1,-7.547095802228E-1));
#1406=DIRECTION('',(0.E0,7.547095802228E-1,6.560590289905E-1));
#1407=AXIS2_PLACEMENT_3D('',#1404,#1405,#1406);
#1408=SPHERICAL_SURFACE('',#1407,1.5E-2);
#1410=ORIENTED_EDGE('',*,*,#1409,.T.);
#1412=ORIENTED_EDGE('',*,*,#1411,.T.);
#1413=ORIENTED_EDGE('',*,*,#1397,.T.);
#1414=EDGE_LOOP('',(#1410,#1412,#1413));
#1415=FACE_OUTER_BOUND('',#1414,.F.);
#1417=CARTESIAN_POINT('',(1.024741925903E-1,6.25E-2,1.730134936576E-1));
#1418=DIRECTION('',(0.E0,0.E0,-1.E0));
#1419=DIRECTION('',(0.E0,1.E0,0.E0));
#1420=AXIS2_PLACEMENT_3D('',#1417,#1418,#1419);
#1421=CYLINDRICAL_SURFACE('',#1420,1.5E-2);
#1422=ORIENTED_EDGE('',*,*,#1147,.T.);
#1423=ORIENTED_EDGE('',*,*,#1184,.F.);
#1425=ORIENTED_EDGE('',*,*,#1424,.F.);
#1426=ORIENTED_EDGE('',*,*,#1409,.F.);
#1427=EDGE_LOOP('',(#1422,#1423,#1425,#1426));
#1428=FACE_OUTER_BOUND('',#1427,.F.);
#1430=CARTESIAN_POINT('',(1.263917310096E-1,9.731931258430E-5,1.825E-1));
#1431=DIRECTION('',(9.902680687416E-1,1.391731009601E-1,0.E0));
#1432=DIRECTION('',(0.E0,0.E0,-1.E0));
#1433=AXIS2_PLACEMENT_3D('',#1430,#1431,#1432);
#1434=PLANE('',#1433);
#1436=ORIENTED_EDGE('',*,*,#1435,.F.);
#1438=ORIENTED_EDGE('',*,*,#1437,.F.);
#1440=ORIENTED_EDGE('',*,*,#1439,.T.);
#1441=ORIENTED_EDGE('',*,*,#1424,.T.);
#1442=ORIENTED_EDGE('',*,*,#1198,.F.);
#1443=ORIENTED_EDGE('',*,*,#1218,.T.);
#1444=EDGE_LOOP('',(#1436,#1438,#1440,#1441,#1442,#1443));
#1445=FACE_OUTER_BOUND('',#1444,.F.);
#1447=CARTESIAN_POINT('',(1.25E-1,0.E0,1.825E-1));
#1448=DIRECTION('',(1.E0,0.E0,0.E0));
#1449=DIRECTION('',(0.E0,0.E0,-1.E0));
#1450=AXIS2_PLACEMENT_3D('',#1447,#1448,#1449);
#1451=PLANE('',#1450);
#1453=ORIENTED_EDGE('',*,*,#1452,.F.);
#1455=ORIENTED_EDGE('',*,*,#1454,.F.);
#1456=ORIENTED_EDGE('',*,*,#1435,.T.);
#1457=ORIENTED_EDGE('',*,*,#1216,.F.);
#1458=EDGE_LOOP('',(#1453,#1455,#1456,#1457));
#1459=FACE_OUTER_BOUND('',#1458,.F.);
#1461=CARTESIAN_POINT('',(1.25E-1,0.E0,1.825E-1));
#1462=DIRECTION('',(9.902680687416E-1,-1.391731009601E-1,0.E0));
#1463=DIRECTION('',(0.E0,0.E0,-1.E0));
#1464=AXIS2_PLACEMENT_3D('',#1461,#1462,#1463);
#1465=PLANE('',#1464);
#1466=ORIENTED_EDGE('',*,*,#1452,.T.);
#1467=ORIENTED_EDGE('',*,*,#1214,.F.);
#1469=ORIENTED_EDGE('',*,*,#1468,.F.);
#1471=ORIENTED_EDGE('',*,*,#1470,.T.);
#1473=ORIENTED_EDGE('',*,*,#1472,.F.);
#1475=ORIENTED_EDGE('',*,*,#1474,.T.);
#1476=EDGE_LOOP('',(#1466,#1467,#1469,#1471,#1473,#1475));
#1477=FACE_OUTER_BOUND('',#1476,.F.);
#1479=CARTESIAN_POINT('',(1.006594726265E-1,-6.541240348560E-2,
-1.581594726265E-1));
#1480=DIRECTION('',(-1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#1481=DIRECTION('',(0.E0,-1.391731009601E-1,-9.902680687416E-1));
#1482=AXIS2_PLACEMENT_3D('',#1479,#1480,#1481);
#1483=CYLINDRICAL_SURFACE('',#1482,1.5E-2);
#1484=ORIENTED_EDGE('',*,*,#1234,.F.);
#1486=ORIENTED_EDGE('',*,*,#1485,.F.);
#1487=ORIENTED_EDGE('',*,*,#1468,.T.);
#1488=ORIENTED_EDGE('',*,*,#1212,.F.);
#1489=EDGE_LOOP('',(#1484,#1486,#1487,#1488));
#1490=FACE_OUTER_BOUND('',#1489,.F.);
#1492=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#1493=DIRECTION('',(0.E0,-6.560590289905E-1,7.547095802228E-1));
#1494=DIRECTION('',(0.E0,-7.547095802228E-1,-6.560590289905E-1));
#1495=AXIS2_PLACEMENT_3D('',#1492,#1493,#1494);
#1496=SPHERICAL_SURFACE('',#1495,1.5E-2);
#1498=ORIENTED_EDGE('',*,*,#1497,.T.);
#1499=ORIENTED_EDGE('',*,*,#1485,.T.);
#1501=ORIENTED_EDGE('',*,*,#1500,.T.);
#1502=EDGE_LOOP('',(#1498,#1499,#1501));
#1503=FACE_OUTER_BOUND('',#1502,.F.);
#1505=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,-1.730134936576E-1));
#1506=DIRECTION('',(0.E0,0.E0,1.E0));
#1507=DIRECTION('',(0.E0,-1.E0,0.E0));
#1508=AXIS2_PLACEMENT_3D('',#1505,#1506,#1507);
#1509=CYLINDRICAL_SURFACE('',#1508,1.5E-2);
#1511=ORIENTED_EDGE('',*,*,#1510,.T.);
#1513=ORIENTED_EDGE('',*,*,#1512,.F.);
#1514=ORIENTED_EDGE('',*,*,#1470,.F.);
#1515=ORIENTED_EDGE('',*,*,#1497,.F.);
#1516=EDGE_LOOP('',(#1511,#1513,#1514,#1515));
#1517=FACE_OUTER_BOUND('',#1516,.F.);
#1519=CARTESIAN_POINT('',(0.E0,-6.75E-2,0.E0));
#1520=DIRECTION('',(0.E0,-1.E0,0.E0));
#1521=DIRECTION('',(-1.E0,0.E0,0.E0));
#1522=AXIS2_PLACEMENT_3D('',#1519,#1520,#1521);
#1523=PLANE('',#1522);
#1524=ORIENTED_EDGE('',*,*,#1510,.F.);
#1526=ORIENTED_EDGE('',*,*,#1525,.F.);
#1528=ORIENTED_EDGE('',*,*,#1527,.F.);
#1530=ORIENTED_EDGE('',*,*,#1529,.F.);
#1531=EDGE_LOOP('',(#1524,#1526,#1528,#1530));
#1532=FACE_OUTER_BOUND('',#1531,.F.);
#1534=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#1535=DIRECTION('',(1.E0,0.E0,0.E0));
#1536=DIRECTION('',(0.E0,-1.E0,0.E0));
#1537=AXIS2_PLACEMENT_3D('',#1534,#1535,#1536);
#1538=CYLINDRICAL_SURFACE('',#1537,1.5E-2);
#1539=ORIENTED_EDGE('',*,*,#1525,.T.);
#1540=ORIENTED_EDGE('',*,*,#1500,.F.);
#1541=ORIENTED_EDGE('',*,*,#1232,.F.);
#1543=ORIENTED_EDGE('',*,*,#1542,.F.);
#1544=EDGE_LOOP('',(#1539,#1540,#1541,#1543));
#1545=FACE_OUTER_BOUND('',#1544,.F.);
#1547=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,-1.599741925903E-1));
#1548=DIRECTION('',(0.E0,6.560590289905E-1,-7.547095802228E-1));
#1549=DIRECTION('',(0.E0,-7.547095802228E-1,-6.560590289905E-1));
#1550=AXIS2_PLACEMENT_3D('',#1547,#1548,#1549);
#1551=SPHERICAL_SURFACE('',#1550,1.5E-2);
#1552=ORIENTED_EDGE('',*,*,#1251,.T.);
#1554=ORIENTED_EDGE('',*,*,#1553,.T.);
#1555=ORIENTED_EDGE('',*,*,#1542,.T.);
#1556=EDGE_LOOP('',(#1552,#1554,#1555));
#1557=FACE_OUTER_BOUND('',#1556,.F.);
#1559=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,1.730134936576E-1));
#1560=DIRECTION('',(0.E0,0.E0,-1.E0));
#1561=DIRECTION('',(0.E0,-1.E0,0.E0));
#1562=AXIS2_PLACEMENT_3D('',#1559,#1560,#1561);
#1563=CYLINDRICAL_SURFACE('',#1562,1.5E-2);
#1564=ORIENTED_EDGE('',*,*,#1527,.T.);
#1565=ORIENTED_EDGE('',*,*,#1553,.F.);
#1566=ORIENTED_EDGE('',*,*,#1267,.F.);
#1568=ORIENTED_EDGE('',*,*,#1567,.F.);
#1569=EDGE_LOOP('',(#1564,#1565,#1566,#1568));
#1570=FACE_OUTER_BOUND('',#1569,.F.);
#1572=CARTESIAN_POINT('',(-1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#1573=DIRECTION('',(0.E0,-6.560590289905E-1,-7.547095802228E-1));
#1574=DIRECTION('',(0.E0,-7.547095802228E-1,6.560590289905E-1));
#1575=AXIS2_PLACEMENT_3D('',#1572,#1573,#1574);
#1576=SPHERICAL_SURFACE('',#1575,1.5E-2);
#1577=ORIENTED_EDGE('',*,*,#1567,.T.);
#1579=ORIENTED_EDGE('',*,*,#1578,.T.);
#1581=ORIENTED_EDGE('',*,*,#1580,.T.);
#1582=EDGE_LOOP('',(#1577,#1579,#1581));
#1583=FACE_OUTER_BOUND('',#1582,.F.);
#1585=CARTESIAN_POINT('',(-1.101459789689E-1,2.087596514401E-3,
1.676459789689E-1));
#1586=DIRECTION('',(-1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#1587=DIRECTION('',(-9.902680687416E-1,-1.391731009601E-1,0.E0));
#1588=AXIS2_PLACEMENT_3D('',#1585,#1586,#1587);
#1589=CYLINDRICAL_SURFACE('',#1588,1.5E-2);
#1590=ORIENTED_EDGE('',*,*,#1265,.F.);
#1592=ORIENTED_EDGE('',*,*,#1591,.T.);
#1594=ORIENTED_EDGE('',*,*,#1593,.T.);
#1595=ORIENTED_EDGE('',*,*,#1578,.F.);
#1596=EDGE_LOOP('',(#1590,#1592,#1594,#1595));
#1597=FACE_OUTER_BOUND('',#1596,.F.);
#1599=CARTESIAN_POINT('',(-1.1E-1,1.E-2,1.675E-1));
#1600=DIRECTION('',(0.E0,1.E0,0.E0));
#1601=DIRECTION('',(-1.E0,0.E0,0.E0));
#1602=AXIS2_PLACEMENT_3D('',#1599,#1600,#1601);
#1603=CYLINDRICAL_SURFACE('',#1602,1.5E-2);
#1604=ORIENTED_EDGE('',*,*,#1283,.F.);
#1605=ORIENTED_EDGE('',*,*,#1301,.F.);
#1606=ORIENTED_EDGE('',*,*,#1365,.F.);
#1607=ORIENTED_EDGE('',*,*,#1386,.F.);
#1609=ORIENTED_EDGE('',*,*,#1608,.T.);
#1611=ORIENTED_EDGE('',*,*,#1610,.F.);
#1612=ORIENTED_EDGE('',*,*,#1591,.F.);
#1613=ORIENTED_EDGE('',*,*,#1263,.F.);
#1614=EDGE_LOOP('',(#1604,#1605,#1606,#1607,#1609,#1611,#1612,#1613));
#1615=FACE_OUTER_BOUND('',#1614,.F.);
#1617=CARTESIAN_POINT('',(-1.25E-1,0.E0,1.825E-1));
#1618=DIRECTION('',(0.E0,0.E0,1.E0));
#1619=DIRECTION('',(1.E0,0.E0,0.E0));
#1620=AXIS2_PLACEMENT_3D('',#1617,#1618,#1619);
#1621=PLANE('',#1620);
#1623=ORIENTED_EDGE('',*,*,#1622,.T.);
#1625=ORIENTED_EDGE('',*,*,#1624,.F.);
#1626=ORIENTED_EDGE('',*,*,#1608,.F.);
#1627=ORIENTED_EDGE('',*,*,#1384,.T.);
#1628=EDGE_LOOP('',(#1623,#1625,#1626,#1627));
#1629=FACE_OUTER_BOUND('',#1628,.F.);
#1631=CARTESIAN_POINT('',(1.1E-1,1.E-2,1.675E-1));
#1632=DIRECTION('',(0.E0,1.E0,0.E0));
#1633=DIRECTION('',(0.E0,0.E0,1.E0));
#1634=AXIS2_PLACEMENT_3D('',#1631,#1632,#1633);
#1635=CYLINDRICAL_SURFACE('',#1634,1.5E-2);
#1636=ORIENTED_EDGE('',*,*,#1622,.F.);
#1637=ORIENTED_EDGE('',*,*,#1382,.T.);
#1639=ORIENTED_EDGE('',*,*,#1638,.T.);
#1640=ORIENTED_EDGE('',*,*,#1437,.T.);
#1641=ORIENTED_EDGE('',*,*,#1454,.T.);
#1642=ORIENTED_EDGE('',*,*,#1474,.F.);
#1644=ORIENTED_EDGE('',*,*,#1643,.F.);
#1646=ORIENTED_EDGE('',*,*,#1645,.F.);
#1647=EDGE_LOOP('',(#1636,#1637,#1639,#1640,#1641,#1642,#1644,#1646));
#1648=FACE_OUTER_BOUND('',#1647,.F.);
#1650=CARTESIAN_POINT('',(1.101459789689E-1,7.912403485599E-3,
1.676459789689E-1));
#1651=DIRECTION('',(1.378445374916E-1,-9.808148484640E-1,1.378445374916E-1));
#1652=DIRECTION('',(9.902680687416E-1,1.391731009601E-1,0.E0));
#1653=AXIS2_PLACEMENT_3D('',#1650,#1651,#1652);
#1654=CYLINDRICAL_SURFACE('',#1653,1.5E-2);
#1655=ORIENTED_EDGE('',*,*,#1439,.F.);
#1656=ORIENTED_EDGE('',*,*,#1638,.F.);
#1657=ORIENTED_EDGE('',*,*,#1380,.T.);
#1658=ORIENTED_EDGE('',*,*,#1411,.F.);
#1659=EDGE_LOOP('',(#1655,#1656,#1657,#1658));
#1660=FACE_OUTER_BOUND('',#1659,.F.);
#1662=CARTESIAN_POINT('',(1.101459789689E-1,2.087596514401E-3,
1.676459789689E-1));
#1663=DIRECTION('',(1.378445374916E-1,9.808148484640E-1,1.378445374916E-1));
#1664=DIRECTION('',(0.E0,-1.391731009601E-1,9.902680687416E-1));
#1665=AXIS2_PLACEMENT_3D('',#1662,#1663,#1664);
#1666=CYLINDRICAL_SURFACE('',#1665,1.5E-2);
#1668=ORIENTED_EDGE('',*,*,#1667,.F.);
#1669=ORIENTED_EDGE('',*,*,#1643,.T.);
#1670=ORIENTED_EDGE('',*,*,#1472,.T.);
#1672=ORIENTED_EDGE('',*,*,#1671,.F.);
#1673=EDGE_LOOP('',(#1668,#1669,#1670,#1672));
#1674=FACE_OUTER_BOUND('',#1673,.F.);
#1676=CARTESIAN_POINT('',(-1.25E-1,0.E0,1.825E-1));
#1677=DIRECTION('',(0.E0,-1.391731009601E-1,9.902680687416E-1));
#1678=DIRECTION('',(1.E0,0.E0,0.E0));
#1679=AXIS2_PLACEMENT_3D('',#1676,#1677,#1678);
#1680=PLANE('',#1679);
#1681=ORIENTED_EDGE('',*,*,#1667,.T.);
#1683=ORIENTED_EDGE('',*,*,#1682,.T.);
#1684=ORIENTED_EDGE('',*,*,#1593,.F.);
#1685=ORIENTED_EDGE('',*,*,#1610,.T.);
#1686=ORIENTED_EDGE('',*,*,#1624,.T.);
#1687=ORIENTED_EDGE('',*,*,#1645,.T.);
#1688=EDGE_LOOP('',(#1681,#1683,#1684,#1685,#1686,#1687));
#1689=FACE_OUTER_BOUND('',#1688,.F.);
#1691=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#1692=DIRECTION('',(-1.E0,0.E0,0.E0));
#1693=DIRECTION('',(0.E0,-1.E0,0.E0));
#1694=AXIS2_PLACEMENT_3D('',#1691,#1692,#1693);
#1695=CYLINDRICAL_SURFACE('',#1694,1.5E-2);
#1696=ORIENTED_EDGE('',*,*,#1529,.T.);
#1697=ORIENTED_EDGE('',*,*,#1580,.F.);
#1698=ORIENTED_EDGE('',*,*,#1682,.F.);
#1700=ORIENTED_EDGE('',*,*,#1699,.F.);
#1701=EDGE_LOOP('',(#1696,#1697,#1698,#1700));
#1702=FACE_OUTER_BOUND('',#1701,.F.);
#1704=CARTESIAN_POINT('',(1.024741925903E-1,-5.25E-2,1.599741925903E-1));
#1705=DIRECTION('',(0.E0,6.560590289905E-1,7.547095802228E-1));
#1706=DIRECTION('',(0.E0,-7.547095802228E-1,6.560590289905E-1));
#1707=AXIS2_PLACEMENT_3D('',#1704,#1705,#1706);
#1708=SPHERICAL_SURFACE('',#1707,1.5E-2);
#1709=ORIENTED_EDGE('',*,*,#1671,.T.);
#1710=ORIENTED_EDGE('',*,*,#1512,.T.);
#1711=ORIENTED_EDGE('',*,*,#1699,.T.);
#1712=EDGE_LOOP('',(#1709,#1710,#1711));
#1713=FACE_OUTER_BOUND('',#1712,.F.);
#1715=CARTESIAN_POINT('',(-2.272857233843E-2,6.25E-2,-1.599741925903E-1));
#1716=DIRECTION('',(-1.E0,0.E0,0.E0));
#1717=DIRECTION('',(0.E0,1.E0,0.E0));
#1718=AXIS2_PLACEMENT_3D('',#1715,#1716,#1717);
#1719=CYLINDRICAL_SURFACE('',#1718,1.5E-2);
#1720=ORIENTED_EDGE('',*,*,#1153,.T.);
#1721=ORIENTED_EDGE('',*,*,#1327,.F.);
#1722=ORIENTED_EDGE('',*,*,#1102,.F.);
#1723=ORIENTED_EDGE('',*,*,#1128,.F.);
#1724=EDGE_LOOP('',(#1720,#1721,#1722,#1723));
#1725=FACE_OUTER_BOUND('',#1724,.F.);
#1727=CARTESIAN_POINT('',(-8.9E-2,7.75E-2,-1.2E-1));
#1728=DIRECTION('',(0.E0,-1.E0,0.E0));
#1729=DIRECTION('',(1.E0,0.E0,0.E0));
#1730=AXIS2_PLACEMENT_3D('',#1727,#1728,#1729);
#1731=CYLINDRICAL_SURFACE('',#1730,1.25E-2);
#1732=ORIENTED_EDGE('',*,*,#1157,.T.);
#1734=ORIENTED_EDGE('',*,*,#1733,.T.);
#1736=ORIENTED_EDGE('',*,*,#1735,.F.);
#1738=ORIENTED_EDGE('',*,*,#1737,.F.);
#1739=EDGE_LOOP('',(#1732,#1734,#1736,#1738));
#1740=FACE_OUTER_BOUND('',#1739,.F.);
#1742=CARTESIAN_POINT('',(-8.9E-2,7.75E-2,-1.2E-1));
#1743=DIRECTION('',(0.E0,-1.E0,0.E0));
#1744=DIRECTION('',(1.E0,0.E0,0.E0));
#1745=AXIS2_PLACEMENT_3D('',#1742,#1743,#1744);
#1746=CYLINDRICAL_SURFACE('',#1745,1.25E-2);
#1747=ORIENTED_EDGE('',*,*,#1159,.T.);
#1748=ORIENTED_EDGE('',*,*,#1737,.T.);
#1750=ORIENTED_EDGE('',*,*,#1749,.F.);
#1751=ORIENTED_EDGE('',*,*,#1733,.F.);
#1752=EDGE_LOOP('',(#1747,#1748,#1750,#1751));
#1753=FACE_OUTER_BOUND('',#1752,.F.);
#1755=CARTESIAN_POINT('',(-8.9E-2,7.25E-2,-1.2E-1));
#1756=DIRECTION('',(0.E0,-1.E0,0.E0));
#1757=DIRECTION('',(1.E0,0.E0,0.E0));
#1758=AXIS2_PLACEMENT_3D('',#1755,#1756,#1757);
#1759=PLANE('',#1758);
#1760=ORIENTED_EDGE('',*,*,#1735,.T.);
#1761=ORIENTED_EDGE('',*,*,#1749,.T.);
#1762=EDGE_LOOP('',(#1760,#1761));
#1763=FACE_OUTER_BOUND('',#1762,.F.);
#1765=CARTESIAN_POINT('',(0.E0,5.75E-2,0.E0));
#1766=DIRECTION('',(0.E0,1.E0,0.E0));
#1767=DIRECTION('',(1.E0,0.E0,0.E0));
#1768=AXIS2_PLACEMENT_3D('',#1765,#1766,#1767);
#1769=PLANE('',#1768);
#1770=ORIENTED_EDGE('',*,*,#1131,.F.);
#1771=ORIENTED_EDGE('',*,*,#1116,.F.);
#1772=EDGE_LOOP('',(#1770,#1771));
#1773=FACE_OUTER_BOUND('',#1772,.F.);
#1775=CLOSED_SHELL('',(#1071,#1094,#1120,#1138,#1163,#1176,#1189,#1203,#1224,
#1241,#1255,#1273,#1287,#1305,#1318,#1331,#1344,#1357,#1371,#1390,#1403,#1416,
#1429,#1446,#1460,#1478,#1491,#1504,#1518,#1533,#1546,#1558,#1571,#1584,#1598,
#1616,#1630,#1649,#1661,#1675,#1690,#1703,#1714,#1726,#1741,#1754,#1764,#1774));
#1776=MANIFOLD_SOLID_BREP('',#1775);
#1777=FILL_AREA_STYLE_COLOUR('',#3);
#1778=FILL_AREA_STYLE('',(#1777));
#1779=SURFACE_STYLE_FILL_AREA(#1778);
#1780=SURFACE_SIDE_STYLE('',(#1779));
#1781=SURFACE_STYLE_USAGE(.BOTH.,#1780);
#1782=PRESENTATION_STYLE_ASSIGNMENT((#1781));
#27=STYLED_ITEM('',(#1782),#1776);
#1784=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1783);
#1785=(CONVERSION_BASED_UNIT('INCH',#1784)LENGTH_UNIT()NAMED_UNIT(*));
#1787=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#1786);
#1788=(CONVERSION_BASED_UNIT('DEGREE',#1787)NAMED_UNIT(*)PLANE_ANGLE_UNIT());
#1790=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(4.866295348609E-5),#1785,
'distance_accuracy_value',
'Maximum model space distance between geometric entities at asserted connectivities');
#1792=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1793=DIRECTION('',(0.E0,0.E0,1.E0));
#1794=DIRECTION('',(1.E0,0.E0,0.E0));
#1797=APPLICATION_CONTEXT('automotive_design');
#1798=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2001,#1797);
#1799=PRODUCT_DEFINITION_CONTEXT('part definition',#1797,'design');
#1800=PRODUCT_CONTEXT('',#1797,'mechanical');
#1801=PRODUCT('BODY_P0008A','BODY_P0008A','NOT SPECIFIED',(#1800));
#1802=PRODUCT_DEFINITION_FORMATION('25','LAST_VERSION',#1801);
#1810=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1809);
#1811=(CONVERSION_BASED_UNIT('INCH',#1810)LENGTH_UNIT()NAMED_UNIT(*));
#1812=DERIVED_UNIT_ELEMENT(#1811,2.E0);
#1813=DERIVED_UNIT((#1812));
#1814=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
3.213932980390E-1),#1813);
#1819=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1818);
#1820=(CONVERSION_BASED_UNIT('INCH',#1819)LENGTH_UNIT()NAMED_UNIT(*));
#1821=DERIVED_UNIT_ELEMENT(#1820,3.E0);
#1822=DERIVED_UNIT((#1821));
#1823=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
1.233347651378E-2),#1822);
#1827=CARTESIAN_POINT('centre point',(1.771102247118E-5,4.886888735932E-3,
2.882059256477E-4));
#1832=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1831);
#1833=(CONVERSION_BASED_UNIT('INCH',#1832)LENGTH_UNIT()NAMED_UNIT(*));
#1834=DERIVED_UNIT_ELEMENT(#1833,2.E0);
#1835=DERIVED_UNIT((#1834));
#1836=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
3.213932980390E-1),#1835);
#1841=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1840);
#1842=(CONVERSION_BASED_UNIT('INCH',#1841)LENGTH_UNIT()NAMED_UNIT(*));
#1843=DERIVED_UNIT_ELEMENT(#1842,3.E0);
#1844=DERIVED_UNIT((#1843));
#1845=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
1.233347651378E-2),#1844);
#1849=CARTESIAN_POINT('centre point',(1.771102247118E-5,4.886888735932E-3,
2.882059256477E-4));
#1854=(GEOMETRIC_REPRESENTATION_CONTEXT(2)PARAMETRIC_REPRESENTATION_CONTEXT()REPRESENTATION_CONTEXT('2D coordinate system context','2'));
#1855=DRAUGHTING_MODEL('Default',(#1867,#1868,#1884),#1854);
#1856=PRESENTATION_VIEW('Default',(#1875,#1888),#1854);
#1857=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1858=CARTESIAN_POINT('',(-2.849929669246E-1,-2.360678706264E-1,
9.502616297702E-1));
#1859=DIRECTION('',(0.E0,0.E0,1.E0));
#1860=DIRECTION('',(1.E0,0.E0,0.E0));
#1861=AXIS2_PLACEMENT_3D('',#1858,#1859,#1860);
#1863=VIEW_VOLUME(.PARALLEL.,#1857,9.502616297702E-1,9.502616297702E-1,.T.,0.E0,
.F.,.T.,#1862);
#1864=CARTESIAN_POINT('',(1.075534032252E-1,6.257986776983E-1,
7.979689694454E-1));
#1865=DIRECTION('',(3.894183423087E-1,7.214918620107E-1,5.725406952575E-1));
#1866=DIRECTION('',(9.210609940029E-1,-3.050418666329E-1,-2.420663234065E-1));
#1867=AXIS2_PLACEMENT_3D('',#1864,#1865,#1866);
#1868=CAMERA_MODEL_D3_WITH_HLHSR('DEFAULT',#1867,#1863,.F.);
#1870=CARTESIAN_POINT('',(-5.026902695145E2,-4.163928071260E2,
1.676137063778E3));
#1871=DIRECTION('',(0.E0,0.E0,1.E0));
#1872=DIRECTION('',(1.E0,0.E0,0.E0));
#1873=AXIS2_PLACEMENT_3D('',#1870,#1871,#1872);
#1875=(CAMERA_IMAGE()CAMERA_IMAGE_3D_WITH_SCALE()GEOMETRIC_REPRESENTATION_ITEM()MAPPED_ITEM(#1869,#1874)REPRESENTATION_ITEM(''));
#1876=CARTESIAN_POINT('',(-2.849929669246E-1,-2.360678706264E-1,
9.502616297702E-1));
#1877=DIRECTION('',(0.E0,0.E0,1.E0));
#1878=DIRECTION('',(1.E0,0.E0,0.E0));
#1879=AXIS2_PLACEMENT_3D('',#1876,#1877,#1878);
#1880=REPRESENTATION_MAP(#1879,#1796);
#1881=CARTESIAN_POINT('',(0.E0,0.E0));
#1882=DIRECTION('',(1.E0,0.E0));
#1883=AXIS2_PLACEMENT_2D('',#1881,#1882);
#1884=MAPPED_ITEM('',#1880,#1883);
#1885=CARTESIAN_POINT('',(-2.849929669246E-1,-2.360678706264E-1,
9.502616297702E-1));
#1886=DIRECTION('',(0.E0,0.E0,1.E0));
#1887=DIRECTION('',(1.E0,0.E0,0.E0));
#1888=AXIS2_PLACEMENT_3D('',#1885,#1886,#1887);
#1889=REPRESENTATION_MAP(#1888,#1856);
#1890=CARTESIAN_POINT('',(0.E0,0.E0));
#1891=DIRECTION('',(1.E0,0.E0));
#1892=AXIS2_PLACEMENT_2D('',#1890,#1891);
#1893=MAPPED_ITEM('',#1889,#1892);
#1895=CARTESIAN_POINT('',(0.E0,0.E0));
#1896=DIRECTION('',(1.E0,0.E0));
#1897=AXIS2_PLACEMENT_2D('',#1895,#1896);
#1898=PLANAR_BOX('',1.E3,8.4375E2,#1897);
#1899=PRESENTATION_SIZE(#1894,#1898);
#1900=PRESENTATION_SET();
#1901=AREA_IN_SET(#1894,#1900);
#1902=APPLIED_PRESENTED_ITEM((#1803));
#1903=PRESENTED_ITEM_REPRESENTATION(#1900,#1902);
#1909=CARTESIAN_POINT('centre point',(1.771102247118E-5,-2.882059256477E-4,
9.310801384452E-2));
#1913=CARTESIAN_POINT('',(0.E0,0.E0,8.822112510859E-2));
#1914=DIRECTION('',(0.E0,-1.E0,0.E0));
#1915=DIRECTION('',(1.E0,0.E0,0.E0));
#1916=AXIS2_PLACEMENT_3D('',#1913,#1914,#1915);
#1917=ITEM_DEFINED_TRANSFORMATION('','',#1795,#1916);
#1918=(REPRESENTATION_RELATIONSHIP('','',#1796,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1917)SHAPE_REPRESENTATION_RELATIONSHIP());
#1919=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1918,#1908);
#1921=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#1922=VECTOR('',#1921,1.E-2);
#1923=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,-3.E-2));
#1924=LINE('',#1923,#1922);
#1925=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1926=PRESENTATION_STYLE_ASSIGNMENT((#1925));
#1927=STYLED_ITEM('',(#1926),#1924);
#1928=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#1929=VECTOR('',#1928,7.413744991544E-2);
#1930=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,-3.E-2));
#1931=LINE('',#1930,#1929);
#1932=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1933=PRESENTATION_STYLE_ASSIGNMENT((#1932));
#1934=STYLED_ITEM('',(#1933),#1931);
#1935=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.E-2));
#1936=DIRECTION('',(0.E0,0.E0,1.E0));
#1937=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#1938=AXIS2_PLACEMENT_3D('',#1935,#1936,#1937);
#1940=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1941=PRESENTATION_STYLE_ASSIGNMENT((#1940));
#1942=STYLED_ITEM('',(#1941),#1939);
#1943=DIRECTION('',(-1.E0,0.E0,0.E0));
#1944=VECTOR('',#1943,1.001413869739E-2);
#1945=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.E-2));
#1946=LINE('',#1945,#1944);
#1947=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1948=PRESENTATION_STYLE_ASSIGNMENT((#1947));
#1949=STYLED_ITEM('',(#1948),#1946);
#1950=DIRECTION('',(0.E0,-1.E0,0.E0));
#1951=VECTOR('',#1950,1.E-2);
#1952=CARTESIAN_POINT('',(0.E0,5.E-3,-3.E-2));
#1953=LINE('',#1952,#1951);
#1954=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1955=PRESENTATION_STYLE_ASSIGNMENT((#1954));
#1956=STYLED_ITEM('',(#1955),#1953);
#1957=DIRECTION('',(1.E0,0.E0,0.E0));
#1958=VECTOR('',#1957,1.001413869739E-2);
#1959=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.E-2));
#1960=LINE('',#1959,#1958);
#1961=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1962=PRESENTATION_STYLE_ASSIGNMENT((#1961));
#1963=STYLED_ITEM('',(#1962),#1960);
#1964=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.E-2));
#1965=DIRECTION('',(0.E0,0.E0,-1.E0));
#1966=DIRECTION('',(0.E0,1.E0,0.E0));
#1967=AXIS2_PLACEMENT_3D('',#1964,#1965,#1966);
#1969=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1970=PRESENTATION_STYLE_ASSIGNMENT((#1969));
#1971=STYLED_ITEM('',(#1970),#1968);
#1972=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#1973=VECTOR('',#1972,7.413744991544E-2);
#1974=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.E-2));
#1975=LINE('',#1974,#1973);
#1976=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1977=PRESENTATION_STYLE_ASSIGNMENT((#1976));
#1978=STYLED_ITEM('',(#1977),#1975);
#1979=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,-7.071067811865E-1));
#1980=VECTOR('',#1979,1.414213562373E-2);
#1981=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-2.E-2));
#1982=LINE('',#1981,#1980);
#1983=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1984=PRESENTATION_STYLE_ASSIGNMENT((#1983));
#1985=STYLED_ITEM('',(#1984),#1982);
#1986=DIRECTION('',(0.E0,0.E0,1.E0));
#1987=VECTOR('',#1986,6.E-2);
#1988=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.E-2));
#1989=LINE('',#1988,#1987);
#1990=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1991=PRESENTATION_STYLE_ASSIGNMENT((#1990));
#1992=STYLED_ITEM('',(#1991),#1989);
#1993=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#1994=VECTOR('',#1993,1.414213562373E-2);
#1995=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.E-2));
#1996=LINE('',#1995,#1994);
#1997=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#1998=PRESENTATION_STYLE_ASSIGNMENT((#1997));
#1999=STYLED_ITEM('',(#1998),#1996);
#2000=DIRECTION('',(0.E0,0.E0,-1.E0));
#2001=VECTOR('',#2000,1.1E-2);
#2002=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.E-2));
#2003=LINE('',#2002,#2001);
#2004=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2005=PRESENTATION_STYLE_ASSIGNMENT((#2004));
#2006=STYLED_ITEM('',(#2005),#2003);
#2007=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2008=VECTOR('',#2007,1.322E-1);
#2009=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,9.E-3));
#2010=LINE('',#2009,#2008);
#2011=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2012=PRESENTATION_STYLE_ASSIGNMENT((#2011));
#2013=STYLED_ITEM('',(#2012),#2010);
#2014=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2015=VECTOR('',#2014,1.322E-1);
#2016=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-9.E-3));
#2017=LINE('',#2016,#2015);
#2018=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2019=PRESENTATION_STYLE_ASSIGNMENT((#2018));
#2020=STYLED_ITEM('',(#2019),#2017);
#2021=DIRECTION('',(0.E0,0.E0,1.E0));
#2022=VECTOR('',#2021,1.1E-2);
#2023=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-2.E-2));
#2024=LINE('',#2023,#2022);
#2025=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2026=PRESENTATION_STYLE_ASSIGNMENT((#2025));
#2027=STYLED_ITEM('',(#2026),#2024);
#2028=DIRECTION('',(0.E0,0.E0,1.E0));
#2029=VECTOR('',#2028,6.E-2);
#2030=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,-3.E-2));
#2031=LINE('',#2030,#2029);
#2032=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2033=PRESENTATION_STYLE_ASSIGNMENT((#2032));
#2034=STYLED_ITEM('',(#2033),#2031);
#2035=DIRECTION('',(0.E0,0.E0,1.E0));
#2036=VECTOR('',#2035,6.E-2);
#2037=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.E-2));
#2038=LINE('',#2037,#2036);
#2039=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2040=PRESENTATION_STYLE_ASSIGNMENT((#2039));
#2041=STYLED_ITEM('',(#2040),#2038);
#2042=DIRECTION('',(0.E0,0.E0,1.E0));
#2043=VECTOR('',#2042,6.E-2);
#2044=CARTESIAN_POINT('',(0.E0,5.E-3,-3.E-2));
#2045=LINE('',#2044,#2043);
#2046=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2047=PRESENTATION_STYLE_ASSIGNMENT((#2046));
#2048=STYLED_ITEM('',(#2047),#2045);
#2049=DIRECTION('',(0.E0,0.E0,1.E0));
#2050=VECTOR('',#2049,6.E-2);
#2051=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.E-2));
#2052=LINE('',#2051,#2050);
#2053=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2054=PRESENTATION_STYLE_ASSIGNMENT((#2053));
#2055=STYLED_ITEM('',(#2054),#2052);
#2056=DIRECTION('',(0.E0,0.E0,1.E0));
#2057=VECTOR('',#2056,6.E-2);
#2058=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587458E-3,-3.E-2));
#2059=LINE('',#2058,#2057);
#2060=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2061=PRESENTATION_STYLE_ASSIGNMENT((#2060));
#2062=STYLED_ITEM('',(#2061),#2059);
#2063=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,-7.071067811865E-1));
#2064=VECTOR('',#2063,1.414213562373E-2);
#2065=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-2.E-2));
#2066=LINE('',#2065,#2064);
#2067=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2068=PRESENTATION_STYLE_ASSIGNMENT((#2067));
#2069=STYLED_ITEM('',(#2068),#2066);
#2070=DIRECTION('',(0.E0,0.E0,1.E0));
#2071=VECTOR('',#2070,1.1E-2);
#2072=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-2.E-2));
#2073=LINE('',#2072,#2071);
#2074=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2075=PRESENTATION_STYLE_ASSIGNMENT((#2074));
#2076=STYLED_ITEM('',(#2075),#2073);
#2077=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2078=VECTOR('',#2077,1.322E-1);
#2079=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-9.E-3));
#2080=LINE('',#2079,#2078);
#2081=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2082=PRESENTATION_STYLE_ASSIGNMENT((#2081));
#2083=STYLED_ITEM('',(#2082),#2080);
#2084=DIRECTION('',(0.E0,0.E0,1.E0));
#2085=VECTOR('',#2084,1.8E-2);
#2086=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-9.E-3));
#2087=LINE('',#2086,#2085);
#2088=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2089=PRESENTATION_STYLE_ASSIGNMENT((#2088));
#2090=STYLED_ITEM('',(#2089),#2087);
#2091=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2092=VECTOR('',#2091,1.322E-1);
#2093=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,9.E-3));
#2094=LINE('',#2093,#2092);
#2095=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2096=PRESENTATION_STYLE_ASSIGNMENT((#2095));
#2097=STYLED_ITEM('',(#2096),#2094);
#2098=DIRECTION('',(0.E0,0.E0,-1.E0));
#2099=VECTOR('',#2098,1.1E-2);
#2100=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#2101=LINE('',#2100,#2099);
#2102=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2103=PRESENTATION_STYLE_ASSIGNMENT((#2102));
#2104=STYLED_ITEM('',(#2103),#2101);
#2105=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#2106=VECTOR('',#2105,1.414213562373E-2);
#2107=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#2108=LINE('',#2107,#2106);
#2109=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2110=PRESENTATION_STYLE_ASSIGNMENT((#2109));
#2111=STYLED_ITEM('',(#2110),#2108);
#2112=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2113=VECTOR('',#2112,1.E-2);
#2114=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-2.E-2));
#2115=LINE('',#2114,#2113);
#2116=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2117=PRESENTATION_STYLE_ASSIGNMENT((#2116));
#2118=STYLED_ITEM('',(#2117),#2115);
#2119=DIRECTION('',(-9.985861302615E-1,-5.315769416943E-2,0.E0));
#2120=VECTOR('',#2119,1.E-2);
#2121=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-9.E-3));
#2122=LINE('',#2121,#2120);
#2123=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2124=PRESENTATION_STYLE_ASSIGNMENT((#2123));
#2125=STYLED_ITEM('',(#2124),#2122);
#2126=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2127=VECTOR('',#2126,1.E-2);
#2128=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-9.E-3));
#2129=LINE('',#2128,#2127);
#2130=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2131=PRESENTATION_STYLE_ASSIGNMENT((#2130));
#2132=STYLED_ITEM('',(#2131),#2129);
#2133=DIRECTION('',(0.E0,0.E0,1.E0));
#2134=VECTOR('',#2133,1.8E-2);
#2135=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-9.E-3));
#2136=LINE('',#2135,#2134);
#2137=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2138=PRESENTATION_STYLE_ASSIGNMENT((#2137));
#2139=STYLED_ITEM('',(#2138),#2136);
#2140=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2141=VECTOR('',#2140,1.E-2);
#2142=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,9.E-3));
#2143=LINE('',#2142,#2141);
#2144=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2145=PRESENTATION_STYLE_ASSIGNMENT((#2144));
#2146=STYLED_ITEM('',(#2145),#2143);
#2147=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2148=VECTOR('',#2147,1.E-2);
#2149=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.E-2));
#2150=LINE('',#2149,#2148);
#2151=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2152=PRESENTATION_STYLE_ASSIGNMENT((#2151));
#2153=STYLED_ITEM('',(#2152),#2150);
#2154=DIRECTION('',(-9.985861302615E-1,-5.315769416944E-2,0.E0));
#2155=VECTOR('',#2154,1.E-2);
#2156=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,9.E-3));
#2157=LINE('',#2156,#2155);
#2158=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2159=PRESENTATION_STYLE_ASSIGNMENT((#2158));
#2160=STYLED_ITEM('',(#2159),#2157);
#2161=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2162=VECTOR('',#2161,1.E-2);
#2163=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,3.E-2));
#2164=LINE('',#2163,#2162);
#2165=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2166=PRESENTATION_STYLE_ASSIGNMENT((#2165));
#2167=STYLED_ITEM('',(#2166),#2164);
#2168=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#2169=VECTOR('',#2168,7.413744991544E-2);
#2170=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,3.E-2));
#2171=LINE('',#2170,#2169);
#2172=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2173=PRESENTATION_STYLE_ASSIGNMENT((#2172));
#2174=STYLED_ITEM('',(#2173),#2171);
#2175=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,3.E-2));
#2176=DIRECTION('',(0.E0,0.E0,-1.E0));
#2177=DIRECTION('',(0.E0,1.E0,0.E0));
#2178=AXIS2_PLACEMENT_3D('',#2175,#2176,#2177);
#2180=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2181=PRESENTATION_STYLE_ASSIGNMENT((#2180));
#2182=STYLED_ITEM('',(#2181),#2179);
#2183=DIRECTION('',(1.E0,0.E0,0.E0));
#2184=VECTOR('',#2183,1.001413869739E-2);
#2185=CARTESIAN_POINT('',(0.E0,-5.E-3,3.E-2));
#2186=LINE('',#2185,#2184);
#2187=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2188=PRESENTATION_STYLE_ASSIGNMENT((#2187));
#2189=STYLED_ITEM('',(#2188),#2186);
#2190=DIRECTION('',(0.E0,-1.E0,0.E0));
#2191=VECTOR('',#2190,1.E-2);
#2192=CARTESIAN_POINT('',(0.E0,5.E-3,3.E-2));
#2193=LINE('',#2192,#2191);
#2194=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2195=PRESENTATION_STYLE_ASSIGNMENT((#2194));
#2196=STYLED_ITEM('',(#2195),#2193);
#2197=DIRECTION('',(-1.E0,0.E0,0.E0));
#2198=VECTOR('',#2197,1.001413869739E-2);
#2199=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,3.E-2));
#2200=LINE('',#2199,#2198);
#2201=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2202=PRESENTATION_STYLE_ASSIGNMENT((#2201));
#2203=STYLED_ITEM('',(#2202),#2200);
#2204=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,3.E-2));
#2205=DIRECTION('',(0.E0,0.E0,1.E0));
#2206=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2207=AXIS2_PLACEMENT_3D('',#2204,#2205,#2206);
#2209=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2210=PRESENTATION_STYLE_ASSIGNMENT((#2209));
#2211=STYLED_ITEM('',(#2210),#2208);
#2212=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2213=VECTOR('',#2212,7.413744991544E-2);
#2214=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,3.E-2));
#2215=LINE('',#2214,#2213);
#2216=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2217=PRESENTATION_STYLE_ASSIGNMENT((#2216));
#2218=STYLED_ITEM('',(#2217),#2215);
#2219=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587458E-3,-3.E-2));
#2220=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.E-2));
#2221=VERTEX_POINT('',#2219);
#2222=VERTEX_POINT('',#2220);
#2223=CARTESIAN_POINT('',(0.E0,5.E-3,-3.E-2));
#2224=VERTEX_POINT('',#2223);
#2225=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.E-2));
#2226=VERTEX_POINT('',#2225);
#2227=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,-3.E-2));
#2228=VERTEX_POINT('',#2227);
#2229=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.E-2));
#2230=VERTEX_POINT('',#2229);
#2231=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587458E-3,3.E-2));
#2232=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,3.E-2));
#2233=VERTEX_POINT('',#2231);
#2234=VERTEX_POINT('',#2232);
#2235=CARTESIAN_POINT('',(0.E0,5.E-3,3.E-2));
#2236=VERTEX_POINT('',#2235);
#2237=CARTESIAN_POINT('',(0.E0,-5.E-3,3.E-2));
#2238=VERTEX_POINT('',#2237);
#2239=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,3.E-2));
#2240=VERTEX_POINT('',#2239);
#2241=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,3.E-2));
#2242=VERTEX_POINT('',#2241);
#2243=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,9.E-3));
#2244=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,9.E-3));
#2245=VERTEX_POINT('',#2243);
#2246=VERTEX_POINT('',#2244);
#2247=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,9.E-3));
#2248=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,9.E-3));
#2249=VERTEX_POINT('',#2247);
#2250=VERTEX_POINT('',#2248);
#2251=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-9.E-3));
#2252=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-9.E-3));
#2253=VERTEX_POINT('',#2251);
#2254=VERTEX_POINT('',#2252);
#2255=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-9.E-3));
#2256=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-9.E-3));
#2257=VERTEX_POINT('',#2255);
#2258=VERTEX_POINT('',#2256);
#2259=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,3.E-2));
#2260=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,3.E-2));
#2261=VERTEX_POINT('',#2259);
#2262=VERTEX_POINT('',#2260);
#2263=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#2264=VERTEX_POINT('',#2263);
#2265=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.E-2));
#2266=VERTEX_POINT('',#2265);
#2267=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,-3.E-2));
#2268=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,-3.E-2));
#2269=VERTEX_POINT('',#2267);
#2270=VERTEX_POINT('',#2268);
#2271=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-2.E-2));
#2272=VERTEX_POINT('',#2271);
#2273=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-2.E-2));
#2274=VERTEX_POINT('',#2273);
#2275=CARTESIAN_POINT('',(0.E0,0.E0,-3.E-2));
#2276=DIRECTION('',(0.E0,0.E0,1.E0));
#2277=DIRECTION('',(1.E0,0.E0,0.E0));
#2278=AXIS2_PLACEMENT_3D('',#2275,#2276,#2277);
#2279=PLANE('',#2278);
#2281=ORIENTED_EDGE('',*,*,#2280,.T.);
#2283=ORIENTED_EDGE('',*,*,#2282,.T.);
#2285=ORIENTED_EDGE('',*,*,#2284,.T.);
#2287=ORIENTED_EDGE('',*,*,#2286,.T.);
#2289=ORIENTED_EDGE('',*,*,#2288,.T.);
#2291=ORIENTED_EDGE('',*,*,#2290,.T.);
#2293=ORIENTED_EDGE('',*,*,#2292,.T.);
#2295=ORIENTED_EDGE('',*,*,#2294,.T.);
#2296=EDGE_LOOP('',(#2281,#2283,#2285,#2287,#2289,#2291,#2293,#2295));
#2297=FACE_OUTER_BOUND('',#2296,.F.);
#2299=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-2.E-2));
#2300=DIRECTION('',(3.758816601945E-2,-7.061070243067E-1,-7.071067811865E-1));
#2301=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,-7.071067811865E-1));
#2302=AXIS2_PLACEMENT_3D('',#2299,#2300,#2301);
#2303=PLANE('',#2302);
#2305=ORIENTED_EDGE('',*,*,#2304,.F.);
#2307=ORIENTED_EDGE('',*,*,#2306,.T.);
#2309=ORIENTED_EDGE('',*,*,#2308,.T.);
#2310=ORIENTED_EDGE('',*,*,#2280,.F.);
#2311=EDGE_LOOP('',(#2305,#2307,#2309,#2310));
#2312=FACE_OUTER_BOUND('',#2311,.F.);
#2314=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.E-2));
#2315=DIRECTION('',(-9.985861302615E-1,-5.315769416943E-2,0.E0));
#2316=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#2317=AXIS2_PLACEMENT_3D('',#2314,#2315,#2316);
#2318=PLANE('',#2317);
#2319=ORIENTED_EDGE('',*,*,#2304,.T.);
#2320=ORIENTED_EDGE('',*,*,#2294,.F.);
#2322=ORIENTED_EDGE('',*,*,#2321,.T.);
#2324=ORIENTED_EDGE('',*,*,#2323,.T.);
#2326=ORIENTED_EDGE('',*,*,#2325,.F.);
#2328=ORIENTED_EDGE('',*,*,#2327,.T.);
#2330=ORIENTED_EDGE('',*,*,#2329,.F.);
#2332=ORIENTED_EDGE('',*,*,#2331,.F.);
#2334=ORIENTED_EDGE('',*,*,#2333,.T.);
#2336=ORIENTED_EDGE('',*,*,#2335,.F.);
#2337=EDGE_LOOP('',(#2319,#2320,#2322,#2324,#2326,#2328,#2330,#2332,#2334,
#2336));
#2338=FACE_OUTER_BOUND('',#2337,.F.);
#2340=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.E-2));
#2341=DIRECTION('',(0.E0,0.E0,1.E0));
#2342=DIRECTION('',(1.E0,0.E0,0.E0));
#2343=AXIS2_PLACEMENT_3D('',#2340,#2341,#2342);
#2344=CYLINDRICAL_SURFACE('',#2343,5.E-3);
#2345=ORIENTED_EDGE('',*,*,#2292,.F.);
#2347=ORIENTED_EDGE('',*,*,#2346,.T.);
#2349=ORIENTED_EDGE('',*,*,#2348,.T.);
#2350=ORIENTED_EDGE('',*,*,#2321,.F.);
#2351=EDGE_LOOP('',(#2345,#2347,#2349,#2350));
#2352=FACE_OUTER_BOUND('',#2351,.F.);
#2354=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.E-2));
#2355=DIRECTION('',(0.E0,-1.E0,0.E0));
#2356=DIRECTION('',(1.E0,0.E0,0.E0));
#2357=AXIS2_PLACEMENT_3D('',#2354,#2355,#2356);
#2358=PLANE('',#2357);
#2359=ORIENTED_EDGE('',*,*,#2290,.F.);
#2361=ORIENTED_EDGE('',*,*,#2360,.T.);
#2363=ORIENTED_EDGE('',*,*,#2362,.T.);
#2364=ORIENTED_EDGE('',*,*,#2346,.F.);
#2365=EDGE_LOOP('',(#2359,#2361,#2363,#2364));
#2366=FACE_OUTER_BOUND('',#2365,.F.);
#2368=CARTESIAN_POINT('',(0.E0,5.E-3,-3.E-2));
#2369=DIRECTION('',(-1.E0,0.E0,0.E0));
#2370=DIRECTION('',(0.E0,-1.E0,0.E0));
#2371=AXIS2_PLACEMENT_3D('',#2368,#2369,#2370);
#2372=PLANE('',#2371);
#2373=ORIENTED_EDGE('',*,*,#2288,.F.);
#2375=ORIENTED_EDGE('',*,*,#2374,.T.);
#2377=ORIENTED_EDGE('',*,*,#2376,.T.);
#2378=ORIENTED_EDGE('',*,*,#2360,.F.);
#2379=EDGE_LOOP('',(#2373,#2375,#2377,#2378));
#2380=FACE_OUTER_BOUND('',#2379,.F.);
#2382=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.E-2));
#2383=DIRECTION('',(0.E0,1.E0,0.E0));
#2384=DIRECTION('',(-1.E0,0.E0,0.E0));
#2385=AXIS2_PLACEMENT_3D('',#2382,#2383,#2384);
#2386=PLANE('',#2385);
#2387=ORIENTED_EDGE('',*,*,#2286,.F.);
#2389=ORIENTED_EDGE('',*,*,#2388,.T.);
#2391=ORIENTED_EDGE('',*,*,#2390,.T.);
#2392=ORIENTED_EDGE('',*,*,#2374,.F.);
#2393=EDGE_LOOP('',(#2387,#2389,#2391,#2392));
#2394=FACE_OUTER_BOUND('',#2393,.F.);
#2396=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.E-2));
#2397=DIRECTION('',(0.E0,0.E0,1.E0));
#2398=DIRECTION('',(1.E0,0.E0,0.E0));
#2399=AXIS2_PLACEMENT_3D('',#2396,#2397,#2398);
#2400=CYLINDRICAL_SURFACE('',#2399,1.5E-2);
#2401=ORIENTED_EDGE('',*,*,#2284,.F.);
#2403=ORIENTED_EDGE('',*,*,#2402,.T.);
#2405=ORIENTED_EDGE('',*,*,#2404,.T.);
#2406=ORIENTED_EDGE('',*,*,#2388,.F.);
#2407=EDGE_LOOP('',(#2401,#2403,#2405,#2406));
#2408=FACE_OUTER_BOUND('',#2407,.F.);
#2410=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-3.E-2));
#2411=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2412=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2413=AXIS2_PLACEMENT_3D('',#2410,#2411,#2412);
#2414=PLANE('',#2413);
#2415=ORIENTED_EDGE('',*,*,#2308,.F.);
#2417=ORIENTED_EDGE('',*,*,#2416,.T.);
#2419=ORIENTED_EDGE('',*,*,#2418,.F.);
#2421=ORIENTED_EDGE('',*,*,#2420,.T.);
#2423=ORIENTED_EDGE('',*,*,#2422,.T.);
#2425=ORIENTED_EDGE('',*,*,#2424,.F.);
#2427=ORIENTED_EDGE('',*,*,#2426,.T.);
#2429=ORIENTED_EDGE('',*,*,#2428,.T.);
#2430=ORIENTED_EDGE('',*,*,#2402,.F.);
#2431=ORIENTED_EDGE('',*,*,#2282,.F.);
#2432=EDGE_LOOP('',(#2415,#2417,#2419,#2421,#2423,#2425,#2427,#2429,#2430,
#2431));
#2433=FACE_OUTER_BOUND('',#2432,.F.);
#2435=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-9.E-3));
#2436=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#2437=DIRECTION('',(0.E0,0.E0,-1.E0));
#2438=AXIS2_PLACEMENT_3D('',#2435,#2436,#2437);
#2439=PLANE('',#2438);
#2440=ORIENTED_EDGE('',*,*,#2306,.F.);
#2441=ORIENTED_EDGE('',*,*,#2335,.T.);
#2443=ORIENTED_EDGE('',*,*,#2442,.F.);
#2444=ORIENTED_EDGE('',*,*,#2416,.F.);
#2445=EDGE_LOOP('',(#2440,#2441,#2443,#2444));
#2446=FACE_OUTER_BOUND('',#2445,.F.);
#2448=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-9.E-3));
#2449=DIRECTION('',(0.E0,0.E0,-1.E0));
#2450=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2451=AXIS2_PLACEMENT_3D('',#2448,#2449,#2450);
#2452=PLANE('',#2451);
#2453=ORIENTED_EDGE('',*,*,#2418,.T.);
#2454=ORIENTED_EDGE('',*,*,#2442,.T.);
#2455=ORIENTED_EDGE('',*,*,#2333,.F.);
#2457=ORIENTED_EDGE('',*,*,#2456,.T.);
#2458=EDGE_LOOP('',(#2453,#2454,#2455,#2457));
#2459=FACE_OUTER_BOUND('',#2458,.F.);
#2461=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-3.E-2));
#2462=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#2463=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2464=AXIS2_PLACEMENT_3D('',#2461,#2462,#2463);
#2465=PLANE('',#2464);
#2466=ORIENTED_EDGE('',*,*,#2456,.F.);
#2467=ORIENTED_EDGE('',*,*,#2331,.T.);
#2469=ORIENTED_EDGE('',*,*,#2468,.T.);
#2470=ORIENTED_EDGE('',*,*,#2420,.F.);
#2471=EDGE_LOOP('',(#2466,#2467,#2469,#2470));
#2472=FACE_OUTER_BOUND('',#2471,.F.);
#2474=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,9.E-3));
#2475=DIRECTION('',(0.E0,0.E0,-1.E0));
#2476=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2477=AXIS2_PLACEMENT_3D('',#2474,#2475,#2476);
#2478=PLANE('',#2477);
#2479=ORIENTED_EDGE('',*,*,#2422,.F.);
#2480=ORIENTED_EDGE('',*,*,#2468,.F.);
#2481=ORIENTED_EDGE('',*,*,#2329,.T.);
#2483=ORIENTED_EDGE('',*,*,#2482,.F.);
#2484=EDGE_LOOP('',(#2479,#2480,#2481,#2483));
#2485=FACE_OUTER_BOUND('',#2484,.F.);
#2487=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,9.E-3));
#2488=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2489=DIRECTION('',(0.E0,0.E0,1.E0));
#2490=AXIS2_PLACEMENT_3D('',#2487,#2488,#2489);
#2491=PLANE('',#2490);
#2493=ORIENTED_EDGE('',*,*,#2492,.T.);
#2494=ORIENTED_EDGE('',*,*,#2424,.T.);
#2495=ORIENTED_EDGE('',*,*,#2482,.T.);
#2496=ORIENTED_EDGE('',*,*,#2327,.F.);
#2497=EDGE_LOOP('',(#2493,#2494,#2495,#2496));
#2498=FACE_OUTER_BOUND('',#2497,.F.);
#2500=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#2501=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,-7.071067811865E-1));
#2502=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#2503=AXIS2_PLACEMENT_3D('',#2500,#2501,#2502);
#2504=PLANE('',#2503);
#2505=ORIENTED_EDGE('',*,*,#2325,.T.);
#2507=ORIENTED_EDGE('',*,*,#2506,.T.);
#2508=ORIENTED_EDGE('',*,*,#2426,.F.);
#2509=ORIENTED_EDGE('',*,*,#2492,.F.);
#2510=EDGE_LOOP('',(#2505,#2507,#2508,#2509));
#2511=FACE_OUTER_BOUND('',#2510,.F.);
#2513=CARTESIAN_POINT('',(0.E0,0.E0,3.E-2));
#2514=DIRECTION('',(0.E0,0.E0,1.E0));
#2515=DIRECTION('',(1.E0,0.E0,0.E0));
#2516=AXIS2_PLACEMENT_3D('',#2513,#2514,#2515);
#2517=PLANE('',#2516);
#2518=ORIENTED_EDGE('',*,*,#2506,.F.);
#2519=ORIENTED_EDGE('',*,*,#2323,.F.);
#2520=ORIENTED_EDGE('',*,*,#2348,.F.);
#2521=ORIENTED_EDGE('',*,*,#2362,.F.);
#2522=ORIENTED_EDGE('',*,*,#2376,.F.);
#2523=ORIENTED_EDGE('',*,*,#2390,.F.);
#2524=ORIENTED_EDGE('',*,*,#2404,.F.);
#2525=ORIENTED_EDGE('',*,*,#2428,.F.);
#2526=EDGE_LOOP('',(#2518,#2519,#2520,#2521,#2522,#2523,#2524,#2525));
#2527=FACE_OUTER_BOUND('',#2526,.F.);
#2529=CLOSED_SHELL('',(#2298,#2313,#2339,#2353,#2367,#2381,#2395,#2409,#2434,
#2447,#2460,#2473,#2486,#2499,#2512,#2528));
#2530=MANIFOLD_SOLID_BREP('',#2529);
#2531=FILL_AREA_STYLE_COLOUR('',#26);
#2532=FILL_AREA_STYLE('',(#2531));
#2533=SURFACE_STYLE_FILL_AREA(#2532);
#2534=SURFACE_SIDE_STYLE('',(#2533));
#2535=SURFACE_STYLE_USAGE(.BOTH.,#2534);
#2536=PRESENTATION_STYLE_ASSIGNMENT((#2535));
#1920=STYLED_ITEM('',(#2536),#2530);
#2538=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2537);
#2539=(CONVERSION_BASED_UNIT('INCH',#2538)LENGTH_UNIT()NAMED_UNIT(*));
#2541=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2540);
#2542=(CONVERSION_BASED_UNIT('DEGREE',#2541)NAMED_UNIT(*)PLANE_ANGLE_UNIT());
#2544=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.412051584078E-5),#2539,
'distance_accuracy_value',
'Maximum model space distance between geometric entities at asserted connectivities');
#2546=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2547=DIRECTION('',(0.E0,0.E0,1.E0));
#2548=DIRECTION('',(1.E0,0.E0,0.E0));
#2551=PRODUCT_CONTEXT('',#1797,'mechanical');
#2552=PRODUCT('LEAD_P0008A','LEAD_P0008A','NOT SPECIFIED',(#2551));
#2553=PRODUCT_DEFINITION_FORMATION('22','LAST_VERSION',#2552);
#2561=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2560);
#2562=(CONVERSION_BASED_UNIT('INCH',#2561)LENGTH_UNIT()NAMED_UNIT(*));
#2563=DERIVED_UNIT_ELEMENT(#2562,2.E0);
#2564=DERIVED_UNIT((#2563));
#2565=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
2.359192411033E-2),#2564);
#2570=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2569);
#2571=(CONVERSION_BASED_UNIT('INCH',#2570)LENGTH_UNIT()NAMED_UNIT(*));
#2572=DERIVED_UNIT_ELEMENT(#2571,3.E0);
#2573=DERIVED_UNIT((#2572));
#2574=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
8.839263456293E-5),#2573);
#2578=CARTESIAN_POINT('centre point',(2.202698720131E-2,-7.161248895199E-2,
0.E0));
#2583=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2582);
#2584=(CONVERSION_BASED_UNIT('INCH',#2583)LENGTH_UNIT()NAMED_UNIT(*));
#2585=DERIVED_UNIT_ELEMENT(#2584,2.E0);
#2586=DERIVED_UNIT((#2585));
#2587=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
2.359192411033E-2),#2586);
#2592=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2591);
#2593=(CONVERSION_BASED_UNIT('INCH',#2592)LENGTH_UNIT()NAMED_UNIT(*));
#2594=DERIVED_UNIT_ELEMENT(#2593,3.E0);
#2595=DERIVED_UNIT((#2594));
#2596=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
8.839263456293E-5),#2595);
#2600=CARTESIAN_POINT('centre point',(2.202698720131E-2,-7.161248895199E-2,
0.E0));
#2605=DRAUGHTING_MODEL('Default',(#2617,#2618,#2634),#1854);
#2606=PRESENTATION_VIEW('Default',(#2625,#2638),#1854);
#2607=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2608=CARTESIAN_POINT('',(-9.310385493076E-2,-1.897618853867E-1,
4.164675265643E-1));
#2609=DIRECTION('',(0.E0,0.E0,1.E0));
#2610=DIRECTION('',(1.E0,0.E0,0.E0));
#2611=AXIS2_PLACEMENT_3D('',#2608,#2609,#2610);
#2613=VIEW_VOLUME(.PARALLEL.,#2607,4.164675265643E-1,4.164675265643E-1,.T.,0.E0,
.F.,.T.,#2612);
#2614=CARTESIAN_POINT('',(7.642576465201E-2,2.109206253524E-1,
4.096275063144E-1));
#2615=DIRECTION('',(3.894183423087E-1,7.214918620107E-1,5.725406952575E-1));
#2616=DIRECTION('',(9.210609940029E-1,-3.050418666329E-1,-2.420663234065E-1));
#2617=AXIS2_PLACEMENT_3D('',#2614,#2615,#2616);
#2618=CAMERA_MODEL_D3_WITH_HLHSR('DEFAULT',#2617,#2613,.F.);
#2620=CARTESIAN_POINT('',(-3.242198384988E2,-6.608165459858E2,
1.450284032848E3));
#2621=DIRECTION('',(0.E0,0.E0,1.E0));
#2622=DIRECTION('',(1.E0,0.E0,0.E0));
#2623=AXIS2_PLACEMENT_3D('',#2620,#2621,#2622);
#2625=(CAMERA_IMAGE()CAMERA_IMAGE_3D_WITH_SCALE()GEOMETRIC_REPRESENTATION_ITEM()MAPPED_ITEM(#2619,#2624)REPRESENTATION_ITEM(''));
#2626=CARTESIAN_POINT('',(-9.310385493076E-2,-1.897618853867E-1,
4.164675265643E-1));
#2627=DIRECTION('',(0.E0,0.E0,1.E0));
#2628=DIRECTION('',(1.E0,0.E0,0.E0));
#2629=AXIS2_PLACEMENT_3D('',#2626,#2627,#2628);
#2630=REPRESENTATION_MAP(#2629,#2550);
#2631=CARTESIAN_POINT('',(0.E0,0.E0));
#2632=DIRECTION('',(1.E0,0.E0));
#2633=AXIS2_PLACEMENT_2D('',#2631,#2632);
#2634=MAPPED_ITEM('',#2630,#2633);
#2635=CARTESIAN_POINT('',(-9.310385493076E-2,-1.897618853867E-1,
4.164675265643E-1));
#2636=DIRECTION('',(0.E0,0.E0,1.E0));
#2637=DIRECTION('',(1.E0,0.E0,0.E0));
#2638=AXIS2_PLACEMENT_3D('',#2635,#2636,#2637);
#2639=REPRESENTATION_MAP(#2638,#2606);
#2640=CARTESIAN_POINT('',(0.E0,0.E0));
#2641=DIRECTION('',(1.E0,0.E0));
#2642=AXIS2_PLACEMENT_2D('',#2640,#2641);
#2643=MAPPED_ITEM('',#2639,#2642);
#2645=CARTESIAN_POINT('',(0.E0,0.E0));
#2646=DIRECTION('',(1.E0,0.E0));
#2647=AXIS2_PLACEMENT_2D('',#2645,#2646);
#2648=PLANAR_BOX('',1.E3,8.4375E2,#2647);
#2649=PRESENTATION_SIZE(#2644,#2648);
#2650=PRESENTATION_SET();
#2651=AREA_IN_SET(#2644,#2650);
#2652=APPLIED_PRESENTED_ITEM((#2554));
#2653=PRESENTED_ITEM_REPRESENTATION(#2650,#2652);
#2656=CARTESIAN_POINT('centre point',(-1.470269872013E-1,5.E-2,
2.160863615660E-2));
#2660=CARTESIAN_POINT('',(-1.25E-1,5.E-2,9.322112510859E-2));
#2661=DIRECTION('',(0.E0,1.E0,0.E0));
#2662=DIRECTION('',(-1.E0,0.E0,0.E0));
#2663=AXIS2_PLACEMENT_3D('',#2660,#2661,#2662);
#2664=ITEM_DEFINED_TRANSFORMATION('','',#2549,#2663);
#2665=(REPRESENTATION_RELATIONSHIP('','',#2550,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2664)SHAPE_REPRESENTATION_RELATIONSHIP());
#2666=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2665,#2655);
#2669=CARTESIAN_POINT('centre point',(-1.470269872013E-1,-5.E-2,
2.160863615660E-2));
#2673=CARTESIAN_POINT('',(-1.25E-1,-5.E-2,9.322112510859E-2));
#2674=DIRECTION('',(0.E0,1.E0,0.E0));
#2675=DIRECTION('',(-1.E0,0.E0,0.E0));
#2676=AXIS2_PLACEMENT_3D('',#2673,#2674,#2675);
#2677=ITEM_DEFINED_TRANSFORMATION('','',#2549,#2676);
#2678=(REPRESENTATION_RELATIONSHIP('','',#2550,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2677)SHAPE_REPRESENTATION_RELATIONSHIP());
#2679=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2678,#2668);
#2682=CARTESIAN_POINT('centre point',(1.470269872013E-1,5.E-2,
2.160863615660E-2));
#2686=CARTESIAN_POINT('',(1.25E-1,5.E-2,9.322112510859E-2));
#2687=DIRECTION('',(0.E0,-1.E0,0.E0));
#2688=DIRECTION('',(1.E0,0.E0,0.E0));
#2689=AXIS2_PLACEMENT_3D('',#2686,#2687,#2688);
#2690=ITEM_DEFINED_TRANSFORMATION('','',#2549,#2689);
#2691=(REPRESENTATION_RELATIONSHIP('','',#2550,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2690)SHAPE_REPRESENTATION_RELATIONSHIP());
#2692=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2691,#2681);
#2695=CARTESIAN_POINT('centre point',(1.470269872013E-1,-5.E-2,
2.160863615660E-2));
#2699=CARTESIAN_POINT('',(1.25E-1,-5.E-2,9.322112510859E-2));
#2700=DIRECTION('',(0.E0,-1.E0,0.E0));
#2701=DIRECTION('',(1.E0,0.E0,0.E0));
#2702=AXIS2_PLACEMENT_3D('',#2699,#2700,#2701);
#2703=ITEM_DEFINED_TRANSFORMATION('','',#2549,#2702);
#2704=(REPRESENTATION_RELATIONSHIP('','',#2550,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2703)SHAPE_REPRESENTATION_RELATIONSHIP());
#2705=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2704,#2694);
#2707=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2708=VECTOR('',#2707,1.E-2);
#2709=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,-3.9E-2));
#2710=LINE('',#2709,#2708);
#2711=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2712=PRESENTATION_STYLE_ASSIGNMENT((#2711));
#2713=STYLED_ITEM('',(#2712),#2710);
#2714=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2715=VECTOR('',#2714,7.413744991544E-2);
#2716=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,-3.9E-2));
#2717=LINE('',#2716,#2715);
#2718=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2719=PRESENTATION_STYLE_ASSIGNMENT((#2718));
#2720=STYLED_ITEM('',(#2719),#2717);
#2721=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.9E-2));
#2722=DIRECTION('',(0.E0,0.E0,1.E0));
#2723=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2724=AXIS2_PLACEMENT_3D('',#2721,#2722,#2723);
#2726=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2727=PRESENTATION_STYLE_ASSIGNMENT((#2726));
#2728=STYLED_ITEM('',(#2727),#2725);
#2729=DIRECTION('',(-1.E0,0.E0,0.E0));
#2730=VECTOR('',#2729,1.001413869739E-2);
#2731=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.9E-2));
#2732=LINE('',#2731,#2730);
#2733=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2734=PRESENTATION_STYLE_ASSIGNMENT((#2733));
#2735=STYLED_ITEM('',(#2734),#2732);
#2736=DIRECTION('',(0.E0,-1.E0,0.E0));
#2737=VECTOR('',#2736,1.E-2);
#2738=CARTESIAN_POINT('',(0.E0,5.E-3,-3.9E-2));
#2739=LINE('',#2738,#2737);
#2740=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2741=PRESENTATION_STYLE_ASSIGNMENT((#2740));
#2742=STYLED_ITEM('',(#2741),#2739);
#2743=DIRECTION('',(1.E0,0.E0,0.E0));
#2744=VECTOR('',#2743,1.001413869739E-2);
#2745=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.9E-2));
#2746=LINE('',#2745,#2744);
#2747=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2748=PRESENTATION_STYLE_ASSIGNMENT((#2747));
#2749=STYLED_ITEM('',(#2748),#2746);
#2750=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.9E-2));
#2751=DIRECTION('',(0.E0,0.E0,-1.E0));
#2752=DIRECTION('',(0.E0,1.E0,0.E0));
#2753=AXIS2_PLACEMENT_3D('',#2750,#2751,#2752);
#2755=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2756=PRESENTATION_STYLE_ASSIGNMENT((#2755));
#2757=STYLED_ITEM('',(#2756),#2754);
#2758=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#2759=VECTOR('',#2758,7.413744991544E-2);
#2760=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.9E-2));
#2761=LINE('',#2760,#2759);
#2762=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2763=PRESENTATION_STYLE_ASSIGNMENT((#2762));
#2764=STYLED_ITEM('',(#2763),#2761);
#2765=DIRECTION('',(3.758816601945E-2,-7.061070243067E-1,7.071067811865E-1));
#2766=VECTOR('',#2765,1.414213562373E-2);
#2767=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,-3.9E-2));
#2768=LINE('',#2767,#2766);
#2769=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2770=PRESENTATION_STYLE_ASSIGNMENT((#2769));
#2771=STYLED_ITEM('',(#2770),#2768);
#2772=DIRECTION('',(0.E0,0.E0,1.E0));
#2773=VECTOR('',#2772,3.9E-2);
#2774=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.9E-2));
#2775=LINE('',#2774,#2773);
#2776=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2777=PRESENTATION_STYLE_ASSIGNMENT((#2776));
#2778=STYLED_ITEM('',(#2777),#2775);
#2779=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#2780=VECTOR('',#2779,2.163374499154E-1);
#2781=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,0.E0));
#2782=LINE('',#2781,#2780);
#2783=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2784=PRESENTATION_STYLE_ASSIGNMENT((#2783));
#2785=STYLED_ITEM('',(#2784),#2782);
#2786=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2787=VECTOR('',#2786,1.322E-1);
#2788=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-1.9E-2));
#2789=LINE('',#2788,#2787);
#2790=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2791=PRESENTATION_STYLE_ASSIGNMENT((#2790));
#2792=STYLED_ITEM('',(#2791),#2789);
#2793=DIRECTION('',(0.E0,0.E0,-1.E0));
#2794=VECTOR('',#2793,1.E-2);
#2795=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-1.9E-2));
#2796=LINE('',#2795,#2794);
#2797=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2798=PRESENTATION_STYLE_ASSIGNMENT((#2797));
#2799=STYLED_ITEM('',(#2798),#2796);
#2800=DIRECTION('',(0.E0,0.E0,1.E0));
#2801=VECTOR('',#2800,3.9E-2);
#2802=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,-3.9E-2));
#2803=LINE('',#2802,#2801);
#2804=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2805=PRESENTATION_STYLE_ASSIGNMENT((#2804));
#2806=STYLED_ITEM('',(#2805),#2803);
#2807=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,0.E0));
#2808=DIRECTION('',(0.E0,0.E0,1.E0));
#2809=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2810=AXIS2_PLACEMENT_3D('',#2807,#2808,#2809);
#2812=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2813=PRESENTATION_STYLE_ASSIGNMENT((#2812));
#2814=STYLED_ITEM('',(#2813),#2811);
#2815=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,0.E0));
#2816=DIRECTION('',(0.E0,0.E0,1.E0));
#2817=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#2818=AXIS2_PLACEMENT_3D('',#2815,#2816,#2817);
#2820=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2821=PRESENTATION_STYLE_ASSIGNMENT((#2820));
#2822=STYLED_ITEM('',(#2821),#2819);
#2823=DIRECTION('',(3.758816601945E-2,-7.061070243067E-1,7.071067811865E-1));
#2824=VECTOR('',#2823,1.414213562373E-2);
#2825=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,-3.9E-2));
#2826=LINE('',#2825,#2824);
#2827=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2828=PRESENTATION_STYLE_ASSIGNMENT((#2827));
#2829=STYLED_ITEM('',(#2828),#2826);
#2830=DIRECTION('',(0.E0,0.E0,-1.E0));
#2831=VECTOR('',#2830,1.E-2);
#2832=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-1.9E-2));
#2833=LINE('',#2832,#2831);
#2834=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2835=PRESENTATION_STYLE_ASSIGNMENT((#2834));
#2836=STYLED_ITEM('',(#2835),#2833);
#2837=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2838=VECTOR('',#2837,1.322E-1);
#2839=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-1.9E-2));
#2840=LINE('',#2839,#2838);
#2841=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2842=PRESENTATION_STYLE_ASSIGNMENT((#2841));
#2843=STYLED_ITEM('',(#2842),#2840);
#2844=DIRECTION('',(0.E0,0.E0,1.E0));
#2845=VECTOR('',#2844,1.9E-2);
#2846=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-1.9E-2));
#2847=LINE('',#2846,#2845);
#2848=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2849=PRESENTATION_STYLE_ASSIGNMENT((#2848));
#2850=STYLED_ITEM('',(#2849),#2847);
#2851=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#2852=VECTOR('',#2851,2.163374499154E-1);
#2853=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,0.E0));
#2854=LINE('',#2853,#2852);
#2855=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2856=PRESENTATION_STYLE_ASSIGNMENT((#2855));
#2857=STYLED_ITEM('',(#2856),#2854);
#2858=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2859=VECTOR('',#2858,1.E-2);
#2860=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-2.9E-2));
#2861=LINE('',#2860,#2859);
#2862=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2863=PRESENTATION_STYLE_ASSIGNMENT((#2862));
#2864=STYLED_ITEM('',(#2863),#2861);
#2865=DIRECTION('',(-9.985861302615E-1,-5.315769416944E-2,0.E0));
#2866=VECTOR('',#2865,1.E-2);
#2867=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-1.9E-2));
#2868=LINE('',#2867,#2866);
#2869=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2870=PRESENTATION_STYLE_ASSIGNMENT((#2869));
#2871=STYLED_ITEM('',(#2870),#2868);
#2872=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2873=VECTOR('',#2872,1.E-2);
#2874=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,0.E0));
#2875=LINE('',#2874,#2873);
#2876=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2877=PRESENTATION_STYLE_ASSIGNMENT((#2876));
#2878=STYLED_ITEM('',(#2877),#2875);
#2879=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#2880=VECTOR('',#2879,1.E-2);
#2881=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-1.9E-2));
#2882=LINE('',#2881,#2880);
#2883=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2884=PRESENTATION_STYLE_ASSIGNMENT((#2883));
#2885=STYLED_ITEM('',(#2884),#2882);
#2886=DIRECTION('',(0.E0,0.E0,1.E0));
#2887=VECTOR('',#2886,1.9E-2);
#2888=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-1.9E-2));
#2889=LINE('',#2888,#2887);
#2890=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2891=PRESENTATION_STYLE_ASSIGNMENT((#2890));
#2892=STYLED_ITEM('',(#2891),#2889);
#2893=DIRECTION('',(0.E0,0.E0,1.E0));
#2894=VECTOR('',#2893,3.9E-2);
#2895=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587458E-3,-3.9E-2));
#2896=LINE('',#2895,#2894);
#2897=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2898=PRESENTATION_STYLE_ASSIGNMENT((#2897));
#2899=STYLED_ITEM('',(#2898),#2896);
#2900=DIRECTION('',(-1.E0,0.E0,0.E0));
#2901=VECTOR('',#2900,1.001413869739E-2);
#2902=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,0.E0));
#2903=LINE('',#2902,#2901);
#2904=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2905=PRESENTATION_STYLE_ASSIGNMENT((#2904));
#2906=STYLED_ITEM('',(#2905),#2903);
#2907=DIRECTION('',(0.E0,0.E0,1.E0));
#2908=VECTOR('',#2907,3.9E-2);
#2909=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.9E-2));
#2910=LINE('',#2909,#2908);
#2911=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2912=PRESENTATION_STYLE_ASSIGNMENT((#2911));
#2913=STYLED_ITEM('',(#2912),#2910);
#2914=DIRECTION('',(0.E0,-1.E0,0.E0));
#2915=VECTOR('',#2914,1.E-2);
#2916=CARTESIAN_POINT('',(0.E0,5.E-3,0.E0));
#2917=LINE('',#2916,#2915);
#2918=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2919=PRESENTATION_STYLE_ASSIGNMENT((#2918));
#2920=STYLED_ITEM('',(#2919),#2917);
#2921=DIRECTION('',(0.E0,0.E0,1.E0));
#2922=VECTOR('',#2921,3.9E-2);
#2923=CARTESIAN_POINT('',(0.E0,5.E-3,-3.9E-2));
#2924=LINE('',#2923,#2922);
#2925=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2926=PRESENTATION_STYLE_ASSIGNMENT((#2925));
#2927=STYLED_ITEM('',(#2926),#2924);
#2928=DIRECTION('',(1.E0,0.E0,0.E0));
#2929=VECTOR('',#2928,1.001413869739E-2);
#2930=CARTESIAN_POINT('',(0.E0,-5.E-3,0.E0));
#2931=LINE('',#2930,#2929);
#2932=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2933=PRESENTATION_STYLE_ASSIGNMENT((#2932));
#2934=STYLED_ITEM('',(#2933),#2931);
#2935=DIRECTION('',(0.E0,0.E0,1.E0));
#2936=VECTOR('',#2935,3.9E-2);
#2937=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.9E-2));
#2938=LINE('',#2937,#2936);
#2939=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#2940=PRESENTATION_STYLE_ASSIGNMENT((#2939));
#2941=STYLED_ITEM('',(#2940),#2938);
#2942=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587458E-3,-3.9E-2));
#2943=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.9E-2));
#2944=VERTEX_POINT('',#2942);
#2945=VERTEX_POINT('',#2943);
#2946=CARTESIAN_POINT('',(0.E0,5.E-3,-3.9E-2));
#2947=VERTEX_POINT('',#2946);
#2948=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.9E-2));
#2949=VERTEX_POINT('',#2948);
#2950=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,-3.9E-2));
#2951=VERTEX_POINT('',#2950);
#2952=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.9E-2));
#2953=VERTEX_POINT('',#2952);
#2954=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-1.9E-2));
#2955=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-1.9E-2));
#2956=VERTEX_POINT('',#2954);
#2957=VERTEX_POINT('',#2955);
#2958=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-1.9E-2));
#2959=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-1.9E-2));
#2960=VERTEX_POINT('',#2958);
#2961=VERTEX_POINT('',#2959);
#2962=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,0.E0));
#2963=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587459E-3,0.E0));
#2964=VERTEX_POINT('',#2962);
#2965=VERTEX_POINT('',#2963);
#2966=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,0.E0));
#2967=VERTEX_POINT('',#2966);
#2968=CARTESIAN_POINT('',(0.E0,5.E-3,0.E0));
#2969=VERTEX_POINT('',#2968);
#2970=CARTESIAN_POINT('',(0.E0,-5.E-3,0.E0));
#2971=VERTEX_POINT('',#2970);
#2972=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,0.E0));
#2973=VERTEX_POINT('',#2972);
#2974=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,0.E0));
#2975=VERTEX_POINT('',#2974);
#2976=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,0.E0));
#2977=VERTEX_POINT('',#2976);
#2978=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,-3.9E-2));
#2979=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,-3.9E-2));
#2980=VERTEX_POINT('',#2978);
#2981=VERTEX_POINT('',#2979);
#2982=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-2.9E-2));
#2983=VERTEX_POINT('',#2982);
#2984=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,-2.9E-2));
#2985=VERTEX_POINT('',#2984);
#2986=CARTESIAN_POINT('',(0.E0,0.E0,-3.9E-2));
#2987=DIRECTION('',(0.E0,0.E0,1.E0));
#2988=DIRECTION('',(1.E0,0.E0,0.E0));
#2989=AXIS2_PLACEMENT_3D('',#2986,#2987,#2988);
#2990=PLANE('',#2989);
#2992=ORIENTED_EDGE('',*,*,#2991,.T.);
#2994=ORIENTED_EDGE('',*,*,#2993,.T.);
#2996=ORIENTED_EDGE('',*,*,#2995,.T.);
#2998=ORIENTED_EDGE('',*,*,#2997,.T.);
#3000=ORIENTED_EDGE('',*,*,#2999,.T.);
#3002=ORIENTED_EDGE('',*,*,#3001,.T.);
#3004=ORIENTED_EDGE('',*,*,#3003,.T.);
#3006=ORIENTED_EDGE('',*,*,#3005,.T.);
#3007=EDGE_LOOP('',(#2992,#2994,#2996,#2998,#3000,#3002,#3004,#3006));
#3008=FACE_OUTER_BOUND('',#3007,.F.);
#3010=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,-3.9E-2));
#3011=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#3012=DIRECTION('',(3.758816601945E-2,-7.061070243067E-1,7.071067811865E-1));
#3013=AXIS2_PLACEMENT_3D('',#3010,#3011,#3012);
#3014=PLANE('',#3013);
#3016=ORIENTED_EDGE('',*,*,#3015,.T.);
#3018=ORIENTED_EDGE('',*,*,#3017,.T.);
#3020=ORIENTED_EDGE('',*,*,#3019,.F.);
#3021=ORIENTED_EDGE('',*,*,#2991,.F.);
#3022=EDGE_LOOP('',(#3016,#3018,#3020,#3021));
#3023=FACE_OUTER_BOUND('',#3022,.F.);
#3025=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.9E-2));
#3026=DIRECTION('',(-9.985861302615E-1,-5.315769416943E-2,0.E0));
#3027=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3028=AXIS2_PLACEMENT_3D('',#3025,#3026,#3027);
#3029=PLANE('',#3028);
#3030=ORIENTED_EDGE('',*,*,#3015,.F.);
#3031=ORIENTED_EDGE('',*,*,#3005,.F.);
#3033=ORIENTED_EDGE('',*,*,#3032,.T.);
#3035=ORIENTED_EDGE('',*,*,#3034,.T.);
#3037=ORIENTED_EDGE('',*,*,#3036,.F.);
#3039=ORIENTED_EDGE('',*,*,#3038,.T.);
#3041=ORIENTED_EDGE('',*,*,#3040,.T.);
#3042=EDGE_LOOP('',(#3030,#3031,#3033,#3035,#3037,#3039,#3041));
#3043=FACE_OUTER_BOUND('',#3042,.F.);
#3045=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.9E-2));
#3046=DIRECTION('',(0.E0,0.E0,1.E0));
#3047=DIRECTION('',(1.E0,0.E0,0.E0));
#3048=AXIS2_PLACEMENT_3D('',#3045,#3046,#3047);
#3049=CYLINDRICAL_SURFACE('',#3048,5.E-3);
#3051=ORIENTED_EDGE('',*,*,#3050,.F.);
#3052=ORIENTED_EDGE('',*,*,#3032,.F.);
#3053=ORIENTED_EDGE('',*,*,#3003,.F.);
#3055=ORIENTED_EDGE('',*,*,#3054,.T.);
#3056=EDGE_LOOP('',(#3051,#3052,#3053,#3055));
#3057=FACE_OUTER_BOUND('',#3056,.F.);
#3059=CARTESIAN_POINT('',(0.E0,5.E-3,0.E0));
#3060=DIRECTION('',(0.E0,0.E0,-1.E0));
#3061=DIRECTION('',(0.E0,-1.E0,0.E0));
#3062=AXIS2_PLACEMENT_3D('',#3059,#3060,#3061);
#3063=PLANE('',#3062);
#3065=ORIENTED_EDGE('',*,*,#3064,.F.);
#3067=ORIENTED_EDGE('',*,*,#3066,.F.);
#3068=ORIENTED_EDGE('',*,*,#3034,.F.);
#3069=ORIENTED_EDGE('',*,*,#3050,.T.);
#3071=ORIENTED_EDGE('',*,*,#3070,.F.);
#3073=ORIENTED_EDGE('',*,*,#3072,.F.);
#3075=ORIENTED_EDGE('',*,*,#3074,.F.);
#3077=ORIENTED_EDGE('',*,*,#3076,.F.);
#3078=EDGE_LOOP('',(#3065,#3067,#3068,#3069,#3071,#3073,#3075,#3077));
#3079=FACE_OUTER_BOUND('',#3078,.F.);
#3081=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-3.9E-2));
#3082=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3083=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3084=AXIS2_PLACEMENT_3D('',#3081,#3082,#3083);
#3085=PLANE('',#3084);
#3086=ORIENTED_EDGE('',*,*,#3019,.T.);
#3088=ORIENTED_EDGE('',*,*,#3087,.F.);
#3090=ORIENTED_EDGE('',*,*,#3089,.F.);
#3092=ORIENTED_EDGE('',*,*,#3091,.T.);
#3093=ORIENTED_EDGE('',*,*,#3064,.T.);
#3095=ORIENTED_EDGE('',*,*,#3094,.F.);
#3096=ORIENTED_EDGE('',*,*,#2993,.F.);
#3097=EDGE_LOOP('',(#3086,#3088,#3090,#3092,#3093,#3095,#3096));
#3098=FACE_OUTER_BOUND('',#3097,.F.);
#3100=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,-1.9E-2));
#3101=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3102=DIRECTION('',(0.E0,0.E0,-1.E0));
#3103=AXIS2_PLACEMENT_3D('',#3100,#3101,#3102);
#3104=PLANE('',#3103);
#3105=ORIENTED_EDGE('',*,*,#3017,.F.);
#3106=ORIENTED_EDGE('',*,*,#3040,.F.);
#3108=ORIENTED_EDGE('',*,*,#3107,.F.);
#3109=ORIENTED_EDGE('',*,*,#3087,.T.);
#3110=EDGE_LOOP('',(#3105,#3106,#3108,#3109));
#3111=FACE_OUTER_BOUND('',#3110,.F.);
#3113=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-1.9E-2));
#3114=DIRECTION('',(0.E0,0.E0,-1.E0));
#3115=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3116=AXIS2_PLACEMENT_3D('',#3113,#3114,#3115);
#3117=PLANE('',#3116);
#3118=ORIENTED_EDGE('',*,*,#3089,.T.);
#3119=ORIENTED_EDGE('',*,*,#3107,.T.);
#3120=ORIENTED_EDGE('',*,*,#3038,.F.);
#3122=ORIENTED_EDGE('',*,*,#3121,.T.);
#3123=EDGE_LOOP('',(#3118,#3119,#3120,#3122));
#3124=FACE_OUTER_BOUND('',#3123,.F.);
#3126=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-3.9E-2));
#3127=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3128=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3129=AXIS2_PLACEMENT_3D('',#3126,#3127,#3128);
#3130=PLANE('',#3129);
#3131=ORIENTED_EDGE('',*,*,#3066,.T.);
#3132=ORIENTED_EDGE('',*,*,#3091,.F.);
#3133=ORIENTED_EDGE('',*,*,#3121,.F.);
#3134=ORIENTED_EDGE('',*,*,#3036,.T.);
#3135=EDGE_LOOP('',(#3131,#3132,#3133,#3134));
#3136=FACE_OUTER_BOUND('',#3135,.F.);
#3138=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.9E-2));
#3139=DIRECTION('',(0.E0,0.E0,1.E0));
#3140=DIRECTION('',(1.E0,0.E0,0.E0));
#3141=AXIS2_PLACEMENT_3D('',#3138,#3139,#3140);
#3142=CYLINDRICAL_SURFACE('',#3141,1.5E-2);
#3143=ORIENTED_EDGE('',*,*,#3076,.T.);
#3145=ORIENTED_EDGE('',*,*,#3144,.F.);
#3146=ORIENTED_EDGE('',*,*,#2995,.F.);
#3147=ORIENTED_EDGE('',*,*,#3094,.T.);
#3148=EDGE_LOOP('',(#3143,#3145,#3146,#3147));
#3149=FACE_OUTER_BOUND('',#3148,.F.);
#3151=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.9E-2));
#3152=DIRECTION('',(0.E0,1.E0,0.E0));
#3153=DIRECTION('',(-1.E0,0.E0,0.E0));
#3154=AXIS2_PLACEMENT_3D('',#3151,#3152,#3153);
#3155=PLANE('',#3154);
#3156=ORIENTED_EDGE('',*,*,#3074,.T.);
#3158=ORIENTED_EDGE('',*,*,#3157,.F.);
#3159=ORIENTED_EDGE('',*,*,#2997,.F.);
#3160=ORIENTED_EDGE('',*,*,#3144,.T.);
#3161=EDGE_LOOP('',(#3156,#3158,#3159,#3160));
#3162=FACE_OUTER_BOUND('',#3161,.F.);
#3164=CARTESIAN_POINT('',(0.E0,5.E-3,-3.9E-2));
#3165=DIRECTION('',(-1.E0,0.E0,0.E0));
#3166=DIRECTION('',(0.E0,-1.E0,0.E0));
#3167=AXIS2_PLACEMENT_3D('',#3164,#3165,#3166);
#3168=PLANE('',#3167);
#3169=ORIENTED_EDGE('',*,*,#3072,.T.);
#3171=ORIENTED_EDGE('',*,*,#3170,.F.);
#3172=ORIENTED_EDGE('',*,*,#2999,.F.);
#3173=ORIENTED_EDGE('',*,*,#3157,.T.);
#3174=EDGE_LOOP('',(#3169,#3171,#3172,#3173));
#3175=FACE_OUTER_BOUND('',#3174,.F.);
#3177=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.9E-2));
#3178=DIRECTION('',(0.E0,-1.E0,0.E0));
#3179=DIRECTION('',(1.E0,0.E0,0.E0));
#3180=AXIS2_PLACEMENT_3D('',#3177,#3178,#3179);
#3181=PLANE('',#3180);
#3182=ORIENTED_EDGE('',*,*,#3070,.T.);
#3183=ORIENTED_EDGE('',*,*,#3054,.F.);
#3184=ORIENTED_EDGE('',*,*,#3001,.F.);
#3185=ORIENTED_EDGE('',*,*,#3170,.T.);
#3186=EDGE_LOOP('',(#3182,#3183,#3184,#3185));
#3187=FACE_OUTER_BOUND('',#3186,.F.);
#3189=CLOSED_SHELL('',(#3009,#3024,#3044,#3058,#3080,#3099,#3112,#3125,#3137,
#3150,#3163,#3176,#3188));
#3190=MANIFOLD_SOLID_BREP('',#3189);
#3191=FILL_AREA_STYLE_COLOUR('',#26);
#3192=FILL_AREA_STYLE('',(#3191));
#3193=SURFACE_STYLE_FILL_AREA(#3192);
#3194=SURFACE_SIDE_STYLE('',(#3193));
#3195=SURFACE_STYLE_USAGE(.BOTH.,#3194);
#3196=PRESENTATION_STYLE_ASSIGNMENT((#3195));
#2706=STYLED_ITEM('',(#3196),#3190);
#3198=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3197);
#3199=(CONVERSION_BASED_UNIT('INCH',#3198)LENGTH_UNIT()NAMED_UNIT(*));
#3201=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#3200);
#3202=(CONVERSION_BASED_UNIT('DEGREE',#3201)NAMED_UNIT(*)PLANE_ANGLE_UNIT());
#3204=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.463000806466E-5),#3199,
'distance_accuracy_value',
'Maximum model space distance between geometric entities at asserted connectivities');
#3206=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#3207=DIRECTION('',(0.E0,0.E0,1.E0));
#3208=DIRECTION('',(1.E0,0.E0,0.E0));
#3211=PRODUCT_DEFINITION_CONTEXT('part definition',#1797,'design');
#3212=PRODUCT_CONTEXT('',#1797,'mechanical');
#3213=PRODUCT('LD_LHALF_P0008A','LD_LHALF_P0008A','NOT SPECIFIED',(#3212));
#3214=PRODUCT_DEFINITION_FORMATION('12','LAST_VERSION',#3213);
#3222=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3221);
#3223=(CONVERSION_BASED_UNIT('INCH',#3222)LENGTH_UNIT()NAMED_UNIT(*));
#3224=DERIVED_UNIT_ELEMENT(#3223,2.E0);
#3225=DERIVED_UNIT((#3224));
#3226=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
1.900313833477E-2),#3225);
#3231=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3230);
#3232=(CONVERSION_BASED_UNIT('INCH',#3231)LENGTH_UNIT()NAMED_UNIT(*));
#3233=DERIVED_UNIT_ELEMENT(#3232,3.E0);
#3234=DERIVED_UNIT((#3233));
#3235=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
6.725581246617E-5),#3234);
#3239=CARTESIAN_POINT('centre point',(2.288715036460E-2,-8.426397449055E-2,
-1.564511632779E-2));
#3244=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3243);
#3245=(CONVERSION_BASED_UNIT('INCH',#3244)LENGTH_UNIT()NAMED_UNIT(*));
#3246=DERIVED_UNIT_ELEMENT(#3245,2.E0);
#3247=DERIVED_UNIT((#3246));
#3248=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
1.900313833477E-2),#3247);
#3253=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3252);
#3254=(CONVERSION_BASED_UNIT('INCH',#3253)LENGTH_UNIT()NAMED_UNIT(*));
#3255=DERIVED_UNIT_ELEMENT(#3254,3.E0);
#3256=DERIVED_UNIT((#3255));
#3257=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
6.725581246617E-5),#3256);
#3261=CARTESIAN_POINT('centre point',(2.288715036460E-2,-8.426397449055E-2,
-1.564511632779E-2));
#3266=DRAUGHTING_MODEL('Default',(#3278,#3279,#3295),#1854);
#3267=PRESENTATION_VIEW('Default',(#3286,#3299),#1854);
#3268=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#3269=CARTESIAN_POINT('',(-7.615322037358E-2,-1.641676602187E-1,
3.961891810426E-1));
#3270=DIRECTION('',(0.E0,0.E0,1.E0));
#3271=DIRECTION('',(1.E0,0.E0,0.E0));
#3272=AXIS2_PLACEMENT_3D('',#3269,#3270,#3271);
#3274=VIEW_VOLUME(.PARALLEL.,#3268,3.961891810426E-1,3.961891810426E-1,.T.,0.E0,
.F.,.T.,#3273);
#3275=CARTESIAN_POINT('',(8.414157326842E-2,2.070289363722E-1,
3.738655051789E-1));
#3276=DIRECTION('',(3.894183423087E-1,7.214918620107E-1,5.725406952575E-1));
#3277=DIRECTION('',(9.210609940029E-1,-3.050418666329E-1,-2.420663234065E-1));
#3278=AXIS2_PLACEMENT_3D('',#3275,#3276,#3277);
#3279=CAMERA_MODEL_D3_WITH_HLHSR('DEFAULT',#3278,#3274,.F.);
#3281=CARTESIAN_POINT('',(-2.898843774382E2,-6.249195995195E2,
1.508131284940E3));
#3282=DIRECTION('',(0.E0,0.E0,1.E0));
#3283=DIRECTION('',(1.E0,0.E0,0.E0));
#3284=AXIS2_PLACEMENT_3D('',#3281,#3282,#3283);
#3286=(CAMERA_IMAGE()CAMERA_IMAGE_3D_WITH_SCALE()GEOMETRIC_REPRESENTATION_ITEM()MAPPED_ITEM(#3280,#3285)REPRESENTATION_ITEM(''));
#3287=CARTESIAN_POINT('',(-7.615322037358E-2,-1.641676602187E-1,
3.961891810426E-1));
#3288=DIRECTION('',(0.E0,0.E0,1.E0));
#3289=DIRECTION('',(1.E0,0.E0,0.E0));
#3290=AXIS2_PLACEMENT_3D('',#3287,#3288,#3289);
#3291=REPRESENTATION_MAP(#3290,#3210);
#3292=CARTESIAN_POINT('',(0.E0,0.E0));
#3293=DIRECTION('',(1.E0,0.E0));
#3294=AXIS2_PLACEMENT_2D('',#3292,#3293);
#3295=MAPPED_ITEM('',#3291,#3294);
#3296=CARTESIAN_POINT('',(-7.615322037358E-2,-1.641676602187E-1,
3.961891810426E-1));
#3297=DIRECTION('',(0.E0,0.E0,1.E0));
#3298=DIRECTION('',(1.E0,0.E0,0.E0));
#3299=AXIS2_PLACEMENT_3D('',#3296,#3297,#3298);
#3300=REPRESENTATION_MAP(#3299,#3267);
#3301=CARTESIAN_POINT('',(0.E0,0.E0));
#3302=DIRECTION('',(1.E0,0.E0));
#3303=AXIS2_PLACEMENT_2D('',#3301,#3302);
#3304=MAPPED_ITEM('',#3300,#3303);
#3306=CARTESIAN_POINT('',(0.E0,0.E0));
#3307=DIRECTION('',(1.E0,0.E0));
#3308=AXIS2_PLACEMENT_2D('',#3306,#3307);
#3309=PLANAR_BOX('',1.E3,8.4375E2,#3308);
#3310=PRESENTATION_SIZE(#3305,#3309);
#3311=PRESENTATION_SET();
#3312=AREA_IN_SET(#3305,#3311);
#3313=APPLIED_PRESENTED_ITEM((#3215));
#3314=PRESENTED_ITEM_REPRESENTATION(#3311,#3313);
#3317=CARTESIAN_POINT('centre point',(1.478871503646E-1,-1.433548836722E-1,
8.957150618036E-3));
#3321=CARTESIAN_POINT('',(1.25E-1,-1.59E-1,9.322112510859E-2));
#3322=DIRECTION('',(0.E0,-1.E0,0.E0));
#3323=DIRECTION('',(1.E0,0.E0,0.E0));
#3324=AXIS2_PLACEMENT_3D('',#3321,#3322,#3323);
#3325=ITEM_DEFINED_TRANSFORMATION('','',#3209,#3324);
#3326=(REPRESENTATION_RELATIONSHIP('','',#3210,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3325)SHAPE_REPRESENTATION_RELATIONSHIP());
#3327=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3326,#3316);
#3329=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3330=VECTOR('',#3329,1.E-2);
#3331=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,3.9E-2));
#3332=LINE('',#3331,#3330);
#3333=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3334=PRESENTATION_STYLE_ASSIGNMENT((#3333));
#3335=STYLED_ITEM('',(#3334),#3332);
#3336=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3337=VECTOR('',#3336,7.413744991544E-2);
#3338=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,3.9E-2));
#3339=LINE('',#3338,#3337);
#3340=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3341=PRESENTATION_STYLE_ASSIGNMENT((#3340));
#3342=STYLED_ITEM('',(#3341),#3339);
#3343=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,3.9E-2));
#3344=DIRECTION('',(0.E0,0.E0,-1.E0));
#3345=DIRECTION('',(0.E0,1.E0,0.E0));
#3346=AXIS2_PLACEMENT_3D('',#3343,#3344,#3345);
#3348=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3349=PRESENTATION_STYLE_ASSIGNMENT((#3348));
#3350=STYLED_ITEM('',(#3349),#3347);
#3351=DIRECTION('',(1.E0,0.E0,0.E0));
#3352=VECTOR('',#3351,1.001413869739E-2);
#3353=CARTESIAN_POINT('',(0.E0,-5.E-3,3.9E-2));
#3354=LINE('',#3353,#3352);
#3355=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3356=PRESENTATION_STYLE_ASSIGNMENT((#3355));
#3357=STYLED_ITEM('',(#3356),#3354);
#3358=DIRECTION('',(0.E0,-1.E0,0.E0));
#3359=VECTOR('',#3358,1.E-2);
#3360=CARTESIAN_POINT('',(0.E0,5.E-3,3.9E-2));
#3361=LINE('',#3360,#3359);
#3362=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3363=PRESENTATION_STYLE_ASSIGNMENT((#3362));
#3364=STYLED_ITEM('',(#3363),#3361);
#3365=DIRECTION('',(-1.E0,0.E0,0.E0));
#3366=VECTOR('',#3365,1.001413869739E-2);
#3367=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,3.9E-2));
#3368=LINE('',#3367,#3366);
#3369=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3370=PRESENTATION_STYLE_ASSIGNMENT((#3369));
#3371=STYLED_ITEM('',(#3370),#3368);
#3372=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,3.9E-2));
#3373=DIRECTION('',(0.E0,0.E0,1.E0));
#3374=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3375=AXIS2_PLACEMENT_3D('',#3372,#3373,#3374);
#3377=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3378=PRESENTATION_STYLE_ASSIGNMENT((#3377));
#3379=STYLED_ITEM('',(#3378),#3376);
#3380=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3381=VECTOR('',#3380,7.413744991544E-2);
#3382=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,3.9E-2));
#3383=LINE('',#3382,#3381);
#3384=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3385=PRESENTATION_STYLE_ASSIGNMENT((#3384));
#3386=STYLED_ITEM('',(#3385),#3383);
#3387=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#3388=VECTOR('',#3387,1.414213562373E-2);
#3389=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.9E-2));
#3390=LINE('',#3389,#3388);
#3391=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3392=PRESENTATION_STYLE_ASSIGNMENT((#3391));
#3393=STYLED_ITEM('',(#3392),#3390);
#3394=DIRECTION('',(0.E0,0.E0,-1.E0));
#3395=VECTOR('',#3394,9.E-3);
#3396=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.9E-2));
#3397=LINE('',#3396,#3395);
#3398=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3399=PRESENTATION_STYLE_ASSIGNMENT((#3398));
#3400=STYLED_ITEM('',(#3399),#3397);
#3401=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3402=VECTOR('',#3401,1.322E-1);
#3403=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,2.E-2));
#3404=LINE('',#3403,#3402);
#3405=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3406=PRESENTATION_STYLE_ASSIGNMENT((#3405));
#3407=STYLED_ITEM('',(#3406),#3404);
#3408=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3409=VECTOR('',#3408,2.163374499154E-1);
#3410=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,0.E0));
#3411=LINE('',#3410,#3409);
#3412=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3413=PRESENTATION_STYLE_ASSIGNMENT((#3412));
#3414=STYLED_ITEM('',(#3413),#3411);
#3415=DIRECTION('',(0.E0,0.E0,1.E0));
#3416=VECTOR('',#3415,3.9E-2);
#3417=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,0.E0));
#3418=LINE('',#3417,#3416);
#3419=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3420=PRESENTATION_STYLE_ASSIGNMENT((#3419));
#3421=STYLED_ITEM('',(#3420),#3418);
#3422=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#3423=VECTOR('',#3422,1.E-2);
#3424=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.9E-2));
#3425=LINE('',#3424,#3423);
#3426=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3427=PRESENTATION_STYLE_ASSIGNMENT((#3426));
#3428=STYLED_ITEM('',(#3427),#3425);
#3429=DIRECTION('',(-9.985861302615E-1,-5.315769416944E-2,0.E0));
#3430=VECTOR('',#3429,1.E-2);
#3431=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#3432=LINE('',#3431,#3430);
#3433=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3434=PRESENTATION_STYLE_ASSIGNMENT((#3433));
#3435=STYLED_ITEM('',(#3434),#3432);
#3436=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#3437=VECTOR('',#3436,1.414213562373E-2);
#3438=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.9E-2));
#3439=LINE('',#3438,#3437);
#3440=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3441=PRESENTATION_STYLE_ASSIGNMENT((#3440));
#3442=STYLED_ITEM('',(#3441),#3439);
#3443=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3444=VECTOR('',#3443,2.163374499154E-1);
#3445=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,0.E0));
#3446=LINE('',#3445,#3444);
#3447=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3448=PRESENTATION_STYLE_ASSIGNMENT((#3447));
#3449=STYLED_ITEM('',(#3448),#3446);
#3450=DIRECTION('',(0.E0,0.E0,1.E0));
#3451=VECTOR('',#3450,2.E-2);
#3452=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,0.E0));
#3453=LINE('',#3452,#3451);
#3454=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3455=PRESENTATION_STYLE_ASSIGNMENT((#3454));
#3456=STYLED_ITEM('',(#3455),#3453);
#3457=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3458=VECTOR('',#3457,1.322E-1);
#3459=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,2.E-2));
#3460=LINE('',#3459,#3458);
#3461=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3462=PRESENTATION_STYLE_ASSIGNMENT((#3461));
#3463=STYLED_ITEM('',(#3462),#3460);
#3464=DIRECTION('',(0.E0,0.E0,-1.E0));
#3465=VECTOR('',#3464,9.E-3);
#3466=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.9E-2));
#3467=LINE('',#3466,#3465);
#3468=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3469=PRESENTATION_STYLE_ASSIGNMENT((#3468));
#3470=STYLED_ITEM('',(#3469),#3467);
#3471=DIRECTION('',(0.E0,0.E0,1.E0));
#3472=VECTOR('',#3471,3.9E-2);
#3473=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587459E-3,0.E0));
#3474=LINE('',#3473,#3472);
#3475=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3476=PRESENTATION_STYLE_ASSIGNMENT((#3475));
#3477=STYLED_ITEM('',(#3476),#3474);
#3478=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,0.E0));
#3479=DIRECTION('',(0.E0,0.E0,1.E0));
#3480=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3481=AXIS2_PLACEMENT_3D('',#3478,#3479,#3480);
#3483=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3484=PRESENTATION_STYLE_ASSIGNMENT((#3483));
#3485=STYLED_ITEM('',(#3484),#3482);
#3486=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,0.E0));
#3487=DIRECTION('',(0.E0,0.E0,1.E0));
#3488=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3489=AXIS2_PLACEMENT_3D('',#3486,#3487,#3488);
#3491=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3492=PRESENTATION_STYLE_ASSIGNMENT((#3491));
#3493=STYLED_ITEM('',(#3492),#3490);
#3494=DIRECTION('',(-1.E0,0.E0,0.E0));
#3495=VECTOR('',#3494,1.001413869739E-2);
#3496=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,0.E0));
#3497=LINE('',#3496,#3495);
#3498=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3499=PRESENTATION_STYLE_ASSIGNMENT((#3498));
#3500=STYLED_ITEM('',(#3499),#3497);
#3501=DIRECTION('',(0.E0,0.E0,1.E0));
#3502=VECTOR('',#3501,3.9E-2);
#3503=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,0.E0));
#3504=LINE('',#3503,#3502);
#3505=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3506=PRESENTATION_STYLE_ASSIGNMENT((#3505));
#3507=STYLED_ITEM('',(#3506),#3504);
#3508=DIRECTION('',(0.E0,-1.E0,0.E0));
#3509=VECTOR('',#3508,1.E-2);
#3510=CARTESIAN_POINT('',(0.E0,5.E-3,0.E0));
#3511=LINE('',#3510,#3509);
#3512=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3513=PRESENTATION_STYLE_ASSIGNMENT((#3512));
#3514=STYLED_ITEM('',(#3513),#3511);
#3515=DIRECTION('',(0.E0,0.E0,1.E0));
#3516=VECTOR('',#3515,3.9E-2);
#3517=CARTESIAN_POINT('',(0.E0,5.E-3,0.E0));
#3518=LINE('',#3517,#3516);
#3519=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3520=PRESENTATION_STYLE_ASSIGNMENT((#3519));
#3521=STYLED_ITEM('',(#3520),#3518);
#3522=DIRECTION('',(1.E0,0.E0,0.E0));
#3523=VECTOR('',#3522,1.001413869739E-2);
#3524=CARTESIAN_POINT('',(0.E0,-5.E-3,0.E0));
#3525=LINE('',#3524,#3523);
#3526=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3527=PRESENTATION_STYLE_ASSIGNMENT((#3526));
#3528=STYLED_ITEM('',(#3527),#3525);
#3529=DIRECTION('',(0.E0,0.E0,1.E0));
#3530=VECTOR('',#3529,3.9E-2);
#3531=CARTESIAN_POINT('',(0.E0,-5.E-3,0.E0));
#3532=LINE('',#3531,#3530);
#3533=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3534=PRESENTATION_STYLE_ASSIGNMENT((#3533));
#3535=STYLED_ITEM('',(#3534),#3532);
#3536=DIRECTION('',(0.E0,0.E0,1.E0));
#3537=VECTOR('',#3536,3.9E-2);
#3538=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,0.E0));
#3539=LINE('',#3538,#3537);
#3540=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3541=PRESENTATION_STYLE_ASSIGNMENT((#3540));
#3542=STYLED_ITEM('',(#3541),#3539);
#3543=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#3544=VECTOR('',#3543,1.E-2);
#3545=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,0.E0));
#3546=LINE('',#3545,#3544);
#3547=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3548=PRESENTATION_STYLE_ASSIGNMENT((#3547));
#3549=STYLED_ITEM('',(#3548),#3546);
#3550=DIRECTION('',(0.E0,0.E0,1.E0));
#3551=VECTOR('',#3550,2.E-2);
#3552=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,0.E0));
#3553=LINE('',#3552,#3551);
#3554=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3555=PRESENTATION_STYLE_ASSIGNMENT((#3554));
#3556=STYLED_ITEM('',(#3555),#3553);
#3557=DIRECTION('',(9.985861302615E-1,5.315769416944E-2,0.E0));
#3558=VECTOR('',#3557,1.E-2);
#3559=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,2.E-2));
#3560=LINE('',#3559,#3558);
#3561=CURVE_STYLE('',#32,POSITIVE_LENGTH_MEASURE(2.E-2),#26);
#3562=PRESENTATION_STYLE_ASSIGNMENT((#3561));
#3563=STYLED_ITEM('',(#3562),#3560);
#3564=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587458E-3,3.9E-2));
#3565=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,3.9E-2));
#3566=VERTEX_POINT('',#3564);
#3567=VERTEX_POINT('',#3565);
#3568=CARTESIAN_POINT('',(0.E0,5.E-3,3.9E-2));
#3569=VERTEX_POINT('',#3568);
#3570=CARTESIAN_POINT('',(0.E0,-5.E-3,3.9E-2));
#3571=VERTEX_POINT('',#3570);
#3572=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,3.9E-2));
#3573=VERTEX_POINT('',#3572);
#3574=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,3.9E-2));
#3575=VERTEX_POINT('',#3574);
#3576=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,2.E-2));
#3577=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#3578=VERTEX_POINT('',#3576);
#3579=VERTEX_POINT('',#3577);
#3580=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,2.E-2));
#3581=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.E-2));
#3582=VERTEX_POINT('',#3580);
#3583=VERTEX_POINT('',#3581);
#3584=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,0.E0));
#3585=CARTESIAN_POINT('',(2.499293065131E-2,-9.202634587459E-3,0.E0));
#3586=VERTEX_POINT('',#3584);
#3587=VERTEX_POINT('',#3585);
#3588=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,0.E0));
#3589=VERTEX_POINT('',#3588);
#3590=CARTESIAN_POINT('',(0.E0,5.E-3,0.E0));
#3591=VERTEX_POINT('',#3590);
#3592=CARTESIAN_POINT('',(0.E0,-5.E-3,0.E0));
#3593=VERTEX_POINT('',#3592);
#3594=CARTESIAN_POINT('',(1.001413869739E-2,-5.E-3,0.E0));
#3595=VERTEX_POINT('',#3594);
#3596=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,0.E0));
#3597=VERTEX_POINT('',#3596);
#3598=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,0.E0));
#3599=VERTEX_POINT('',#3598);
#3600=CARTESIAN_POINT('',(1.894804523780E-2,-8.376684074767E-2,3.9E-2));
#3601=CARTESIAN_POINT('',(2.893390654041E-2,-8.323526380597E-2,3.9E-2));
#3602=VERTEX_POINT('',#3600);
#3603=VERTEX_POINT('',#3601);
#3604=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.9E-2));
#3605=VERTEX_POINT('',#3604);
#3606=CARTESIAN_POINT('',(1.947962217949E-2,-9.375270205028E-2,2.9E-2));
#3607=VERTEX_POINT('',#3606);
#3608=CARTESIAN_POINT('',(0.E0,0.E0,3.9E-2));
#3609=DIRECTION('',(0.E0,0.E0,1.E0));
#3610=DIRECTION('',(1.E0,0.E0,0.E0));
#3611=AXIS2_PLACEMENT_3D('',#3608,#3609,#3610);
#3612=PLANE('',#3611);
#3614=ORIENTED_EDGE('',*,*,#3613,.F.);
#3616=ORIENTED_EDGE('',*,*,#3615,.F.);
#3618=ORIENTED_EDGE('',*,*,#3617,.F.);
#3620=ORIENTED_EDGE('',*,*,#3619,.F.);
#3622=ORIENTED_EDGE('',*,*,#3621,.F.);
#3624=ORIENTED_EDGE('',*,*,#3623,.F.);
#3626=ORIENTED_EDGE('',*,*,#3625,.F.);
#3628=ORIENTED_EDGE('',*,*,#3627,.F.);
#3629=EDGE_LOOP('',(#3614,#3616,#3618,#3620,#3622,#3624,#3626,#3628));
#3630=FACE_OUTER_BOUND('',#3629,.F.);
#3632=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.9E-2));
#3633=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,-7.071067811865E-1));
#3634=DIRECTION('',(-3.758816601945E-2,7.061070243067E-1,7.071067811865E-1));
#3635=AXIS2_PLACEMENT_3D('',#3632,#3633,#3634);
#3636=PLANE('',#3635);
#3638=ORIENTED_EDGE('',*,*,#3637,.T.);
#3639=ORIENTED_EDGE('',*,*,#3613,.T.);
#3641=ORIENTED_EDGE('',*,*,#3640,.F.);
#3643=ORIENTED_EDGE('',*,*,#3642,.F.);
#3644=EDGE_LOOP('',(#3638,#3639,#3641,#3643));
#3645=FACE_OUTER_BOUND('',#3644,.F.);
#3647=CARTESIAN_POINT('',(1.500706934869E-2,-9.734211529153E-3,-3.9E-2));
#3648=DIRECTION('',(-9.985861302615E-1,-5.315769416943E-2,0.E0));
#3649=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3650=AXIS2_PLACEMENT_3D('',#3647,#3648,#3649);
#3651=PLANE('',#3650);
#3652=ORIENTED_EDGE('',*,*,#3637,.F.);
#3654=ORIENTED_EDGE('',*,*,#3653,.T.);
#3656=ORIENTED_EDGE('',*,*,#3655,.F.);
#3658=ORIENTED_EDGE('',*,*,#3657,.F.);
#3660=ORIENTED_EDGE('',*,*,#3659,.F.);
#3662=ORIENTED_EDGE('',*,*,#3661,.T.);
#3663=ORIENTED_EDGE('',*,*,#3615,.T.);
#3664=EDGE_LOOP('',(#3652,#3654,#3656,#3658,#3660,#3662,#3663));
#3665=FACE_OUTER_BOUND('',#3664,.F.);
#3667=CARTESIAN_POINT('',(2.946548348211E-2,-9.322112510859E-2,2.E-2));
#3668=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3669=DIRECTION('',(0.E0,0.E0,1.E0));
#3670=AXIS2_PLACEMENT_3D('',#3667,#3668,#3669);
#3671=PLANE('',#3670);
#3672=ORIENTED_EDGE('',*,*,#3642,.T.);
#3674=ORIENTED_EDGE('',*,*,#3673,.T.);
#3676=ORIENTED_EDGE('',*,*,#3675,.T.);
#3677=ORIENTED_EDGE('',*,*,#3653,.F.);
#3678=EDGE_LOOP('',(#3672,#3674,#3676,#3677));
#3679=FACE_OUTER_BOUND('',#3678,.F.);
#3681=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,-3.9E-2));
#3682=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3683=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3684=AXIS2_PLACEMENT_3D('',#3681,#3682,#3683);
#3685=PLANE('',#3684);
#3686=ORIENTED_EDGE('',*,*,#3640,.T.);
#3687=ORIENTED_EDGE('',*,*,#3627,.T.);
#3689=ORIENTED_EDGE('',*,*,#3688,.F.);
#3691=ORIENTED_EDGE('',*,*,#3690,.F.);
#3693=ORIENTED_EDGE('',*,*,#3692,.T.);
#3695=ORIENTED_EDGE('',*,*,#3694,.T.);
#3696=ORIENTED_EDGE('',*,*,#3673,.F.);
#3697=EDGE_LOOP('',(#3686,#3687,#3689,#3691,#3693,#3695,#3696));
#3698=FACE_OUTER_BOUND('',#3697,.F.);
#3700=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.9E-2));
#3701=DIRECTION('',(0.E0,0.E0,1.E0));
#3702=DIRECTION('',(1.E0,0.E0,0.E0));
#3703=AXIS2_PLACEMENT_3D('',#3700,#3701,#3702);
#3704=CYLINDRICAL_SURFACE('',#3703,1.5E-2);
#3706=ORIENTED_EDGE('',*,*,#3705,.F.);
#3707=ORIENTED_EDGE('',*,*,#3688,.T.);
#3708=ORIENTED_EDGE('',*,*,#3625,.T.);
#3710=ORIENTED_EDGE('',*,*,#3709,.F.);
#3711=EDGE_LOOP('',(#3706,#3707,#3708,#3710));
#3712=FACE_OUTER_BOUND('',#3711,.F.);
#3714=CARTESIAN_POINT('',(0.E0,-2.257657884708E-1,0.E0));
#3715=DIRECTION('',(0.E0,0.E0,1.E0));
#3716=DIRECTION('',(0.E0,1.E0,0.E0));
#3717=AXIS2_PLACEMENT_3D('',#3714,#3715,#3716);
#3718=PLANE('',#3717);
#3719=ORIENTED_EDGE('',*,*,#3690,.T.);
#3720=ORIENTED_EDGE('',*,*,#3705,.T.);
#3722=ORIENTED_EDGE('',*,*,#3721,.T.);
#3724=ORIENTED_EDGE('',*,*,#3723,.T.);
#3726=ORIENTED_EDGE('',*,*,#3725,.T.);
#3728=ORIENTED_EDGE('',*,*,#3727,.F.);
#3729=ORIENTED_EDGE('',*,*,#3659,.T.);
#3731=ORIENTED_EDGE('',*,*,#3730,.T.);
#3732=EDGE_LOOP('',(#3719,#3720,#3722,#3724,#3726,#3728,#3729,#3731));
#3733=FACE_OUTER_BOUND('',#3732,.F.);
#3735=CARTESIAN_POINT('',(1.001413869739E-2,5.E-3,-3.9E-2));
#3736=DIRECTION('',(0.E0,1.E0,0.E0));
#3737=DIRECTION('',(-1.E0,0.E0,0.E0));
#3738=AXIS2_PLACEMENT_3D('',#3735,#3736,#3737);
#3739=PLANE('',#3738);
#3740=ORIENTED_EDGE('',*,*,#3721,.F.);
#3741=ORIENTED_EDGE('',*,*,#3709,.T.);
#3742=ORIENTED_EDGE('',*,*,#3623,.T.);
#3744=ORIENTED_EDGE('',*,*,#3743,.F.);
#3745=EDGE_LOOP('',(#3740,#3741,#3742,#3744));
#3746=FACE_OUTER_BOUND('',#3745,.F.);
#3748=CARTESIAN_POINT('',(0.E0,5.E-3,-3.9E-2));
#3749=DIRECTION('',(-1.E0,0.E0,0.E0));
#3750=DIRECTION('',(0.E0,-1.E0,0.E0));
#3751=AXIS2_PLACEMENT_3D('',#3748,#3749,#3750);
#3752=PLANE('',#3751);
#3753=ORIENTED_EDGE('',*,*,#3723,.F.);
#3754=ORIENTED_EDGE('',*,*,#3743,.T.);
#3755=ORIENTED_EDGE('',*,*,#3621,.T.);
#3757=ORIENTED_EDGE('',*,*,#3756,.F.);
#3758=EDGE_LOOP('',(#3753,#3754,#3755,#3757));
#3759=FACE_OUTER_BOUND('',#3758,.F.);
#3761=CARTESIAN_POINT('',(0.E0,-5.E-3,-3.9E-2));
#3762=DIRECTION('',(0.E0,-1.E0,0.E0));
#3763=DIRECTION('',(1.E0,0.E0,0.E0));
#3764=AXIS2_PLACEMENT_3D('',#3761,#3762,#3763);
#3765=PLANE('',#3764);
#3766=ORIENTED_EDGE('',*,*,#3725,.F.);
#3767=ORIENTED_EDGE('',*,*,#3756,.T.);
#3768=ORIENTED_EDGE('',*,*,#3619,.T.);
#3770=ORIENTED_EDGE('',*,*,#3769,.F.);
#3771=EDGE_LOOP('',(#3766,#3767,#3768,#3770));
#3772=FACE_OUTER_BOUND('',#3771,.F.);
#3774=CARTESIAN_POINT('',(1.001413869739E-2,-1.E-2,-3.9E-2));
#3775=DIRECTION('',(0.E0,0.E0,1.E0));
#3776=DIRECTION('',(1.E0,0.E0,0.E0));
#3777=AXIS2_PLACEMENT_3D('',#3774,#3775,#3776);
#3778=CYLINDRICAL_SURFACE('',#3777,5.E-3);
#3779=ORIENTED_EDGE('',*,*,#3727,.T.);
#3780=ORIENTED_EDGE('',*,*,#3769,.T.);
#3781=ORIENTED_EDGE('',*,*,#3617,.T.);
#3782=ORIENTED_EDGE('',*,*,#3661,.F.);
#3783=EDGE_LOOP('',(#3779,#3780,#3781,#3782));
#3784=FACE_OUTER_BOUND('',#3783,.F.);
#3786=CARTESIAN_POINT('',(2.650706934869E-2,-2.257657884708E-1,-3.9E-2));
#3787=DIRECTION('',(5.315769416943E-2,-9.985861302615E-1,0.E0));
#3788=DIRECTION('',(9.985861302615E-1,5.315769416943E-2,0.E0));
#3789=AXIS2_PLACEMENT_3D('',#3786,#3787,#3788);
#3790=PLANE('',#3789);
#3791=ORIENTED_EDGE('',*,*,#3730,.F.);
#3792=ORIENTED_EDGE('',*,*,#3657,.T.);
#3794=ORIENTED_EDGE('',*,*,#3793,.T.);
#3795=ORIENTED_EDGE('',*,*,#3692,.F.);
#3796=EDGE_LOOP('',(#3791,#3792,#3794,#3795));
#3797=FACE_OUTER_BOUND('',#3796,.F.);
#3799=CARTESIAN_POINT('',(3.649293065131E-2,-2.252342115292E-1,2.E-2));
#3800=DIRECTION('',(0.E0,0.E0,-1.E0));
#3801=DIRECTION('',(-5.315769416943E-2,9.985861302615E-1,0.E0));
#3802=AXIS2_PLACEMENT_3D('',#3799,#3800,#3801);
#3803=PLANE('',#3802);
#3804=ORIENTED_EDGE('',*,*,#3694,.F.);
#3805=ORIENTED_EDGE('',*,*,#3793,.F.);
#3806=ORIENTED_EDGE('',*,*,#3655,.T.);
#3807=ORIENTED_EDGE('',*,*,#3675,.F.);
#3808=EDGE_LOOP('',(#3804,#3805,#3806,#3807));
#3809=FACE_OUTER_BOUND('',#3808,.F.);
#3811=CLOSED_SHELL('',(#3631,#3646,#3666,#3680,#3699,#3713,#3734,#3747,#3760,
#3773,#3785,#3798,#3810));
#3812=MANIFOLD_SOLID_BREP('',#3811);
#3813=FILL_AREA_STYLE_COLOUR('',#26);
#3814=FILL_AREA_STYLE('',(#3813));
#3815=SURFACE_STYLE_FILL_AREA(#3814);
#3816=SURFACE_SIDE_STYLE('',(#3815));
#3817=SURFACE_STYLE_USAGE(.BOTH.,#3816);
#3818=PRESENTATION_STYLE_ASSIGNMENT((#3817));
#3328=STYLED_ITEM('',(#3818),#3812);
#3820=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3819);
#3821=(CONVERSION_BASED_UNIT('INCH',#3820)LENGTH_UNIT()NAMED_UNIT(*));
#3823=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#3822);
#3824=(CONVERSION_BASED_UNIT('DEGREE',#3823)NAMED_UNIT(*)PLANE_ANGLE_UNIT());
#3826=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.463000806466E-5),#3821,
'distance_accuracy_value',
'Maximum model space distance between geometric entities at asserted connectivities');
#3828=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#3829=DIRECTION('',(0.E0,0.E0,1.E0));
#3830=DIRECTION('',(1.E0,0.E0,0.E0));
#3833=PRODUCT_CONTEXT('',#1797,'mechanical');
#3834=PRODUCT('LD_RHALF_P0008A','LD_RHALF_P0008A','NOT SPECIFIED',(#3833));
#3835=PRODUCT_DEFINITION_FORMATION('18','LAST_VERSION',#3834);
#3843=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3842);
#3844=(CONVERSION_BASED_UNIT('INCH',#3843)LENGTH_UNIT()NAMED_UNIT(*));
#3845=DERIVED_UNIT_ELEMENT(#3844,2.E0);
#3846=DERIVED_UNIT((#3845));
#3847=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
1.926753833476E-2),#3846);
#3852=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3851);
#3853=(CONVERSION_BASED_UNIT('INCH',#3852)LENGTH_UNIT()NAMED_UNIT(*));
#3854=DERIVED_UNIT_ELEMENT(#3853,3.E0);
#3855=DERIVED_UNIT((#3854));
#3856=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
6.857781246608E-5),#3855);
#3860=CARTESIAN_POINT('centre point',(2.298544811445E-2,-8.571420120413E-2,
1.571942835426E-2));
#3865=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3864);
#3866=(CONVERSION_BASED_UNIT('INCH',#3865)LENGTH_UNIT()NAMED_UNIT(*));
#3867=DERIVED_UNIT_ELEMENT(#3866,2.E0);
#3868=DERIVED_UNIT((#3867));
#3869=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
1.926753833476E-2),#3868);
#3874=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3873);
#3875=(CONVERSION_BASED_UNIT('INCH',#3874)LENGTH_UNIT()NAMED_UNIT(*));
#3876=DERIVED_UNIT_ELEMENT(#3875,3.E0);
#3877=DERIVED_UNIT((#3876));
#3878=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
6.857781246608E-5),#3877);
#3882=CARTESIAN_POINT('centre point',(2.298544811445E-2,-8.571420120413E-2,
1.571942835426E-2));
#3887=DRAUGHTING_MODEL('Default',(#3899,#3900,#3916),#1854);
#3888=PRESENTATION_VIEW('Default',(#3907,#3920),#1854);
#3889=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#3890=CARTESIAN_POINT('',(-8.559380698643E-2,-1.947174096941E-1,
4.185182681576E-1));
#3891=DIRECTION('',(0.E0,0.E0,1.E0));
#3892=DIRECTION('',(1.E0,0.E0,0.E0));
#3893=AXIS2_PLACEMENT_3D('',#3890,#3891,#3892);
#3895=VIEW_VOLUME(.PARALLEL.,#3889,4.185182681576E-1,4.185182681576E-1,.T.,0.E0,
.F.,.T.,#3894);
#3896=CARTESIAN_POINT('',(8.414157326842E-2,2.070289363722E-1,
4.128655051789E-1));
#3897=DIRECTION('',(3.894183423087E-1,7.214918620107E-1,5.725406952575E-1));
#3898=DIRECTION('',(9.210609940029E-1,-3.050418666329E-1,-2.420663234065E-1));
#3899=AXIS2_PLACEMENT_3D('',#3896,#3897,#3898);
#3900=CAMERA_MODEL_D3_WITH_HLHSR('DEFAULT',#3899,#3895,.F.);
#3902=CARTESIAN_POINT('',(-3.258208560204E2,-7.412100868311E2,
1.593129049779E3));
#3903=DIRECTION('',(0.E0,0.E0,1.E0));
#3904=DIRECTION('',(1.E0,0.E0,0.E0));
#3905=AXIS2_PLACEMENT_3D('',#3902,#3903,#3904);
#3907=(CAMERA_IMAGE()CAMERA_IMAGE_3D_WITH_SCALE()GEOMETRIC_REPRESENTATION_ITEM()MAPPED_ITEM(#3901,#3906)REPRESENTATION_ITEM(''));
#3908=CARTESIAN_POINT('',(-8.559380698643E-2,-1.947174096941E-1,
4.185182681576E-1));
#3909=DIRECTION('',(0.E0,0.E0,1.E0));
#3910=DIRECTION('',(1.E0,0.E0,0.E0));
#3911=AXIS2_PLACEMENT_3D('',#3908,#3909,#3910);
#3912=REPRESENTATION_MAP(#3911,#3832);
#3913=CARTESIAN_POINT('',(0.E0,0.E0));
#3914=DIRECTION('',(1.E0,0.E0));
#3915=AXIS2_PLACEMENT_2D('',#3913,#3914);
#3916=MAPPED_ITEM('',#3912,#3915);
#3917=CARTESIAN_POINT('',(-8.559380698643E-2,-1.947174096941E-1,
4.185182681576E-1));
#3918=DIRECTION('',(0.E0,0.E0,1.E0));
#3919=DIRECTION('',(1.E0,0.E0,0.E0));
#3920=AXIS2_PLACEMENT_3D('',#3917,#3918,#3919);
#3921=REPRESENTATION_MAP(#3920,#3888);
#3922=CARTESIAN_POINT('',(0.E0,0.E0));
#3923=DIRECTION('',(1.E0,0.E0));
#3924=AXIS2_PLACEMENT_2D('',#3922,#3923);
#3925=MAPPED_ITEM('',#3921,#3924);
#3927=CARTESIAN_POINT('',(0.E0,0.E0));
#3928=DIRECTION('',(1.E0,0.E0));
#3929=AXIS2_PLACEMENT_2D('',#3927,#3928);
#3930=PLANAR_BOX('',1.E3,8.4375E2,#3929);
#3931=PRESENTATION_SIZE(#3926,#3930);
#3932=PRESENTATION_SET();
#3933=AREA_IN_SET(#3926,#3932);
#3934=APPLIED_PRESENTED_ITEM((#3836));
#3935=PRESENTED_ITEM_REPRESENTATION(#3932,#3934);
#3938=CARTESIAN_POINT('centre point',(1.479854481144E-1,1.432805716457E-1,
7.506923904458E-3));
#3942=CARTESIAN_POINT('',(1.25E-1,1.59E-1,9.322112510859E-2));
#3943=DIRECTION('',(0.E0,-1.E0,0.E0));
#3944=DIRECTION('',(1.E0,0.E0,0.E0));
#3945=AXIS2_PLACEMENT_3D('',#3942,#3943,#3944);
#3946=ITEM_DEFINED_TRANSFORMATION('','',#3831,#3945);
#3947=(REPRESENTATION_RELATIONSHIP('','',#3832,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3946)SHAPE_REPRESENTATION_RELATIONSHIP());
#3948=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3947,#3937);
#3951=CARTESIAN_POINT('centre point',(-1.478871503646E-1,1.433548836722E-1,
8.957150618036E-3));
#3955=CARTESIAN_POINT('',(-1.25E-1,1.59E-1,9.322112510859E-2));
#3956=DIRECTION('',(0.E0,1.E0,0.E0));
#3957=DIRECTION('',(-1.E0,0.E0,0.E0));
#3958=AXIS2_PLACEMENT_3D('',#3955,#3956,#3957);
#3959=ITEM_DEFINED_TRANSFORMATION('','',#3209,#3958);
#3960=(REPRESENTATION_RELATIONSHIP('','',#3210,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3959)SHAPE_REPRESENTATION_RELATIONSHIP());
#3961=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3960,#3950);
#3964=CARTESIAN_POINT('centre point',(-1.479854481144E-1,-1.432805716457E-1,
7.506923904459E-3));
#3968=CARTESIAN_POINT('',(-1.25E-1,-1.59E-1,9.322112510859E-2));
#3969=DIRECTION('',(0.E0,1.E0,0.E0));
#3970=DIRECTION('',(-1.E0,0.E0,0.E0));
#3971=AXIS2_PLACEMENT_3D('',#3968,#3969,#3970);
#3972=ITEM_DEFINED_TRANSFORMATION('','',#3831,#3971);
#3973=(REPRESENTATION_RELATIONSHIP('','',#3832,#1906)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3972)SHAPE_REPRESENTATION_RELATIONSHIP());
#3974=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3973,#3963);
#3976=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3975);
#3977=(CONVERSION_BASED_UNIT('INCH',#3976)LENGTH_UNIT()NAMED_UNIT(*));
#3979=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#3978);
#3980=(CONVERSION_BASED_UNIT('DEGREE',#3979)NAMED_UNIT(*)PLANE_ANGLE_UNIT());
#3982=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.714593030990E-5),#3977,
'distance_accuracy_value',
'Maximum model space distance between geometric entities at asserted connectivities');
#3984=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#3985=DIRECTION('',(0.E0,0.E0,1.E0));
#3986=DIRECTION('',(1.E0,0.E0,0.E0));
#3988=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#35,#42,#50,
#64,#72,#79,#87,#95,#103,#110,#118,#126,#134,#141,#155,#162,#170,#178,#186,#193,
#207,#214,#221,#229,#243,#251,#258,#265,#273,#280,#294,#301,#308,#316,#323,#331,
#338,#345,#353,#361,#369,#376,#383,#391,#399,#407,#414,#428,#435,#443,#450,#457,
#465,#473,#481,#488,#495,#503,#510,#517,#525,#532,#539,#547,#555,#563,#570,#577,
#584,#591,#599,#607,#615,#622,#629,#637,#645,#653,#660,#667,#674,#681,#689,#703,
#711,#718,#725,#733,#747,#755,#762,#769,#776,#783,#797,#804,#811,#819,#826,#833,
#841,#849,#857,#864,#871,#885,#892,#899,#907,#915,#923,#27,#1927,#1934,#1942,
#1949,#1956,#1963,#1971,#1978,#1985,#1992,#1999,#2006,#2013,#2020,#2027,#2034,
#2041,#2048,#2055,#2062,#2069,#2076,#2083,#2090,#2097,#2104,#2111,#2118,#2125,
#2132,#2139,#2146,#2153,#2160,#2167,#2174,#2182,#2189,#2196,#2203,#2211,#2218,
#1920,#2713,#2720,#2728,#2735,#2742,#2749,#2757,#2764,#2771,#2778,#2785,#2792,
#2799,#2806,#2814,#2822,#2829,#2836,#2843,#2850,#2857,#2864,#2871,#2878,#2885,
#2892,#2899,#2906,#2913,#2920,#2927,#2934,#2941,#2706,#3335,#3342,#3350,#3357,
#3364,#3371,#3379,#3386,#3393,#3400,#3407,#3414,#3421,#3428,#3435,#3442,#3449,
#3456,#3463,#3470,#3477,#3485,#3493,#3500,#3507,#3514,#3521,#3528,#3535,#3542,
#3549,#3556,#3563,#3328),#3983);
#3989=PRODUCT_DEFINITION_CONTEXT('part definition',#1797,'design');
#3990=PRODUCT_CONTEXT('',#1797,'mechanical');
#3991=PRODUCT('P0008A_ASM','P0008A_ASM','NOT SPECIFIED',(#3990));
#3992=PRODUCT_DEFINITION_FORMATION('61','LAST_VERSION',#3991);
#3995=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#3994);
#3996=(CONVERSION_BASED_UNIT('INCH',#3995)LENGTH_UNIT()NAMED_UNIT(*));
#3997=DERIVED_UNIT_ELEMENT(#3996,2.E0);
#3998=DERIVED_UNIT((#3997));
#3999=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
4.923023478193E-1),#3998);
#4004=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#4003);
#4005=(CONVERSION_BASED_UNIT('INCH',#4004)LENGTH_UNIT()NAMED_UNIT(*));
#4006=DERIVED_UNIT_ELEMENT(#4005,3.E0);
#4007=DERIVED_UNIT((#4006));
#4008=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
1.295871430189E-2),#4007);
#4012=CARTESIAN_POINT('centre point',(1.685649321332E-5,-2.743004384770E-4,
8.937770444304E-2));
#4016=PRODUCT_RELATED_PRODUCT_CATEGORY('part','',(#3991,#1801,#2552,#3213,
#3834));
#4017=DRAUGHTING_MODEL('Default',(#4029,#4030,#4046),#1854);
#4018=PRESENTATION_VIEW('Default',(#4037,#4050),#1854);
#4019=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#4020=CARTESIAN_POINT('',(-3.332889291152E-1,-3.441770631096E-1,
1.111010301776E0));
#4021=DIRECTION('',(0.E0,0.E0,1.E0));
#4022=DIRECTION('',(1.E0,0.E0,0.E0));
#4023=AXIS2_PLACEMENT_3D('',#4020,#4021,#4022);
#4025=VIEW_VOLUME(.PARALLEL.,#4019,1.111010301776E0,1.111010301776E0,.T.,0.E0,
.F.,.T.,#4024);
#4026=CARTESIAN_POINT('',(1.256683576646E-1,6.893080751280E-1,
9.863797915306E-1));
#4027=DIRECTION('',(3.894183423087E-1,7.214918620107E-1,5.725406952575E-1));
#4028=DIRECTION('',(9.210609940029E-1,-3.050418666329E-1,-2.420663234065E-1));
#4029=AXIS2_PLACEMENT_3D('',#4026,#4027,#4028);
#4030=CAMERA_MODEL_D3_WITH_HLHSR('DEFAULT',#4029,#4025,.F.);
#4032=CARTESIAN_POINT('',(-4.692360277967E2,-4.845653840979E2,
1.564186551983E3));
#4033=DIRECTION('',(0.E0,0.E0,1.E0));
#4034=DIRECTION('',(1.E0,0.E0,0.E0));
#4035=AXIS2_PLACEMENT_3D('',#4032,#4033,#4034);
#4037=(CAMERA_IMAGE()CAMERA_IMAGE_3D_WITH_SCALE()GEOMETRIC_REPRESENTATION_ITEM()MAPPED_ITEM(#4031,#4036)REPRESENTATION_ITEM(''));
#4038=CARTESIAN_POINT('',(-3.332889291152E-1,-3.441770631096E-1,
1.111010301776E0));
#4039=DIRECTION('',(0.E0,0.E0,1.E0));
#4040=DIRECTION('',(1.E0,0.E0,0.E0));
#4041=AXIS2_PLACEMENT_3D('',#4038,#4039,#4040);
#4042=REPRESENTATION_MAP(#4041,#1906);
#4043=CARTESIAN_POINT('',(0.E0,0.E0));
#4044=DIRECTION('',(1.E0,0.E0));
#4045=AXIS2_PLACEMENT_2D('',#4043,#4044);
#4046=MAPPED_ITEM('',#4042,#4045);
#4047=CARTESIAN_POINT('',(-3.332889291152E-1,-3.441770631096E-1,
1.111010301776E0));
#4048=DIRECTION('',(0.E0,0.E0,1.E0));
#4049=DIRECTION('',(1.E0,0.E0,0.E0));
#4050=AXIS2_PLACEMENT_3D('',#4047,#4048,#4049);
#4051=REPRESENTATION_MAP(#4050,#4018);
#4052=CARTESIAN_POINT('',(0.E0,0.E0));
#4053=DIRECTION('',(1.E0,0.E0));
#4054=AXIS2_PLACEMENT_2D('',#4052,#4053);
#4055=MAPPED_ITEM('',#4051,#4054);
#4057=CARTESIAN_POINT('',(0.E0,0.E0));
#4058=DIRECTION('',(1.E0,0.E0));
#4059=AXIS2_PLACEMENT_2D('',#4057,#4058);
#4060=PLANAR_BOX('',1.E3,8.4375E2,#4059);
#4061=PRESENTATION_SIZE(#4056,#4060);
#4062=PRESENTATION_SET();
#4063=AREA_IN_SET(#4056,#4062);
#4064=APPLIED_PRESENTED_ITEM((#3993));
#4065=PRESENTED_ITEM_REPRESENTATION(#4062,#4064);
#1=COLOUR_RGB('',0.E0,4.3E-1,1.E0);
#2=COLOUR_RGB('',0.E0,6.6E-1,0.E0);
#3=COLOUR_RGB('',9.8039E-2,9.8039E-2,9.8039E-2);
#4=COLOUR_RGB('',1.80392E-1,2.7451E-1,9.8039E-2);
#5=COLOUR_RGB('',3.E-1,1.29412E-1,2.5098E-1);
#6=COLOUR_RGB('',3.13725E-1,3.13725E-1,3.13725E-1);
#7=COLOUR_RGB('',3.2E-1,4.9E-1,1.76471E-1);
#8=COLOUR_RGB('',3.5E-1,3.5E-1,3.5E-1);
#9=COLOUR_RGB('',3.92157E-1,3.92157E-1,3.92157E-1);
#10=COLOUR_RGB('',5.7E-1,0.E0,1.E0);
#11=COLOUR_RGB('',5.9E-1,3.E-1,0.E0);
#12=COLOUR_RGB('',6.E-1,6.E-1,6.E-1);
#13=COLOUR_RGB('',6.2E-1,3.3E-1,2.3E-1);
#14=COLOUR_RGB('',6.35294E-1,6.86275E-1,7.4902E-1);
#15=COLOUR_RGB('',6.9E-1,0.E0,0.E0);
#16=COLOUR_RGB('',7.E-1,7.E-1,7.E-1);
#17=COLOUR_RGB('',8.23529E-1,7.05882E-1,4.31373E-1);
#18=COLOUR_RGB('',8.39216E-1,8.90196E-1,7.68627E-1);
#19=COLOUR_RGB('',8.7E-1,7.5E-1,4.9E-1);
#20=COLOUR_RGB('',9.01961E-1,9.01961E-1,9.01961E-1);
#21=COLOUR_RGB('',9.80392E-1,9.80392E-1,9.80392E-1);
#22=COLOUR_RGB('',9.9E-1,1.E0,0.E0);
#23=COLOUR_RGB('',1.E0,5.E-1,0.E0);
#24=COLOUR_RGB('',1.E0,6.19608E-1,5.17647E-1);
#25=COLOUR_RGB('',1.E0,6.2E-1,5.2E-1);
#26=DRAUGHTING_PRE_DEFINED_COLOUR('white');
#47=B_SPLINE_CURVE_WITH_KNOTS('',3,(#43,#44,#45,#46),.UNSPECIFIED.,.F.,.F.,(4,
4),(0.E0,1.E0),.UNSPECIFIED.);
#61=B_SPLINE_CURVE_WITH_KNOTS('',3,(#51,#52,#53,#54,#55,#56,#57,#58,#59,#60),
.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#69=B_SPLINE_CURVE_WITH_KNOTS('',3,(#65,#66,#67,#68),.UNSPECIFIED.,.F.,.F.,(4,
4),(0.E0,1.E0),.UNSPECIFIED.);
#84=B_SPLINE_CURVE_WITH_KNOTS('',3,(#80,#81,#82,#83),.UNSPECIFIED.,.F.,.F.,(4,
4),(0.E0,1.E0),.UNSPECIFIED.);
#92=B_SPLINE_CURVE_WITH_KNOTS('',3,(#88,#89,#90,#91),.UNSPECIFIED.,.F.,.F.,(4,
4),(0.E0,1.E0),.UNSPECIFIED.);
#100=B_SPLINE_CURVE_WITH_KNOTS('',3,(#96,#97,#98,#99),.UNSPECIFIED.,.F.,.F.,(4,
4),(0.E0,1.E0),.UNSPECIFIED.);
#115=CIRCLE('',#114,3.2E-2);
#123=CIRCLE('',#122,1.25E-2);
#131=CIRCLE('',#130,1.25E-2);
#152=B_SPLINE_CURVE_WITH_KNOTS('',3,(#142,#143,#144,#145,#146,#147,#148,#149,
#150,#151),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#167=CIRCLE('',#166,1.5E-2);
#175=CIRCLE('',#174,1.5E-2);
#183=CIRCLE('',#182,1.5E-2);
#204=B_SPLINE_CURVE_WITH_KNOTS('',3,(#194,#195,#196,#197,#198,#199,#200,#201,
#202,#203),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#226=B_SPLINE_CURVE_WITH_KNOTS('',3,(#222,#223,#224,#225),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#240=B_SPLINE_CURVE_WITH_KNOTS('',3,(#230,#231,#232,#233,#234,#235,#236,#237,
#238,#239),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#248=B_SPLINE_CURVE_WITH_KNOTS('',3,(#244,#245,#246,#247),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#270=B_SPLINE_CURVE_WITH_KNOTS('',3,(#266,#267,#268,#269),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#291=B_SPLINE_CURVE_WITH_KNOTS('',3,(#281,#282,#283,#284,#285,#286,#287,#288,
#289,#290),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#313=B_SPLINE_CURVE_WITH_KNOTS('',3,(#309,#310,#311,#312),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#328=B_SPLINE_CURVE_WITH_KNOTS('',3,(#324,#325,#326,#327),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#350=CIRCLE('',#349,1.5E-2);
#358=CIRCLE('',#357,1.5E-2);
#366=CIRCLE('',#365,1.5E-2);
#388=CIRCLE('',#387,1.5E-2);
#396=CIRCLE('',#395,1.5E-2);
#404=CIRCLE('',#403,1.5E-2);
#425=B_SPLINE_CURVE_WITH_KNOTS('',3,(#415,#416,#417,#418,#419,#420,#421,#422,
#423,#424),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#440=B_SPLINE_CURVE_WITH_KNOTS('',3,(#436,#437,#438,#439),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#462=CIRCLE('',#461,1.5E-2);
#470=CIRCLE('',#469,1.5E-2);
#478=CIRCLE('',#477,1.5E-2);
#500=B_SPLINE_CURVE_WITH_KNOTS('',3,(#496,#497,#498,#499),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#522=B_SPLINE_CURVE_WITH_KNOTS('',3,(#518,#519,#520,#521),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#544=CIRCLE('',#543,1.5E-2);
#552=CIRCLE('',#551,1.5E-2);
#560=CIRCLE('',#559,1.5E-2);
#596=CIRCLE('',#595,1.5E-2);
#604=CIRCLE('',#603,1.5E-2);
#612=CIRCLE('',#611,1.5E-2);
#634=CIRCLE('',#633,1.5E-2);
#642=CIRCLE('',#641,1.5E-2);
#650=CIRCLE('',#649,1.5E-2);
#686=B_SPLINE_CURVE_WITH_KNOTS('',3,(#682,#683,#684,#685),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#700=B_SPLINE_CURVE_WITH_KNOTS('',3,(#690,#691,#692,#693,#694,#695,#696,#697,
#698,#699),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#708=B_SPLINE_CURVE_WITH_KNOTS('',3,(#704,#705,#706,#707),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#730=B_SPLINE_CURVE_WITH_KNOTS('',3,(#726,#727,#728,#729),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#744=B_SPLINE_CURVE_WITH_KNOTS('',3,(#734,#735,#736,#737,#738,#739,#740,#741,
#742,#743),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#752=B_SPLINE_CURVE_WITH_KNOTS('',3,(#748,#749,#750,#751),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#794=B_SPLINE_CURVE_WITH_KNOTS('',3,(#784,#785,#786,#787,#788,#789,#790,#791,
#792,#793),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#816=B_SPLINE_CURVE_WITH_KNOTS('',3,(#812,#813,#814,#815),.UNSPECIFIED.,.F.,.F.,
(4,4),(0.E0,1.E0),.UNSPECIFIED.);
#838=CIRCLE('',#837,1.5E-2);
#846=CIRCLE('',#845,1.5E-2);
#854=CIRCLE('',#853,1.5E-2);
#882=B_SPLINE_CURVE_WITH_KNOTS('',3,(#872,#873,#874,#875,#876,#877,#878,#879,
#880,#881),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,4),(0.E0,1.428571428571E-1,
2.857142857143E-1,4.285714285714E-1,5.714285714286E-1,7.142857142857E-1,
8.571428571429E-1,1.E0),.UNSPECIFIED.);
#904=CIRCLE('',#903,1.25E-2);
#912=CIRCLE('',#911,1.25E-2);
#920=CIRCLE('',#919,3.2E-2);
#1061=EDGE_CURVE('',#988,#989,#39,.T.);
#1063=EDGE_CURVE('',#1008,#989,#262,.T.);
#1065=EDGE_CURVE('',#1008,#1009,#218,.T.);
#1067=EDGE_CURVE('',#1009,#988,#31,.T.);
#1071=ADVANCED_FACE('',(#1070),#1060,.T.);
#1078=EDGE_CURVE('',#988,#998,#47,.T.);
#1080=EDGE_CURVE('',#998,#995,#61,.T.);
#1082=EDGE_CURVE('',#995,#992,#69,.T.);
#1084=EDGE_CURVE('',#992,#993,#76,.T.);
#1086=EDGE_CURVE('',#985,#993,#313,.T.);
#1088=EDGE_CURVE('',#982,#985,#291,.T.);
#1090=EDGE_CURVE('',#989,#982,#270,.T.);
#1094=ADVANCED_FACE('',(#1093),#1076,.T.);
#1100=EDGE_CURVE('',#926,#1043,#84,.T.);
#1102=EDGE_CURVE('',#1043,#999,#868,.T.);
#1104=EDGE_CURVE('',#998,#999,#342,.T.);
#1108=EDGE_CURVE('',#1009,#1002,#92,.T.);
#1110=EDGE_CURVE('',#1002,#1003,#190,.T.);
#1112=EDGE_CURVE('',#1003,#1047,#159,.T.);
#1114=EDGE_CURVE('',#1047,#927,#100,.T.);
#1116=EDGE_CURVE('',#927,#926,#107,.T.);
#1120=ADVANCED_FACE('',(#1119),#1099,.T.);
#1126=EDGE_CURVE('',#1041,#1045,#115,.T.);
#1128=EDGE_CURVE('',#1041,#1043,#882,.T.);
#1131=EDGE_CURVE('',#926,#927,#920,.T.);
#1134=EDGE_CURVE('',#1045,#1047,#152,.T.);
#1138=ADVANCED_FACE('',(#1137),#1125,.F.);
#1145=EDGE_CURVE('',#939,#1045,#138,.T.);
#1147=EDGE_CURVE('',#938,#939,#485,.T.);
#1149=EDGE_CURVE('',#931,#938,#447,.T.);
#1151=EDGE_CURVE('',#930,#931,#373,.T.);
#1153=EDGE_CURVE('',#1041,#930,#861,.T.);
#1157=EDGE_CURVE('',#1054,#1055,#123,.T.);
#1159=EDGE_CURVE('',#1055,#1054,#131,.T.);
#1163=ADVANCED_FACE('',(#1156,#1162),#1143,.T.);
#1172=EDGE_CURVE('',#939,#1003,#183,.T.);
#1176=ADVANCED_FACE('',(#1175),#1168,.T.);
#1182=EDGE_CURVE('',#1003,#943,#167,.T.);
#1184=EDGE_CURVE('',#943,#939,#175,.T.);
#1189=ADVANCED_FACE('',(#1188),#1181,.T.);
#1196=EDGE_CURVE('',#1002,#1005,#204,.T.);
#1198=EDGE_CURVE('',#1005,#943,#211,.T.);
#1203=ADVANCED_FACE('',(#1202),#1194,.T.);
#1210=EDGE_CURVE('',#1008,#1017,#226,.T.);
#1212=EDGE_CURVE('',#1017,#1019,#240,.T.);
#1214=EDGE_CURVE('',#1019,#1012,#248,.T.);
#1216=EDGE_CURVE('',#1012,#1013,#255,.T.);
#1218=EDGE_CURVE('',#1005,#1013,#500,.T.);
#1224=ADVANCED_FACE('',(#1223),#1208,.T.);
#1230=EDGE_CURVE('',#982,#983,#277,.T.);
#1232=EDGE_CURVE('',#983,#1016,#588,.T.);
#1234=EDGE_CURVE('',#1016,#1017,#529,.T.);
#1241=ADVANCED_FACE('',(#1240),#1229,.T.);
#1249=EDGE_CURVE('',#985,#959,#298,.T.);
#1251=EDGE_CURVE('',#983,#959,#596,.T.);
#1255=ADVANCED_FACE('',(#1254),#1246,.T.);
#1261=EDGE_CURVE('',#993,#969,#305,.T.);
#1263=EDGE_CURVE('',#969,#975,#708,.T.);
#1265=EDGE_CURVE('',#975,#958,#657,.T.);
#1267=EDGE_CURVE('',#958,#959,#626,.T.);
#1273=ADVANCED_FACE('',(#1272),#1260,.T.);
#1281=EDGE_CURVE('',#992,#968,#320,.T.);
#1283=EDGE_CURVE('',#968,#969,#671,.T.);
#1287=ADVANCED_FACE('',(#1286),#1278,.T.);
#1295=EDGE_CURVE('',#995,#934,#335,.T.);
#1297=EDGE_CURVE('',#934,#935,#380,.T.);
#1299=EDGE_CURVE('',#965,#935,#432,.T.);
#1301=EDGE_CURVE('',#965,#968,#328,.T.);
#1305=ADVANCED_FACE('',(#1304),#1292,.T.);
#1314=EDGE_CURVE('',#934,#999,#358,.T.);
#1318=ADVANCED_FACE('',(#1317),#1310,.T.);
#1324=EDGE_CURVE('',#930,#934,#350,.T.);
#1327=EDGE_CURVE('',#999,#930,#366,.T.);
#1331=ADVANCED_FACE('',(#1330),#1323,.T.);
#1338=EDGE_CURVE('',#935,#931,#396,.T.);
#1344=ADVANCED_FACE('',(#1343),#1336,.T.);
#1350=EDGE_CURVE('',#963,#935,#388,.T.);
#1353=EDGE_CURVE('',#931,#963,#404,.T.);
#1357=ADVANCED_FACE('',(#1356),#1349,.T.);
#1363=EDGE_CURVE('',#962,#963,#411,.T.);
#1365=EDGE_CURVE('',#962,#965,#425,.T.);
#1371=ADVANCED_FACE('',(#1370),#1362,.T.);
#1378=EDGE_CURVE('',#963,#1039,#454,.T.);
#1380=EDGE_CURVE('',#1038,#1039,#773,.T.);
#1382=EDGE_CURVE('',#1028,#1038,#730,.T.);
#1384=EDGE_CURVE('',#972,#1028,#715,.T.);
#1386=EDGE_CURVE('',#972,#962,#440,.T.);
#1390=ADVANCED_FACE('',(#1389),#1376,.T.);
#1397=EDGE_CURVE('',#1039,#938,#478,.T.);
#1403=ADVANCED_FACE('',(#1402),#1395,.T.);
#1409=EDGE_CURVE('',#938,#942,#462,.T.);
#1411=EDGE_CURVE('',#942,#1039,#470,.T.);
#1416=ADVANCED_FACE('',(#1415),#1408,.T.);
#1424=EDGE_CURVE('',#942,#943,#492,.T.);
#1429=ADVANCED_FACE('',(#1428),#1421,.T.);
#1435=EDGE_CURVE('',#1032,#1013,#507,.T.);
#1437=EDGE_CURVE('',#1035,#1032,#752,.T.);
#1439=EDGE_CURVE('',#1035,#942,#766,.T.);
#1446=ADVANCED_FACE('',(#1445),#1434,.T.);
#1452=EDGE_CURVE('',#1033,#1012,#514,.T.);
#1454=EDGE_CURVE('',#1032,#1033,#759,.T.);
#1460=ADVANCED_FACE('',(#1459),#1451,.T.);
#1468=EDGE_CURVE('',#950,#1019,#536,.T.);
#1470=EDGE_CURVE('',#950,#951,#574,.T.);
#1472=EDGE_CURVE('',#1025,#951,#801,.T.);
#1474=EDGE_CURVE('',#1025,#1033,#522,.T.);
#1478=ADVANCED_FACE('',(#1477),#1465,.T.);
#1485=EDGE_CURVE('',#950,#1016,#552,.T.);
#1491=ADVANCED_FACE('',(#1490),#1483,.T.);
#1497=EDGE_CURVE('',#946,#950,#544,.T.);
#1500=EDGE_CURVE('',#1016,#946,#560,.T.);
#1504=ADVANCED_FACE('',(#1503),#1496,.T.);
#1510=EDGE_CURVE('',#946,#947,#567,.T.);
#1512=EDGE_CURVE('',#951,#947,#846,.T.);
#1518=ADVANCED_FACE('',(#1517),#1509,.T.);
#1525=EDGE_CURVE('',#955,#946,#581,.T.);
#1527=EDGE_CURVE('',#954,#955,#619,.T.);
#1529=EDGE_CURVE('',#947,#954,#823,.T.);
#1533=ADVANCED_FACE('',(#1532),#1523,.T.);
#1542=EDGE_CURVE('',#955,#983,#612,.T.);
#1546=ADVANCED_FACE('',(#1545),#1538,.T.);
#1553=EDGE_CURVE('',#959,#955,#604,.T.);
#1558=ADVANCED_FACE('',(#1557),#1551,.T.);
#1567=EDGE_CURVE('',#954,#958,#634,.T.);
#1571=ADVANCED_FACE('',(#1570),#1563,.T.);
#1578=EDGE_CURVE('',#958,#979,#642,.T.);
#1580=EDGE_CURVE('',#979,#954,#650,.T.);
#1584=ADVANCED_FACE('',(#1583),#1576,.T.);
#1591=EDGE_CURVE('',#975,#978,#700,.T.);
#1593=EDGE_CURVE('',#978,#979,#664,.T.);
#1598=ADVANCED_FACE('',(#1597),#1589,.T.);
#1608=EDGE_CURVE('',#972,#973,#678,.T.);
#1610=EDGE_CURVE('',#978,#973,#686,.T.);
#1616=ADVANCED_FACE('',(#1615),#1603,.T.);
#1622=EDGE_CURVE('',#1028,#1029,#722,.T.);
#1624=EDGE_CURVE('',#973,#1029,#808,.T.);
#1630=ADVANCED_FACE('',(#1629),#1621,.T.);
#1638=EDGE_CURVE('',#1038,#1035,#744,.T.);
#1643=EDGE_CURVE('',#1022,#1025,#794,.T.);
#1645=EDGE_CURVE('',#1029,#1022,#816,.T.);
#1649=ADVANCED_FACE('',(#1648),#1635,.T.);
#1661=ADVANCED_FACE('',(#1660),#1654,.T.);
#1667=EDGE_CURVE('',#1022,#1023,#780,.T.);
#1671=EDGE_CURVE('',#1023,#951,#838,.T.);
#1675=ADVANCED_FACE('',(#1674),#1666,.T.);
#1682=EDGE_CURVE('',#1023,#979,#830,.T.);
#1690=ADVANCED_FACE('',(#1689),#1680,.T.);
#1699=EDGE_CURVE('',#947,#1023,#854,.T.);
#1703=ADVANCED_FACE('',(#1702),#1695,.T.);
#1714=ADVANCED_FACE('',(#1713),#1708,.T.);
#1726=ADVANCED_FACE('',(#1725),#1719,.T.);
#1733=EDGE_CURVE('',#1055,#1051,#896,.T.);
#1735=EDGE_CURVE('',#1050,#1051,#904,.T.);
#1737=EDGE_CURVE('',#1054,#1050,#889,.T.);
#1741=ADVANCED_FACE('',(#1740),#1731,.F.);
#1749=EDGE_CURVE('',#1051,#1050,#912,.T.);
#1754=ADVANCED_FACE('',(#1753),#1746,.F.);
#1764=ADVANCED_FACE('',(#1763),#1759,.F.);
#1774=ADVANCED_FACE('',(#1773),#1769,.T.);
#1783=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1786=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1789=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#1791=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#1790))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1785,#1788,#1789))REPRESENTATION_CONTEXT(
'ID1','3'));
#1795=AXIS2_PLACEMENT_3D('',#1792,#1793,#1794);
#1796=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1795,#1776),#1791);
#1803=PRODUCT_DEFINITION('part definition','',#1802,#1799);
#1804=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR BODY_P0008A.',#1803);
#1805=SHAPE_ASPECT('','solid data associated with BODY_P0008A',#1804,.F.);
#1806=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#1805);
#1807=SHAPE_REPRESENTATION('',(#1776),#1791);
#1808=SHAPE_DEFINITION_REPRESENTATION(#1806,#1807);
#1809=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1815=PROPERTY_DEFINITION('geometric validation property','area of BODY_P0008A',
#1805);
#1816=REPRESENTATION('surface area',(#1814),#1791);
#1817=PROPERTY_DEFINITION_REPRESENTATION(#1815,#1816);
#1818=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1824=PROPERTY_DEFINITION('geometric validation property',
'volume of BODY_P0008A',#1805);
#1825=REPRESENTATION('volume',(#1823),#1791);
#1826=PROPERTY_DEFINITION_REPRESENTATION(#1824,#1825);
#1828=PROPERTY_DEFINITION('geometric validation property',
'centroid of BODY_P0008A',#1805);
#1829=REPRESENTATION('centroid',(#1827),#1791);
#1830=PROPERTY_DEFINITION_REPRESENTATION(#1828,#1829);
#1831=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1837=PROPERTY_DEFINITION('geometric validation property','area of BODY_P0008A',
#1804);
#1838=REPRESENTATION('surface area',(#1836),#1791);
#1839=PROPERTY_DEFINITION_REPRESENTATION(#1837,#1838);
#1840=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1846=PROPERTY_DEFINITION('geometric validation property',
'volume of BODY_P0008A',#1804);
#1847=REPRESENTATION('volume',(#1845),#1791);
#1848=PROPERTY_DEFINITION_REPRESENTATION(#1846,#1847);
#1850=PROPERTY_DEFINITION('geometric validation property',
'centroid of BODY_P0008A',#1804);
#1851=REPRESENTATION('centroid',(#1849),#1791);
#1852=PROPERTY_DEFINITION_REPRESENTATION(#1850,#1851);
#1853=SHAPE_DEFINITION_REPRESENTATION(#1804,#1796);
#1862=PLANAR_BOX('',5.669355151829E-1,4.783518409356E-1,#1861);
#1869=CAMERA_USAGE(#1868,#1855);
#1874=PLANAR_BOX('',1.E3,8.4375E2,#1873);
#1894=PRESENTATION_AREA('',(#1893,#1898),#1854);
#1904=SHAPE_DEFINITION_REPRESENTATION(#1905,#1906);
#1905=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR P0008A_ASM.',#3993);
#1906=SHAPE_REPRESENTATION('',(#1916,#2663,#2676,#2689,#2702,#3324,#3945,#3958,
#3971,#3987),#3983);
#1907=NEXT_ASSEMBLY_USAGE_OCCURRENCE('0','Next assembly relationship',
'BODY_P0008A',#3993,#1803,$);
#1908=PRODUCT_DEFINITION_SHAPE('Placement #0',
'Placement of BODY_P0008A with respect to P0008A_ASM',#1907);
#1910=PROPERTY_DEFINITION('geometric validation property',
'centroid of BODY_P0008A',#1908);
#1911=REPRESENTATION('centroid',(#1909),#1791);
#1912=PROPERTY_DEFINITION_REPRESENTATION(#1910,#1911);
#1939=CIRCLE('',#1938,1.5E-2);
#1968=CIRCLE('',#1967,5.E-3);
#2179=CIRCLE('',#2178,5.E-3);
#2208=CIRCLE('',#2207,1.5E-2);
#2280=EDGE_CURVE('',#2269,#2270,#1924,.T.);
#2282=EDGE_CURVE('',#2270,#2221,#1931,.T.);
#2284=EDGE_CURVE('',#2221,#2222,#1939,.T.);
#2286=EDGE_CURVE('',#2222,#2224,#1946,.T.);
#2288=EDGE_CURVE('',#2224,#2226,#1953,.T.);
#2290=EDGE_CURVE('',#2226,#2228,#1960,.T.);
#2292=EDGE_CURVE('',#2228,#2230,#1968,.T.);
#2294=EDGE_CURVE('',#2230,#2269,#1975,.T.);
#2298=ADVANCED_FACE('',(#2297),#2279,.F.);
#2304=EDGE_CURVE('',#2274,#2269,#1982,.T.);
#2306=EDGE_CURVE('',#2274,#2272,#2115,.T.);
#2308=EDGE_CURVE('',#2272,#2270,#2066,.T.);
#2313=ADVANCED_FACE('',(#2312),#2303,.T.);
#2321=EDGE_CURVE('',#2230,#2242,#1989,.T.);
#2323=EDGE_CURVE('',#2242,#2261,#2171,.T.);
#2325=EDGE_CURVE('',#2266,#2261,#1996,.T.);
#2327=EDGE_CURVE('',#2266,#2250,#2003,.T.);
#2329=EDGE_CURVE('',#2249,#2250,#2010,.T.);
#2331=EDGE_CURVE('',#2257,#2249,#2136,.T.);
#2333=EDGE_CURVE('',#2257,#2258,#2017,.T.);
#2335=EDGE_CURVE('',#2274,#2258,#2024,.T.);
#2339=ADVANCED_FACE('',(#2338),#2318,.T.);
#2346=EDGE_CURVE('',#2228,#2240,#2031,.T.);
#2348=EDGE_CURVE('',#2240,#2242,#2179,.T.);
#2353=ADVANCED_FACE('',(#2352),#2344,.F.);
#2360=EDGE_CURVE('',#2226,#2238,#2038,.T.);
#2362=EDGE_CURVE('',#2238,#2240,#2186,.T.);
#2367=ADVANCED_FACE('',(#2366),#2358,.T.);
#2374=EDGE_CURVE('',#2224,#2236,#2045,.T.);
#2376=EDGE_CURVE('',#2236,#2238,#2193,.T.);
#2381=ADVANCED_FACE('',(#2380),#2372,.T.);
#2388=EDGE_CURVE('',#2222,#2234,#2052,.T.);
#2390=EDGE_CURVE('',#2234,#2236,#2200,.T.);
#2395=ADVANCED_FACE('',(#2394),#2386,.T.);
#2402=EDGE_CURVE('',#2221,#2233,#2059,.T.);
#2404=EDGE_CURVE('',#2233,#2234,#2208,.T.);
#2409=ADVANCED_FACE('',(#2408),#2400,.T.);
#2416=EDGE_CURVE('',#2272,#2254,#2073,.T.);
#2418=EDGE_CURVE('',#2253,#2254,#2080,.T.);
#2420=EDGE_CURVE('',#2253,#2245,#2087,.T.);
#2422=EDGE_CURVE('',#2245,#2246,#2094,.T.);
#2424=EDGE_CURVE('',#2264,#2246,#2101,.T.);
#2426=EDGE_CURVE('',#2264,#2262,#2108,.T.);
#2428=EDGE_CURVE('',#2262,#2233,#2215,.T.);
#2434=ADVANCED_FACE('',(#2433),#2414,.T.);
#2442=EDGE_CURVE('',#2254,#2258,#2122,.T.);
#2447=ADVANCED_FACE('',(#2446),#2439,.T.);
#2456=EDGE_CURVE('',#2257,#2253,#2129,.T.);
#2460=ADVANCED_FACE('',(#2459),#2452,.T.);
#2468=EDGE_CURVE('',#2249,#2245,#2143,.T.);
#2473=ADVANCED_FACE('',(#2472),#2465,.T.);
#2482=EDGE_CURVE('',#2246,#2250,#2157,.T.);
#2486=ADVANCED_FACE('',(#2485),#2478,.F.);
#2492=EDGE_CURVE('',#2266,#2264,#2150,.T.);
#2499=ADVANCED_FACE('',(#2498),#2491,.F.);
#2506=EDGE_CURVE('',#2261,#2262,#2164,.T.);
#2512=ADVANCED_FACE('',(#2511),#2504,.F.);
#2528=ADVANCED_FACE('',(#2527),#2517,.T.);
#2537=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2540=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2543=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2545=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2544))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2539,#2542,#2543))REPRESENTATION_CONTEXT(
'ID2','3'));
#2549=AXIS2_PLACEMENT_3D('',#2546,#2547,#2548);
#2550=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#2549,#2530),#2545);
#2554=PRODUCT_DEFINITION('part definition','',#2553,#1799);
#2555=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR LEAD_P0008A.',#2554);
#2556=SHAPE_ASPECT('','solid data associated with LEAD_P0008A',#2555,.F.);
#2557=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#2556);
#2558=SHAPE_REPRESENTATION('',(#2530),#2545);
#2559=SHAPE_DEFINITION_REPRESENTATION(#2557,#2558);
#2560=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2566=PROPERTY_DEFINITION('geometric validation property','area of LEAD_P0008A',
#2556);
#2567=REPRESENTATION('surface area',(#2565),#2545);
#2568=PROPERTY_DEFINITION_REPRESENTATION(#2566,#2567);
#2569=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2575=PROPERTY_DEFINITION('geometric validation property',
'volume of LEAD_P0008A',#2556);
#2576=REPRESENTATION('volume',(#2574),#2545);
#2577=PROPERTY_DEFINITION_REPRESENTATION(#2575,#2576);
#2579=PROPERTY_DEFINITION('geometric validation property',
'centroid of LEAD_P0008A',#2556);
#2580=REPRESENTATION('centroid',(#2578),#2545);
#2581=PROPERTY_DEFINITION_REPRESENTATION(#2579,#2580);
#2582=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2588=PROPERTY_DEFINITION('geometric validation property','area of LEAD_P0008A',
#2555);
#2589=REPRESENTATION('surface area',(#2587),#2545);
#2590=PROPERTY_DEFINITION_REPRESENTATION(#2588,#2589);
#2591=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2597=PROPERTY_DEFINITION('geometric validation property',
'volume of LEAD_P0008A',#2555);
#2598=REPRESENTATION('volume',(#2596),#2545);
#2599=PROPERTY_DEFINITION_REPRESENTATION(#2597,#2598);
#2601=PROPERTY_DEFINITION('geometric validation property',
'centroid of LEAD_P0008A',#2555);
#2602=REPRESENTATION('centroid',(#2600),#2545);
#2603=PROPERTY_DEFINITION_REPRESENTATION(#2601,#2602);
#2604=SHAPE_DEFINITION_REPRESENTATION(#2555,#2550);
#2612=PLANAR_BOX('',2.871627330451E-1,2.422935560068E-1,#2611);
#2619=CAMERA_USAGE(#2618,#2605);
#2624=PLANAR_BOX('',1.E3,8.4375E2,#2623);
#2644=PRESENTATION_AREA('',(#2643,#2648),#1854);
#2654=NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','Next assembly relationship',
'LEAD_P0008A',#3993,#2554,$);
#2655=PRODUCT_DEFINITION_SHAPE('Placement #1',
'Placement of LEAD_P0008A with respect to P0008A_ASM',#2654);
#2657=PROPERTY_DEFINITION('geometric validation property',
'centroid of LEAD_P0008A',#2655);
#2658=REPRESENTATION('centroid',(#2656),#2545);
#2659=PROPERTY_DEFINITION_REPRESENTATION(#2657,#2658);
#2667=NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','Next assembly relationship',
'LEAD_P0008A',#3993,#2554,$);
#2668=PRODUCT_DEFINITION_SHAPE('Placement #2',
'Placement of LEAD_P0008A with respect to P0008A_ASM',#2667);
#2670=PROPERTY_DEFINITION('geometric validation property',
'centroid of LEAD_P0008A',#2668);
#2671=REPRESENTATION('centroid',(#2669),#2545);
#2672=PROPERTY_DEFINITION_REPRESENTATION(#2670,#2671);
#2680=NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','Next assembly relationship',
'LEAD_P0008A',#3993,#2554,$);
#2681=PRODUCT_DEFINITION_SHAPE('Placement #3',
'Placement of LEAD_P0008A with respect to P0008A_ASM',#2680);
#2683=PROPERTY_DEFINITION('geometric validation property',
'centroid of LEAD_P0008A',#2681);
#2684=REPRESENTATION('centroid',(#2682),#2545);
#2685=PROPERTY_DEFINITION_REPRESENTATION(#2683,#2684);
#2693=NEXT_ASSEMBLY_USAGE_OCCURRENCE('4','Next assembly relationship',
'LEAD_P0008A',#3993,#2554,$);
#2694=PRODUCT_DEFINITION_SHAPE('Placement #4',
'Placement of LEAD_P0008A with respect to P0008A_ASM',#2693);
#2696=PROPERTY_DEFINITION('geometric validation property',
'centroid of LEAD_P0008A',#2694);
#2697=REPRESENTATION('centroid',(#2695),#2545);
#2698=PROPERTY_DEFINITION_REPRESENTATION(#2696,#2697);
#2725=CIRCLE('',#2724,1.5E-2);
#2754=CIRCLE('',#2753,5.E-3);
#2811=CIRCLE('',#2810,5.E-3);
#2819=CIRCLE('',#2818,1.5E-2);
#2991=EDGE_CURVE('',#2980,#2981,#2710,.T.);
#2993=EDGE_CURVE('',#2981,#2944,#2717,.T.);
#2995=EDGE_CURVE('',#2944,#2945,#2725,.T.);
#2997=EDGE_CURVE('',#2945,#2947,#2732,.T.);
#2999=EDGE_CURVE('',#2947,#2949,#2739,.T.);
#3001=EDGE_CURVE('',#2949,#2951,#2746,.T.);
#3003=EDGE_CURVE('',#2951,#2953,#2754,.T.);
#3005=EDGE_CURVE('',#2953,#2980,#2761,.T.);
#3009=ADVANCED_FACE('',(#3008),#2990,.F.);
#3015=EDGE_CURVE('',#2980,#2985,#2768,.T.);
#3017=EDGE_CURVE('',#2985,#2983,#2861,.T.);
#3019=EDGE_CURVE('',#2981,#2983,#2826,.T.);
#3024=ADVANCED_FACE('',(#3023),#3014,.F.);
#3032=EDGE_CURVE('',#2953,#2975,#2775,.T.);
#3034=EDGE_CURVE('',#2975,#2977,#2782,.T.);
#3036=EDGE_CURVE('',#2960,#2977,#2889,.T.);
#3038=EDGE_CURVE('',#2960,#2961,#2789,.T.);
#3040=EDGE_CURVE('',#2961,#2985,#2796,.T.);
#3044=ADVANCED_FACE('',(#3043),#3029,.T.);
#3050=EDGE_CURVE('',#2975,#2973,#2811,.T.);
#3054=EDGE_CURVE('',#2951,#2973,#2803,.T.);
#3058=ADVANCED_FACE('',(#3057),#3049,.F.);
#3064=EDGE_CURVE('',#2964,#2965,#2854,.T.);
#3066=EDGE_CURVE('',#2977,#2964,#2875,.T.);
#3070=EDGE_CURVE('',#2971,#2973,#2931,.T.);
#3072=EDGE_CURVE('',#2969,#2971,#2917,.T.);
#3074=EDGE_CURVE('',#2967,#2969,#2903,.T.);
#3076=EDGE_CURVE('',#2965,#2967,#2819,.T.);
#3080=ADVANCED_FACE('',(#3079),#3063,.F.);
#3087=EDGE_CURVE('',#2957,#2983,#2833,.T.);
#3089=EDGE_CURVE('',#2956,#2957,#2840,.T.);
#3091=EDGE_CURVE('',#2956,#2964,#2847,.T.);
#3094=EDGE_CURVE('',#2944,#2965,#2896,.T.);
#3099=ADVANCED_FACE('',(#3098),#3085,.T.);
#3107=EDGE_CURVE('',#2957,#2961,#2868,.T.);
#3112=ADVANCED_FACE('',(#3111),#3104,.T.);
#3121=EDGE_CURVE('',#2960,#2956,#2882,.T.);
#3125=ADVANCED_FACE('',(#3124),#3117,.T.);
#3137=ADVANCED_FACE('',(#3136),#3130,.T.);
#3144=EDGE_CURVE('',#2945,#2967,#2910,.T.);
#3150=ADVANCED_FACE('',(#3149),#3142,.T.);
#3157=EDGE_CURVE('',#2947,#2969,#2924,.T.);
#3163=ADVANCED_FACE('',(#3162),#3155,.T.);
#3170=EDGE_CURVE('',#2949,#2971,#2938,.T.);
#3176=ADVANCED_FACE('',(#3175),#3168,.T.);
#3188=ADVANCED_FACE('',(#3187),#3181,.T.);
#3197=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3200=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#3203=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#3205=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#3204))GLOBAL_UNIT_ASSIGNED_CONTEXT((#3199,#3202,#3203))REPRESENTATION_CONTEXT(
'ID3','3'));
#3209=AXIS2_PLACEMENT_3D('',#3206,#3207,#3208);
#3210=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#3209,#3190),#3205);
#3215=PRODUCT_DEFINITION('part definition','',#3214,#3211);
#3216=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR LD_LHALF_P0008A.',#3215);
#3217=SHAPE_ASPECT('','solid data associated with LD_LHALF_P0008A',#3216,.F.);
#3218=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#3217);
#3219=SHAPE_REPRESENTATION('',(#3190),#3205);
#3220=SHAPE_DEFINITION_REPRESENTATION(#3218,#3219);
#3221=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3227=PROPERTY_DEFINITION('geometric validation property',
'area of LD_LHALF_P0008A',#3217);
#3228=REPRESENTATION('surface area',(#3226),#3205);
#3229=PROPERTY_DEFINITION_REPRESENTATION(#3227,#3228);
#3230=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3236=PROPERTY_DEFINITION('geometric validation property',
'volume of LD_LHALF_P0008A',#3217);
#3237=REPRESENTATION('volume',(#3235),#3205);
#3238=PROPERTY_DEFINITION_REPRESENTATION(#3236,#3237);
#3240=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_LHALF_P0008A',#3217);
#3241=REPRESENTATION('centroid',(#3239),#3205);
#3242=PROPERTY_DEFINITION_REPRESENTATION(#3240,#3241);
#3243=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3249=PROPERTY_DEFINITION('geometric validation property',
'area of LD_LHALF_P0008A',#3216);
#3250=REPRESENTATION('surface area',(#3248),#3205);
#3251=PROPERTY_DEFINITION_REPRESENTATION(#3249,#3250);
#3252=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3258=PROPERTY_DEFINITION('geometric validation property',
'volume of LD_LHALF_P0008A',#3216);
#3259=REPRESENTATION('volume',(#3257),#3205);
#3260=PROPERTY_DEFINITION_REPRESENTATION(#3258,#3259);
#3262=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_LHALF_P0008A',#3216);
#3263=REPRESENTATION('centroid',(#3261),#3205);
#3264=PROPERTY_DEFINITION_REPRESENTATION(#3262,#3263);
#3265=SHAPE_DEFINITION_REPRESENTATION(#3216,#3210);
#3273=PLANAR_BOX('',2.627020505436E-1,2.216548551462E-1,#3272);
#3280=CAMERA_USAGE(#3279,#3266);
#3285=PLANAR_BOX('',1.E3,8.4375E2,#3284);
#3305=PRESENTATION_AREA('',(#3304,#3309),#1854);
#3315=NEXT_ASSEMBLY_USAGE_OCCURRENCE('5','Next assembly relationship',
'LD_LHALF_P0008A',#3993,#3215,$);
#3316=PRODUCT_DEFINITION_SHAPE('Placement #5',
'Placement of LD_LHALF_P0008A with respect to P0008A_ASM',#3315);
#3318=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_LHALF_P0008A',#3316);
#3319=REPRESENTATION('centroid',(#3317),#3205);
#3320=PROPERTY_DEFINITION_REPRESENTATION(#3318,#3319);
#3347=CIRCLE('',#3346,5.E-3);
#3376=CIRCLE('',#3375,1.5E-2);
#3482=CIRCLE('',#3481,1.5E-2);
#3490=CIRCLE('',#3489,5.E-3);
#3613=EDGE_CURVE('',#3602,#3603,#3332,.T.);
#3615=EDGE_CURVE('',#3575,#3602,#3339,.T.);
#3617=EDGE_CURVE('',#3573,#3575,#3347,.T.);
#3619=EDGE_CURVE('',#3571,#3573,#3354,.T.);
#3621=EDGE_CURVE('',#3569,#3571,#3361,.T.);
#3623=EDGE_CURVE('',#3567,#3569,#3368,.T.);
#3625=EDGE_CURVE('',#3566,#3567,#3376,.T.);
#3627=EDGE_CURVE('',#3603,#3566,#3383,.T.);
#3631=ADVANCED_FACE('',(#3630),#3612,.T.);
#3637=EDGE_CURVE('',#3607,#3602,#3390,.T.);
#3640=EDGE_CURVE('',#3605,#3603,#3439,.T.);
#3642=EDGE_CURVE('',#3607,#3605,#3425,.T.);
#3646=ADVANCED_FACE('',(#3645),#3636,.F.);
#3653=EDGE_CURVE('',#3607,#3583,#3397,.T.);
#3655=EDGE_CURVE('',#3582,#3583,#3404,.T.);
#3657=EDGE_CURVE('',#3599,#3582,#3553,.T.);
#3659=EDGE_CURVE('',#3597,#3599,#3411,.T.);
#3661=EDGE_CURVE('',#3597,#3575,#3418,.T.);
#3666=ADVANCED_FACE('',(#3665),#3651,.T.);
#3673=EDGE_CURVE('',#3605,#3579,#3467,.T.);
#3675=EDGE_CURVE('',#3579,#3583,#3432,.T.);
#3680=ADVANCED_FACE('',(#3679),#3671,.F.);
#3688=EDGE_CURVE('',#3587,#3566,#3474,.T.);
#3690=EDGE_CURVE('',#3586,#3587,#3446,.T.);
#3692=EDGE_CURVE('',#3586,#3578,#3453,.T.);
#3694=EDGE_CURVE('',#3578,#3579,#3460,.T.);
#3699=ADVANCED_FACE('',(#3698),#3685,.T.);
#3705=EDGE_CURVE('',#3587,#3589,#3482,.T.);
#3709=EDGE_CURVE('',#3589,#3567,#3504,.T.);
#3713=ADVANCED_FACE('',(#3712),#3704,.T.);
#3721=EDGE_CURVE('',#3589,#3591,#3497,.T.);
#3723=EDGE_CURVE('',#3591,#3593,#3511,.T.);
#3725=EDGE_CURVE('',#3593,#3595,#3525,.T.);
#3727=EDGE_CURVE('',#3597,#3595,#3490,.T.);
#3730=EDGE_CURVE('',#3599,#3586,#3546,.T.);
#3734=ADVANCED_FACE('',(#3733),#3718,.F.);
#3743=EDGE_CURVE('',#3591,#3569,#3518,.T.);
#3747=ADVANCED_FACE('',(#3746),#3739,.T.);
#3756=EDGE_CURVE('',#3593,#3571,#3532,.T.);
#3760=ADVANCED_FACE('',(#3759),#3752,.T.);
#3769=EDGE_CURVE('',#3595,#3573,#3539,.T.);
#3773=ADVANCED_FACE('',(#3772),#3765,.T.);
#3785=ADVANCED_FACE('',(#3784),#3778,.F.);
#3793=EDGE_CURVE('',#3582,#3578,#3560,.T.);
#3798=ADVANCED_FACE('',(#3797),#3790,.T.);
#3810=ADVANCED_FACE('',(#3809),#3803,.F.);
#3819=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3822=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#3825=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#3827=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#3826))GLOBAL_UNIT_ASSIGNED_CONTEXT((#3821,#3824,#3825))REPRESENTATION_CONTEXT(
'ID4','3'));
#3831=AXIS2_PLACEMENT_3D('',#3828,#3829,#3830);
#3832=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#3831,#3812),#3827);
#3836=PRODUCT_DEFINITION('part definition','',#3835,#3211);
#3837=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR LD_RHALF_P0008A.',#3836);
#3838=SHAPE_ASPECT('','solid data associated with LD_RHALF_P0008A',#3837,.F.);
#3839=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#3838);
#3840=SHAPE_REPRESENTATION('',(#3812),#3827);
#3841=SHAPE_DEFINITION_REPRESENTATION(#3839,#3840);
#3842=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3848=PROPERTY_DEFINITION('geometric validation property',
'area of LD_RHALF_P0008A',#3838);
#3849=REPRESENTATION('surface area',(#3847),#3827);
#3850=PROPERTY_DEFINITION_REPRESENTATION(#3848,#3849);
#3851=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3857=PROPERTY_DEFINITION('geometric validation property',
'volume of LD_RHALF_P0008A',#3838);
#3858=REPRESENTATION('volume',(#3856),#3827);
#3859=PROPERTY_DEFINITION_REPRESENTATION(#3857,#3858);
#3861=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_RHALF_P0008A',#3838);
#3862=REPRESENTATION('centroid',(#3860),#3827);
#3863=PROPERTY_DEFINITION_REPRESENTATION(#3861,#3862);
#3864=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3870=PROPERTY_DEFINITION('geometric validation property',
'area of LD_RHALF_P0008A',#3837);
#3871=REPRESENTATION('surface area',(#3869),#3827);
#3872=PROPERTY_DEFINITION_REPRESENTATION(#3870,#3871);
#3873=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3879=PROPERTY_DEFINITION('geometric validation property',
'volume of LD_RHALF_P0008A',#3837);
#3880=REPRESENTATION('volume',(#3878),#3827);
#3881=PROPERTY_DEFINITION_REPRESENTATION(#3879,#3880);
#3883=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_RHALF_P0008A',#3837);
#3884=REPRESENTATION('centroid',(#3882),#3827);
#3885=PROPERTY_DEFINITION_REPRESENTATION(#3883,#3884);
#3886=SHAPE_DEFINITION_REPRESENTATION(#3837,#3832);
#3894=PLANAR_BOX('',2.627020505436E-1,2.216548551462E-1,#3893);
#3901=CAMERA_USAGE(#3900,#3887);
#3906=PLANAR_BOX('',1.E3,8.4375E2,#3905);
#3926=PRESENTATION_AREA('',(#3925,#3930),#1854);
#3936=NEXT_ASSEMBLY_USAGE_OCCURRENCE('6','Next assembly relationship',
'LD_RHALF_P0008A',#3993,#3836,$);
#3937=PRODUCT_DEFINITION_SHAPE('Placement #6',
'Placement of LD_RHALF_P0008A with respect to P0008A_ASM',#3936);
#3939=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_RHALF_P0008A',#3937);
#3940=REPRESENTATION('centroid',(#3938),#3827);
#3941=PROPERTY_DEFINITION_REPRESENTATION(#3939,#3940);
#3949=NEXT_ASSEMBLY_USAGE_OCCURRENCE('7','Next assembly relationship',
'LD_LHALF_P0008A',#3993,#3215,$);
#3950=PRODUCT_DEFINITION_SHAPE('Placement #7',
'Placement of LD_LHALF_P0008A with respect to P0008A_ASM',#3949);
#3952=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_LHALF_P0008A',#3950);
#3953=REPRESENTATION('centroid',(#3951),#3827);
#3954=PROPERTY_DEFINITION_REPRESENTATION(#3952,#3953);
#3962=NEXT_ASSEMBLY_USAGE_OCCURRENCE('8','Next assembly relationship',
'LD_RHALF_P0008A',#3993,#3836,$);
#3963=PRODUCT_DEFINITION_SHAPE('Placement #8',
'Placement of LD_RHALF_P0008A with respect to P0008A_ASM',#3962);
#3965=PROPERTY_DEFINITION('geometric validation property',
'centroid of LD_RHALF_P0008A',#3963);
#3966=REPRESENTATION('centroid',(#3964),#3827);
#3967=PROPERTY_DEFINITION_REPRESENTATION(#3965,#3966);
#3975=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#3978=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#3981=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#3983=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#3982))GLOBAL_UNIT_ASSIGNED_CONTEXT((#3977,#3980,#3981))REPRESENTATION_CONTEXT(
'ID5','3'));
#3987=AXIS2_PLACEMENT_3D('',#3984,#3985,#3986);
#3993=PRODUCT_DEFINITION('part definition','',#3992,#3989);
#3994=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#4000=PROPERTY_DEFINITION('geometric validation property','area of P0008A_ASM',
#1905);
#4001=REPRESENTATION('surface area',(#3999),#3983);
#4002=PROPERTY_DEFINITION_REPRESENTATION(#4000,#4001);
#4003=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#4009=PROPERTY_DEFINITION('geometric validation property',
'volume of P0008A_ASM',#1905);
#4010=REPRESENTATION('volume',(#4008),#3983);
#4011=PROPERTY_DEFINITION_REPRESENTATION(#4009,#4010);
#4013=PROPERTY_DEFINITION('geometric validation property',
'centroid of P0008A_ASM',#1905);
#4014=REPRESENTATION('centroid',(#4012),#3983);
#4015=PROPERTY_DEFINITION_REPRESENTATION(#4013,#4014);
#4024=PLANAR_BOX('',7.102799217701E-1,5.992986839935E-1,#4023);
#4031=CAMERA_USAGE(#4030,#4017);
#4036=PLANAR_BOX('',1.E3,8.4375E2,#4035);
#4056=PRESENTATION_AREA('',(#4055,#4060),#1854);
ENDSEC;
END-ISO-10303-21;
