#!/usr/bin/env python3
"""
SnapEDA Download with Modal Handling - Handle popups and modals after download click
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def download_with_modal_handling():
    print("🔍 SNAPEDA DOWNLOAD WITH MODAL HANDLING")
    print("=" * 60)
    
    # Setup Chrome with download directory
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
        "profile.default_content_setting_values.notifications": 2
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 20)
    
    try:
        # Login and navigate (using working flow)
        print("🔸 Logging in and navigating...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Click login
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        
        # Enter part number and login credentials
        search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
        search_field.send_keys("LM358N")
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(10)
        
        # Go to part page
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        
        # Click 3D Model tab
        three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
        three_d_tab.click()
        time.sleep(10)
        print("✅ 3D Model section opened")
        
        # List files before download
        before_files = set(os.listdir(download_dir))
        print(f"\n📁 Files before: {len(before_files)} files")
        
        # Find download button
        print(f"\n🎯 Finding download button...")
        selectors = [
            "//a[contains(@class, '3D-model-download')]",
            "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
            "//a[text()='Download 3D Model' and contains(@class, 'orange')]"
        ]
        
        download_button = None
        for selector in selectors:
            try:
                button = driver.find_element(By.XPATH, selector)
                if button.is_displayed():
                    download_button = button
                    print(f"✅ Found button with selector: {selector}")
                    break
            except:
                continue
        
        if not download_button:
            print("❌ Download button not found!")
            return
        
        print(f"✅ Download button found!")
        print(f"   Text: '{download_button.text.strip()}'")
        print(f"   Class: '{download_button.get_attribute('class')}'")
        print(f"   href: '{download_button.get_attribute('href')}'")
        
        # Get initial window handles
        initial_handles = driver.window_handles
        initial_url = driver.current_url
        
        # Click download button
        print(f"\n🔸 CLICKING DOWNLOAD BUTTON...")
        download_button.click()
        print(f"✅ CLICKED!")
        
        # Wait a moment for any immediate response
        time.sleep(3)
        
        # Check for modals, popups, or new windows
        print(f"\n🔍 Checking for modals, popups, or new windows...")
        
        # Check for new windows/tabs
        current_handles = driver.window_handles
        if len(current_handles) > len(initial_handles):
            print(f"🎯 NEW WINDOW/TAB detected!")
            for handle in current_handles:
                if handle not in initial_handles:
                    print(f"🔸 Switching to new window...")
                    driver.switch_to.window(handle)
                    new_url = driver.current_url
                    print(f"   New window URL: {new_url}")
                    
                    # Check if this looks like a download URL
                    if any(keyword in new_url.lower() for keyword in ['download', 'file', 'cdn', 'blob', 'amazonaws']):
                        print(f"🎉 DOWNLOAD URL DETECTED!")
                    
                    time.sleep(5)  # Wait for potential download
                    break
        
        # Check for modals
        modal_selectors = [
            ".modal:not(.fade)",
            ".modal.show",
            ".modal.in",
            "[role='dialog']",
            ".popup",
            ".overlay"
        ]
        
        modal_found = False
        for selector in modal_selectors:
            try:
                modals = driver.find_elements(By.CSS_SELECTOR, selector)
                for modal in modals:
                    if modal.is_displayed():
                        print(f"🎯 MODAL DETECTED with selector: {selector}")
                        modal_text = modal.text[:200]
                        print(f"   Modal content: {modal_text}...")
                        
                        # Look for download links or buttons in the modal
                        modal_download_selectors = [
                            ".//a[contains(text(), 'Download')]",
                            ".//button[contains(text(), 'Download')]",
                            ".//a[contains(@href, 'download')]",
                            ".//a[contains(@href, '.step')]",
                            ".//a[contains(@href, '.stp')]"
                        ]
                        
                        for dl_selector in modal_download_selectors:
                            try:
                                modal_download = modal.find_element(By.XPATH, dl_selector)
                                if modal_download.is_displayed():
                                    print(f"🎯 Found download link in modal!")
                                    print(f"   Text: '{modal_download.text}'")
                                    print(f"   href: '{modal_download.get_attribute('href')}'")
                                    
                                    print(f"🔸 Clicking modal download link...")
                                    modal_download.click()
                                    modal_found = True
                                    break
                            except:
                                continue
                        
                        if modal_found:
                            break
                
                if modal_found:
                    break
            except:
                continue
        
        # Check for URL changes
        current_url = driver.current_url
        if current_url != initial_url:
            print(f"🎯 URL CHANGED: {current_url}")
        
        # Monitor for downloads with extended time
        print(f"\n📁 Monitoring downloads for 45 seconds...")
        for i in range(45):
            time.sleep(1)
            current_files = set(os.listdir(download_dir))
            new_files = current_files - before_files
            
            if new_files:
                print(f"🎉 NEW FILE DOWNLOADED after {i+1} seconds!")
                for f in sorted(new_files):
                    file_path = os.path.join(download_dir, f)
                    file_size = os.path.getsize(file_path)
                    print(f"  📄 {f} ({file_size} bytes)")
                    
                    # If it's a STEP file, rename it properly
                    if f.endswith('.step') or f.endswith('.stp'):
                        final_name = f"SnapEDA-Texas Instruments-LM358N.step"
                        final_path = os.path.join(download_dir, final_name)
                        if not os.path.exists(final_path):
                            os.rename(file_path, final_path)
                            print(f"✅ Renamed to: {final_name}")
                break
            
            if i % 5 == 0 and i > 0:
                print(f"   ⏳ {i+1}/45 seconds...")
        
        if not new_files:
            print(f"❌ No download detected after 45 seconds")
            
            # Debug: Check current page state
            print(f"\n🔍 DEBUG INFO:")
            print(f"   Current URL: {driver.current_url}")
            print(f"   Page title: {driver.title}")
            print(f"   Window handles: {len(driver.window_handles)}")
            
            # Look for any error messages
            try:
                error_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'error') or contains(text(), 'Error') or contains(text(), 'failed') or contains(text(), 'Failed')]")
                if error_elements:
                    print(f"   Potential errors found:")
                    for elem in error_elements[:3]:  # Show first 3
                        if elem.is_displayed():
                            print(f"     - {elem.text[:100]}")
            except:
                pass
        
        print(f"\n🔸 Test complete - browser staying open for inspection")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    download_with_modal_handling()
