#!/usr/bin/env python3
"""
Test the progress bar visibility in the Component Finder
"""

import tkinter as tk
from tkinter import ttk
import time

def test_progress_bar_visibility():
    """Test that the progress bar is visible and animated"""
    
    root = tk.Tk()
    root.title("Progress Bar Visibility Test")
    root.geometry("600x300")
    
    # VS Colors
    vs_colors = {
        'form_bg': '#f0f0f0',
        'button_bg': '#e1e1e1',
        'text_color': '#000000'
    }
    root.configure(bg=vs_colors['form_bg'])
    
    # Main frame
    main_frame = tk.Frame(root, bg=vs_colors['form_bg'])
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Title
    title_label = tk.Label(main_frame, text="Progress Bar Visibility Test", 
                          bg=vs_colors['form_bg'], fg=vs_colors['text_color'],
                          font=('Microsoft Sans Serif', 14, 'bold'))
    title_label.pack(pady=10)
    
    # Info
    info_label = tk.Label(main_frame, 
                         text="This test shows how the progress bar should look in the Component Finder",
                         bg=vs_colors['form_bg'], fg=vs_colors['text_color'],
                         font=('Microsoft Sans Serif', 9))
    info_label.pack(pady=5)
    
    # Status bar simulation
    status_frame = tk.Frame(root, bg=vs_colors['button_bg'], height=30)
    status_frame.pack(fill='x', side='bottom')
    status_frame.pack_propagate(False)
    
    # Status text
    status_text = tk.StringVar(value="Ready")
    status_label = tk.Label(status_frame,
                           textvariable=status_text,
                           bg=vs_colors['button_bg'],
                           fg=vs_colors['text_color'],
                           font=('Microsoft Sans Serif', 8),
                           anchor='w')
    status_label.pack(side='left', padx=10, pady=5)
    
    # Configure progress bar style
    style = ttk.Style()
    style.configure('Test.Horizontal.TProgressbar',
                   background='#007acc',  # Blue color
                   troughcolor='#ffffff',  # White trough for contrast
                   borderwidth=2,
                   lightcolor='#007acc',
                   darkcolor='#005a9e',
                   relief='sunken')
    
    # Progress bar
    progress_bar = ttk.Progressbar(status_frame,
                                  mode='indeterminate',
                                  length=300,
                                  style='Test.Horizontal.TProgressbar')
    progress_bar.pack(side='right', padx=10, pady=5)
    
    # Control buttons
    button_frame = tk.Frame(main_frame, bg=vs_colors['form_bg'])
    button_frame.pack(pady=20)
    
    def start_animation():
        print("🔵 Starting progress bar animation...")
        progress_bar.start(50)  # 50ms interval
        status_text.set("Searching... (Progress bar should be moving)")
        
    def stop_animation():
        print("🔴 Stopping progress bar animation...")
        progress_bar.stop()
        status_text.set("Ready")
        
    def test_sequence():
        """Test the full sequence like Component Finder"""
        status_text.set("Starting search...")
        root.after(500, lambda: progress_bar.start(50))
        root.after(600, lambda: status_text.set("Searching APIs..."))
        root.after(2000, lambda: status_text.set("Downloading files..."))
        root.after(3500, lambda: status_text.set("Processing results..."))
        root.after(5000, lambda: progress_bar.stop())
        root.after(5100, lambda: status_text.set("Search Complete! ✅"))
    
    start_btn = tk.Button(button_frame, text="Start Progress Bar", command=start_animation,
                         bg=vs_colors['button_bg'], fg=vs_colors['text_color'],
                         font=('Microsoft Sans Serif', 9), width=15)
    start_btn.pack(side='left', padx=5)
    
    stop_btn = tk.Button(button_frame, text="Stop Progress Bar", command=stop_animation,
                        bg=vs_colors['button_bg'], fg=vs_colors['text_color'],
                        font=('Microsoft Sans Serif', 9), width=15)
    stop_btn.pack(side='left', padx=5)
    
    test_btn = tk.Button(button_frame, text="Test Full Sequence", command=test_sequence,
                        bg=vs_colors['button_bg'], fg=vs_colors['text_color'],
                        font=('Microsoft Sans Serif', 9), width=15)
    test_btn.pack(side='left', padx=5)
    
    # Instructions
    instructions = tk.Label(main_frame, 
                           text="Look at the bottom status bar - you should see a blue moving progress bar when active.\n"
                                "The progress bar should be on the right side of the status bar.",
                           bg=vs_colors['form_bg'], fg='gray',
                           font=('Microsoft Sans Serif', 8),
                           justify='center')
    instructions.pack(pady=10)
    
    # Highlight the status bar area
    highlight_label = tk.Label(main_frame,
                              text="👇 The progress bar should appear in the status bar below 👇",
                              bg=vs_colors['form_bg'], fg='red',
                              font=('Microsoft Sans Serif', 9, 'bold'))
    highlight_label.pack(side='bottom', pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_progress_bar_visibility()
