#!/usr/bin/env python3
"""
Test script to verify the component finder follows the correct flow from the spreadsheet
"""

import sys
import os
from unittest.mock import Mock, patch
import io
from contextlib import redirect_stdout

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_component_flow():
    """Test the component finder flow by testing individual methods"""
    print("🧪 Testing Component Finder Flow")
    print("=" * 50)

    try:
        # Test Digi-Key API directly
        print("\n🔍 Testing Digi-Key API")
        print("-" * 30)

        try:
            from digikey_datasheet_improved import DigikeyDatasheetFinder
            dk_finder = DigikeyDatasheetFinder()
            if dk_finder.load_credentials():
                print("✅ Digi-Key API credentials loaded")
                dk_result = dk_finder.search_part("Texas Instruments", "LM358N")
                if dk_result:
                    print("✅ Digi-Key API found part")
                else:
                    print("❌ Digi-Key API did not find part")
            else:
                print("❌ Digi-Key API credentials not available")
        except ImportError:
            print("⚠️ Digi-Key API module not available")
        except Exception as e:
            print(f"❌ Digi-Key API error: {e}")

        # Test Mouser API directly (we know this works)
        print("\n🔍 Testing Mouser API")
        print("-" * 30)

        from mouser_datasheet_improved import MouserDatasheetFinder
        mouser_finder = MouserDatasheetFinder()
        if mouser_finder.load_credentials():
            print("✅ Mouser API credentials loaded")
            mouser_result = mouser_finder.search_part("Texas Instruments", "LM358N")
            if mouser_result:
                print("✅ Mouser API found part")
                print(f"   Part: {mouser_result.get('MouserPartNumber', 'Unknown')}")
                print(f"   Manufacturer: {mouser_result.get('Manufacturer', 'Unknown')}")
            else:
                print("❌ Mouser API did not find part")
        else:
            print("❌ Mouser API credentials not available")

        # Test the expected flow sequence
        print("\n📝 Expected Flow Sequence:")
        print("-" * 30)
        expected_flow = [
            "1. 🔍 Searching for datasheet and 3D Models for Texas Instruments LM358N",
            "2. 📡 Searching API Digi-Key website for Texas Instruments LM358N Datasheet",
            "3. ✅ Found Link for Texas Instruments LM358N DataSheet (if found)",
            "   OR ❌ Link for Texas Instruments LM358N DataSheet Not Found",
            "4. 📡 Searching API Mouser website for Texas Instruments LM358N Datasheet (if Digi-Key fails)",
            "5. ✅ Found Link for Texas Instruments LM358N DataSheet on Mouser (if found)",
            "   OR ❌ Link for Texas Instruments LM358N DataSheet Not Found on Mouser",
            "6. 🔗 Link to manufacturers website - If found Updating Manufacturer...",
            "7. 📥 Downloading from Texas Instruments LM358N Datasheet",
            "8. 🔍 Searching Datasheet for part number match...",
            "9. 📋 Part-Number – Found or not found, Package Found or Not Found",
            "10. Package extraction and 3D model searches..."
        ]

        for step in expected_flow:
            print(f"   {step}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mouser_api_directly():
    """Test Mouser API directly"""
    print("\n🧪 Testing Mouser API Directly")
    print("=" * 50)
    
    try:
        from mouser_datasheet_improved import MouserDatasheetFinder
        
        finder = MouserDatasheetFinder()
        
        # Test credentials
        if finder.load_credentials():
            print("✅ Mouser API credentials loaded successfully")
            
            # Test search
            result = finder.search_part("Texas Instruments", "LM358N")
            if result:
                print("✅ Mouser API search successful")
                print(f"   Found part: {result.get('MouserPartNumber', 'Unknown')}")
                print(f"   Manufacturer: {result.get('Manufacturer', 'Unknown')}")
                return True
            else:
                print("❌ Mouser API search failed")
                return False
        else:
            print("❌ Mouser API credentials failed to load")
            return False
            
    except Exception as e:
        print(f"❌ Mouser API test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Component Finder Flow Test Suite")
    print("=" * 60)
    
    # Test 1: Mouser API
    mouser_ok = test_mouser_api_directly()
    
    # Test 2: Component flow
    flow_ok = test_component_flow()
    
    print(f"\n🏁 Test Results Summary:")
    print(f"   Mouser API: {'✅ PASS' if mouser_ok else '❌ FAIL'}")
    print(f"   Flow Test:  {'✅ PASS' if flow_ok else '❌ FAIL'}")
    
    if mouser_ok and flow_ok:
        print(f"\n🎉 All tests passed! The component finder should follow the correct flow.")
    else:
        print(f"\n💥 Some tests failed. Check the output above for details.")
