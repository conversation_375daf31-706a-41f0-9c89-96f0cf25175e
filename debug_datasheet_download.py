#!/usr/bin/env python3
"""
Debug Datasheet Download - Step by step with screen visibility
"""

import os
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_debug_driver():
    """Setup Chrome driver for debugging with visible browser"""
    download_dir = os.path.abspath("datasheets")
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    chrome_options = Options()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    
    # Keep browser visible for debugging
    # chrome_options.add_argument("--headless")  # Commented out for debugging
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def debug_digikey_search(driver, manufacturer, part_number):
    """Debug Digi-Key search step by step"""
    print(f"\n🔍 DEBUGGING DIGI-KEY SEARCH FOR {manufacturer} {part_number}")
    print("=" * 60)
    
    # Step 1: Go to Digi-Key
    print("📍 Step 1: Going to Digi-Key...")
    driver.get("https://www.digikey.com")
    input("Press Enter after you see Digi-Key homepage...")
    
    # Step 2: Search for part
    print(f"📍 Step 2: Searching for {part_number}...")
    search_url = f"https://www.digikey.com/en/products/result?keywords={part_number}"
    driver.get(search_url)
    input("Press Enter after you see search results...")
    
    # Step 3: Look for the part
    print("📍 Step 3: Looking for part in results...")
    current_url = driver.current_url
    print(f"Current URL: {current_url}")
    
    # Check if we found results
    page_source = driver.page_source.lower()
    if part_number.lower() in page_source:
        print(f"✅ Found {part_number} on page")
    else:
        print(f"❌ {part_number} not found on page")
    
    # Look for datasheet links
    print("📍 Step 4: Looking for datasheet links...")
    datasheet_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'datasheet') or contains(text(), 'datasheet') or contains(text(), 'Datasheet')]")
    
    print(f"Found {len(datasheet_links)} potential datasheet links:")
    for i, link in enumerate(datasheet_links[:5]):  # Show first 5
        try:
            href = link.get_attribute('href')
            text = link.text.strip()
            print(f"  Link {i+1}: '{text}' -> {href}")
        except:
            print(f"  Link {i+1}: Could not get details")
    
    input("Press Enter to continue...")
    
    return datasheet_links

def debug_datasheet_download(driver, datasheet_links):
    """Debug the actual datasheet download process"""
    print("\n📥 DEBUGGING DATASHEET DOWNLOAD")
    print("=" * 40)
    
    if not datasheet_links:
        print("❌ No datasheet links to test")
        return None
    
    # Try the first datasheet link
    link = datasheet_links[0]
    try:
        href = link.get_attribute('href')
        text = link.text.strip()
        print(f"📍 Testing link: '{text}' -> {href}")
        
        # Check what files exist before download
        datasheets_dir = "datasheets"
        files_before = set(os.listdir(datasheets_dir)) if os.path.exists(datasheets_dir) else set()
        print(f"Files before download: {len(files_before)}")
        
        # Click the link
        print("📍 Clicking datasheet link...")
        link.click()
        
        input("Press Enter after clicking the link (check what happened)...")
        
        # Check current URL
        current_url = driver.current_url
        print(f"Current URL after click: {current_url}")
        
        # Check if we're on a PDF page or download page
        if current_url.endswith('.pdf'):
            print("✅ Navigated directly to PDF")
        elif 'pdf' in current_url.lower():
            print("✅ Navigated to PDF-related page")
        else:
            print("⚠️ Not on a PDF page")
        
        # Wait a moment for download
        print("📍 Waiting for potential download...")
        time.sleep(3)
        
        # Check what files exist after
        files_after = set(os.listdir(datasheets_dir)) if os.path.exists(datasheets_dir) else set()
        new_files = files_after - files_before
        
        if new_files:
            print(f"✅ New files downloaded: {new_files}")
            for new_file in new_files:
                file_path = os.path.join(datasheets_dir, new_file)
                file_size = os.path.getsize(file_path)
                print(f"  File: {new_file} (Size: {file_size} bytes)")
                
                # Check if it's really a PDF
                with open(file_path, 'rb') as f:
                    header = f.read(10)
                    if header.startswith(b'%PDF'):
                        print(f"  ✅ {new_file} is a valid PDF")
                    else:
                        print(f"  ❌ {new_file} is NOT a PDF! Header: {header}")
        else:
            print("❌ No new files downloaded")
            
            # Maybe we need to save the current page
            print("📍 Checking if current page is the datasheet...")
            page_source = driver.page_source
            
            if '%PDF' in page_source[:100]:
                print("✅ Current page appears to be PDF content")
            else:
                print("❌ Current page is not PDF content")
                print(f"Page starts with: {page_source[:200]}")
        
        return new_files
        
    except Exception as e:
        print(f"❌ Error during download test: {e}")
        return None

def debug_lm358n_datasheet():
    """Debug LM358N datasheet download specifically"""
    print("🎯 DEBUGGING LM358N DATASHEET DOWNLOAD")
    print("=" * 50)
    
    driver = setup_debug_driver()
    
    try:
        # Test Digi-Key search
        datasheet_links = debug_digikey_search(driver, "Texas Instruments", "LM358N")
        
        # Test download process
        if datasheet_links:
            debug_datasheet_download(driver, datasheet_links)
        
        input("Press Enter to close browser and finish debugging...")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        input("Press Enter to close browser...")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_lm358n_datasheet()
