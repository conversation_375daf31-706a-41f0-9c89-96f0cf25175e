#!/usr/bin/env python3
"""
Fixed SnapEDA test - Direct approach
"""

def main():
    print("🚀 Starting SnapEDA with fixed GUI...")
    
    # Import and create GUI
    from working_screen_gui import get_working_gui
    gui = get_working_gui()
    print("✅ GUI created")
    
    # Import SnapEDA finder
    from snapeda_3d_finder import SnapEDA3DFinder
    finder = SnapEDA3DFinder()
    finder.gui = gui
    print("✅ SnapEDA finder created with GUI")
    
    # Run the search and download
    result = finder.search_and_download("Texas Instruments", "LM358N", silent=False)
    
    if result:
        print(f"✅ SUCCESS: {result}")
    else:
        print("❌ FAILED")

if __name__ == "__main__":
    main()
