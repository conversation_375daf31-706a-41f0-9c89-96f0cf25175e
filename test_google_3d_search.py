#!/usr/bin/env python3
"""
Test Google 3D Model Search
"""

from direct_manufacturer_3d_finder import google_search_3d_models

def test_google_search():
    """Test Google search for 3D models"""
    
    test_cases = [
        ("STMicroelectronics", "STM32F103C8T6"),
        ("Texas Instruments", "LM358N"),
        ("Arduino", "Uno"),
        ("Raspberry Pi", "4"),
        ("Intel", "8086")
    ]
    
    for manufacturer, part in test_cases:
        print(f"\n🔍 Testing: {manufacturer} {part}")
        print("=" * 50)
        
        results = google_search_3d_models(manufacturer, part)
        
        if results:
            print(f"✅ Found {len(results)} potential 3D model links:")
            for i, link in enumerate(results, 1):
                print(f"   {i}. {link}")
        else:
            print("❌ No 3D model links found")

if __name__ == "__main__":
    test_google_search()
