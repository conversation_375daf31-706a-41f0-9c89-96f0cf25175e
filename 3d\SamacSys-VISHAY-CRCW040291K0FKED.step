ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'H:\\Per\\3D Modelle\\D10_CRCW0402 e3.stp',
/* time_stamp */ '2020-11-13T12:03:41+01:00',
/* author */ ('rpetersen'),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v17',
/* originating_system */ 'Autodesk Inventor 2018',
/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#849);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#856,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#848);
#13=STYLED_ITEM('',(#865),#14);
#14=MANIFOLD_SOLID_BREP('Volumenk\X\F6rper1',#527);
#15=FACE_OUTER_BOUND('',#41,.T.);
#16=FACE_OUTER_BOUND('',#42,.T.);
#17=FACE_OUTER_BOUND('',#43,.T.);
#18=FACE_OUTER_BOUND('',#44,.T.);
#19=FACE_OUTER_BOUND('',#45,.T.);
#20=FACE_OUTER_BOUND('',#46,.T.);
#21=FACE_OUTER_BOUND('',#47,.T.);
#22=FACE_OUTER_BOUND('',#48,.T.);
#23=FACE_OUTER_BOUND('',#49,.T.);
#24=FACE_OUTER_BOUND('',#50,.T.);
#25=FACE_OUTER_BOUND('',#51,.T.);
#26=FACE_OUTER_BOUND('',#52,.T.);
#27=FACE_OUTER_BOUND('',#53,.T.);
#28=FACE_OUTER_BOUND('',#54,.T.);
#29=FACE_OUTER_BOUND('',#55,.T.);
#30=FACE_OUTER_BOUND('',#56,.T.);
#31=FACE_OUTER_BOUND('',#57,.T.);
#32=FACE_OUTER_BOUND('',#58,.T.);
#33=FACE_OUTER_BOUND('',#59,.T.);
#34=FACE_OUTER_BOUND('',#60,.T.);
#35=FACE_OUTER_BOUND('',#61,.T.);
#36=FACE_OUTER_BOUND('',#62,.T.);
#37=FACE_OUTER_BOUND('',#63,.T.);
#38=FACE_OUTER_BOUND('',#64,.T.);
#39=FACE_OUTER_BOUND('',#65,.T.);
#40=FACE_OUTER_BOUND('',#66,.T.);
#41=EDGE_LOOP('',(#331,#332,#333,#334));
#42=EDGE_LOOP('',(#335,#336,#337,#338));
#43=EDGE_LOOP('',(#339,#340,#341,#342));
#44=EDGE_LOOP('',(#343,#344,#345,#346));
#45=EDGE_LOOP('',(#347,#348,#349,#350));
#46=EDGE_LOOP('',(#351,#352,#353,#354));
#47=EDGE_LOOP('',(#355,#356,#357,#358,#359,#360,#361,#362));
#48=EDGE_LOOP('',(#363,#364,#365,#366,#367,#368,#369,#370));
#49=EDGE_LOOP('',(#371,#372,#373,#374));
#50=EDGE_LOOP('',(#375,#376,#377,#378));
#51=EDGE_LOOP('',(#379,#380,#381,#382));
#52=EDGE_LOOP('',(#383,#384,#385,#386));
#53=EDGE_LOOP('',(#387,#388,#389,#390));
#54=EDGE_LOOP('',(#391,#392,#393,#394));
#55=EDGE_LOOP('',(#395,#396,#397,#398));
#56=EDGE_LOOP('',(#399,#400,#401,#402,#403,#404,#405,#406));
#57=EDGE_LOOP('',(#407,#408,#409,#410));
#58=EDGE_LOOP('',(#411,#412,#413,#414));
#59=EDGE_LOOP('',(#415,#416,#417,#418));
#60=EDGE_LOOP('',(#419,#420,#421,#422));
#61=EDGE_LOOP('',(#423,#424,#425,#426,#427,#428,#429,#430));
#62=EDGE_LOOP('',(#431,#432,#433,#434));
#63=EDGE_LOOP('',(#435,#436,#437,#438,#439,#440,#441,#442,#443,#444,#445,
#446,#447,#448,#449,#450));
#64=EDGE_LOOP('',(#451,#452,#453,#454));
#65=EDGE_LOOP('',(#455,#456,#457,#458));
#66=EDGE_LOOP('',(#459,#460,#461,#462,#463,#464,#465,#466,#467,#468,#469,
#470,#471,#472,#473,#474));
#67=LINE('',#703,#139);
#68=LINE('',#705,#140);
#69=LINE('',#707,#141);
#70=LINE('',#708,#142);
#71=LINE('',#712,#143);
#72=LINE('',#714,#144);
#73=LINE('',#716,#145);
#74=LINE('',#717,#146);
#75=LINE('',#719,#147);
#76=LINE('',#720,#148);
#77=LINE('',#723,#149);
#78=LINE('',#725,#150);
#79=LINE('',#726,#151);
#80=LINE('',#729,#152);
#81=LINE('',#731,#153);
#82=LINE('',#732,#154);
#83=LINE('',#736,#155);
#84=LINE('',#738,#156);
#85=LINE('',#740,#157);
#86=LINE('',#741,#158);
#87=LINE('',#745,#159);
#88=LINE('',#747,#160);
#89=LINE('',#749,#161);
#90=LINE('',#751,#162);
#91=LINE('',#753,#163);
#92=LINE('',#755,#164);
#93=LINE('',#757,#165);
#94=LINE('',#758,#166);
#95=LINE('',#762,#167);
#96=LINE('',#764,#168);
#97=LINE('',#766,#169);
#98=LINE('',#768,#170);
#99=LINE('',#770,#171);
#100=LINE('',#772,#172);
#101=LINE('',#774,#173);
#102=LINE('',#775,#174);
#103=LINE('',#779,#175);
#104=LINE('',#780,#176);
#105=LINE('',#781,#177);
#106=LINE('',#784,#178);
#107=LINE('',#785,#179);
#108=LINE('',#788,#180);
#109=LINE('',#789,#181);
#110=LINE('',#793,#182);
#111=LINE('',#794,#183);
#112=LINE('',#795,#184);
#113=LINE('',#798,#185);
#114=LINE('',#799,#186);
#115=LINE('',#802,#187);
#116=LINE('',#803,#188);
#117=LINE('',#807,#189);
#118=LINE('',#808,#190);
#119=LINE('',#809,#191);
#120=LINE('',#812,#192);
#121=LINE('',#813,#193);
#122=LINE('',#814,#194);
#123=LINE('',#817,#195);
#124=LINE('',#818,#196);
#125=LINE('',#820,#197);
#126=LINE('',#824,#198);
#127=LINE('',#825,#199);
#128=LINE('',#826,#200);
#129=LINE('',#829,#201);
#130=LINE('',#830,#202);
#131=LINE('',#833,#203);
#132=LINE('',#834,#204);
#133=LINE('',#835,#205);
#134=LINE('',#837,#206);
#135=LINE('',#839,#207);
#136=LINE('',#840,#208);
#137=LINE('',#842,#209);
#138=LINE('',#844,#210);
#139=VECTOR('',#577,10.);
#140=VECTOR('',#578,10.);
#141=VECTOR('',#579,10.);
#142=VECTOR('',#580,10.);
#143=VECTOR('',#583,10.);
#144=VECTOR('',#584,10.);
#145=VECTOR('',#585,10.);
#146=VECTOR('',#586,10.);
#147=VECTOR('',#589,10.);
#148=VECTOR('',#590,10.);
#149=VECTOR('',#593,10.);
#150=VECTOR('',#594,10.);
#151=VECTOR('',#595,10.);
#152=VECTOR('',#598,10.);
#153=VECTOR('',#599,10.);
#154=VECTOR('',#600,10.);
#155=VECTOR('',#603,10.);
#156=VECTOR('',#604,10.);
#157=VECTOR('',#605,10.);
#158=VECTOR('',#606,10.);
#159=VECTOR('',#609,10.);
#160=VECTOR('',#610,10.);
#161=VECTOR('',#611,10.);
#162=VECTOR('',#612,10.);
#163=VECTOR('',#613,10.);
#164=VECTOR('',#614,10.);
#165=VECTOR('',#615,10.);
#166=VECTOR('',#616,10.);
#167=VECTOR('',#619,10.);
#168=VECTOR('',#620,10.);
#169=VECTOR('',#621,10.);
#170=VECTOR('',#622,10.);
#171=VECTOR('',#623,10.);
#172=VECTOR('',#624,10.);
#173=VECTOR('',#625,10.);
#174=VECTOR('',#626,10.);
#175=VECTOR('',#629,10.);
#176=VECTOR('',#630,10.);
#177=VECTOR('',#631,10.);
#178=VECTOR('',#634,10.);
#179=VECTOR('',#635,10.);
#180=VECTOR('',#638,10.);
#181=VECTOR('',#639,10.);
#182=VECTOR('',#642,10.);
#183=VECTOR('',#643,10.);
#184=VECTOR('',#644,10.);
#185=VECTOR('',#647,10.);
#186=VECTOR('',#648,10.);
#187=VECTOR('',#651,10.);
#188=VECTOR('',#652,10.);
#189=VECTOR('',#655,10.);
#190=VECTOR('',#656,10.);
#191=VECTOR('',#657,10.);
#192=VECTOR('',#660,10.);
#193=VECTOR('',#661,10.);
#194=VECTOR('',#662,10.);
#195=VECTOR('',#665,10.);
#196=VECTOR('',#666,10.);
#197=VECTOR('',#669,10.);
#198=VECTOR('',#672,10.);
#199=VECTOR('',#673,10.);
#200=VECTOR('',#674,10.);
#201=VECTOR('',#677,10.);
#202=VECTOR('',#678,10.);
#203=VECTOR('',#681,10.);
#204=VECTOR('',#682,10.);
#205=VECTOR('',#683,10.);
#206=VECTOR('',#686,10.);
#207=VECTOR('',#689,10.);
#208=VECTOR('',#690,10.);
#209=VECTOR('',#693,10.);
#210=VECTOR('',#696,10.);
#211=VERTEX_POINT('',#701);
#212=VERTEX_POINT('',#702);
#213=VERTEX_POINT('',#704);
#214=VERTEX_POINT('',#706);
#215=VERTEX_POINT('',#710);
#216=VERTEX_POINT('',#711);
#217=VERTEX_POINT('',#713);
#218=VERTEX_POINT('',#715);
#219=VERTEX_POINT('',#722);
#220=VERTEX_POINT('',#724);
#221=VERTEX_POINT('',#728);
#222=VERTEX_POINT('',#730);
#223=VERTEX_POINT('',#734);
#224=VERTEX_POINT('',#735);
#225=VERTEX_POINT('',#737);
#226=VERTEX_POINT('',#739);
#227=VERTEX_POINT('',#743);
#228=VERTEX_POINT('',#744);
#229=VERTEX_POINT('',#746);
#230=VERTEX_POINT('',#748);
#231=VERTEX_POINT('',#750);
#232=VERTEX_POINT('',#752);
#233=VERTEX_POINT('',#754);
#234=VERTEX_POINT('',#756);
#235=VERTEX_POINT('',#760);
#236=VERTEX_POINT('',#761);
#237=VERTEX_POINT('',#763);
#238=VERTEX_POINT('',#765);
#239=VERTEX_POINT('',#767);
#240=VERTEX_POINT('',#769);
#241=VERTEX_POINT('',#771);
#242=VERTEX_POINT('',#773);
#243=VERTEX_POINT('',#777);
#244=VERTEX_POINT('',#778);
#245=VERTEX_POINT('',#783);
#246=VERTEX_POINT('',#787);
#247=VERTEX_POINT('',#791);
#248=VERTEX_POINT('',#792);
#249=VERTEX_POINT('',#797);
#250=VERTEX_POINT('',#801);
#251=VERTEX_POINT('',#805);
#252=VERTEX_POINT('',#806);
#253=VERTEX_POINT('',#811);
#254=VERTEX_POINT('',#816);
#255=VERTEX_POINT('',#822);
#256=VERTEX_POINT('',#823);
#257=VERTEX_POINT('',#828);
#258=VERTEX_POINT('',#832);
#259=EDGE_CURVE('',#211,#212,#67,.T.);
#260=EDGE_CURVE('',#213,#211,#68,.T.);
#261=EDGE_CURVE('',#214,#213,#69,.T.);
#262=EDGE_CURVE('',#212,#214,#70,.T.);
#263=EDGE_CURVE('',#215,#216,#71,.T.);
#264=EDGE_CURVE('',#217,#215,#72,.T.);
#265=EDGE_CURVE('',#218,#217,#73,.T.);
#266=EDGE_CURVE('',#216,#218,#74,.T.);
#267=EDGE_CURVE('',#211,#216,#75,.T.);
#268=EDGE_CURVE('',#215,#212,#76,.T.);
#269=EDGE_CURVE('',#217,#219,#77,.T.);
#270=EDGE_CURVE('',#220,#219,#78,.T.);
#271=EDGE_CURVE('',#218,#220,#79,.T.);
#272=EDGE_CURVE('',#221,#213,#80,.T.);
#273=EDGE_CURVE('',#221,#222,#81,.T.);
#274=EDGE_CURVE('',#222,#214,#82,.T.);
#275=EDGE_CURVE('',#223,#224,#83,.T.);
#276=EDGE_CURVE('',#225,#223,#84,.T.);
#277=EDGE_CURVE('',#226,#225,#85,.T.);
#278=EDGE_CURVE('',#224,#226,#86,.T.);
#279=EDGE_CURVE('',#227,#228,#87,.T.);
#280=EDGE_CURVE('',#228,#229,#88,.T.);
#281=EDGE_CURVE('',#229,#230,#89,.T.);
#282=EDGE_CURVE('',#231,#230,#90,.T.);
#283=EDGE_CURVE('',#231,#232,#91,.T.);
#284=EDGE_CURVE('',#232,#233,#92,.T.);
#285=EDGE_CURVE('',#233,#234,#93,.T.);
#286=EDGE_CURVE('',#234,#227,#94,.T.);
#287=EDGE_CURVE('',#235,#236,#95,.T.);
#288=EDGE_CURVE('',#236,#237,#96,.T.);
#289=EDGE_CURVE('',#237,#238,#97,.T.);
#290=EDGE_CURVE('',#238,#239,#98,.T.);
#291=EDGE_CURVE('',#240,#239,#99,.T.);
#292=EDGE_CURVE('',#241,#240,#100,.T.);
#293=EDGE_CURVE('',#241,#242,#101,.T.);
#294=EDGE_CURVE('',#242,#235,#102,.T.);
#295=EDGE_CURVE('',#243,#244,#103,.T.);
#296=EDGE_CURVE('',#227,#243,#104,.T.);
#297=EDGE_CURVE('',#234,#244,#105,.T.);
#298=EDGE_CURVE('',#244,#245,#106,.T.);
#299=EDGE_CURVE('',#245,#233,#107,.T.);
#300=EDGE_CURVE('',#246,#243,#108,.T.);
#301=EDGE_CURVE('',#246,#245,#109,.T.);
#302=EDGE_CURVE('',#247,#248,#110,.T.);
#303=EDGE_CURVE('',#238,#247,#111,.T.);
#304=EDGE_CURVE('',#237,#248,#112,.T.);
#305=EDGE_CURVE('',#248,#249,#113,.T.);
#306=EDGE_CURVE('',#236,#249,#114,.T.);
#307=EDGE_CURVE('',#249,#250,#115,.T.);
#308=EDGE_CURVE('',#247,#250,#116,.T.);
#309=EDGE_CURVE('',#251,#252,#117,.T.);
#310=EDGE_CURVE('',#229,#251,#118,.T.);
#311=EDGE_CURVE('',#228,#252,#119,.T.);
#312=EDGE_CURVE('',#252,#253,#120,.T.);
#313=EDGE_CURVE('',#224,#246,#121,.T.);
#314=EDGE_CURVE('',#253,#226,#122,.T.);
#315=EDGE_CURVE('',#254,#251,#123,.T.);
#316=EDGE_CURVE('',#230,#254,#124,.T.);
#317=EDGE_CURVE('',#254,#253,#125,.T.);
#318=EDGE_CURVE('',#255,#256,#126,.T.);
#319=EDGE_CURVE('',#235,#255,#127,.T.);
#320=EDGE_CURVE('',#242,#256,#128,.T.);
#321=EDGE_CURVE('',#256,#257,#129,.T.);
#322=EDGE_CURVE('',#257,#241,#130,.T.);
#323=EDGE_CURVE('',#258,#255,#131,.T.);
#324=EDGE_CURVE('',#258,#225,#132,.T.);
#325=EDGE_CURVE('',#250,#223,#133,.T.);
#326=EDGE_CURVE('',#258,#257,#134,.T.);
#327=EDGE_CURVE('',#220,#231,#135,.T.);
#328=EDGE_CURVE('',#240,#221,#136,.T.);
#329=EDGE_CURVE('',#219,#232,#137,.T.);
#330=EDGE_CURVE('',#239,#222,#138,.T.);
#331=ORIENTED_EDGE('',*,*,#259,.F.);
#332=ORIENTED_EDGE('',*,*,#260,.F.);
#333=ORIENTED_EDGE('',*,*,#261,.F.);
#334=ORIENTED_EDGE('',*,*,#262,.F.);
#335=ORIENTED_EDGE('',*,*,#263,.F.);
#336=ORIENTED_EDGE('',*,*,#264,.F.);
#337=ORIENTED_EDGE('',*,*,#265,.F.);
#338=ORIENTED_EDGE('',*,*,#266,.F.);
#339=ORIENTED_EDGE('',*,*,#267,.F.);
#340=ORIENTED_EDGE('',*,*,#259,.T.);
#341=ORIENTED_EDGE('',*,*,#268,.F.);
#342=ORIENTED_EDGE('',*,*,#263,.T.);
#343=ORIENTED_EDGE('',*,*,#265,.T.);
#344=ORIENTED_EDGE('',*,*,#269,.T.);
#345=ORIENTED_EDGE('',*,*,#270,.F.);
#346=ORIENTED_EDGE('',*,*,#271,.F.);
#347=ORIENTED_EDGE('',*,*,#261,.T.);
#348=ORIENTED_EDGE('',*,*,#272,.F.);
#349=ORIENTED_EDGE('',*,*,#273,.T.);
#350=ORIENTED_EDGE('',*,*,#274,.T.);
#351=ORIENTED_EDGE('',*,*,#275,.F.);
#352=ORIENTED_EDGE('',*,*,#276,.F.);
#353=ORIENTED_EDGE('',*,*,#277,.F.);
#354=ORIENTED_EDGE('',*,*,#278,.F.);
#355=ORIENTED_EDGE('',*,*,#279,.T.);
#356=ORIENTED_EDGE('',*,*,#280,.T.);
#357=ORIENTED_EDGE('',*,*,#281,.T.);
#358=ORIENTED_EDGE('',*,*,#282,.F.);
#359=ORIENTED_EDGE('',*,*,#283,.T.);
#360=ORIENTED_EDGE('',*,*,#284,.T.);
#361=ORIENTED_EDGE('',*,*,#285,.T.);
#362=ORIENTED_EDGE('',*,*,#286,.T.);
#363=ORIENTED_EDGE('',*,*,#287,.T.);
#364=ORIENTED_EDGE('',*,*,#288,.T.);
#365=ORIENTED_EDGE('',*,*,#289,.T.);
#366=ORIENTED_EDGE('',*,*,#290,.T.);
#367=ORIENTED_EDGE('',*,*,#291,.F.);
#368=ORIENTED_EDGE('',*,*,#292,.F.);
#369=ORIENTED_EDGE('',*,*,#293,.T.);
#370=ORIENTED_EDGE('',*,*,#294,.T.);
#371=ORIENTED_EDGE('',*,*,#295,.F.);
#372=ORIENTED_EDGE('',*,*,#296,.F.);
#373=ORIENTED_EDGE('',*,*,#286,.F.);
#374=ORIENTED_EDGE('',*,*,#297,.T.);
#375=ORIENTED_EDGE('',*,*,#298,.F.);
#376=ORIENTED_EDGE('',*,*,#297,.F.);
#377=ORIENTED_EDGE('',*,*,#285,.F.);
#378=ORIENTED_EDGE('',*,*,#299,.F.);
#379=ORIENTED_EDGE('',*,*,#300,.T.);
#380=ORIENTED_EDGE('',*,*,#295,.T.);
#381=ORIENTED_EDGE('',*,*,#298,.T.);
#382=ORIENTED_EDGE('',*,*,#301,.F.);
#383=ORIENTED_EDGE('',*,*,#302,.F.);
#384=ORIENTED_EDGE('',*,*,#303,.F.);
#385=ORIENTED_EDGE('',*,*,#289,.F.);
#386=ORIENTED_EDGE('',*,*,#304,.T.);
#387=ORIENTED_EDGE('',*,*,#305,.F.);
#388=ORIENTED_EDGE('',*,*,#304,.F.);
#389=ORIENTED_EDGE('',*,*,#288,.F.);
#390=ORIENTED_EDGE('',*,*,#306,.T.);
#391=ORIENTED_EDGE('',*,*,#302,.T.);
#392=ORIENTED_EDGE('',*,*,#305,.T.);
#393=ORIENTED_EDGE('',*,*,#307,.T.);
#394=ORIENTED_EDGE('',*,*,#308,.F.);
#395=ORIENTED_EDGE('',*,*,#309,.F.);
#396=ORIENTED_EDGE('',*,*,#310,.F.);
#397=ORIENTED_EDGE('',*,*,#280,.F.);
#398=ORIENTED_EDGE('',*,*,#311,.T.);
#399=ORIENTED_EDGE('',*,*,#312,.F.);
#400=ORIENTED_EDGE('',*,*,#311,.F.);
#401=ORIENTED_EDGE('',*,*,#279,.F.);
#402=ORIENTED_EDGE('',*,*,#296,.T.);
#403=ORIENTED_EDGE('',*,*,#300,.F.);
#404=ORIENTED_EDGE('',*,*,#313,.F.);
#405=ORIENTED_EDGE('',*,*,#278,.T.);
#406=ORIENTED_EDGE('',*,*,#314,.F.);
#407=ORIENTED_EDGE('',*,*,#315,.F.);
#408=ORIENTED_EDGE('',*,*,#316,.F.);
#409=ORIENTED_EDGE('',*,*,#281,.F.);
#410=ORIENTED_EDGE('',*,*,#310,.T.);
#411=ORIENTED_EDGE('',*,*,#309,.T.);
#412=ORIENTED_EDGE('',*,*,#312,.T.);
#413=ORIENTED_EDGE('',*,*,#317,.F.);
#414=ORIENTED_EDGE('',*,*,#315,.T.);
#415=ORIENTED_EDGE('',*,*,#318,.F.);
#416=ORIENTED_EDGE('',*,*,#319,.F.);
#417=ORIENTED_EDGE('',*,*,#294,.F.);
#418=ORIENTED_EDGE('',*,*,#320,.T.);
#419=ORIENTED_EDGE('',*,*,#321,.F.);
#420=ORIENTED_EDGE('',*,*,#320,.F.);
#421=ORIENTED_EDGE('',*,*,#293,.F.);
#422=ORIENTED_EDGE('',*,*,#322,.F.);
#423=ORIENTED_EDGE('',*,*,#323,.F.);
#424=ORIENTED_EDGE('',*,*,#324,.T.);
#425=ORIENTED_EDGE('',*,*,#276,.T.);
#426=ORIENTED_EDGE('',*,*,#325,.F.);
#427=ORIENTED_EDGE('',*,*,#307,.F.);
#428=ORIENTED_EDGE('',*,*,#306,.F.);
#429=ORIENTED_EDGE('',*,*,#287,.F.);
#430=ORIENTED_EDGE('',*,*,#319,.T.);
#431=ORIENTED_EDGE('',*,*,#318,.T.);
#432=ORIENTED_EDGE('',*,*,#321,.T.);
#433=ORIENTED_EDGE('',*,*,#326,.F.);
#434=ORIENTED_EDGE('',*,*,#323,.T.);
#435=ORIENTED_EDGE('',*,*,#266,.T.);
#436=ORIENTED_EDGE('',*,*,#271,.T.);
#437=ORIENTED_EDGE('',*,*,#327,.T.);
#438=ORIENTED_EDGE('',*,*,#282,.T.);
#439=ORIENTED_EDGE('',*,*,#316,.T.);
#440=ORIENTED_EDGE('',*,*,#317,.T.);
#441=ORIENTED_EDGE('',*,*,#314,.T.);
#442=ORIENTED_EDGE('',*,*,#277,.T.);
#443=ORIENTED_EDGE('',*,*,#324,.F.);
#444=ORIENTED_EDGE('',*,*,#326,.T.);
#445=ORIENTED_EDGE('',*,*,#322,.T.);
#446=ORIENTED_EDGE('',*,*,#292,.T.);
#447=ORIENTED_EDGE('',*,*,#328,.T.);
#448=ORIENTED_EDGE('',*,*,#272,.T.);
#449=ORIENTED_EDGE('',*,*,#260,.T.);
#450=ORIENTED_EDGE('',*,*,#267,.T.);
#451=ORIENTED_EDGE('',*,*,#329,.T.);
#452=ORIENTED_EDGE('',*,*,#283,.F.);
#453=ORIENTED_EDGE('',*,*,#327,.F.);
#454=ORIENTED_EDGE('',*,*,#270,.T.);
#455=ORIENTED_EDGE('',*,*,#330,.T.);
#456=ORIENTED_EDGE('',*,*,#273,.F.);
#457=ORIENTED_EDGE('',*,*,#328,.F.);
#458=ORIENTED_EDGE('',*,*,#291,.T.);
#459=ORIENTED_EDGE('',*,*,#262,.T.);
#460=ORIENTED_EDGE('',*,*,#274,.F.);
#461=ORIENTED_EDGE('',*,*,#330,.F.);
#462=ORIENTED_EDGE('',*,*,#290,.F.);
#463=ORIENTED_EDGE('',*,*,#303,.T.);
#464=ORIENTED_EDGE('',*,*,#308,.T.);
#465=ORIENTED_EDGE('',*,*,#325,.T.);
#466=ORIENTED_EDGE('',*,*,#275,.T.);
#467=ORIENTED_EDGE('',*,*,#313,.T.);
#468=ORIENTED_EDGE('',*,*,#301,.T.);
#469=ORIENTED_EDGE('',*,*,#299,.T.);
#470=ORIENTED_EDGE('',*,*,#284,.F.);
#471=ORIENTED_EDGE('',*,*,#329,.F.);
#472=ORIENTED_EDGE('',*,*,#269,.F.);
#473=ORIENTED_EDGE('',*,*,#264,.T.);
#474=ORIENTED_EDGE('',*,*,#268,.T.);
#475=PLANE('',#547);
#476=PLANE('',#548);
#477=PLANE('',#549);
#478=PLANE('',#550);
#479=PLANE('',#551);
#480=PLANE('',#552);
#481=PLANE('',#553);
#482=PLANE('',#554);
#483=PLANE('',#555);
#484=PLANE('',#556);
#485=PLANE('',#557);
#486=PLANE('',#558);
#487=PLANE('',#559);
#488=PLANE('',#560);
#489=PLANE('',#561);
#490=PLANE('',#562);
#491=PLANE('',#563);
#492=PLANE('',#564);
#493=PLANE('',#565);
#494=PLANE('',#566);
#495=PLANE('',#567);
#496=PLANE('',#568);
#497=PLANE('',#569);
#498=PLANE('',#570);
#499=PLANE('',#571);
#500=PLANE('',#572);
#501=ADVANCED_FACE('',(#15),#475,.F.);
#502=ADVANCED_FACE('',(#16),#476,.F.);
#503=ADVANCED_FACE('',(#17),#477,.F.);
#504=ADVANCED_FACE('',(#18),#478,.T.);
#505=ADVANCED_FACE('',(#19),#479,.T.);
#506=ADVANCED_FACE('',(#20),#480,.T.);
#507=ADVANCED_FACE('',(#21),#481,.T.);
#508=ADVANCED_FACE('',(#22),#482,.T.);
#509=ADVANCED_FACE('',(#23),#483,.F.);
#510=ADVANCED_FACE('',(#24),#484,.F.);
#511=ADVANCED_FACE('',(#25),#485,.T.);
#512=ADVANCED_FACE('',(#26),#486,.F.);
#513=ADVANCED_FACE('',(#27),#487,.F.);
#514=ADVANCED_FACE('',(#28),#488,.T.);
#515=ADVANCED_FACE('',(#29),#489,.F.);
#516=ADVANCED_FACE('',(#30),#490,.F.);
#517=ADVANCED_FACE('',(#31),#491,.F.);
#518=ADVANCED_FACE('',(#32),#492,.T.);
#519=ADVANCED_FACE('',(#33),#493,.F.);
#520=ADVANCED_FACE('',(#34),#494,.F.);
#521=ADVANCED_FACE('',(#35),#495,.F.);
#522=ADVANCED_FACE('',(#36),#496,.T.);
#523=ADVANCED_FACE('',(#37),#497,.F.);
#524=ADVANCED_FACE('',(#38),#498,.T.);
#525=ADVANCED_FACE('',(#39),#499,.T.);
#526=ADVANCED_FACE('',(#40),#500,.T.);
#527=CLOSED_SHELL('',(#501,#502,#503,#504,#505,#506,#507,#508,#509,#510,
#511,#512,#513,#514,#515,#516,#517,#518,#519,#520,#521,#522,#523,#524,#525,
#526));
#528=DERIVED_UNIT_ELEMENT(#530,1.);
#529=DERIVED_UNIT_ELEMENT(#851,3.);
#530=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.GRAM.)
);
#531=DERIVED_UNIT((#528,#529));
#532=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7.9),#531);
#533=PROPERTY_DEFINITION_REPRESENTATION(#538,#535);
#534=PROPERTY_DEFINITION_REPRESENTATION(#539,#536);
#535=REPRESENTATION('material name',(#537),#848);
#536=REPRESENTATION('density',(#532),#848);
#537=DESCRIPTIVE_REPRESENTATION_ITEM('-','-');
#538=PROPERTY_DEFINITION('material property','material name',#858);
#539=PROPERTY_DEFINITION('material property','density of part',#858);
#540=DATE_TIME_ROLE('creation_date');
#541=APPLIED_DATE_AND_TIME_ASSIGNMENT(#542,#540,(#858));
#542=DATE_AND_TIME(#543,#544);
#543=CALENDAR_DATE(2020,17,8);
#544=LOCAL_TIME(22,0,0.,#545);
#545=COORDINATED_UNIVERSAL_TIME_OFFSET(0,0,.BEHIND.);
#546=AXIS2_PLACEMENT_3D('placement',#699,#573,#574);
#547=AXIS2_PLACEMENT_3D('',#700,#575,#576);
#548=AXIS2_PLACEMENT_3D('',#709,#581,#582);
#549=AXIS2_PLACEMENT_3D('',#718,#587,#588);
#550=AXIS2_PLACEMENT_3D('',#721,#591,#592);
#551=AXIS2_PLACEMENT_3D('',#727,#596,#597);
#552=AXIS2_PLACEMENT_3D('',#733,#601,#602);
#553=AXIS2_PLACEMENT_3D('',#742,#607,#608);
#554=AXIS2_PLACEMENT_3D('',#759,#617,#618);
#555=AXIS2_PLACEMENT_3D('',#776,#627,#628);
#556=AXIS2_PLACEMENT_3D('',#782,#632,#633);
#557=AXIS2_PLACEMENT_3D('',#786,#636,#637);
#558=AXIS2_PLACEMENT_3D('',#790,#640,#641);
#559=AXIS2_PLACEMENT_3D('',#796,#645,#646);
#560=AXIS2_PLACEMENT_3D('',#800,#649,#650);
#561=AXIS2_PLACEMENT_3D('',#804,#653,#654);
#562=AXIS2_PLACEMENT_3D('',#810,#658,#659);
#563=AXIS2_PLACEMENT_3D('',#815,#663,#664);
#564=AXIS2_PLACEMENT_3D('',#819,#667,#668);
#565=AXIS2_PLACEMENT_3D('',#821,#670,#671);
#566=AXIS2_PLACEMENT_3D('',#827,#675,#676);
#567=AXIS2_PLACEMENT_3D('',#831,#679,#680);
#568=AXIS2_PLACEMENT_3D('',#836,#684,#685);
#569=AXIS2_PLACEMENT_3D('',#838,#687,#688);
#570=AXIS2_PLACEMENT_3D('',#841,#691,#692);
#571=AXIS2_PLACEMENT_3D('',#843,#694,#695);
#572=AXIS2_PLACEMENT_3D('',#845,#697,#698);
#573=DIRECTION('axis',(0.,0.,1.));
#574=DIRECTION('refdir',(1.,0.,0.));
#575=DIRECTION('center_axis',(1.,0.,0.));
#576=DIRECTION('ref_axis',(0.,0.,-1.));
#577=DIRECTION('',(0.,0.,1.));
#578=DIRECTION('',(0.,1.,0.));
#579=DIRECTION('',(0.,0.,-1.));
#580=DIRECTION('',(0.,-1.,0.));
#581=DIRECTION('center_axis',(-1.,0.,-2.77555756156289E-16));
#582=DIRECTION('ref_axis',(-2.77555756156289E-16,0.,1.));
#583=DIRECTION('',(2.77555756156289E-16,3.39907768361723E-32,-1.));
#584=DIRECTION('',(0.,1.,0.));
#585=DIRECTION('',(-2.77555756156289E-16,-3.39907768361723E-32,1.));
#586=DIRECTION('',(0.,-1.,0.));
#587=DIRECTION('center_axis',(-1.22464679914735E-16,1.,1.00908584181338E-32));
#588=DIRECTION('ref_axis',(0.,-1.00908584181338E-32,1.));
#589=DIRECTION('',(-1.,-1.22464679914735E-16,0.));
#590=DIRECTION('',(1.,1.22464679914735E-16,0.));
#591=DIRECTION('center_axis',(1.22464679914735E-16,-1.,0.));
#592=DIRECTION('ref_axis',(0.,0.,-1.));
#593=DIRECTION('',(-1.,-1.22464679914735E-16,0.));
#594=DIRECTION('',(0.,0.,1.));
#595=DIRECTION('',(-1.,-1.22464679914735E-16,0.));
#596=DIRECTION('center_axis',(1.22464679914735E-16,-1.,0.));
#597=DIRECTION('ref_axis',(0.,0.,-1.));
#598=DIRECTION('',(-1.,-1.22464679914735E-16,0.));
#599=DIRECTION('',(0.,0.,1.));
#600=DIRECTION('',(-1.,-1.22464679914735E-16,0.));
#601=DIRECTION('center_axis',(-1.21188266784806E-16,1.,-5.03735343168675E-18));
#602=DIRECTION('ref_axis',(0.,5.03735343168675E-18,1.));
#603=DIRECTION('',(-1.,-2.08166817117217E-16,0.));
#604=DIRECTION('',(2.31296463463575E-15,4.8148248609681E-31,1.));
#605=DIRECTION('',(1.,2.08166817117217E-16,0.));
#606=DIRECTION('',(0.,0.,-1.));
#607=DIRECTION('center_axis',(-2.08166817117217E-16,1.,0.));
#608=DIRECTION('ref_axis',(0.,0.,1.));
#609=DIRECTION('',(0.,0.,-1.));
#610=DIRECTION('',(-1.,-2.08166817117217E-16,1.73472347597681E-16));
#611=DIRECTION('',(0.,0.,-1.));
#612=DIRECTION('',(1.,2.08166817117217E-16,0.));
#613=DIRECTION('',(0.,0.,1.));
#614=DIRECTION('',(1.,2.08166817117217E-16,0.));
#615=DIRECTION('',(0.,0.,-1.));
#616=DIRECTION('',(1.,2.08166817117217E-16,0.));
#617=DIRECTION('center_axis',(-2.08166817117217E-16,1.,0.));
#618=DIRECTION('ref_axis',(0.,0.,1.));
#619=DIRECTION('',(2.31296463463575E-15,4.8148248609681E-31,1.));
#620=DIRECTION('',(1.,2.08166817117217E-16,-6.93889390390721E-16));
#621=DIRECTION('',(0.,0.,1.));
#622=DIRECTION('',(1.,2.08166817117217E-16,0.));
#623=DIRECTION('',(0.,0.,1.));
#624=DIRECTION('',(1.,2.08166817117217E-16,0.));
#625=DIRECTION('',(0.,0.,1.));
#626=DIRECTION('',(-1.,-2.08166817117217E-16,3.46944695195362E-16));
#627=DIRECTION('center_axis',(0.,0.,-1.));
#628=DIRECTION('ref_axis',(-1.,0.,0.));
#629=DIRECTION('',(-1.,-2.08166817117217E-16,0.));
#630=DIRECTION('',(0.,-1.,0.));
#631=DIRECTION('',(0.,-1.,0.));
#632=DIRECTION('center_axis',(-1.,0.,0.));
#633=DIRECTION('ref_axis',(0.,0.,1.));
#634=DIRECTION('',(0.,0.,1.));
#635=DIRECTION('',(0.,1.,0.));
#636=DIRECTION('center_axis',(-6.11036925866406E-16,1.,-3.45218602184428E-16));
#637=DIRECTION('ref_axis',(0.,3.45218602184428E-16,1.));
#638=DIRECTION('',(0.,0.,-1.));
#639=DIRECTION('',(-1.,-6.11036925866406E-16,0.));
#640=DIRECTION('center_axis',(1.,0.,0.));
#641=DIRECTION('ref_axis',(0.,0.,-1.));
#642=DIRECTION('',(0.,0.,-1.));
#643=DIRECTION('',(0.,-1.,0.));
#644=DIRECTION('',(0.,-1.,0.));
#645=DIRECTION('center_axis',(-6.93889390390721E-16,0.,-1.));
#646=DIRECTION('ref_axis',(-1.,0.,6.93889390390721E-16));
#647=DIRECTION('',(-1.,-2.08166817117217E-16,6.93889390390721E-16));
#648=DIRECTION('',(0.,-1.,0.));
#649=DIRECTION('center_axis',(-6.11036925866452E-16,1.,-6.04132553822779E-16));
#650=DIRECTION('ref_axis',(0.,6.04132553822779E-16,1.));
#651=DIRECTION('',(2.31296463463575E-15,4.8148248609681E-31,1.));
#652=DIRECTION('',(-1.,-6.11036925866452E-16,0.));
#653=DIRECTION('center_axis',(1.73472347597681E-16,0.,1.));
#654=DIRECTION('ref_axis',(1.,0.,-1.73472347597681E-16));
#655=DIRECTION('',(1.,2.08166817117217E-16,-1.73472347597681E-16));
#656=DIRECTION('',(0.,-1.,0.));
#657=DIRECTION('',(0.,-1.,0.));
#658=DIRECTION('center_axis',(1.,0.,0.));
#659=DIRECTION('ref_axis',(0.,0.,-1.));
#660=DIRECTION('',(0.,0.,-1.));
#661=DIRECTION('',(0.,-1.,0.));
#662=DIRECTION('',(0.,1.,0.));
#663=DIRECTION('center_axis',(-1.,0.,0.));
#664=DIRECTION('ref_axis',(0.,0.,1.));
#665=DIRECTION('',(0.,0.,1.));
#666=DIRECTION('',(0.,-1.,0.));
#667=DIRECTION('center_axis',(-6.62819716194101E-16,1.,2.58913951638327E-16));
#668=DIRECTION('ref_axis',(0.,-2.58913951638327E-16,1.));
#669=DIRECTION('',(1.,6.62819716194101E-16,0.));
#670=DIRECTION('center_axis',(3.46944695195362E-16,0.,1.));
#671=DIRECTION('ref_axis',(1.,0.,-3.46944695195362E-16));
#672=DIRECTION('',(1.,2.08166817117217E-16,-3.46944695195362E-16));
#673=DIRECTION('',(0.,-1.,0.));
#674=DIRECTION('',(0.,-1.,0.));
#675=DIRECTION('center_axis',(1.,0.,0.));
#676=DIRECTION('ref_axis',(0.,0.,-1.));
#677=DIRECTION('',(0.,0.,-1.));
#678=DIRECTION('',(0.,1.,0.));
#679=DIRECTION('center_axis',(-1.,0.,0.));
#680=DIRECTION('ref_axis',(0.,0.,1.));
#681=DIRECTION('',(0.,0.,1.));
#682=DIRECTION('',(0.,1.,0.));
#683=DIRECTION('',(0.,1.,0.));
#684=DIRECTION('center_axis',(-6.62819716194121E-16,1.,5.17827903276658E-16));
#685=DIRECTION('ref_axis',(0.,-5.17827903276658E-16,1.));
#686=DIRECTION('',(1.,6.62819716194121E-16,0.));
#687=DIRECTION('center_axis',(0.,0.,1.));
#688=DIRECTION('ref_axis',(1.,0.,0.));
#689=DIRECTION('',(0.,1.,0.));
#690=DIRECTION('',(3.26795350822019E-46,-1.,0.));
#691=DIRECTION('center_axis',(-1.,0.,0.));
#692=DIRECTION('ref_axis',(0.,0.,1.));
#693=DIRECTION('',(0.,1.,0.));
#694=DIRECTION('center_axis',(1.,3.26795350822019E-46,0.));
#695=DIRECTION('ref_axis',(0.,0.,-1.));
#696=DIRECTION('',(3.26795350822019E-46,-1.,0.));
#697=DIRECTION('center_axis',(0.,0.,1.));
#698=DIRECTION('ref_axis',(1.,0.,0.));
#699=CARTESIAN_POINT('',(0.,0.,0.));
#700=CARTESIAN_POINT('Origin',(-0.25,-3.06161699786838E-17,0.));
#701=CARTESIAN_POINT('',(-0.25,0.035,0.));
#702=CARTESIAN_POINT('',(-0.25,0.035,0.5));
#703=CARTESIAN_POINT('',(-0.25,0.035,0.));
#704=CARTESIAN_POINT('',(-0.25,-3.06161699786838E-17,0.));
#705=CARTESIAN_POINT('',(-0.25,0.08375,0.));
#706=CARTESIAN_POINT('',(-0.25,-3.06161699786838E-17,0.5));
#707=CARTESIAN_POINT('',(-0.25,-3.06161699786838E-17,0.));
#708=CARTESIAN_POINT('',(-0.25,0.08375,0.5));
#709=CARTESIAN_POINT('Origin',(-0.75,-9.18485099360515E-17,0.5));
#710=CARTESIAN_POINT('',(-0.75,0.0349999999999999,0.5));
#711=CARTESIAN_POINT('',(-0.75,0.0349999999999999,0.));
#712=CARTESIAN_POINT('',(-0.75,0.0349999999999999,0.5));
#713=CARTESIAN_POINT('',(-0.75,-9.18485099360515E-17,0.5));
#714=CARTESIAN_POINT('',(-0.75,0.0837499999999999,0.5));
#715=CARTESIAN_POINT('',(-0.75,-9.18485099360515E-17,0.));
#716=CARTESIAN_POINT('',(-0.75,-9.18485099360515E-17,0.25));
#717=CARTESIAN_POINT('',(-0.75,0.0837499999999999,0.));
#718=CARTESIAN_POINT('Origin',(-0.5,0.0349999999999999,0.25));
#719=CARTESIAN_POINT('',(-0.5,0.0349999999999999,0.));
#720=CARTESIAN_POINT('',(-0.5,0.035,0.5));
#721=CARTESIAN_POINT('Origin',(0.,0.,0.));
#722=CARTESIAN_POINT('',(-1.,-1.22464679914735E-16,0.5));
#723=CARTESIAN_POINT('',(0.,0.,0.5));
#724=CARTESIAN_POINT('',(-1.,-1.22464679914735E-16,0.));
#725=CARTESIAN_POINT('',(-1.,-1.22464679914735E-16,0.));
#726=CARTESIAN_POINT('',(0.,0.,0.));
#727=CARTESIAN_POINT('Origin',(0.,0.,0.));
#728=CARTESIAN_POINT('',(0.,0.,0.));
#729=CARTESIAN_POINT('',(0.,0.,0.));
#730=CARTESIAN_POINT('',(0.,0.,0.5));
#731=CARTESIAN_POINT('',(0.,0.,0.));
#732=CARTESIAN_POINT('',(0.,0.,0.5));
#733=CARTESIAN_POINT('Origin',(-0.5,0.35,0.25));
#734=CARTESIAN_POINT('',(-0.2,0.35,0.5));
#735=CARTESIAN_POINT('',(-0.8,0.35,0.5));
#736=CARTESIAN_POINT('',(-1.,0.35,0.5));
#737=CARTESIAN_POINT('',(-0.2,0.35,0.));
#738=CARTESIAN_POINT('',(-0.2,0.35,0.5));
#739=CARTESIAN_POINT('',(-0.8,0.35,0.));
#740=CARTESIAN_POINT('',(0.,0.35,0.));
#741=CARTESIAN_POINT('',(-0.8,0.35,0.));
#742=CARTESIAN_POINT('Origin',(-1.,0.335,0.));
#743=CARTESIAN_POINT('',(-0.8,0.335,0.44));
#744=CARTESIAN_POINT('',(-0.8,0.335,0.06));
#745=CARTESIAN_POINT('',(-0.8,0.335,0.03));
#746=CARTESIAN_POINT('',(-0.9,0.335,0.06));
#747=CARTESIAN_POINT('',(-0.95,0.335,0.06));
#748=CARTESIAN_POINT('',(-0.9,0.335,0.));
#749=CARTESIAN_POINT('',(-0.9,0.335,0.));
#750=CARTESIAN_POINT('',(-1.,0.335,0.));
#751=CARTESIAN_POINT('',(-1.,0.335,0.));
#752=CARTESIAN_POINT('',(-1.,0.335,0.5));
#753=CARTESIAN_POINT('',(-1.,0.335,0.));
#754=CARTESIAN_POINT('',(-0.9,0.335,0.5));
#755=CARTESIAN_POINT('',(-1.,0.335,0.5));
#756=CARTESIAN_POINT('',(-0.9,0.335,0.44));
#757=CARTESIAN_POINT('',(-0.9,0.335,0.22));
#758=CARTESIAN_POINT('',(-0.9,0.335,0.44));
#759=CARTESIAN_POINT('Origin',(-1.,0.335,0.));
#760=CARTESIAN_POINT('',(-0.2,0.335,0.0599999999999999));
#761=CARTESIAN_POINT('',(-0.2,0.335,0.44));
#762=CARTESIAN_POINT('',(-0.200000000000001,0.335,0.219999999999999));
#763=CARTESIAN_POINT('',(-0.1,0.335,0.44));
#764=CARTESIAN_POINT('',(-0.55,0.335,0.44));
#765=CARTESIAN_POINT('',(-0.1,0.335,0.5));
#766=CARTESIAN_POINT('',(-0.1,0.335,0.25));
#767=CARTESIAN_POINT('',(-1.09476442525376E-46,0.335,0.5));
#768=CARTESIAN_POINT('',(-1.,0.335,0.5));
#769=CARTESIAN_POINT('',(-1.09476442525376E-46,0.335,0.));
#770=CARTESIAN_POINT('',(-1.09476442525376E-46,0.335,0.));
#771=CARTESIAN_POINT('',(-0.1,0.335,0.));
#772=CARTESIAN_POINT('',(-1.,0.335,0.));
#773=CARTESIAN_POINT('',(-0.1,0.335,0.0599999999999999));
#774=CARTESIAN_POINT('',(-0.1,0.335,0.0299999999999999));
#775=CARTESIAN_POINT('',(-0.6,0.335,0.06));
#776=CARTESIAN_POINT('Origin',(-0.8,0.335,0.44));
#777=CARTESIAN_POINT('',(-0.8,0.3,0.44));
#778=CARTESIAN_POINT('',(-0.9,0.3,0.44));
#779=CARTESIAN_POINT('',(-0.8,0.3,0.44));
#780=CARTESIAN_POINT('',(-0.8,0.335,0.44));
#781=CARTESIAN_POINT('',(-0.9,0.335,0.44));
#782=CARTESIAN_POINT('Origin',(-0.9,0.335,0.44));
#783=CARTESIAN_POINT('',(-0.9,0.3,0.5));
#784=CARTESIAN_POINT('',(-0.9,0.3,0.44));
#785=CARTESIAN_POINT('',(-0.9,0.25125,0.5));
#786=CARTESIAN_POINT('Origin',(-0.85,0.3,0.47));
#787=CARTESIAN_POINT('',(-0.8,0.3,0.5));
#788=CARTESIAN_POINT('',(-0.8,0.3,0.5));
#789=CARTESIAN_POINT('',(-0.675,0.3,0.5));
#790=CARTESIAN_POINT('Origin',(-0.1,0.335,0.5));
#791=CARTESIAN_POINT('',(-0.1,0.3,0.5));
#792=CARTESIAN_POINT('',(-0.1,0.3,0.44));
#793=CARTESIAN_POINT('',(-0.1,0.3,0.5));
#794=CARTESIAN_POINT('',(-0.1,0.25125,0.5));
#795=CARTESIAN_POINT('',(-0.1,0.335,0.44));
#796=CARTESIAN_POINT('Origin',(-0.1,0.335,0.44));
#797=CARTESIAN_POINT('',(-0.2,0.3,0.44));
#798=CARTESIAN_POINT('',(-0.1,0.3,0.44));
#799=CARTESIAN_POINT('',(-0.2,0.335,0.44));
#800=CARTESIAN_POINT('Origin',(-0.15,0.3,0.47));
#801=CARTESIAN_POINT('',(-0.2,0.3,0.5));
#802=CARTESIAN_POINT('',(-0.2,0.3,0.44));
#803=CARTESIAN_POINT('',(-0.325,0.3,0.5));
#804=CARTESIAN_POINT('Origin',(-0.9,0.335,0.06));
#805=CARTESIAN_POINT('',(-0.9,0.3,0.06));
#806=CARTESIAN_POINT('',(-0.8,0.3,0.06));
#807=CARTESIAN_POINT('',(-0.9,0.3,0.06));
#808=CARTESIAN_POINT('',(-0.9,0.335,0.06));
#809=CARTESIAN_POINT('',(-0.8,0.335,0.06));
#810=CARTESIAN_POINT('Origin',(-0.8,0.335,0.06));
#811=CARTESIAN_POINT('',(-0.8,0.3,0.));
#812=CARTESIAN_POINT('',(-0.8,0.3,0.06));
#813=CARTESIAN_POINT('',(-0.8,0.25125,0.5));
#814=CARTESIAN_POINT('',(-0.8,0.335,0.));
#815=CARTESIAN_POINT('Origin',(-0.9,0.335,0.));
#816=CARTESIAN_POINT('',(-0.9,0.3,0.));
#817=CARTESIAN_POINT('',(-0.9,0.3,0.));
#818=CARTESIAN_POINT('',(-0.9,0.25125,0.));
#819=CARTESIAN_POINT('Origin',(-0.85,0.3,0.03));
#820=CARTESIAN_POINT('',(-0.675,0.3,0.));
#821=CARTESIAN_POINT('Origin',(-0.2,0.335,0.0599999999999999));
#822=CARTESIAN_POINT('',(-0.2,0.3,0.0599999999999999));
#823=CARTESIAN_POINT('',(-0.1,0.3,0.0599999999999999));
#824=CARTESIAN_POINT('',(-0.2,0.3,0.0599999999999999));
#825=CARTESIAN_POINT('',(-0.2,0.335,0.0599999999999999));
#826=CARTESIAN_POINT('',(-0.1,0.335,0.0599999999999999));
#827=CARTESIAN_POINT('Origin',(-0.1,0.335,0.0599999999999999));
#828=CARTESIAN_POINT('',(-0.1,0.3,0.));
#829=CARTESIAN_POINT('',(-0.1,0.3,0.0599999999999999));
#830=CARTESIAN_POINT('',(-0.1,0.25125,0.));
#831=CARTESIAN_POINT('Origin',(-0.2,0.335,0.));
#832=CARTESIAN_POINT('',(-0.2,0.3,0.));
#833=CARTESIAN_POINT('',(-0.2,0.3,0.));
#834=CARTESIAN_POINT('',(-0.2,0.335,0.));
#835=CARTESIAN_POINT('',(-0.2,0.25125,0.5));
#836=CARTESIAN_POINT('Origin',(-0.15,0.3,0.0299999999999999));
#837=CARTESIAN_POINT('',(-0.325,0.3,0.));
#838=CARTESIAN_POINT('Origin',(-0.5,0.1675,0.));
#839=CARTESIAN_POINT('',(-1.,-1.22464679914735E-16,0.));
#840=CARTESIAN_POINT('',(-1.09476442525376E-46,0.335,0.));
#841=CARTESIAN_POINT('Origin',(-1.,-1.22464679914735E-16,0.));
#842=CARTESIAN_POINT('',(-1.,-1.22464679914735E-16,0.5));
#843=CARTESIAN_POINT('Origin',(-1.09476442525376E-46,0.335,0.));
#844=CARTESIAN_POINT('',(-1.09476442525376E-46,0.335,0.5));
#845=CARTESIAN_POINT('Origin',(-0.5,0.1675,0.5));
#846=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#850,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#847=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#850,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#848=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#846))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#850,#853,#852))
REPRESENTATION_CONTEXT('','3D')
);
#849=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#847))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#850,#853,#852))
REPRESENTATION_CONTEXT('','3D')
);
#850=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#851=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.CENTI.,.METRE.)
);
#852=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#853=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#854=SHAPE_DEFINITION_REPRESENTATION(#855,#856);
#855=PRODUCT_DEFINITION_SHAPE('',$,#858);
#856=SHAPE_REPRESENTATION('',(#546),#848);
#857=PRODUCT_DEFINITION_CONTEXT('part definition',#862,'design');
#858=PRODUCT_DEFINITION('124556','124556',#859,#857);
#859=PRODUCT_DEFINITION_FORMATION('',$,#864);
#860=PRODUCT_RELATED_PRODUCT_CATEGORY('124556','124556',(#864));
#861=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#862);
#862=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#863=PRODUCT_CONTEXT('part definition',#862,'mechanical');
#864=PRODUCT('124556','124556',$,(#863));
#865=PRESENTATION_STYLE_ASSIGNMENT((#866));
#866=SURFACE_STYLE_USAGE(.BOTH.,#867);
#867=SURFACE_SIDE_STYLE('',(#868));
#868=SURFACE_STYLE_FILL_AREA(#869);
#869=FILL_AREA_STYLE('',(#870));
#870=FILL_AREA_STYLE_COLOUR('',#871);
#871=COLOUR_RGB('',0.752941176470588,0.752941176470588,0.752941176470588);
ENDSEC;
END-ISO-10303-21;
