#!/usr/bin/env python3
"""
SnapEDA Corrected <PERSON><PERSON> - <PERSON><PERSON> first, THEN enter part number
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def corrected_snapeda_download():
    print("🔍 SNAPEDA CORRECTED LOGIN SEQUENCE")
    print("=" * 60)
    
    # Setup Chrome with download directory
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
        "profile.default_content_setting_values.notifications": 2
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Go to SnapEDA and login (NO part number yet)
        print("🔸 Step 1: Going to SnapEDA login page...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Click login link
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        print("✅ On login page")
        
        # Enter ONLY email and password (NO part number)
        print("🔸 Step 2: Entering credentials...")
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.clear()
        email_field.send_keys("<EMAIL>")
        print("✅ Email entered")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.clear()
        password_field.send_keys("Lennyai123#")
        print("✅ Password entered")
        
        # Click Log In button (without part number)
        print("🔸 Step 3: Clicking Log In button...")
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(10)
        
        # Check if login was successful
        current_url = driver.current_url.lower()
        if "login" not in current_url:
            print("✅ Login successful - now on main page")
        else:
            print("❌ Login may have failed - still on login page")
            print(f"   Current URL: {driver.current_url}")
        
        # Step 4: NOW search for the part number
        print("🔸 Step 4: Searching for part number...")
        
        # Try to find search field on the main page
        search_selectors = [
            "input[name='q']",
            "input[placeholder*='search']",
            "#search-input",
            ".search-input",
            "input[type='search']"
        ]
        
        search_field = None
        for selector in search_selectors:
            try:
                search_field = driver.find_element(By.CSS_SELECTOR, selector)
                if search_field.is_displayed():
                    print(f"✅ Found search field with selector: {selector}")
                    break
            except:
                continue
        
        if search_field:
            search_field.clear()
            search_field.send_keys("LM358N")
            search_field.send_keys(Keys.RETURN)
            time.sleep(5)
            print("✅ Part number search submitted")
        else:
            print("⚠️ Could not find search field, navigating directly to part page")
        
        # Step 5: Navigate to the specific part page
        print("🔸 Step 5: Going to LM358N part page...")
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        print(f"✅ On part page: {driver.current_url}")
        
        # Step 6: Click 3D Model tab
        print("🔸 Step 6: Looking for 3D Model tab...")
        three_d_selectors = [
            "//li[text()='3D Model']",
            "//a[text()='3D Model']",
            "//span[text()='3D Model']",
            "//div[contains(@class, '3d-model')]//a",
            "//button[contains(text(), '3D Model')]"
        ]
        
        three_d_tab = None
        for selector in three_d_selectors:
            try:
                tab = driver.find_element(By.XPATH, selector)
                if tab.is_displayed():
                    three_d_tab = tab
                    print(f"✅ Found 3D Model tab with selector: {selector}")
                    break
            except:
                continue
        
        if not three_d_tab:
            print("❌ Could not find 3D Model tab")
            print("🔍 Available tabs/elements:")
            try:
                all_tabs = driver.find_elements(By.XPATH, "//li | //a | //button")
                for i, tab in enumerate(all_tabs[:10]):  # Show first 10
                    if tab.is_displayed() and tab.text.strip():
                        print(f"   {i+1}. {tab.tag_name}: '{tab.text.strip()}'")
            except:
                pass
            return
        
        # Click 3D Model tab
        print("🔸 Step 7: Clicking 3D Model tab...")
        three_d_tab.click()
        time.sleep(10)
        print("✅ 3D Model section opened")
        
        # Step 8: Find and click download button
        print("🔸 Step 8: Looking for download button...")
        before_files = set(os.listdir(download_dir))
        print(f"📁 Files before: {len(before_files)} files")
        
        download_selectors = [
            "//a[contains(@class, '3D-model-download')]",
            "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
            "//a[text()='Download 3D Model' and contains(@class, 'orange')]",
            "//a[contains(text(), 'Download 3D Model')]",
            "//button[contains(text(), 'Download')]"
        ]
        
        download_button = None
        for selector in download_selectors:
            try:
                button = driver.find_element(By.XPATH, selector)
                if button.is_displayed():
                    download_button = button
                    print(f"✅ Found download button with selector: {selector}")
                    break
            except:
                continue
        
        if not download_button:
            print("❌ Could not find download button")
            return
        
        print(f"✅ Download button found!")
        print(f"   Text: '{download_button.text.strip()}'")
        print(f"   Class: '{download_button.get_attribute('class')}'")
        
        # Click download button
        print("🔸 Step 9: Clicking download button...")
        download_button.click()
        print("✅ Download button clicked!")
        
        # Monitor for downloads
        print("📁 Monitoring downloads for 30 seconds...")
        for i in range(30):
            time.sleep(1)
            current_files = set(os.listdir(download_dir))
            new_files = current_files - before_files
            
            if new_files:
                print(f"🎉 NEW FILE DOWNLOADED after {i+1} seconds!")
                for f in sorted(new_files):
                    file_path = os.path.join(download_dir, f)
                    file_size = os.path.getsize(file_path)
                    print(f"  📄 {f} ({file_size} bytes)")
                    
                    # If it's a STEP file, rename it properly
                    if f.endswith('.step') or f.endswith('.stp'):
                        final_name = f"SnapEDA-Texas Instruments-LM358N.step"
                        final_path = os.path.join(download_dir, final_name)
                        if not os.path.exists(final_path):
                            os.rename(file_path, final_path)
                            print(f"✅ Renamed to: {final_name}")
                            return final_path
                break
            
            if i % 5 == 0 and i > 0:
                print(f"   ⏳ {i+1}/30 seconds...")
        
        if not new_files:
            print("❌ No download detected")
            
            # Debug info
            print(f"\n🔍 DEBUG INFO:")
            print(f"   Current URL: {driver.current_url}")
            print(f"   Page title: {driver.title}")
        
        print(f"\n🔸 Test complete - browser staying open for inspection")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    corrected_snapeda_download()
