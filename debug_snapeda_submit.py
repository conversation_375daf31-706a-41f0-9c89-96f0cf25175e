#!/usr/bin/env python3
"""
Debug SnapEDA submit button specifically
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def debug_submit():
    print("🔍 Debugging SnapEDA submit button...")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Use correct SnapEDA credentials
        email = "<EMAIL>"
        password = "Lennyai123#"
        print(f"✅ Using SnapEDA credentials: {email}")
        
        # Go to login page
        print("🔸 Going to login page...")
        driver.get("https://www.snapeda.com/account/login/")
        time.sleep(3)
        
        # Fill form
        print("🔸 Filling form...")
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        password_field = driver.find_element(By.CSS_SELECTOR, "#id_password")
        
        email_field.clear()
        email_field.send_keys(email)
        password_field.clear()
        password_field.send_keys(password)
        print("✅ Form filled")
        
        # Debug all buttons
        print("\n🔍 DEBUGGING ALL BUTTONS:")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        all_submits = driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
        
        print(f"Found {len(all_buttons)} buttons:")
        for i, btn in enumerate(all_buttons):
            try:
                if btn.is_displayed():
                    text = btn.text.strip()
                    btn_type = btn.get_attribute('type')
                    btn_class = btn.get_attribute('class')
                    enabled = btn.is_enabled()
                    print(f"  Button {i+1}: '{text}' type='{btn_type}' class='{btn_class}' enabled={enabled}")
            except Exception as e:
                print(f"  Button {i+1}: Error - {e}")
        
        print(f"\nFound {len(all_submits)} submit inputs:")
        for i, sub in enumerate(all_submits):
            try:
                if sub.is_displayed():
                    value = sub.get_attribute('value')
                    sub_class = sub.get_attribute('class')
                    enabled = sub.is_enabled()
                    print(f"  Submit {i+1}: value='{value}' class='{sub_class}' enabled={enabled}")
            except Exception as e:
                print(f"  Submit {i+1}: Error - {e}")
        
        # Try to find and click submit button
        print("\n🔸 Looking for submit button...")
        submit_button = None
        
        # Try the most common selectors first - SnapEDA uses "Log In"
        selectors = [
            "//button[contains(text(), 'Log In')]",  # Try text-based first
            "//input[@value='Log In']",
            "button[type='submit']",
            "input[type='submit']",
            "form button",
            "form input[type='submit']"
        ]
        
        for selector in selectors:
            try:
                submit_button = driver.find_element(By.CSS_SELECTOR, selector)
                if submit_button.is_displayed() and submit_button.is_enabled():
                    btn_text = submit_button.text or submit_button.get_attribute('value')
                    print(f"✅ Found submit button: {selector} - '{btn_text}'")
                    break
            except:
                continue
        
        if submit_button:
            print("🔸 Attempting to click submit button...")
            try:
                submit_button.click()
                print("✅ Clicked submit button!")
                time.sleep(5)
                print(f"✅ New URL: {driver.current_url}")
                
                if "login" not in driver.current_url.lower():
                    print("✅ LOGIN SUCCESS - redirected away from login page!")
                else:
                    print("❌ Still on login page - login may have failed")
                    
            except Exception as e:
                print(f"❌ Click failed: {e}")
                print("🔸 Trying JavaScript click...")
                try:
                    driver.execute_script("arguments[0].click();", submit_button)
                    print("✅ JavaScript click succeeded!")
                    time.sleep(5)
                    print(f"✅ New URL: {driver.current_url}")
                except Exception as js_e:
                    print(f"❌ JavaScript click also failed: {js_e}")
        else:
            print("❌ No submit button found!")
        
        print("\n🎯 Debug complete - browser will stay open")
        input("Press Enter to close...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_submit()
