#!/usr/bin/env python3
"""
Test the fixed SnapEDA finder
"""

import sys
import os
sys.path.append('save')

from snapeda_3d_finder_final import SnapEDA3DFinder

def test_snapeda():
    print("🔍 Testing fixed SnapEDA finder...")
    
    finder = SnapEDA3DFinder()
    
    # Test with LM358N
    result = finder.search_and_download("Texas Instruments", "LM358N", silent=False)
    
    if result:
        print(f"✅ SUCCESS: Downloaded {result}")
    else:
        print("❌ FAILED: No file downloaded")
    
    return result

if __name__ == "__main__":
    test_snapeda()
