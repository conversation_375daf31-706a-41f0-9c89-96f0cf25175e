#!/usr/bin/env python3
"""
SnapEDA Manual Sequence - Exactly mimic the manual login process
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON><PERSON><PERSON>

def snapeda_manual_sequence():
    print("🔍 SNAPEDA MANUAL SEQUENCE - MIMIC EXACT MANUAL PROCESS")
    print("=" * 70)
    
    # Setup Chrome with more human-like behavior
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Add user agent to look more human
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # Remove webdriver property to avoid detection
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Step 1: Go to SnapEDA homepage
        print("🔸 Step 1: Going to SnapEDA homepage...")
        driver.get("https://www.snapeda.com/")
        time.sleep(3)
        print(f"✅ On homepage: {driver.current_url}")
        
        # Step 2: Click login link (first click)
        print("🔸 Step 2: Clicking login link...")
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        
        # Use ActionChains for more human-like clicking
        actions = ActionChains(driver)
        actions.move_to_element(login_link).pause(0.5).click().perform()
        time.sleep(3)
        
        current_url = driver.current_url
        print(f"✅ After login click: {current_url}")
        
        if "login" not in current_url.lower():
            print("❌ Not on login page after clicking login link")
            return
        
        # Step 3: Type email (slowly, like human)
        print("🔸 Step 3: Typing email...")
        email_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "#id_username"))
        )
        
        # Clear field first
        email_field.clear()
        time.sleep(0.5)
        
        # Type email character by character (more human-like)
        email = "<EMAIL>"
        for char in email:
            email_field.send_keys(char)
            time.sleep(0.05)  # Small delay between characters
        
        print("✅ Email typed")
        
        # Step 4: Type password (slowly, like human)
        print("🔸 Step 4: Typing password...")
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        
        # Clear field first
        password_field.clear()
        time.sleep(0.5)
        
        # Type password character by character
        password = "Lennyai123#"
        for char in password:
            password_field.send_keys(char)
            time.sleep(0.05)  # Small delay between characters
        
        print("✅ Password typed")
        
        # Step 5: Click Log In button (second click) - try multiple methods
        print("🔸 Step 5: Clicking Log In button...")
        
        # Wait a moment like a human would
        time.sleep(1)
        
        login_success = False
        
        # Method 1: Find submit button and click with ActionChains
        try:
            submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            if submit_btn.is_displayed() and submit_btn.is_enabled():
                print("✅ Found submit button - clicking with ActionChains...")
                actions = ActionChains(driver)
                actions.move_to_element(submit_btn).pause(0.5).click().perform()
                login_success = True
        except Exception as e:
            print(f"⚠️ Method 1 failed: {e}")
        
        # Method 2: Try JavaScript click
        if not login_success:
            try:
                submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                print("✅ Trying JavaScript click...")
                driver.execute_script("arguments[0].click();", submit_btn)
                login_success = True
            except Exception as e:
                print(f"⚠️ Method 2 failed: {e}")
        
        # Method 3: Try Enter key on password field
        if not login_success:
            try:
                print("✅ Trying Enter key on password field...")
                password_field.send_keys(Keys.RETURN)
                login_success = True
            except Exception as e:
                print(f"⚠️ Method 3 failed: {e}")
        
        if not login_success:
            print("❌ Could not click Log In button")
            return
        
        print("✅ Log In button clicked!")
        
        # Step 6: Wait for login to complete and check result
        print("🔸 Step 6: Waiting for login to complete...")
        time.sleep(8)  # Give more time for login
        
        final_url = driver.current_url
        print(f"✅ After login: {final_url}")
        
        if "login" in final_url.lower():
            print("❌ Still on login page - login failed")
            
            # Check for specific error messages
            try:
                error_selectors = [
                    ".alert-danger",
                    ".error",
                    ".errorlist",
                    "[class*='error']",
                    ".invalid-feedback"
                ]
                
                for selector in error_selectors:
                    try:
                        errors = driver.find_elements(By.CSS_SELECTOR, selector)
                        for error in errors:
                            if error.is_displayed() and error.text.strip():
                                print(f"   Error: {error.text.strip()}")
                    except:
                        continue
            except:
                pass
            return
        else:
            print("🎉 LOGIN SUCCESSFUL! Now on signed-in page")
        
        # Step 7: Search for part number
        print("🔸 Step 7: Searching for LM358N...")
        
        # Look for search field
        search_selectors = [
            "input[name='q']",
            "input[placeholder*='search']",
            "#search-input",
            ".search-input"
        ]
        
        search_field = None
        for selector in search_selectors:
            try:
                search_field = driver.find_element(By.CSS_SELECTOR, selector)
                if search_field.is_displayed():
                    print(f"✅ Found search field: {selector}")
                    break
            except:
                continue
        
        if search_field:
            search_field.clear()
            search_field.send_keys("LM358N")
            search_field.send_keys(Keys.RETURN)
            time.sleep(5)
            print("✅ Part search submitted")
        
        # Step 8: Go to part page
        print("🔸 Step 8: Going to LM358N part page...")
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(8)
        print("✅ On part page")
        
        # Step 9: Look for and click 3D Model tab
        print("🔸 Step 9: Looking for 3D Model tab...")
        
        three_d_selectors = [
            "//li[text()='3D Model']",
            "//a[text()='3D Model']",
            "//span[text()='3D Model']",
            "//li[contains(text(), '3D Model')]"
        ]
        
        three_d_tab = None
        for selector in three_d_selectors:
            try:
                tab = driver.find_element(By.XPATH, selector)
                if tab.is_displayed():
                    three_d_tab = tab
                    print(f"✅ Found 3D Model tab")
                    break
            except:
                continue
        
        if not three_d_tab:
            print("❌ 3D Model tab not found")
            print("🔍 Available elements:")
            try:
                elements = driver.find_elements(By.XPATH, "//li | //a | //span")
                for i, elem in enumerate(elements[:15]):
                    if elem.is_displayed() and elem.text.strip():
                        print(f"   {i+1}. {elem.tag_name}: '{elem.text.strip()}'")
            except:
                pass
            
            print(f"\n🔸 Browser staying open for manual inspection")
            print(f"🔸 Press Enter to close...")
            input()
            return
        
        # Click 3D Model tab
        actions = ActionChains(driver)
        actions.move_to_element(three_d_tab).pause(0.5).click().perform()
        time.sleep(8)
        print("✅ 3D Model tab clicked")
        
        # Step 10: Look for download button
        print("🔸 Step 10: Looking for download button...")
        
        download_selectors = [
            "//a[contains(@class, '3D-model-download')]",
            "//a[contains(text(), 'Download 3D Model')]",
            "//button[contains(text(), 'Download')]"
        ]
        
        download_button = None
        for selector in download_selectors:
            try:
                button = driver.find_element(By.XPATH, selector)
                if button.is_displayed():
                    download_button = button
                    print(f"✅ Found download button")
                    break
            except:
                continue
        
        if download_button:
            # Monitor files before download
            before_files = set(os.listdir(download_dir))
            print(f"📁 Files before: {len(before_files)}")
            
            # Click download button
            print("🔸 Step 11: Clicking download button...")
            actions = ActionChains(driver)
            actions.move_to_element(download_button).pause(0.5).click().perform()
            print("✅ Download button clicked!")
            
            # Monitor for downloads
            print("📁 Monitoring for downloads (45 seconds)...")
            for i in range(45):
                time.sleep(1)
                current_files = set(os.listdir(download_dir))
                new_files = current_files - before_files
                
                if new_files:
                    print(f"🎉 Download detected after {i+1} seconds!")
                    for f in new_files:
                        file_path = os.path.join(download_dir, f)
                        file_size = os.path.getsize(file_path)
                        print(f"  📄 {f} ({file_size} bytes)")
                        
                        if f.endswith('.step') or f.endswith('.stp'):
                            final_name = f"SnapEDA-Texas Instruments-LM358N.step"
                            final_path = os.path.join(download_dir, final_name)
                            if not os.path.exists(final_path):
                                os.rename(file_path, final_path)
                                print(f"✅ Renamed to: {final_name}")
                    break
                
                if i % 5 == 0 and i > 0:
                    print(f"   ⏳ {i+1}/45 seconds...")
            
            if not new_files:
                print("❌ No download detected")
        else:
            print("❌ Download button not found")
        
        print(f"\n🔸 Process complete - browser staying open for inspection")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    snapeda_manual_sequence()
