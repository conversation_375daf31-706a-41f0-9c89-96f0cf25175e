#!/usr/bin/env python3
"""
Debug UltraLibrarian search box issue
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def debug_ultralibrarian():
    """Debug what's actually on the UltraLibrarian page"""
    
    print("🔍 Debugging UltraLibrarian Search Box Issue")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # Run in background
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Load homepage
        print("🔸 Loading UltraLibrarian homepage...")
        driver.get('https://www.ultralibrarian.com/')
        time.sleep(5)
        
        print(f"✅ Page loaded: {driver.title}")
        
        # Step 2: Look for login links
        print("\n🔸 Looking for login links...")
        login_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='Login'], a[href*='login'], a[href*='signin'], a[href*='sign-in']")
        print(f"Found {len(login_links)} login links:")
        for i, link in enumerate(login_links):
            href = link.get_attribute('href')
            text = link.text.strip()
            print(f"  {i+1}. '{text}' -> {href}")
        
        # Step 3: Try to login
        if login_links:
            print(f"\n🔸 Clicking first login link...")
            try:
                login_links[0].click()
                time.sleep(5)
                print(f"✅ After login click: {driver.current_url}")
            except Exception as e:
                print(f"❌ Login click failed: {e}")
                return
        
        # Step 4: Look for all input fields
        print("\n🔸 Looking for all input fields...")
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(all_inputs)} input fields:")
        
        for i, inp in enumerate(all_inputs):
            try:
                input_type = inp.get_attribute('type') or 'text'
                placeholder = inp.get_attribute('placeholder') or ''
                name = inp.get_attribute('name') or ''
                id_attr = inp.get_attribute('id') or ''
                visible = inp.is_displayed()
                enabled = inp.is_enabled()
                
                print(f"  {i+1}. Type: {input_type}, Placeholder: '{placeholder}', Name: '{name}', ID: '{id_attr}', Visible: {visible}, Enabled: {enabled}")
            except Exception as e:
                print(f"  {i+1}. Error reading input: {e}")
        
        # Step 5: Look for search-related elements
        print("\n🔸 Looking for search-related elements...")
        
        # Try different search selectors
        selectors = [
            "input[placeholder*='search' i]",
            "input[type='search']",
            "input[name*='search' i]",
            "input[id*='search' i]",
            "input[class*='search' i]",
            ".search input",
            "#search",
            "[data-search]"
        ]
        
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ Found {len(elements)} elements with selector: {selector}")
                    for j, elem in enumerate(elements):
                        visible = elem.is_displayed()
                        enabled = elem.is_enabled()
                        placeholder = elem.get_attribute('placeholder') or ''
                        print(f"    {j+1}. Visible: {visible}, Enabled: {enabled}, Placeholder: '{placeholder}'")
                else:
                    print(f"  ❌ No elements found with selector: {selector}")
            except Exception as e:
                print(f"  ❌ Error with selector {selector}: {e}")
        
        # Step 6: Save page source for analysis
        print("\n🔸 Saving page source...")
        with open('ultralibrarian_debug.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("✅ Page source saved to ultralibrarian_debug.html")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        driver.quit()
        print("\n🔒 Browser closed")

if __name__ == "__main__":
    debug_ultralibrarian()
