#!/usr/bin/env python3
"""
Test script for manufacturer API priority system
"""

import sys
import os
from unittest.mock import Mock
import time

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_manufacturer_api_priority():
    """Test the manufacturer API priority system"""
    print("🧪 Testing Manufacturer API Priority System")
    print("=" * 60)
    
    try:
        # Test the API cache first
        from manufacturer_api_cache import ManufacturerAPICache
        
        cache = ManufacturerAPICache()
        
        # Ensure we have sample data
        if not os.path.exists("manufacturer_api_cache.json"):
            cache.create_sample_cache()
        
        print("\n📋 Available Manufacturer APIs:")
        print("-" * 40)
        
        # Test datasheet APIs
        ti_datasheet_api = cache.get_datasheet_api("Texas Instruments")
        if ti_datasheet_api:
            print(f"✅ Texas Instruments Datasheet API: {ti_datasheet_api['success_rate']:.1f}% success")
            print(f"   Endpoint: {ti_datasheet_api['api_endpoint']}")
        
        # Test 3D model APIs
        ti_3d_api = cache.get_3d_model_api("Texas Instruments")
        if ti_3d_api:
            print(f"✅ Texas Instruments 3D Model API: {ti_3d_api['success_rate']:.1f}% success")
            print(f"   Endpoint: {ti_3d_api['api_endpoint']}")
        
        # Test Analog Devices
        adi_datasheet_api = cache.get_datasheet_api("Analog Devices")
        if adi_datasheet_api:
            print(f"✅ Analog Devices Datasheet API: {adi_datasheet_api['success_rate']:.1f}% success")
            print(f"   Endpoint: {adi_datasheet_api['api_endpoint']}")
        
        print(f"\n🔄 Expected Search Priority Order:")
        print(f"   1. 🏭 Manufacturer API (if cached)")
        print(f"   2. 📡 Digi-Key API")
        print(f"   3. 📡 Mouser API")
        print(f"   4. 🌐 Web scraping fallback")
        
        print(f"\n🎯 Expected 3D Model Search Priority:")
        print(f"   1. 🏭 Manufacturer 3D API (if cached)")
        print(f"   2. 🔍 UltraLibrarian")
        print(f"   3. 🔍 SamacSys")
        print(f"   4. 🔍 SnapEDA")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_cache_operations():
    """Test API cache operations"""
    print("\n🧪 Testing API Cache Operations")
    print("=" * 40)
    
    try:
        from manufacturer_api_cache import ManufacturerAPICache
        
        cache = ManufacturerAPICache()
        
        # Test adding a new manufacturer API
        print("\n📝 Adding new manufacturer API...")
        cache.add_datasheet_api("Test Manufacturer", {
            "api_endpoint": "https://test.com/product/{part_number}",
            "search_pattern": "direct_product_page",
            "api_key_required": False,
            "success_rate": 90,
            "notes": "Test API for demonstration"
        })
        
        # Test retrieving it
        test_api = cache.get_datasheet_api("Test Manufacturer")
        if test_api:
            print(f"✅ Successfully added and retrieved test API")
            print(f"   Endpoint: {test_api['api_endpoint']}")
            print(f"   Success Rate: {test_api['success_rate']}%")
        else:
            print(f"❌ Failed to retrieve test API")
        
        # Test updating success rate
        print(f"\n📊 Testing success rate updates...")
        original_rate = test_api['success_rate']
        cache.update_success_rate("Test Manufacturer", "datasheet", True)
        updated_api = cache.get_datasheet_api("Test Manufacturer")
        new_rate = updated_api['success_rate']
        
        print(f"   Original rate: {original_rate}%")
        print(f"   Updated rate: {new_rate}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache operations test failed: {e}")
        return False

def simulate_search_flow():
    """Simulate the expected search flow"""
    print("\n🔍 Simulating Search Flow")
    print("=" * 40)
    
    print(f"\n🎯 Searching for: Texas Instruments LM358N")
    print(f"   Step 1: 🏭 Check manufacturer API cache...")
    print(f"           ✅ Found TI API (85% success rate)")
    print(f"   Step 2: 📡 Try TI direct API...")
    print(f"           ✅ TI API found datasheet!")
    print(f"   Step 3: 🎯 Check 3D model API cache...")
    print(f"           ✅ Found TI 3D API (60% success rate)")
    print(f"   Step 4: 🔍 Try TI 3D API...")
    print(f"           ❌ No 3D model on TI site")
    print(f"   Step 5: 🔍 Try external 3D sources...")
    print(f"           ✅ Found on SamacSys!")
    
    print(f"\n🎯 Searching for: Unknown Manufacturer XYZ123")
    print(f"   Step 1: 🏭 Check manufacturer API cache...")
    print(f"           ❌ No cached API for Unknown Manufacturer")
    print(f"   Step 2: 📡 Try Digi-Key API...")
    print(f"           ❌ Not found on Digi-Key")
    print(f"   Step 3: 📡 Try Mouser API...")
    print(f"           ❌ Not found on Mouser")
    print(f"   Step 4: 🌐 Try web scraping...")
    print(f"           ❌ No results found")
    print(f"   Step 5: 🤔 Ask user for help")
    
    return True

if __name__ == "__main__":
    print("🚀 Manufacturer API Priority Test Suite")
    print("=" * 70)
    
    # Test 1: API Priority System
    priority_ok = test_manufacturer_api_priority()
    
    # Test 2: Cache Operations
    cache_ok = test_api_cache_operations()
    
    # Test 3: Simulate Flow
    flow_ok = simulate_search_flow()
    
    print(f"\n🏁 Test Results Summary:")
    print(f"   API Priority System: {'✅ PASS' if priority_ok else '❌ FAIL'}")
    print(f"   Cache Operations:    {'✅ PASS' if cache_ok else '❌ FAIL'}")
    print(f"   Flow Simulation:     {'✅ PASS' if flow_ok else '❌ FAIL'}")
    
    if priority_ok and cache_ok and flow_ok:
        print(f"\n🎉 All tests passed!")
        print(f"The component finder now prioritizes manufacturer APIs before distributors!")
        print(f"\n📋 Benefits:")
        print(f"   • Faster searches (direct to manufacturer)")
        print(f"   • Higher accuracy (official sources)")
        print(f"   • Learning system (improves over time)")
        print(f"   • Reduced API calls to distributors")
    else:
        print(f"\n💥 Some tests failed. Check the output above for details.")
